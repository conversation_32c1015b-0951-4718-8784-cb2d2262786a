//go:build !windows
// +build !windows

package utils

import (
	"path/filepath"
	"syscall"
)

// GetDiskUsage 获取磁盘使用情况 (Unix/Linux/macOS)
func GetDiskUsage(path string) (total, free, used uint64, err error) {
	var stat syscall.Statfs_t

	// 获取绝对路径
	absPath, err := filepath.Abs(path)
	if err != nil {
		return 0, 0, 0, err
	}

	// 获取文件系统统计信息
	err = syscall.Statfs(absPath, &stat)
	if err != nil {
		return 0, 0, 0, err
	}

	// 计算总空间、可用空间和已用空间
	total = stat.Blocks * uint64(stat.Bsize)
	free = stat.Bfree * uint64(stat.Bsize)
	used = total - free

	return total, free, used, nil
}
