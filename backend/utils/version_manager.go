package utils

import (
	"database/sql"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"school-package-system/utils/migrations"
	"time"
)

// VersionManager 版本管理器
type VersionManager struct {
	currentVersion string
	migrations     []migrations.Migration
	ctx            *migrations.MigrationContext
	logger         *Logger
	db             *sql.DB
}

// LoggerAdapter 适配器，将utils.Logger适配为migrations.Logger
type LoggerAdapter struct {
	logger *Logger
}

func (la *LoggerAdapter) Info(module, action, message string) {
	la.logger.Info(module, action, message)
}

func (la *LoggerAdapter) Error(module, action, message string) {
	la.logger.Error(module, action, message)
}

func (la *LoggerAdapter) Warn(module, action, message string) {
	la.logger.Warn(module, action, message)
}

// NewVersionManager 创建版本管理器
func NewVersionManager(currentVersion string, logger *Logger, db *sql.DB) *VersionManager {
	loggerAdapter := &LoggerAdapter{logger: logger}

	return &VersionManager{
		currentVersion: currentVersion,
		logger:         logger,
		db:             db,
		ctx: &migrations.MigrationContext{
			DB:        db,
			Logger:    loggerAdapter,
			DataDir:   "./data",
			ConfigDir: "./config",
			BackupDir: "./backup",
			TempDir:   "./temp",
		},
	}
}

// RegisterMigrations 注册所有迁移
func (vm *VersionManager) RegisterMigrations() {
	// 使用集中配置获取所有迁移
	vm.migrations = migrations.GetMigrations()
}

// CheckAndMigrate 检查并执行迁移
func (vm *VersionManager) CheckAndMigrate() error {
	// 注册所有迁移
	vm.RegisterMigrations()

	// 获取数据库中记录的版本
	dbVersion, err := vm.getCurrentVersion()
	if err != nil {
		return fmt.Errorf("获取数据库版本失败: %v", err)
	}

	vm.logger.Info("VersionManager", "CheckAndMigrate",
		fmt.Sprintf("数据库版本: %s, 程序版本: %s", dbVersion, vm.currentVersion))

	// 如果版本相同，无需迁移
	if dbVersion == vm.currentVersion {
		vm.logger.Info("VersionManager", "CheckAndMigrate", "版本一致，无需迁移")
		return nil
	}

	// 执行线性迁移
	return vm.executeLinearMigration(dbVersion, vm.currentVersion)
}

// executeLinearMigration 执行线性迁移
func (vm *VersionManager) executeLinearMigration(fromVersion, toVersion string) error {
	vm.logger.Info("VersionManager", "Migration",
		fmt.Sprintf("开始版本迁移: %s → %s", fromVersion, toVersion))

	// 创建备份目录
	if err := vm.ensureBackupDir(); err != nil {
		return fmt.Errorf("创建备份目录失败: %v", err)
	}

	// 备份数据库
	if err := vm.backupDatabase(); err != nil {
		return fmt.Errorf("备份数据库失败: %v", err)
	}

	// 获取需要执行的迁移
	migrationsToRun, err := vm.getMigrationsToRun(fromVersion, toVersion)
	if err != nil {
		return fmt.Errorf("计算迁移路径失败: %v", err)
	}

	vm.logger.Info("VersionManager", "Migration",
		fmt.Sprintf("需要执行 %d 个迁移步骤", len(migrationsToRun)))

	// 逐步执行迁移
	for i, migration := range migrationsToRun {
		vm.logger.Info("VersionManager", "Migration",
			fmt.Sprintf("执行迁移 %d/%d: %s", i+1, len(migrationsToRun), migration.Version()))

		if err := vm.executeMigration(migration); err != nil {
			vm.logger.Error("VersionManager", "Migration",
				fmt.Sprintf("迁移失败: %s - %v", migration.Version(), err))

			// 记录失败日志
			vm.addMigrationLog(fromVersion, migration.Version(),
				migration.Description(), "failed", err.Error())

			return fmt.Errorf("迁移失败 %s: %v", migration.Version(), err)
		}

		// 更新版本记录
		if err := vm.setCurrentVersion(migration.Version()); err != nil {
			return fmt.Errorf("更新版本记录失败: %v", err)
		}

		// 记录成功日志
		vm.addMigrationLog(fromVersion, migration.Version(),
			migration.Description(), "success")

		fromVersion = migration.Version() // 更新起始版本
	}

	vm.logger.Info("VersionManager", "Migration",
		fmt.Sprintf("版本迁移完成: %s", toVersion))

	return nil
}

// getMigrationsToRun 获取需要执行的迁移
func (vm *VersionManager) getMigrationsToRun(fromVersion, toVersion string) ([]migrations.Migration, error) {
	// 使用集中配置获取版本升级路径
	allVersions := migrations.GetVersionPath()

	fromIndex := vm.findVersionIndex(allVersions, fromVersion)
	toIndex := vm.findVersionIndex(allVersions, toVersion)

	if fromIndex == -1 {
		return nil, fmt.Errorf("起始版本 %s 不在支持的升级路径中", fromVersion)
	}

	if toIndex == -1 {
		return nil, fmt.Errorf("目标版本 %s 不在支持的升级路径中", toVersion)
	}

	if fromIndex >= toIndex {
		return nil, fmt.Errorf("不支持版本回退: %s → %s", fromVersion, toVersion)
	}

	var migrationsToRun []migrations.Migration
	for i := fromIndex; i < toIndex; i++ {
		targetVersion := allVersions[i+1]

		// 查找对应的迁移类
		for _, migration := range vm.migrations {
			if migration.Version() == targetVersion {
				migrationsToRun = append(migrationsToRun, migration)
				break
			}
		}
	}

	return migrationsToRun, nil
}

// findVersionIndex 查找版本在数组中的索引
func (vm *VersionManager) findVersionIndex(versions []string, version string) int {
	for i, v := range versions {
		if v == version {
			return i
		}
	}
	return -1
}

// executeMigration 执行单个迁移
func (vm *VersionManager) executeMigration(migration migrations.Migration) error {
	vm.logger.Info("VersionManager", "ExecuteMigration",
		fmt.Sprintf("开始执行迁移: %s - %s", migration.Version(), migration.Description()))

	// 执行迁移
	if err := migration.Execute(vm.ctx); err != nil {
		return err
	}

	vm.logger.Info("VersionManager", "ExecuteMigration",
		fmt.Sprintf("迁移执行成功: %s", migration.Version()))

	return nil
}

// ensureBackupDir 确保备份目录存在
func (vm *VersionManager) ensureBackupDir() error {
	return os.MkdirAll(vm.ctx.BackupDir, 0755)
}

// backupDatabase 备份数据库
func (vm *VersionManager) backupDatabase() error {
	dbPath := os.Getenv("DB_PATH")
	if dbPath == "" {
		dbPath = "./data/school-package.db"
	}

	backupPath := filepath.Join(vm.ctx.BackupDir,
		fmt.Sprintf("school-package-%s.db.backup", vm.currentVersion))

	vm.logger.Info("VersionManager", "Backup",
		fmt.Sprintf("备份数据库: %s → %s", dbPath, backupPath))

	return vm.copyFile(dbPath, backupPath)
}

// copyFile 复制文件
func (vm *VersionManager) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return err
	}

	return destFile.Sync()
}

// getCurrentVersion 获取当前版本
func (vm *VersionManager) getCurrentVersion() (string, error) {
	var version string
	query := `SELECT version FROM app_versions WHERE is_current = TRUE ORDER BY installed_at DESC LIMIT 1`

	err := vm.db.QueryRow(query).Scan(&version)
	if err != nil {
		if err == sql.ErrNoRows {
			return "25052901", nil // 默认初始版本
		}
		return "", err
	}

	return version, nil
}

// setCurrentVersion 设置当前版本
func (vm *VersionManager) setCurrentVersion(version string) error {
	tx, err := vm.db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 将所有版本标记为非当前
	_, err = tx.Exec("UPDATE app_versions SET is_current = FALSE")
	if err != nil {
		return err
	}

	// 检查版本是否已存在
	var count int
	err = tx.QueryRow("SELECT COUNT(*) FROM app_versions WHERE version = ?", version).Scan(&count)
	if err != nil {
		return err
	}

	if count > 0 {
		// 更新现有版本为当前版本
		_, err = tx.Exec("UPDATE app_versions SET is_current = TRUE WHERE version = ?", version)
	} else {
		// 插入新版本记录
		_, err = tx.Exec(`
			INSERT INTO app_versions (version, is_current, installed_at)
			VALUES (?, TRUE, ?)
		`, version, time.Now())
	}

	if err != nil {
		return err
	}

	return tx.Commit()
}

// addMigrationLog 添加迁移日志
func (vm *VersionManager) addMigrationLog(fromVersion, toVersion, description, status string, errorMessage ...string) error {
	var errMsg string
	if len(errorMessage) > 0 {
		errMsg = errorMessage[0]
	}

	query := `INSERT INTO migration_logs (from_version, to_version, description, status, error_message, executed_at)
			  VALUES (?, ?, ?, ?, ?, ?)`

	_, err := vm.db.Exec(query, fromVersion, toVersion, description, status, errMsg, time.Now())
	return err
}

// GetMigrationStatus 获取迁移状态
func (vm *VersionManager) GetMigrationStatus() map[string]interface{} {
	currentVersion, _ := vm.getCurrentVersion()

	return map[string]interface{}{
		"currentVersion": currentVersion,
		"targetVersion":  vm.currentVersion,
		"needsMigration": currentVersion != vm.currentVersion,
	}
}
