package utils

import (
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"time"
)

// CheckDiskSpace 检查是否有足够的磁盘空间
func CheckDiskSpace(path string, requiredSpace int64) (bool, error) {
	// 确保目录存在
	if err := os.MkdirAll(path, 0755); err != nil {
		return false, err
	}

	// 获取磁盘使用情况
	_, free, _, err := GetDiskUsage(path)
	if err != nil {
		return false, err
	}

	// 检查是否有足够的空间
	return int64(free) >= requiredSpace, nil
}

// GetDirectorySize 获取目录大小
func GetDirectorySize(path string) (int64, error) {
	var size int64

	// 遍历目录
	err := filepath.Walk(path, func(_ string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			size += info.Size()
		}
		return nil
	})

	if err != nil {
		return 0, err
	}

	return size, nil
}

// CleanupOldFiles 清理旧文件，保留指定数量的最新文件
func CleanupOldFiles(dirPath string, maxFiles int) error {
	// 读取目录
	files, err := os.ReadDir(dirPath)
	if err != nil {
		return err
	}

	// 如果文件数量小于等于最大数量，不需要清理
	if len(files) <= maxFiles {
		return nil
	}

	// 获取文件信息，包括修改时间
	type fileInfo struct {
		path    string
		modTime time.Time
	}

	fileInfos := make([]fileInfo, 0, len(files))
	for _, file := range files {
		if file.IsDir() {
			continue
		}

		info, err := file.Info()
		if err != nil {
			continue
		}

		fileInfos = append(fileInfos, fileInfo{
			path:    filepath.Join(dirPath, file.Name()),
			modTime: info.ModTime(),
		})
	}

	// 按修改时间排序（最旧的在前面）
	sort.Slice(fileInfos, func(i, j int) bool {
		return fileInfos[i].modTime.Before(fileInfos[j].modTime)
	})

	// 删除多余的旧文件
	for i := 0; i < len(fileInfos)-maxFiles; i++ {
		if err := os.Remove(fileInfos[i].path); err != nil {
			return fmt.Errorf("failed to remove file %s: %v", fileInfos[i].path, err)
		}
	}

	return nil
}
