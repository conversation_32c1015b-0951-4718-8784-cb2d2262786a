package utils

import (
	"errors"
	"os"
	"time"

	"github.com/golang-jwt/jwt/v4"
)

// JWTClaims 自定义JWT声明
type JWTClaims struct {
	UserID   int64  `json:"userId"`
	Username string `json:"username"`
	SchoolID string `json:"schoolId"`
	jwt.RegisteredClaims
}

// 获取JWT密钥
func getJWTSecret() []byte {
	secret := os.Getenv("JWT_SECRET")
	if secret == "" {
		secret = "school-package-system-secret-key" // 默认密钥，实际应用中应使用环境变量
	}
	return []byte(secret)
}

// GenerateToken 生成JWT令牌
func GenerateToken(userID int64, username, schoolID string, expireDuration time.Duration) (string, error) {
	// 设置过期时间
	expireTime := time.Now().Add(expireDuration)
	
	// 创建JWT声明
	claims := JWTClaims{
		UserID:   userID,
		Username: username,
		SchoolID: schoolID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expireTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "school-package-system",
		},
	}
	
	// 创建令牌
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	
	// 签名令牌
	return token.SignedString(getJWTSecret())
}

// ParseToken 解析JWT令牌
func ParseToken(tokenString string) (*JWTClaims, error) {
	// 解析令牌
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		return getJWTSecret(), nil
	})
	
	if err != nil {
		return nil, err
	}
	
	// 验证令牌
	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}
	
	return nil, errors.New("invalid token")
}

// ValidateToken 验证JWT令牌
func ValidateToken(tokenString string) (bool, *JWTClaims, error) {
	claims, err := ParseToken(tokenString)
	if err != nil {
		return false, nil, err
	}
	
	// 检查令牌是否过期
	if time.Now().Unix() > claims.ExpiresAt.Unix() {
		return false, claims, errors.New("token expired")
	}
	
	return true, claims, nil
}
