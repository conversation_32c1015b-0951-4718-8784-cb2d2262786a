//go:build windows
// +build windows

package utils

import (
	"path/filepath"
	"syscall"
	"unsafe"
)

var (
	kernel32         = syscall.NewLazyDLL("kernel32.dll")
	getDiskFreeSpace = kernel32.NewProc("GetDiskFreeSpaceExW")
)

// GetDiskUsage 获取磁盘使用情况 (Windows)
func GetDiskUsage(path string) (total, free, used uint64, err error) {
	// 获取绝对路径
	absPath, err := filepath.Abs(path)
	if err != nil {
		return 0, 0, 0, err
	}

	// 转换为UTF-16
	pathPtr, err := syscall.UTF16PtrFromString(absPath)
	if err != nil {
		return 0, 0, 0, err
	}

	var freeBytesAvailable, totalNumberOfBytes, totalNumberOfFreeBytes uint64

	// 调用Windows API
	ret, _, err := getDiskFreeSpace.Call(
		uintptr(unsafe.Pointer(pathPtr)),
		uintptr(unsafe.Pointer(&freeBytesAvailable)),
		uintptr(unsafe.Pointer(&totalNumberOfBytes)),
		uintptr(unsafe.Pointer(&totalNumberOfFreeBytes)),
	)

	if ret == 0 {
		return 0, 0, 0, err
	}

	total = totalNumberOfBytes
	free = freeBytesAvailable
	used = total - free

	return total, free, used, nil
}
