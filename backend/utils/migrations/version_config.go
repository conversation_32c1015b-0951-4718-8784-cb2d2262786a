package migrations

import "fmt"

// VersionConfig 版本配置 - 集中管理所有版本信息
type VersionConfig struct {
	// 当前程序版本 - 修改这里会自动更新整个系统
	CurrentVersion string

	// 版本升级路径 - 按顺序排列的所有版本
	VersionPath []string

	// 迁移映射 - 版本到迁移实例的映射
	MigrationMap map[string]Migration
}

// GetVersionConfig 获取版本配置 - 这是唯一需要修改的地方
func GetVersionConfig() *VersionConfig {
	// ===== 新增版本时，只需要修改这里 =====

	// 1. 定义版本升级路径（按时间顺序，最后一个就是当前版本）
	versionPath := []string{
		"25052901", // 初始版本
		"25053001",
		"25053002",
		"25053003",
		// 新版本在这里继续添加...
	}

	// 2. 注册迁移实例（每个版本对应一个迁移）
	migrationMap := map[string]Migration{
		"25053001": &Migration_25052901_to_25053001{},
		"25053002": &Migration_25053001_to_25053002{},
		"25053003": &Migration_25053002_to_25053003{},
		// 新迁移在这里继续添加...
	}

	// ===== 修改结束 =====

	// 自动获取当前版本（versionPath的最后一个元素）
	currentVersion := versionPath[len(versionPath)-1]
	//currentVersion = "25053001" // todo 调试时使用

	return &VersionConfig{
		CurrentVersion: currentVersion,
		VersionPath:    versionPath,
		MigrationMap:   migrationMap,
	}
}

// GetCurrentVersion 获取当前程序版本
func GetCurrentVersion() string {
	return GetVersionConfig().CurrentVersion
}

// GetVersionPath 获取版本升级路径
func GetVersionPath() []string {
	return GetVersionConfig().VersionPath
}

// GetMigrations 获取所有迁移实例（按版本顺序）
func GetMigrations() []Migration {
	config := GetVersionConfig()
	var migrations []Migration

	// 跳过第一个版本（初始版本），从第二个版本开始
	for i := 1; i < len(config.VersionPath); i++ {
		version := config.VersionPath[i]
		if migration, exists := config.MigrationMap[version]; exists {
			migrations = append(migrations, migration)
		}
	}

	return migrations
}

// GetMigrationForVersion 获取指定版本的迁移实例
func GetMigrationForVersion(version string) (Migration, bool) {
	config := GetVersionConfig()
	migration, exists := config.MigrationMap[version]
	return migration, exists
}

// ValidateVersionConfig 验证版本配置的一致性
func ValidateVersionConfig() error {
	config := GetVersionConfig()

	// 检查版本路径不能为空
	if len(config.VersionPath) == 0 {
		return fmt.Errorf("版本路径不能为空")
	}

	// 检查每个版本（除了第一个）是否都有对应的迁移
	for i := 1; i < len(config.VersionPath); i++ {
		version := config.VersionPath[i]
		if _, exists := config.MigrationMap[version]; !exists {
			return fmt.Errorf("版本 %s 缺少对应的迁移实例", version)
		}
	}

	// 检查迁移映射中是否有多余的版本
	for version := range config.MigrationMap {
		found := false
		for _, v := range config.VersionPath {
			if v == version {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("迁移映射中的版本 %s 不在版本路径中", version)
		}
	}

	return nil
}
