# 版本升级开发说明

## 概述

本系统采用集中化的版本管理方式，所有版本相关的配置都集中在 `version_config.go` 文件中。新增版本时，只需要修改一个地方即可完成整个系统的版本升级配置。

## 新增版本的步骤

### 1. 创建迁移脚本文件

在 `migrations` 目录下创建新的迁移文件，命名格式：`migration_{from_version}_to_{to_version}.go`

例如：`migration_25053001_to_25053002.go`

```go
package migrations

import (
	"fmt"
	"os"
)

// Migration_25053001_to_25053002 从25053001版本升级到25053002版本
type Migration_25053001_to_25053002 struct{}

func (m *Migration_25053001_to_25053002) Version() string {
	return "25053002"
}

func (m *Migration_25053001_to_25053002) Description() string {
	return "升级到25053002版本的描述"
}

func (m *Migration_25053001_to_25053002) Execute(ctx *MigrationContext) error {
	ctx.Logger.Info("Migration", "25053002", "开始执行版本升级")

	// 在这里添加升级逻辑
	// 例如：
	// 1. 更新Web前端文件
	// if err := UpdateWebDistFromVersion(ctx, "25053002"); err != nil {
	//     return fmt.Errorf("更新Web前端文件失败: %v", err)
	// }

	// 2. 数据库迁移
	// 3. 文件操作
	// 4. 配置更新等

	ctx.Logger.Info("Migration", "25053002", "版本升级完成")
	return nil
}

func (m *Migration_25053001_to_25053002) Rollback(ctx *MigrationContext) error {
	ctx.Logger.Info("Migration", "25053002", "开始回滚版本升级")

	// 在这里添加回滚逻辑

	ctx.Logger.Info("Migration", "25053002", "版本回滚完成")
	return nil
}
```

### 2. 更新版本配置

**这是唯一需要修改的配置文件！**

编辑 `version_config.go` 文件中的 `GetVersionConfig()` 函数：

```go
func GetVersionConfig() *VersionConfig {
	// ===== 新增版本时，只需要修改这里 =====

	// 1. 定义版本升级路径（按时间顺序，最后一个就是当前版本）
	versionPath := []string{
		"25052901", // 初始版本
		"25053001", // 第一次升级
		"25053002", // 新增版本在这里添加
		// 新版本在这里继续添加...
	}

	// 2. 注册迁移实例（每个版本对应一个迁移）
	migrationMap := map[string]Migration{
		"25053001": &Migration_25052901_to_25053001{},
		"25053002": &Migration_25053001_to_25053002{}, // 新增迁移在这里添加
		// 新迁移在这里继续添加...
	}

	// ===== 修改结束 =====

	// 当前版本自动从versionPath的最后一个元素获取
	currentVersion := versionPath[len(versionPath)-1]
}
```

**重要说明：** 当前版本会自动从 `versionPath` 的最后一个元素获取，这样可以避免忘记修改 `currentVersion` 导致的无限循环更新问题。

### 3. 完成！

就这么简单！系统会自动：
- 更新 `main.go` 中的 `BuildVersion`
- 更新版本管理器中的版本路径
- 注册新的迁移实例

## 常用迁移操作

### 更新Web前端文件

```go
func (m *Migration_XXX_to_YYY) Execute(ctx *MigrationContext) error {
	// 更新Web前端文件
	if err := UpdateWebDistFromVersion(ctx, "YYY"); err != nil {
		return fmt.Errorf("更新Web前端文件失败: %v", err)
	}
	return nil
}
```

### 数据库迁移

```go
func (m *Migration_XXX_to_YYY) Execute(ctx *MigrationContext) error {
	// 执行SQL语句
	sqls := []string{
		`ALTER TABLE packages ADD COLUMN new_field TEXT DEFAULT ''`,
		`CREATE INDEX IF NOT EXISTS idx_packages_new_field ON packages(new_field)`,
	}

	for _, sql := range sqls {
		if _, err := ctx.DB.Exec(sql); err != nil {
			return fmt.Errorf("执行SQL失败 [%s]: %v", sql, err)
		}
	}
	return nil
}
```

### 文件操作

```go
func (m *Migration_XXX_to_YYY) Execute(ctx *MigrationContext) error {
	// 创建目录
	if err := os.MkdirAll("./new_directory", 0755); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}

	// 复制文件
	// 删除文件
	// 移动文件等

	return nil
}
```

### 配置文件更新

```go
func (m *Migration_XXX_to_YYY) Execute(ctx *MigrationContext) error {
	// 读取配置文件
	// 修改配置
	// 写回配置文件

	return nil
}
```

## 版本号规范

使用8位数字格式：`YYYYMMDD`
- `YYYY`: 年份
- `MM`: 月份
- `DD`: 日期

例如：`25053001` 表示 2025年5月30日第1个版本

## 注意事项

1. **迁移的幂等性**：确保迁移可以重复执行而不会出错
2. **回滚支持**：实现 `Rollback` 方法以支持版本回退
3. **错误处理**：提供详细的错误信息
4. **日志记录**：记录迁移过程的关键步骤
5. **测试**：在测试环境充分测试迁移脚本
6. **版本安全**：当前版本自动从 `versionPath` 最后一个元素获取，避免无限循环更新

## 文件结构

```
migrations/
├── README.md                           # 本说明文档
├── version_config.go                   # 版本配置（唯一需要修改的地方）
├── types.go                           # 类型定义
├── common.go                          # 通用函数
├── migration_25052901_to_25053001.go  # 具体迁移脚本
├── migration_25053001_to_25053002.go  # 具体迁移脚本
└── ...                                # 更多迁移脚本
```

## 优势

1. **集中管理**：所有版本配置集中在一个文件中
2. **自动同步**：修改配置后自动更新整个系统
3. **减少错误**：避免在多个文件中重复修改
4. **易于维护**：清晰的文件结构和命名规范
5. **可扩展性**：支持复杂的迁移逻辑
