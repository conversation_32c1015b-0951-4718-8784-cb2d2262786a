package migrations

import (
	"database/sql"
)

// Logger 简单的日志接口，避免循环依赖
type Logger interface {
	Info(module, action, message string)
	Error(module, action, message string)
	Warn(module, action, message string)
}

// Migration 迁移接口
type Migration interface {
	Version() string                      // 目标版本
	Description() string                  // 迁移描述
	Execute(ctx *MigrationContext) error  // 执行迁移
	Rollback(ctx *MigrationContext) error // 回滚迁移
}

// MigrationContext 迁移上下文
type MigrationContext struct {
	DB        *sql.DB
	Logger    Logger
	DataDir   string
	ConfigDir string
	BackupDir string
	TempDir   string
}
