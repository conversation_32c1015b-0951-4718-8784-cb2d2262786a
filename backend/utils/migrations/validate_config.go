package migrations

import (
	"fmt"
	"log"
)

// ValidateAndPrintConfig 验证并打印版本配置信息
func ValidateAndPrintConfig() {
	fmt.Println("=== 版本配置验证 ===")
	
	// 获取配置
	config := GetVersionConfig()
	
	fmt.Printf("当前版本: %s\n", config.CurrentVersion)
	fmt.Printf("版本路径: %v\n", config.VersionPath)
	fmt.Printf("迁移数量: %d\n", len(config.MigrationMap))
	
	// 验证配置
	if err := ValidateVersionConfig(); err != nil {
		log.Fatalf("版本配置验证失败: %v", err)
	}
	
	fmt.Println("✅ 版本配置验证通过")
	
	// 打印迁移信息
	fmt.Println("\n=== 迁移列表 ===")
	migrations := GetMigrations()
	for i, migration := range migrations {
		fmt.Printf("%d. %s - %s\n", i+1, migration.Version(), migration.Description())
	}
	
	fmt.Println("\n=== 配置完整性检查 ===")
	
	// 检查版本路径连续性
	for i := 1; i < len(config.VersionPath); i++ {
		version := config.VersionPath[i]
		if migration, exists := config.MigrationMap[version]; exists {
			fmt.Printf("✅ 版本 %s 有对应的迁移: %s\n", version, migration.Description())
		} else {
			fmt.Printf("❌ 版本 %s 缺少对应的迁移\n", version)
		}
	}
	
	fmt.Println("\n=== 验证完成 ===")
}
