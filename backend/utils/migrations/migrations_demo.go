package migrations

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"
)

// Migration_1_0_0_to_1_0_1 简单SQL迁移示例
type Migration_1_0_0_to_1_0_1 struct{}

func (m *Migration_1_0_0_to_1_0_1) Version() string {
	return "1.0.1"
}

func (m *Migration_1_0_0_to_1_0_1) Description() string {
	return "添加数据库索引优化"
}

func (m *Migration_1_0_0_to_1_0_1) Execute(ctx *MigrationContext) error {
	ctx.Logger.Info("Migration", "1.0.1", "开始执行数据库索引优化")

	// 执行SQL脚本
	sqlScript := `
		CREATE INDEX IF NOT EXISTS idx_package_versions_school_id
		ON package_versions(school_id);

		CREATE INDEX IF NOT EXISTS idx_sync_logs_created_at
		ON sync_logs(created_at);

		CREATE INDEX IF NOT EXISTS idx_download_logs_package_version_id
		ON download_logs(package_version_id);
	`

	_, err := ctx.DB.Exec(sqlScript)
	if err != nil {
		return fmt.Errorf("执行SQL脚本失败: %v", err)
	}

	ctx.Logger.Info("Migration", "1.0.1", "数据库索引优化完成")
	return nil
}

func (m *Migration_1_0_0_to_1_0_1) Rollback(ctx *MigrationContext) error {
	// 回滚：删除索引
	rollbackScript := `
		DROP INDEX IF EXISTS idx_package_versions_school_id;
		DROP INDEX IF EXISTS idx_sync_logs_created_at;
		DROP INDEX IF EXISTS idx_download_logs_package_version_id;
	`

	_, err := ctx.DB.Exec(rollbackScript)
	return err
}

// Migration_1_0_1_to_1_1_0 复杂迁移示例
type Migration_1_0_1_to_1_1_0 struct{}

func (m *Migration_1_0_1_to_1_1_0) Version() string {
	return "1.1.0"
}

func (m *Migration_1_0_1_to_1_1_0) Description() string {
	return "添加统计功能，重组文件结构，更新配置"
}

func (m *Migration_1_0_1_to_1_1_0) Execute(ctx *MigrationContext) error {
	ctx.Logger.Info("Migration", "1.1.0", "开始执行复杂迁移")

	// 1. 创建新表
	if err := m.createStatisticsTables(ctx); err != nil {
		return fmt.Errorf("创建统计表失败: %v", err)
	}

	// 2. 迁移现有数据
	if err := m.migrateExistingData(ctx); err != nil {
		return fmt.Errorf("迁移现有数据失败: %v", err)
	}

	// 3. 重组文件结构
	if err := m.reorganizeFiles(ctx); err != nil {
		return fmt.Errorf("重组文件结构失败: %v", err)
	}

	// 4. 更新配置
	if err := m.updateConfigs(ctx); err != nil {
		return fmt.Errorf("更新配置失败: %v", err)
	}

	ctx.Logger.Info("Migration", "1.1.0", "复杂迁移完成")
	return nil
}

func (m *Migration_1_0_1_to_1_1_0) createStatisticsTables(ctx *MigrationContext) error {
	createTableSQL := `
		CREATE TABLE IF NOT EXISTS download_statistics (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			package_version_id TEXT NOT NULL,
			download_count INTEGER DEFAULT 0,
			last_download_at DATETIME,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
		);

		CREATE TABLE IF NOT EXISTS daily_stats (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			date TEXT NOT NULL,
			total_downloads INTEGER DEFAULT 0,
			unique_packages INTEGER DEFAULT 0,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP
		);
	`

	_, err := ctx.DB.Exec(createTableSQL)
	return err
}

func (m *Migration_1_0_1_to_1_1_0) migrateExistingData(ctx *MigrationContext) error {
	// 从下载日志生成统计数据
	migrateDataSQL := `
		INSERT OR REPLACE INTO download_statistics (package_version_id, download_count, last_download_at)
		SELECT
			package_version_id,
			COUNT(*) as download_count,
			MAX(download_time) as last_download_at
		FROM download_logs
		GROUP BY package_version_id;
	`

	_, err := ctx.DB.Exec(migrateDataSQL)
	return err
}

func (m *Migration_1_0_1_to_1_1_0) reorganizeFiles(ctx *MigrationContext) error {
	// 重组日志文件结构：将旧的 logs/ 目录重命名为 logs/archive/
	oldLogsDir := filepath.Join(ctx.DataDir, "..", "logs")
	newLogsDir := filepath.Join(ctx.DataDir, "..", "logs", "archive")

	// 检查旧日志目录是否存在
	if _, err := os.Stat(oldLogsDir); err == nil {
		// 创建新目录
		if err := os.MkdirAll(filepath.Dir(newLogsDir), 0755); err != nil {
			return err
		}

		// 移动旧日志文件到归档目录
		entries, err := os.ReadDir(oldLogsDir)
		if err != nil {
			return err
		}

		// 确保归档目录存在
		if err := os.MkdirAll(newLogsDir, 0755); err != nil {
			return err
		}

		for _, entry := range entries {
			if entry.IsDir() {
				continue
			}

			oldPath := filepath.Join(oldLogsDir, entry.Name())
			newPath := filepath.Join(newLogsDir, entry.Name())

			// 移动文件
			if err := os.Rename(oldPath, newPath); err != nil {
				ctx.Logger.Warn("Migration", "1.1.0",
					fmt.Sprintf("移动日志文件失败: %s -> %s: %v", oldPath, newPath, err))
			}
		}
	}

	return nil
}

func (m *Migration_1_0_1_to_1_1_0) updateConfigs(ctx *MigrationContext) error {
	// 添加新的配置项
	newConfigs := map[string]string{
		"enable_statistics":        "true",
		"stats_retention_days":     "90",
		"auto_cleanup_logs":        "true",
		"max_concurrent_downloads": "5",
	}

	for key, value := range newConfigs {
		_, err := ctx.DB.Exec(`
			INSERT OR IGNORE INTO configs (key, value, description, updated_at)
			VALUES (?, ?, ?, ?)
		`, key, value, fmt.Sprintf("v1.1.0新增配置: %s", key), time.Now())

		if err != nil {
			return err
		}
	}

	return nil
}

func (m *Migration_1_0_1_to_1_1_0) Rollback(ctx *MigrationContext) error {
	// 回滚操作
	// 1. 删除新表
	ctx.DB.Exec("DROP TABLE IF EXISTS download_statistics")
	ctx.DB.Exec("DROP TABLE IF EXISTS daily_stats")

	// 2. 删除新配置
	ctx.DB.Exec("DELETE FROM configs WHERE key IN ('enable_statistics', 'stats_retention_days', 'auto_cleanup_logs', 'max_concurrent_downloads')")

	// 3. 恢复文件结构（简化处理）
	oldLogsDir := filepath.Join(ctx.DataDir, "..", "logs")
	archiveLogsDir := filepath.Join(ctx.DataDir, "..", "logs", "archive")

	if _, err := os.Stat(archiveLogsDir); err == nil {
		// 将归档文件移回原位置
		entries, _ := os.ReadDir(archiveLogsDir)
		for _, entry := range entries {
			if !entry.IsDir() {
				oldPath := filepath.Join(archiveLogsDir, entry.Name())
				newPath := filepath.Join(oldLogsDir, entry.Name())
				os.Rename(oldPath, newPath)
			}
		}
		// 删除归档目录
		os.RemoveAll(archiveLogsDir)
	}

	return nil
}

// Migration_1_1_0_to_1_2_0 高级迁移示例
type Migration_1_1_0_to_1_2_0 struct{}

func (m *Migration_1_1_0_to_1_2_0) Version() string {
	return "1.2.0"
}

func (m *Migration_1_1_0_to_1_2_0) Description() string {
	return "配置格式升级，缓存优化，API增强"
}

func (m *Migration_1_1_0_to_1_2_0) Execute(ctx *MigrationContext) error {
	ctx.Logger.Info("Migration", "1.2.0", "开始执行高级迁移")

	// 1. 升级配置格式
	if err := m.upgradeConfigFormat(ctx); err != nil {
		return fmt.Errorf("升级配置格式失败: %v", err)
	}

	// 2. 优化缓存结构
	if err := m.optimizeCache(ctx); err != nil {
		return fmt.Errorf("优化缓存失败: %v", err)
	}

	// 3. 更新API配置
	if err := m.updateAPIConfig(ctx); err != nil {
		return fmt.Errorf("更新API配置失败: %v", err)
	}

	ctx.Logger.Info("Migration", "1.2.0", "高级迁移完成")
	return nil
}

func (m *Migration_1_1_0_to_1_2_0) upgradeConfigFormat(ctx *MigrationContext) error {
	// 读取现有配置并转换格式
	configPath := filepath.Join(ctx.ConfigDir, "app.json")

	// 创建新格式的配置文件
	newConfig := map[string]interface{}{
		"version": "2.0",
		"server": map[string]interface{}{
			"port":    18080,
			"mode":    "production",
			"timeout": 30,
		},
		"database": map[string]interface{}{
			"path":            "./data/school-package.db",
			"max_connections": 10,
		},
		"features": map[string]interface{}{
			"auto_update":   true,
			"statistics":    true,
			"cache_enabled": true,
		},
		"updated_at": time.Now().Format(time.RFC3339),
	}

	// 保存新配置
	configData, err := json.MarshalIndent(newConfig, "", "  ")
	if err != nil {
		return err
	}

	// 确保配置目录存在
	if err := os.MkdirAll(ctx.ConfigDir, 0755); err != nil {
		return err
	}

	return os.WriteFile(configPath, configData, 0644)
}

func (m *Migration_1_1_0_to_1_2_0) optimizeCache(ctx *MigrationContext) error {
	// 清理旧缓存文件
	cacheDir := filepath.Join(ctx.DataDir, "cache")

	if _, err := os.Stat(cacheDir); err == nil {
		// 删除超过30天的缓存文件
		err := filepath.Walk(cacheDir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return nil // 忽略错误，继续清理
			}

			if !info.IsDir() && time.Since(info.ModTime()) > 30*24*time.Hour {
				os.Remove(path)
				ctx.Logger.Info("Migration", "1.2.0",
					fmt.Sprintf("清理过期缓存文件: %s", path))
			}

			return nil
		})

		if err != nil {
			ctx.Logger.Warn("Migration", "1.2.0",
				fmt.Sprintf("清理缓存时出现错误: %v", err))
		}
	}

	return nil
}

func (m *Migration_1_1_0_to_1_2_0) updateAPIConfig(ctx *MigrationContext) error {
	// 更新API相关配置
	apiConfigs := map[string]string{
		"api_version":         "v2",
		"enable_cors":         "true",
		"max_request_size":    "50MB",
		"rate_limit_enabled":  "true",
		"rate_limit_requests": "100",
		"rate_limit_window":   "60",
	}

	for key, value := range apiConfigs {
		_, err := ctx.DB.Exec(`
			INSERT OR REPLACE INTO configs (key, value, description, updated_at)
			VALUES (?, ?, ?, ?)
		`, key, value, fmt.Sprintf("v1.2.0 API配置: %s", key), time.Now())

		if err != nil {
			return err
		}
	}

	return nil
}

func (m *Migration_1_1_0_to_1_2_0) Rollback(ctx *MigrationContext) error {
	// 回滚操作
	// 1. 删除新配置文件
	configPath := filepath.Join(ctx.ConfigDir, "app.json")
	os.Remove(configPath)

	// 2. 删除API配置
	ctx.DB.Exec(`DELETE FROM configs WHERE key IN (
		'api_version', 'enable_cors', 'max_request_size',
		'rate_limit_enabled', 'rate_limit_requests', 'rate_limit_window'
	)`)

	return nil
}
