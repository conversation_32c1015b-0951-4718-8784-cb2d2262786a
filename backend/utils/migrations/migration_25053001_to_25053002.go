package migrations

import (
	"fmt"
	"os"
)

// Migration_25053001_to_25053002
type Migration_25053001_to_25053002 struct{}

func (m *Migration_25053001_to_25053002) Version() string {
	return "25053002"
} // 这儿返回的version一定要对，不然升级不成功

func (m *Migration_25053001_to_25053002) Description() string {
	return "升级到25053002版本"
}

func (m *Migration_25053001_to_25053002) Execute(ctx *MigrationContext) error {
	ctx.Logger.Info("Migration", "25053002", "开始执行版本升级")

	// 在当前目录创建25053002.txt文件
	file, err := os.Create("25053002.txt")
	if err != nil {
		ctx.Logger.Error("Migration", "25053002", fmt.Sprintf("创建文件失败: %v", err))
		return err
	}
	defer file.Close()
	ctx.Logger.Info("Migration", "25053002", "版本升级完成")
	return nil
}

func (m *Migration_25053001_to_25053002) Rollback(ctx *MigrationContext) error {
	ctx.Logger.Info("Migration", "25053002", "开始回滚版本升级")

	ctx.Logger.Info("Migration", "25053002", "版本回滚完成")
	return nil
}
