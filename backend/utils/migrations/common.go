package migrations

import (
	"archive/zip"
	"bytes"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

// UpdateWebDistFromVersion 通用的webDist更新方法
// 从appVersions表中获取指定版本的webDist zip文件路径，删除现有web/dist目录，解压新的webDist文件
func UpdateWebDistFromVersion(ctx *MigrationContext, version string) error {
	ctx.Logger.Info("Migration", "UpdateWebDist", fmt.Sprintf("开始更新Web前端文件，版本: %s", version))

	// 从数据库获取指定版本的webDist文件路径
	query := `SELECT web_dist_path FROM app_versions WHERE version = ? AND web_dist_path != ''`
	var webDistPath string
	err := ctx.DB.QueryRow(query, version).Scan(&webDistPath)
	if err != nil {
		return fmt.Errorf("获取Web前端文件路径失败: %v", err)
	}

	if webDistPath == "" {
		ctx.Logger.Info("Migration", "UpdateWebDist", "没有Web前端文件需要更新")
		return nil
	}

	ctx.Logger.Info("Migration", "UpdateWebDist", fmt.Sprintf("Web前端文件路径: %s", webDistPath))

	// 检查zip文件是否存在
	if _, err := os.Stat(webDistPath); os.IsNotExist(err) {
		return fmt.Errorf("web前端文件不存在: %s", webDistPath)
	}

	// 获取程序根目录（相对于当前工作目录）
	webDistDir := "./web/dist"

	// 删除现有的web/dist目录
	if _, err := os.Stat(webDistDir); err == nil {
		ctx.Logger.Info("Migration", "UpdateWebDist", "删除现有web/dist目录")
		if err := os.RemoveAll(webDistDir); err != nil {
			return fmt.Errorf("删除现有web/dist目录失败: %v", err)
		}
	}

	// 创建新的web/dist目录
	ctx.Logger.Info("Migration", "UpdateWebDist", "创建新的web/dist目录")
	if err := os.MkdirAll(webDistDir, 0755); err != nil {
		return fmt.Errorf("创建web/dist目录失败: %v", err)
	}

	// 解压zip文件到web/dist目录
	ctx.Logger.Info("Migration", "UpdateWebDist", "开始解压Web前端文件")
	if err := ExtractZipToDir(webDistPath, webDistDir); err != nil {
		return fmt.Errorf("解压Web前端文件失败: %v", err)
	}

	ctx.Logger.Info("Migration", "UpdateWebDist", "Web前端文件更新完成")
	return nil
}

// ExtractZipToDir 解压zip文件到指定目录的通用函数
func ExtractZipToDir(zipPath, destDir string) error {
	// 打开zip文件
	reader, err := zip.OpenReader(zipPath)
	if err != nil {
		return fmt.Errorf("打开zip文件失败: %v", err)
	}
	defer reader.Close()

	// 获取目标目录的绝对路径
	absDestDir, err := filepath.Abs(destDir)
	if err != nil {
		return fmt.Errorf("获取目标目录绝对路径失败: %v", err)
	}

	// 解压每个文件
	for i, file := range reader.File {
		// 处理文件名编码问题
		var decodedFileName string
		if file.Flags == 0 {
			// 如果标志位是0，则是默认的本地编码（通常是GBK）
			reader := bytes.NewReader([]byte(file.Name))
			decoder := transform.NewReader(reader, simplifiedchinese.GB18030.NewDecoder())
			content, err := io.ReadAll(decoder)
			if err != nil {
				// 解码失败，使用原始文件名
				decodedFileName = file.Name
			} else {
				decodedFileName = string(content)
			}
		} else {
			// 如果标志位不是0，则使用UTF-8编码
			decodedFileName = file.Name
		}

		// 构建目标路径，使用解码后的文件名
		destPath := filepath.Join(absDestDir, decodedFileName)

		// 获取目标路径的绝对路径
		absDestPath, err := filepath.Abs(destPath)
		if err != nil {
			return fmt.Errorf("获取目标文件绝对路径失败: %v", err)
		}

		// 检查路径是否在目标目录内（防止zip slip漏洞）
		if !strings.HasPrefix(absDestPath, absDestDir+string(filepath.Separator)) && absDestPath != absDestDir {
			return fmt.Errorf("非法的文件路径: %s -> %s", decodedFileName, absDestPath)
		}

		// 如果是目录，创建目录
		if file.FileInfo().IsDir() {
			if err := os.MkdirAll(absDestPath, file.Mode()); err != nil {
				return fmt.Errorf("创建目录失败 %s: %v", absDestPath, err)
			}
			continue
		}

		// 创建父目录
		if err := os.MkdirAll(filepath.Dir(absDestPath), 0755); err != nil {
			return fmt.Errorf("创建父目录失败 %s: %v", filepath.Dir(absDestPath), err)
		}

		// 打开zip中的文件
		srcFile, err := file.Open()
		if err != nil {
			return fmt.Errorf("打开zip中的文件失败 %s: %v", decodedFileName, err)
		}

		// 创建目标文件
		destFile, err := os.OpenFile(absDestPath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, file.Mode())
		if err != nil {
			srcFile.Close()
			return fmt.Errorf("创建目标文件失败 %s: %v", absDestPath, err)
		}

		// 复制文件内容
		_, err = io.Copy(destFile, srcFile)
		srcFile.Close()
		destFile.Close()

		if err != nil {
			return fmt.Errorf("复制文件内容失败 %s: %v", decodedFileName, err)
		}

		// 记录进度（每10个文件记录一次）
		if i%10 == 0 || i == len(reader.File)-1 {
			fmt.Printf("解压进度: %d/%d 文件\n", i+1, len(reader.File))
		}
	}

	return nil
}
