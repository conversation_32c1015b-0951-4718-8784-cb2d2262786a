package utils

import (
	"fmt"
	"os"
	"path/filepath"
	"time"
)

// LogLevel 日志级别
type LogLevel int

const (
	DEBUG LogLevel = iota
	INFO
	WARN
	ERROR
)

// Logger 日志记录器
type Logger struct {
	Level     LogLevel
	LogPath   string
	LogFile   *os.File
	MaxSize   int64
	MaxBackup int
}

// NewLogger 创建新的日志记录器
func NewLogger(level LogLevel, logPath string, maxSize int64, maxBackup int) (*Logger, error) {
	// 确保日志目录存在
	if err := os.MkdirAll(filepath.Dir(logPath), 0755); err != nil {
		return nil, err
	}
	
	// 打开日志文件
	file, err := os.OpenFile(logPath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		return nil, err
	}
	
	return &Logger{
		Level:     level,
		LogPath:   logPath,
		LogFile:   file,
		MaxSize:   maxSize,
		MaxBackup: maxBackup,
	}, nil
}

// Close 关闭日志记录器
func (l *Logger) Close() {
	if l.LogFile != nil {
		l.LogFile.Close()
	}
}

// checkRotate 检查是否需要轮转日志
func (l *Logger) checkRotate() error {
	// 获取文件信息
	info, err := l.LogFile.Stat()
	if err != nil {
		return err
	}
	
	// 检查文件大小
	if info.Size() < l.MaxSize {
		return nil
	}
	
	// 关闭当前日志文件
	l.LogFile.Close()
	
	// 生成新的文件名
	timestamp := time.Now().Format("20060102150405")
	backupPath := fmt.Sprintf("%s.%s", l.LogPath, timestamp)
	
	// 重命名当前日志文件
	if err := os.Rename(l.LogPath, backupPath); err != nil {
		return err
	}
	
	// 打开新的日志文件
	file, err := os.OpenFile(l.LogPath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}
	l.LogFile = file
	
	// 清理旧的日志文件
	return l.cleanOldLogs()
}

// cleanOldLogs 清理旧的日志文件
func (l *Logger) cleanOldLogs() error {
	// 获取日志目录
	dir := filepath.Dir(l.LogPath)
	base := filepath.Base(l.LogPath)
	
	// 读取目录
	files, err := os.ReadDir(dir)
	if err != nil {
		return err
	}
	
	// 收集备份日志文件
	var backups []string
	for _, file := range files {
		if !file.IsDir() && filepath.Base(file.Name()) != base && len(file.Name()) > len(base) && file.Name()[:len(base)] == base {
			backups = append(backups, filepath.Join(dir, file.Name()))
		}
	}
	
	// 如果备份文件数量超过最大备份数，删除最旧的文件
	if len(backups) > l.MaxBackup {
		// 按文件名排序（文件名包含时间戳，所以可以按字母顺序排序）
		for i := 0; i < len(backups)-l.MaxBackup; i++ {
			if err := os.Remove(backups[i]); err != nil {
				return err
			}
		}
	}
	
	return nil
}

// log 记录日志
func (l *Logger) log(level LogLevel, module, action, message string) error {
	// 检查日志级别
	if level < l.Level {
		return nil
	}
	
	// 检查是否需要轮转日志
	if err := l.checkRotate(); err != nil {
		return err
	}
	
	// 获取级别文本
	levelText := "DEBUG"
	switch level {
	case INFO:
		levelText = "INFO"
	case WARN:
		levelText = "WARN"
	case ERROR:
		levelText = "ERROR"
	}
	
	// 格式化日志消息
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logMessage := fmt.Sprintf("[%s] [%s] [%s] [%s] %s\n", timestamp, levelText, module, action, message)
	
	// 写入日志文件
	if _, err := l.LogFile.WriteString(logMessage); err != nil {
		return err
	}
	
	// 如果是错误级别，同时输出到标准错误
	if level == ERROR {
		fmt.Fprint(os.Stderr, logMessage)
	}
	
	return nil
}

// Debug 记录调试级别日志
func (l *Logger) Debug(module, action, message string) error {
	return l.log(DEBUG, module, action, message)
}

// Info 记录信息级别日志
func (l *Logger) Info(module, action, message string) error {
	return l.log(INFO, module, action, message)
}

// Warn 记录警告级别日志
func (l *Logger) Warn(module, action, message string) error {
	return l.log(WARN, module, action, message)
}

// Error 记录错误级别日志
func (l *Logger) Error(module, action, message string) error {
	return l.log(ERROR, module, action, message)
}
