package utils

import (
	"bufio"
	"fmt"
	"io"
	"os"

	"github.com/cespare/xxhash/v2"
)

// GetFileSize 获取文件大小
func GetFileSize(filePath string) (int64, error) {
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return 0, err
	}
	return fileInfo.Size(), nil
}

// CalculateFileHash 计算文件哈希值（使用xxHash算法）
func CalculateFileHash(filePath string) (string, error) {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 创建xxHash64摘要器
	h := xxhash.New()

	// 分块读取文件并更新哈希
	buffer := make([]byte, 4*1024*1024) // 4MB 缓冲区，与中央端保持一致
	reader := bufio.NewReader(file)

	for {
		n, err := reader.Read(buffer)
		if err != nil && err != io.EOF {
			return "", err
		}

		if n == 0 {
			break
		}

		h.Write(buffer[:n])
	}

	// 获取哈希值并转换为16位十六进制字符串
	hashValue := h.Sum64()
	hashHex := fmt.Sprintf("%016x", hashValue)

	return hashHex, nil
}

// VerifyFileHash 验证文件哈希值
func VerifyFileHash(filePath, expectedHash string) (bool, error) {
	actualHash, err := CalculateFileHash(filePath)
	if err != nil {
		return false, err
	}

	return actualHash == expectedHash, nil
}

// EnsureDirectoryExists 确保目录存在
func EnsureDirectoryExists(dirPath string) error {
	return os.MkdirAll(dirPath, 0755)
}

// FileExists 检查文件是否存在
func FileExists(filePath string) bool {
	_, err := os.Stat(filePath)
	return !os.IsNotExist(err)
}

// FormatFileSize 格式化文件大小
func FormatFileSize(size int64) string {
	const (
		B  = 1
		KB = 1024 * B
		MB = 1024 * KB
		GB = 1024 * MB
		TB = 1024 * GB
	)

	switch {
	case size >= TB:
		return fmt.Sprintf("%.2f TB", float64(size)/TB)
	case size >= GB:
		return fmt.Sprintf("%.2f GB", float64(size)/GB)
	case size >= MB:
		return fmt.Sprintf("%.2f MB", float64(size)/MB)
	case size >= KB:
		return fmt.Sprintf("%.2f KB", float64(size)/KB)
	default:
		return fmt.Sprintf("%d B", size)
	}
}
