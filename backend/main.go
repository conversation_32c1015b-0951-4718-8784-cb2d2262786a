package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strconv"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"

	"school-package-system/controllers"
	"school-package-system/middleware"
	"school-package-system/models"
	"school-package-system/services"
	"school-package-system/utils"
	"school-package-system/utils/migrations"
)

// 全局日志记录器
var logger *utils.Logger

// 程序版本信息（自动从配置获取）
var BuildVersion = migrations.GetCurrentVersion() // 自动从版本配置获取

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("警告：未找到.env文件，使用默认设置")
	}

	// 设置运行模式
	mode := os.Getenv("GIN_MODE")
	if mode == "" {
		mode = "debug"
	}
	gin.SetMode(mode)

	// 确保必要的目录存在
	ensureDirectories()

	// 初始化日志记录器
	initLogger()
	defer logger.Close()

	// 初始化数据库
	if err := models.InitDB(); err != nil {
		logger.Error("Main", "InitDB", fmt.Sprintf("初始化数据库失败：%v", err))
		log.Fatalf("初始化数据库失败：%v", err)
	}
	defer models.CloseDB()

	// 插入初始版本记录（使用当前程序版本）
	if err := models.InsertInitialVersion(BuildVersion); err != nil {
		logger.Error("Main", "InsertInitialVersion", fmt.Sprintf("插入初始版本记录失败：%v", err))
		log.Fatalf("插入初始版本记录失败：%v", err)
	}

	// 版本检查和数据库迁移
	versionManager := utils.NewVersionManager(BuildVersion, logger, models.DB)
	if err := versionManager.CheckAndMigrate(); err != nil {
		logger.Error("Main", "Migration", fmt.Sprintf("版本迁移失败：%v", err))
		log.Fatalf("版本迁移失败：%v", err)
	}

	// 初始化系统控制器
	controllers.InitSystemController()
	controllers.SetBuildVersion(BuildVersion)

	// 初始化更新服务
	controllers.InitUpdateService(BuildVersion, logger)

	// 初始化同步服务
	syncService := services.NewSyncService(logger)
	controllers.SyncServiceInstance = syncService

	// 创建Gin实例
	r := gin.Default()

	// 配置CORS
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
	}))

	// 静态文件服务
	r.Static("/packages", "./packages")
	r.Static("/web", "./web/dist")
	r.Static("/updates", "./updates") // 提供更新文件的静态服务，供学生端下载

	// 路由设置
	setupRoutes(r)

	// 学生端API路由设置
	setupStudentRoutes(r)

	// 获取端口
	port := os.Getenv("PORT")
	if port == "" {
		port = "18080"
	}

	// 记录启动日志
	logger.Info("Main", "Startup", fmt.Sprintf("学校端包管理系统启动，版本：%s，端口：%s", BuildVersion, port))
	models.AddSystemLog("INFO", "Main", "Startup", fmt.Sprintf("学校端包管理系统启动，版本：%s，端口：%s", BuildVersion, port))

	// 启动同步服务
	if err := syncService.Start(); err != nil {
		logger.Error("Main", "Startup", fmt.Sprintf("启动同步服务失败：%v", err))
		log.Printf("警告：启动同步服务失败：%v", err)
	}
	defer syncService.Stop()

	// 启动服务器
	fmt.Printf("=== 学校端包管理系统 ===\n")
	fmt.Printf("版本：%s\n", BuildVersion)
	fmt.Printf("服务器运行在 http://localhost:%s\n", port)
	fmt.Printf("========================\n")

	// 延迟打开浏览器，确保服务器已经启动
	go func() {
		time.Sleep(2 * time.Second) // 等待2秒确保服务器启动完成
		openBrowser(fmt.Sprintf("http://localhost:%s/web", port))
	}()

	if err := r.Run(":" + port); err != nil {
		logger.Error("Main", "Startup", fmt.Sprintf("启动服务器失败：%v", err))
		log.Fatalf("启动服务器失败：%v", err)
	}
}

// openBrowser 打开浏览器
func openBrowser(url string) {
	var err error

	switch runtime.GOOS {
	case "linux":
		err = exec.Command("xdg-open", url).Start()
	case "windows":
		err = exec.Command("rundll32", "url.dll,FileProtocolHandler", url).Start()
	case "darwin":
		err = exec.Command("open", url).Start()
	default:
		err = fmt.Errorf("unsupported platform")
	}

	if err != nil {
		logger.Error("Main", "OpenBrowser", fmt.Sprintf("打开浏览器失败：%v", err))
		log.Printf("打开浏览器失败：%v，请手动访问：%s", err, url)
	} else {
		logger.Info("Main", "OpenBrowser", fmt.Sprintf("已自动打开浏览器：%s", url))
		log.Printf("已自动打开浏览器：%s", url)
	}
}

// 确保必要的目录存在
func ensureDirectories() {
	dirs := []string{
		"./config",
		"./logs",
		"./data",
		"./packages",
		"./updates",
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			log.Fatalf("创建目录失败 %s：%v", dir, err)
		}
	}
}

// 初始化日志记录器
func initLogger() {
	// 获取日志配置
	logPath := os.Getenv("LOG_PATH")
	if logPath == "" {
		logPath = "./logs/app.log"
	} else {
		logPath = filepath.Join(logPath, "app.log")
	}

	// 获取日志级别
	logLevelStr := os.Getenv("LOG_LEVEL")
	var logLevel utils.LogLevel
	switch logLevelStr {
	case "debug":
		logLevel = utils.DEBUG
	case "info":
		logLevel = utils.INFO
	case "warn":
		logLevel = utils.WARN
	case "error":
		logLevel = utils.ERROR
	default:
		logLevel = utils.INFO
	}

	// 获取日志大小限制
	maxSizeStr := os.Getenv("MAX_LOG_SIZE")
	maxSize := int64(104857600) // 默认100MB
	if maxSizeStr != "" {
		if size, err := strconv.ParseInt(maxSizeStr, 10, 64); err == nil {
			maxSize = size
		}
	}

	// 获取日志备份数量
	maxBackupsStr := os.Getenv("MAX_LOG_BACKUPS")
	maxBackups := 5 // 默认5个备份
	if maxBackupsStr != "" {
		if backups, err := strconv.Atoi(maxBackupsStr); err == nil {
			maxBackups = backups
		}
	}

	// 创建日志记录器
	var err error
	logger, err = utils.NewLogger(logLevel, logPath, maxSize, maxBackups)
	if err != nil {
		log.Fatalf("初始化日志记录器失败：%v", err)
	}
}

// 设置路由
func setupRoutes(r *gin.Engine) {
	// API路由组
	api := r.Group("/api")
	{
		// 认证相关路由
		auth := api.Group("/auth")
		{
			auth.POST("/login", controllers.HandleSessionLogin)
			auth.GET("/status", middleware.AuthMiddleware(), controllers.HandleSessionInfo)
		}

		// 用户相关路由
		user := api.Group("/user")
		{
			user.POST("/login", controllers.HandleSessionLogin)
			user.GET("/info", middleware.AuthMiddleware(), controllers.HandleSessionInfo)
			user.POST("/logout", controllers.HandleSessionLogout)
			// 移除修改密码功能，因为密码只能在中央平台修改
		}

		// 系统相关路由
		system := api.Group("/system")
		system.Use(middleware.AuthMiddleware())
		{
			system.GET("/status", controllers.GetSystemStatus)
		}

		// 包管理相关路由 - 需要认证
		packages := api.Group("/packages")
		packages.Use(middleware.AuthMiddleware())
		{
			packages.GET("", controllers.GetPackages)
			packages.GET("/:id", controllers.GetPackage)
			packages.POST("/:id/sync", controllers.SyncPackage)
			packages.GET("/sync/status", controllers.GetSyncStatus)
			packages.POST("/sync/all", controllers.ManualSyncAll)
		}

		// 日志相关路由 - 需要认证
		logs := api.Group("/logs")
		logs.Use(middleware.AuthMiddleware())
		{
			logs.GET("/sync", controllers.GetSyncLogs)
			logs.GET("/system", controllers.GetSystemLogs)
			logs.GET("/recent", controllers.GetRecentLogs)
		}

		// 配置相关路由 - 需要认证
		config := api.Group("/config")
		config.Use(middleware.AuthMiddleware())
		{
			config.GET("", controllers.GetConfig)
			config.PUT("", controllers.UpdateConfig)
		}

		// 同步相关路由 - 需要认证
		sync := api.Group("/sync")
		sync.Use(middleware.AuthMiddleware())
		{
			sync.GET("/check", handleSyncCheck)
			sync.POST("/download", handleSyncDownload)
			sync.POST("/update-status", handleUpdateSyncStatus)
		}

		// 更新相关路由 - 需要认证
		update := api.Group("/update")
		update.Use(middleware.AuthMiddleware())
		{
			update.GET("/check", controllers.CheckForUpdates)
			update.POST("/download", controllers.DownloadAndUpdate)
			update.POST("/download-complete", controllers.DownloadCompleteUpdate)
			update.POST("/auto", controllers.CheckAndAutoUpdate)
			update.GET("/status", controllers.GetUpdateStatus)
			update.GET("/versions", controllers.GetVersionHistory)
			update.GET("/migrations", controllers.GetMigrationLogs)
			update.POST("/auto-update/enable", controllers.SetAutoUpdate)
			update.GET("/auto-update/status", controllers.GetAutoUpdateStatus)
		}
	}

	// 前端路由处理
	r.NoRoute(func(c *gin.Context) {
		path := c.Request.URL.Path
		if filepath.Ext(path) == "" {
			c.File("./web/dist/index.html")
		} else {
			c.Status(http.StatusNotFound)
		}
	})
}

// 路由处理函数 - 这些函数将在实际开发中实现
func handleSyncCheck(c *gin.Context)    { c.JSON(http.StatusOK, gin.H{"message": "功能未实现"}) }
func handleSyncDownload(c *gin.Context) { c.JSON(http.StatusOK, gin.H{"message": "功能未实现"}) }
func handleUpdateSyncStatus(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "功能未实现"})
}

// 设置学生端路由
func setupStudentRoutes(r *gin.Engine) {
	// 学生端API路由组
	student := r.Group("/api/student")
	{
		// 所有学生端路由都不需要认证
		student.GET("/system/status", controllers.GetStudentSystemStatus)
		student.GET("/packages", controllers.GetStudentPackages)
		student.GET("/packages/:id", controllers.GetStudentPackage)

		// 下载相关路由
		student.POST("/download/record", controllers.RecordDownload)
		student.GET("/download/logs", controllers.GetDownloadLogs)
		student.GET("/download/stats/:packageVersionId", controllers.GetPackageDownloadStats)

		// 学生端更新相关路由
		student.GET("/update/check", controllers.CheckStudentUpdate)
	}
}
