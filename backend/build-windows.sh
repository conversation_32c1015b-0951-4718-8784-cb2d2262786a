#!/bin/bash

# 学校端后端Windows打包脚本
# 使用方法：在backend目录下执行 ./build-windows.sh

set -e

echo "开始构建学校端后端Windows版本..."

# 设置变量
APP_NAME="school-package-system"
BUILD_DIR="build/windows"
DIST_DIR="dist"
FRONTEND_DIR="../frontend"

# 清理之前的构建
echo "清理之前的构建文件..."
rm -rf $BUILD_DIR
rm -rf $DIST_DIR
mkdir -p $BUILD_DIR
mkdir -p $BUILD_DIR/web/dist
mkdir -p $DIST_DIR

# 检查前端目录是否存在
if [ ! -d "$FRONTEND_DIR" ]; then
    echo "错误：未找到前端目录 $FRONTEND_DIR"
    echo "请确保在正确的目录下执行此脚本"
    exit 1
fi

# 构建前端
echo "开始构建前端..."
cd $FRONTEND_DIR

# 检查是否安装了Node.js和npm
if ! command -v node &> /dev/null; then
    echo "错误：未找到 Node.js"
    echo "请先安装 Node.js"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "错误：未找到 npm"
    echo "请先安装 npm"
    exit 1
fi

# 安装前端依赖
echo "安装前端依赖..."
npm install

# 构建前端
echo "构建前端项目..."
npm run build

# 检查前端构建是否成功（前端配置为直接输出到 ../backend/web/dist）
if [ ! -d "../backend/web/dist" ]; then
    echo "错误：前端构建失败，未找到 ../backend/web/dist 目录"
    exit 1
fi

echo "前端构建完成"

# 返回到backend目录
cd - > /dev/null

# 检查是否安装了mingw-w64
if ! command -v x86_64-w64-mingw32-gcc &> /dev/null; then
    echo "错误：未找到 x86_64-w64-mingw32-gcc"
    echo "请先安装 mingw-w64："
    echo "brew install mingw-w64"
    exit 1
fi

# 构建Windows可执行文件
echo "构建Windows可执行文件..."
env GOOS=windows GOARCH=amd64 CGO_ENABLED=1 CC=x86_64-w64-mingw32-gcc \
    go build -ldflags "-s -w" -o $BUILD_DIR/${APP_NAME}.exe .

# 复制必要的文件和目录
echo "复制必要的文件..."

# 复制web前端文件（前端构建时已直接输出到 web/dist）
if [ -d "web/dist" ]; then
    cp -r web/dist/* $BUILD_DIR/web/dist/
    echo "已复制前端文件"
else
    echo "错误：未找到前端构建文件 web/dist"
    echo "请确保前端已正确构建到 web/dist 目录"
    exit 1
fi

# 创建配置文件模板
cat > $BUILD_DIR/.env << EOF
# 服务器配置
PORT=18080
GIN_MODE=debug

# 数据库配置
DB_PATH=./data/school-package.db

# JWT配置
JWT_SECRET=school-package-system-secret-key

# 中央平台配置
CENTRAL_API_URL=https://api.cdzyhd.com/system/erp
CENTRAL_API_TIMEOUT=30

# 同步配置
SYNC_INTERVAL=30 # 分钟
SYNC_RETRY_COUNT=3
SYNC_RETRY_DELAY=5 # 分钟

# 存储配置
PACKAGE_STORAGE_PATH=./packages
MAX_STORAGE_SIZE=10737418240 # 10GB

# 日志配置
LOG_LEVEL=info
LOG_PATH=./logs
MAX_LOG_SIZE=104857600 # 100MB
MAX_LOG_BACKUPS=5
MAX_LOG_AGE=30 # 天

EOF

# 创建启动脚本
cat > $BUILD_DIR/start.bat << 'EOF'
@echo off
chcp 65001 > nul
echo 正在启动学校端实验包管理系统...
echo.

REM 检查是否存在配置文件
if not exist ".env" (
    echo 错误：未找到配置文件 .env
    echo 请先配置 .env 文件中的相关参数
    pause
    exit /b 1
)

REM 启动程序
school-package-system.exe

REM 如果程序异常退出，暂停以查看错误信息
if errorlevel 1 (
    echo.
    echo 程序异常退出，错误代码：%errorlevel%
    pause
)
EOF

# 创建安装说明
cat > $BUILD_DIR/README.txt << 'EOF'
学校端实验包管理系统 - Windows版本

安装说明：
1. 解压所有文件到目标目录
2. 编辑 .env 文件，配置中央平台连接信息
3. 双击 start.bat 启动程序
4. 在浏览器中访问 http://localhost:18080

目录结构：
- school-package-system.exe  主程序
- .env                       配置文件
- start.bat                  启动脚本
- web/                       前端文件
- data/                      数据库文件（自动创建）
- logs/                      日志文件（自动创建）
- packages/                  实验包文件（自动创建）

注意事项：
- 首次运行会自动创建必要的目录和数据库
- 请确保防火墙允许程序访问网络
- 如需修改端口，请编辑 .env 文件中的 PORT 参数

技术支持：
如有问题请联系系统管理员
EOF

# 创建目录结构
mkdir -p $BUILD_DIR/data
mkdir -p $BUILD_DIR/logs
mkdir -p $BUILD_DIR/packages
mkdir -p $BUILD_DIR/config

# 打包成zip文件
echo "创建分发包..."
cd $BUILD_DIR
zip -r ../../$DIST_DIR/${APP_NAME}-windows.zip .
cd ../..

echo "构建完成！"
echo "输出文件："
echo "  - 构建目录：$BUILD_DIR"
echo "  - 分发包：$DIST_DIR/${APP_NAME}-windows.zip"
echo ""
echo "部署说明："
echo "1. 将 ${APP_NAME}-windows.zip 传输到Windows服务器"
echo "2. 解压到目标目录"
echo "3. 编辑 .env 文件配置中央平台连接信息"
echo "4. 运行 start.bat 启动服务"
