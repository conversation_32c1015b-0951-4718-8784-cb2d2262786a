# 学校端包管理系统 - 后端

这是学校端包管理系统的Go后端，用于与中央云平台对接，同步和管理包版本。

## 功能特点

- 与中央云平台的认证集成
- 本地用户管理和JWT认证
- 包版本同步和管理
- 日志记录和查询
- 配置管理
- 静态文件服务
- 磁盘空间管理
- 系统状态监控

## 目录结构

```
/backend/
├── config/             # 配置文件
├── controllers/        # 控制器
├── middleware/         # 中间件
├── models/             # 数据模型
├── services/           # 服务
├── utils/              # 工具函数
├── data/               # SQLite数据库文件
├── logs/               # 日志文件
├── packages/           # 同步的包文件存储目录（静态服务）
├── web/                # 前端构建文件
├── .env                # 环境变量配置
├── .env.example        # 环境变量示例
├── go.mod              # Go模块文件
├── go.sum              # Go依赖校验文件
└── main.go             # 主入口文件
```

## 开发环境要求

- Go 1.18+
- SQLite 3

## 快速开始

1. 复制环境变量示例文件并根据需要修改

```bash
cp .env.example .env
```

2. 运行应用

```bash
go run main.go
```

## API接口

### 认证相关

- `POST /api/auth/login`: 登录中央平台
- `GET /api/auth/status`: 获取认证状态

### 用户相关

- `POST /api/user/login`: 用户登录
- `GET /api/user/info`: 获取用户信息
- `POST /api/user/logout`: 用户登出

### 包管理相关

- `GET /api/packages`: 获取包列表
- `GET /api/packages/:id`: 获取包详情
- `POST /api/packages/:id/sync`: 手动同步包
- `GET /api/packages/sync/status`: 获取同步状态

### 日志相关

- `GET /api/logs/sync`: 获取同步日志
- `GET /api/logs/system`: 获取系统日志
- `GET /api/logs/recent`: 获取最近的日志

### 配置相关

- `GET /api/config`: 获取配置
- `PUT /api/config`: 更新配置

### 系统相关

- `GET /api/system/status`: 获取系统状态

### 同步相关

- `GET /api/sync/check`: 检查新的推送
- `POST /api/sync/download`: 下载包文件
- `POST /api/sync/update-status`: 更新同步状态

## 构建

```bash
go build -o school-package-system
```

## 部署

1. 构建可执行文件
2. 创建并配置 `.env` 文件
3. 确保必要的目录存在（config, logs, data, packages, web/dist）
4. 运行可执行文件

```bash
./school-package-system
```

## 安全特性

- 密码使用bcrypt加密存储
- JWT令牌认证
- 文件哈希验证
- 磁盘空间检查

## 同步功能

- 定时自动同步
- 手动触发同步
- 文件完整性验证
- 同步状态跟踪
- 详细日志记录

## 许可证

MIT
