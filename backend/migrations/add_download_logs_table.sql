-- 创建下载日志表
CREATE TABLE IF NOT EXISTS download_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    package_version_id TEXT NOT NULL,
    student_mac TEXT NOT NULL,
    student_ip TEXT NOT NULL,
    download_time TIMESTAMP NOT NULL,
    user_agent TEXT,
    school_id TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_download_logs_package_version_id ON download_logs(package_version_id);
CREATE INDEX IF NOT EXISTS idx_download_logs_school_id ON download_logs(school_id);
CREATE INDEX IF NOT EXISTS idx_download_logs_download_time ON download_logs(download_time);
CREATE INDEX IF NOT EXISTS idx_download_logs_student_mac ON download_logs(student_mac);
