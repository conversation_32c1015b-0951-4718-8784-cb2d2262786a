package services

import (
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"school-package-system/models"
	"school-package-system/utils"

	"github.com/robfig/cron/v3"
)

// SyncService 同步服务
type SyncService struct {
	cron        *cron.Cron
	syncJobID   cron.EntryID
	logger      *utils.Logger
	isRunning   bool
	lastRunTime time.Time
}

// NewSyncService 创建同步服务
func NewSyncService(logger *utils.Logger) *SyncService {
	return &SyncService{
		cron:      cron.New(),
		logger:    logger,
		isRunning: false,
	}
}

// Start 启动同步服务
func (s *SyncService) Start() error {
	// 获取同步间隔
	syncIntervalStr := os.Getenv("SYNC_INTERVAL")
	syncInterval := 30 // 默认30分钟
	if syncIntervalStr != "" {
		if interval, err := strconv.Atoi(syncIntervalStr); err == nil {
			syncInterval = interval
		}
	}

	// 设置定时任务
	cronSpec := fmt.Sprintf("*/%d * * * *", syncInterval)
	if syncInterval >= 60 {
		hours := syncInterval / 60
		minutes := syncInterval % 60
		if minutes == 0 {
			cronSpec = fmt.Sprintf("0 */%d * * *", hours)
		} else {
			cronSpec = fmt.Sprintf("%d */%d * * *", minutes, hours)
		}
	}

	var err error
	s.syncJobID, err = s.cron.AddFunc(cronSpec, s.syncJob)
	if err != nil {
		return err
	}

	// 启动定时任务
	s.cron.Start()
	s.logger.Info("SyncService", "Start", fmt.Sprintf("同步服务已启动，同步间隔：%d 分钟", syncInterval))
	models.AddSystemLog("INFO", "SyncService", "Start", fmt.Sprintf("同步服务已启动，同步间隔：%d 分钟", syncInterval))

	// 立即执行一次同步
	go s.syncJob()

	return nil
}

// Stop 停止同步服务
func (s *SyncService) Stop() {
	if s.cron != nil {
		s.cron.Stop()
		s.logger.Info("SyncService", "Stop", "同步服务已停止")
		models.AddSystemLog("INFO", "SyncService", "Stop", "同步服务已停止")
	}
}

// syncJob 同步任务
func (s *SyncService) syncJob() {
	// 防止任务重复执行
	if s.isRunning {
		s.logger.Info("SyncService", "SyncJob", "同步任务已在运行中，跳过本次执行")
		return
	}

	s.isRunning = true
	s.lastRunTime = time.Now()

	s.logger.Info("SyncService", "SyncJob", "开始执行同步任务")
	models.AddSystemLog("INFO", "SyncService", "SyncJob", "开始执行同步任务")

	// 执行同步
	err := s.performSync()
	if err != nil {
		s.logger.Error("SyncService", "SyncJob", fmt.Sprintf("同步任务失败：%v", err))
		models.AddSystemLog("ERROR", "SyncService", "SyncJob", fmt.Sprintf("同步任务失败：%v", err))
	} else {
		s.logger.Info("SyncService", "SyncJob", "同步任务成功完成")
		models.AddSystemLog("INFO", "SyncService", "SyncJob", "同步任务成功完成")
	}

	// 清理旧日志文件
	s.cleanupOldLogs()

	s.isRunning = false
}

// cleanupOldLogs 清理旧日志文件
func (s *SyncService) cleanupOldLogs() {
	// 获取最大日志文件数量
	maxLogFilesStr := os.Getenv("MAX_LOG_BACKUPS")
	maxLogFiles := 5 // 默认保留5个日志文件
	if maxLogFilesStr != "" {
		if val, err := strconv.Atoi(maxLogFilesStr); err == nil && val > 0 {
			maxLogFiles = val
		}
	}

	// 获取日志目录
	logPath := os.Getenv("LOG_PATH")
	if logPath == "" {
		logPath = "./logs"
	}

	// 清理旧日志文件
	if err := utils.CleanupOldFiles(logPath, maxLogFiles); err != nil {
		s.logger.Error("SyncService", "CleanupOldLogs", fmt.Sprintf("清理旧日志文件失败：%v", err))
	} else {
		s.logger.Info("SyncService", "CleanupOldLogs", "旧日志文件清理成功")
	}
}

// performSync 执行同步
func (s *SyncService) performSync() error {
	// 获取所有会话
	sessions, err := getAllSessions()
	if err != nil {
		return fmt.Errorf("获取会话失败：%v", err)
	}

	if len(sessions) == 0 {
		return fmt.Errorf("未找到会话信息")
	}

	// 使用第一个会话的凭据
	session := sessions[0]

	// 检查令牌是否有效
	valid, err := models.ValidateCentralToken(session.CentralToken)
	if err != nil || !valid {
		// 如果令牌无效，尝试刷新
		newToken, expiresAt, err := models.RefreshCentralToken(session.CentralToken)
		if err != nil {
			return fmt.Errorf("刷新令牌失败：%v", err)
		}

		// 更新会话令牌
		session.CentralToken = newToken
		session.TokenExpiresAt = expiresAt
		if err := models.UpdateSession(&session); err != nil {
			return fmt.Errorf("更新会话令牌失败：%v", err)
		}
	}

	// 获取推送列表
	pushes, err := models.GetPendingPushes(session.CentralToken, session.SchoolID)
	if err != nil {
		return fmt.Errorf("获取推送列表失败：%v", err)
	}

	s.logger.Info("SyncService", "PerformSync", fmt.Sprintf("获取到 %d 个推送记录", len(pushes)))

	// 按实验版本ID对推送进行分组
	experimentVersionGroups := make(map[string][]models.PackagePushItem)
	for _, push := range pushes {
		if len(push.PackageVersion) > 0 {
			experimentVersionID := push.PackageVersion[0].ExperimentVersionID
			experimentVersionGroups[experimentVersionID] = append(experimentVersionGroups[experimentVersionID], push)
		}
	}

	// 处理每个实验版本组
	for experimentVersionID, groupPushes := range experimentVersionGroups {
		s.logger.Info("SyncService", "PerformSync", fmt.Sprintf("处理实验版本 %s，包含 %d 个包版本", experimentVersionID, len(groupPushes)))

		// 获取该实验版本中最大的3个包版本ID（用于判断是否需要下载）
		packageVersionIDs := make([]int, 0, len(groupPushes))
		for _, push := range groupPushes {
			if len(push.PackageVersion) > 0 {
				if id, err := strconv.Atoi(push.PackageVersion[0].PackageVersionID); err == nil {
					packageVersionIDs = append(packageVersionIDs, id)
				}
			}
		}

		// 对包版本ID排序，获取最大的3个
		sort.Slice(packageVersionIDs, func(i, j int) bool {
			return packageVersionIDs[i] > packageVersionIDs[j] // 降序排列
		})

		downloadableCount := 3
		if len(packageVersionIDs) < downloadableCount {
			downloadableCount = len(packageVersionIDs)
		}

		// 创建一个map来快速查找是否需要下载
		shouldDownloadMap := make(map[int]bool)
		for i := 0; i < downloadableCount; i++ {
			shouldDownloadMap[packageVersionIDs[i]] = true
		}

		s.logger.Info("SyncService", "PerformSync", fmt.Sprintf("实验版本 %s 将下载 %d 个最新包版本，其余 %d 个仅创建记录",
			experimentVersionID, downloadableCount, len(groupPushes)-downloadableCount))

		// 按原始列表顺序处理每个推送（保持旧包在前面的处理顺序）
		for _, push := range groupPushes {
			// 确保有包版本信息
			if len(push.PackageVersion) == 0 {
				s.logger.Error("SyncService", "PerformSync", "推送记录中没有包版本信息")
				models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "check", 0, "推送记录中没有包版本信息")
				continue
			}

			// 获取包版本信息
			pkgVersion := push.PackageVersion[0]

			// 判断当前包版本是否需要下载
			currentPackageVersionID, err := strconv.Atoi(pkgVersion.PackageVersionID)
			shouldDownload := false
			if err == nil {
				shouldDownload = shouldDownloadMap[currentPackageVersionID]
			}

			// 检查包版本是否已存在
			packageVersion, err := models.GetPackageVersionByPackageVersionID(push.PackageVersionID)
			if err != nil {
				s.logger.Error("SyncService", "PerformSync", fmt.Sprintf("检查包版本失败：%v", err))
				models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "check", 0, fmt.Sprintf("检查包版本失败：%v", err))
				continue
			}

			// 如果包版本已存在且已经处理过（无论是否下载），跳过处理
			if packageVersion != nil {
				// 对于已存在的包版本，完全跳过处理，不记录任何日志
				// 只确保推送状态是正确的
				if !push.SchoolAccept || !push.SchoolDownload {
					if err := models.UpdatePushStatus(session.CentralToken, push.PackagePushID, 1, 1); err != nil {
						// 只在出错时记录错误日志
						s.logger.Error("SyncService", "PerformSync", fmt.Sprintf("更新推送状态失败：%v", err))
					}
				}
				continue
			}

			// 如果包版本不存在，创建新的包版本记录
			if packageVersion == nil {
				// 获取实验名称
				expName := "未知"
				if len(push.ExperimentVersion) > 0 {
					expName = push.ExperimentVersion[0].ExperimentName
				}

				// 所有包版本都创建为未同步状态
				packageVersion = &models.PackageVersion{
					PackageVersionID:      push.PackageVersionID,
					PackagePushID:         push.PackagePushID,
					ExperimentID:          pkgVersion.ExperimentID,
					ExperimentName:        expName,
					ExperimentVersionID:   pkgVersion.ExperimentVersionID,
					ExperimentVersionName: pkgVersion.ExperimentVersionName,
					Version:               pkgVersion.Version,
					VersionName:           pkgVersion.VersionName,
					VersionDesc:           pkgVersion.VersionDesc,
					FileHash:              pkgVersion.FileHash,
					FileSize:              pkgVersion.FileInfo.Size,
					DownloadURL:           pkgVersion.DownloadURL,
					SyncStatus:            models.SyncStatusNotSynced,
					SchoolID:              session.SchoolID, // 设置当前登录的学校ID
				}

				if err := models.CreatePackageVersion(packageVersion); err != nil {
					s.logger.Error("SyncService", "PerformSync", fmt.Sprintf("创建包版本失败：%v", err))
					models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "create", 0, fmt.Sprintf("创建包版本失败：%v", err))
					continue
				}

				// 获取实验版本信息
				var expVersionName string = "未知"
				if len(push.ExperimentVersion) > 0 {
					expVersionName = push.ExperimentVersion[0].Name
					expName = push.ExperimentVersion[0].ExperimentName
				}

				downloadAction := "需要下载"
				if !shouldDownload {
					downloadAction = "仅记录，跳过下载"
				}

				logMsg := fmt.Sprintf("已创建新的包版本 - 实验：%s | 实验版本：%s | 包版本：%s | 版本号：%s | 处理方式：%s",
					expName, expVersionName, pkgVersion.VersionName, pkgVersion.Version, downloadAction)
				s.logger.Info("SyncService", "PerformSync", logMsg)
				models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "create", 1, logMsg)

				// 更新接受状态
				if !push.SchoolAccept {
					if err := models.UpdatePushStatus(session.CentralToken, push.PackagePushID, 1, 0); err != nil {
						s.logger.Error("SyncService", "PerformSync", fmt.Sprintf("更新接受状态失败：%v", err))
						models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "accept", 0, fmt.Sprintf("更新接受状态失败：%v", err))
						continue
					}
				}

				// 如果不需要下载，直接标记为已完成状态，不下载文件
				if !shouldDownload {
					logMsg := fmt.Sprintf("跳过下载（非最新3个版本） - 实验：%s | 实验版本：%s | 包版本：%s | 版本号：%s",
						expName, expVersionName, pkgVersion.VersionName, pkgVersion.Version)
					s.logger.Info("SyncService", "PerformSync", logMsg)
					models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "skip", 1, logMsg)

					// 设置为已完成状态，但不下载文件（filePath为空）
					if err := models.UpdatePackageVersionSyncStatus(packageVersion.ID, models.SyncStatusSynced, "", 0); err != nil {
						s.logger.Error("SyncService", "PerformSync", fmt.Sprintf("更新包版本状态失败：%v", err))
						models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "status", 0, fmt.Sprintf("更新包版本状态失败：%v", err))
					}

					// 更新下载状态为已完成
					if err := models.UpdatePushStatus(session.CentralToken, push.PackagePushID, 1, 1); err != nil {
						s.logger.Error("SyncService", "PerformSync", fmt.Sprintf("更新下载状态失败：%v", err))
						models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "status", 0, fmt.Sprintf("更新下载状态失败：%v", err))
					}
					continue
				}
			}

			// 如果包版本未同步或同步失败，下载包文件（只对需要下载的包版本执行）
			if packageVersion.SyncStatus == models.SyncStatusNotSynced || packageVersion.SyncStatus == models.SyncStatusFailed {
				// 更新同步状态为同步中
				if err := models.UpdatePackageVersionSyncStatus(packageVersion.ID, models.SyncStatusSyncing, "", 0); err != nil {
					s.logger.Error("SyncService", "PerformSync", fmt.Sprintf("更新同步状态失败：%v", err))
					models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "status", 0, fmt.Sprintf("更新同步状态失败：%v", err))
					continue
				}

				// 获取实验版本信息
				var expVersionName, expName string = "未知", "未知"
				if len(push.ExperimentVersion) > 0 {
					expVersionName = push.ExperimentVersion[0].Name
					expName = push.ExperimentVersion[0].ExperimentName
				}

				formattedSize := utils.FormatFileSize(pkgVersion.FileInfo.Size)
				logMsg := fmt.Sprintf("开始下载包 - 实验：%s | 实验版本：%s | 包版本：%s | 版本号：%s | 文件大小：%s",
					expName, expVersionName, pkgVersion.VersionName, pkgVersion.Version, formattedSize)
				s.logger.Info("SyncService", "PerformSync", logMsg)
				models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "download", 1, logMsg)

				// 检查磁盘空间是否足够
				storagePath := os.Getenv("PACKAGE_STORAGE_PATH")
				if storagePath == "" {
					storagePath = "./packages"
				}

				// 确保存储目录存在
				if err := utils.EnsureDirectoryExists(storagePath); err != nil {
					s.logger.Error("SyncService", "PerformSync", fmt.Sprintf("创建存储目录失败：%v", err))
					models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "download", 0, fmt.Sprintf("创建存储目录失败：%v", err))

					// 更新同步状态为同步失败
					models.UpdatePackageVersionSyncStatus(packageVersion.ID, models.SyncStatusFailed, "", 0)
					continue
				}

				// 检查磁盘空间
				hasSpace, err := utils.CheckDiskSpace(storagePath, pkgVersion.FileInfo.Size*2) // 需要2倍空间作为缓冲
				if err != nil {
					s.logger.Error("SyncService", "PerformSync", fmt.Sprintf("检查磁盘空间失败：%v", err))
					models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "download", 0, fmt.Sprintf("检查磁盘空间失败：%v", err))

					// 更新同步状态为同步失败
					models.UpdatePackageVersionSyncStatus(packageVersion.ID, models.SyncStatusFailed, "", 0)
					continue
				}

				if !hasSpace {
					s.logger.Error("SyncService", "PerformSync", "磁盘空间不足，无法下载")
					models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "download", 0, "磁盘空间不足，无法下载")

					// 更新同步状态为同步失败
					models.UpdatePackageVersionSyncStatus(packageVersion.ID, models.SyncStatusFailed, "", 0)
					continue
				}

				// 创建学校特定的存储目录
				schoolStoragePath := fmt.Sprintf("%s/%s", storagePath, session.SchoolID)
				if err := utils.EnsureDirectoryExists(schoolStoragePath); err != nil {
					s.logger.Error("SyncService", "PerformSync", fmt.Sprintf("创建学校存储目录失败：%v", err))
					models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "download", 0, fmt.Sprintf("创建学校存储目录失败：%v", err))

					// 更新同步状态为同步失败
					models.UpdatePackageVersionSyncStatus(packageVersion.ID, models.SyncStatusFailed, "", 0)
					continue
				}

				// 下载包文件
				filePath, err := models.DownloadPackage(session.CentralToken, pkgVersion.DownloadURL, pkgVersion.PackageVersionID, pkgVersion.FileHash, pkgVersion.FileInfo.Size, schoolStoragePath)
				if err != nil {
					s.logger.Error("SyncService", "PerformSync", fmt.Sprintf("下载包文件失败：%v", err))
					models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "download", 0, fmt.Sprintf("下载包文件失败：%v", err))

					// 更新同步状态为同步失败
					models.UpdatePackageVersionSyncStatus(packageVersion.ID, models.SyncStatusFailed, "", 0)
					continue
				}

				// 获取文件大小
				var fileSizeBytes int64
				fileSizeBytes, err = utils.GetFileSize(filePath)
				if err != nil {
					s.logger.Error("SyncService", "PerformSync", fmt.Sprintf("获取文件大小失败：%v", err))
					models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "verify", 0, fmt.Sprintf("获取文件大小失败：%v", err))

					// 更新同步状态为同步失败
					models.UpdatePackageVersionSyncStatus(packageVersion.ID, models.SyncStatusFailed, filePath, 0)
					continue
				}

				// 验证文件哈希
				valid, err := utils.VerifyFileHash(filePath, pkgVersion.FileHash)
				if err != nil {
					s.logger.Error("SyncService", "PerformSync", fmt.Sprintf("验证文件哈希失败：%v", err))
					models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "verify", 0, fmt.Sprintf("验证文件哈希失败：%v", err))

					// 更新同步状态为同步失败
					models.UpdatePackageVersionSyncStatus(packageVersion.ID, models.SyncStatusFailed, filePath, fileSizeBytes)
					continue
				}

				if !valid {
					s.logger.Error("SyncService", "PerformSync", "文件哈希验证失败")
					models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "verify", 0, "文件哈希验证失败")

					// 更新同步状态为同步失败
					models.UpdatePackageVersionSyncStatus(packageVersion.ID, models.SyncStatusFailed, filePath, fileSizeBytes)
					continue
				}

				// 转换文件路径为HTTP可访问的URL路径
				// filePath 格式：./packages/schoolId/packageId.zip 或 .\packages\schoolId\packageId.zip (Windows)
				// 需要转换为：/packages/schoolId/packageId.zip
				httpPath := filePath
				// 移除开头的 "./" 或 ".\"
				if strings.HasPrefix(httpPath, "./") {
					httpPath = httpPath[2:]
				} else if strings.HasPrefix(httpPath, ".\\") {
					httpPath = httpPath[2:]
				}
				// 将Windows路径分隔符转换为URL路径分隔符
				httpPath = strings.ReplaceAll(httpPath, "\\", "/")
				// 确保以 "/" 开头
				if !strings.HasPrefix(httpPath, "/") {
					httpPath = "/" + httpPath
				}

				// 更新同步状态为已同步
				if err := models.UpdatePackageVersionSyncStatus(packageVersion.ID, models.SyncStatusSynced, httpPath, fileSizeBytes); err != nil {
					s.logger.Error("SyncService", "PerformSync", fmt.Sprintf("更新同步状态失败：%v", err))
					models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "status", 0, fmt.Sprintf("更新同步状态失败：%v", err))
					continue
				}

				// 更新下载状态
				if err := models.UpdatePushStatus(session.CentralToken, push.PackagePushID, 1, 1); err != nil {
					s.logger.Error("SyncService", "PerformSync", fmt.Sprintf("更新下载状态失败：%v", err))
					models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "status", 0, fmt.Sprintf("更新下载状态失败：%v", err))
					continue
				}

				// 重置实验版本信息
				expVersionName = "未知"
				expName = "未知"
				if len(push.ExperimentVersion) > 0 {
					expVersionName = push.ExperimentVersion[0].Name
					expName = push.ExperimentVersion[0].ExperimentName
				}

				formattedSize = utils.FormatFileSize(fileSizeBytes)
				logMsg = fmt.Sprintf("包下载并验证成功 - 实验：%s | 实验版本：%s | 包版本：%s | 版本号：%s | 文件大小：%s | 存储路径：%s",
					expName, expVersionName, pkgVersion.VersionName, pkgVersion.Version, formattedSize, filePath)
				s.logger.Info("SyncService", "PerformSync", logMsg)
				models.AddSyncLog(push.PackageVersionID, push.PackagePushID, "complete", 1, logMsg)
			}
		}

		// 清理该实验版本的旧包文件（现在不需要了，因为我们已经只下载了最新3个）
		// 但为了保险起见，仍然执行一次清理，以防数据库中已有旧文件
		if err := s.cleanupOldPackageFiles(experimentVersionID); err != nil {
			s.logger.Error("SyncService", "PerformSync", fmt.Sprintf("清理旧包文件失败：%v", err))
			// 清理失败不影响同步，继续执行
		}
	}

	return nil
}

// getAllSessions 获取所有会话
func getAllSessions() ([]models.Session, error) {
	// 从数据库获取所有会话
	query := `SELECT id, username, school_id, school_name, central_token, local_token, token_expires_at, last_login, created_at, updated_at
			  FROM sessions`

	rows, err := models.DB.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 解析结果
	sessions := []models.Session{}
	for rows.Next() {
		session := models.Session{}
		err := rows.Scan(
			&session.ID,
			&session.Username,
			&session.SchoolID,
			&session.SchoolName,
			&session.CentralToken,
			&session.LocalToken,
			&session.TokenExpiresAt,
			&session.LastLogin,
			&session.CreatedAt,
			&session.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		sessions = append(sessions, session)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return sessions, nil
}

// ManualSync 手动同步
func (s *SyncService) ManualSync() error {
	if s.isRunning {
		return fmt.Errorf("同步任务已在运行中")
	}

	go s.syncJob()
	return nil
}

// GetStatus 获取同步状态
func (s *SyncService) GetStatus() map[string]interface{} {
	return map[string]interface{}{
		"isRunning":   s.isRunning,
		"lastRunTime": s.lastRunTime,
	}
}

// cleanupOldPackageFiles 清理同一实验版本的旧包文件，保留最近3次（包括当前要下载的）
func (s *SyncService) cleanupOldPackageFiles(experimentVersionID string) error {
	// 获取同一实验版本的所有包版本，按更新时间倒序排列
	packageVersions, err := models.GetPackageVersionsByExperimentVersionID(experimentVersionID)
	if err != nil {
		return fmt.Errorf("获取实验版本包列表失败：%v", err)
	}

	// 如果包版本数量小于等于2，不需要清理（保留最近2次 + 当前要下载的1次 = 3次）
	if len(packageVersions) <= 2 {
		s.logger.Info("SyncService", "CleanupOldPackageFiles", fmt.Sprintf("实验版本 %s 的包数量为 %d，无需清理", experimentVersionID, len(packageVersions)))
		return nil
	}

	// 需要清理的包版本（保留最近3次，清理其余的）
	packagesToCleanup := packageVersions[3:]

	s.logger.Info("SyncService", "CleanupOldPackageFiles", fmt.Sprintf("实验版本 %s 共有 %d 个包，需要清理 %d 个旧包", experimentVersionID, len(packageVersions), len(packagesToCleanup)))

	// 清理旧包文件
	for _, pkg := range packagesToCleanup {
		// 只清理有文件路径的包
		if pkg.FilePath != "" {
			// 转换HTTP路径格式为本地文件路径格式
			localFilePath := pkg.FilePath
			if strings.HasPrefix(localFilePath, "/packages/") {
				localFilePath = "." + localFilePath
			}
			// 将URL路径分隔符转换为本地文件系统路径分隔符
			localFilePath = filepath.FromSlash(localFilePath)

			// 检查文件是否存在
			if _, err := os.Stat(localFilePath); os.IsNotExist(err) {
				s.logger.Info("SyncService", "CleanupOldPackageFiles", fmt.Sprintf("文件不存在，跳过删除：%s", localFilePath))
			} else if err != nil {
				s.logger.Error("SyncService", "CleanupOldPackageFiles", fmt.Sprintf("检查文件状态失败 %s：%v", localFilePath, err))
			} else {
				// 文件存在，删除它
				if err := os.Remove(localFilePath); err != nil {
					s.logger.Error("SyncService", "CleanupOldPackageFiles", fmt.Sprintf("删除文件失败 %s：%v", localFilePath, err))
				} else {
					s.logger.Info("SyncService", "CleanupOldPackageFiles", fmt.Sprintf("成功删除旧包文件：%s", localFilePath))
				}
			}

			// 清空数据库中的文件路径和下载URL
			if err := models.ClearPackageVersionFilePaths(pkg.ID); err != nil {
				s.logger.Error("SyncService", "CleanupOldPackageFiles", fmt.Sprintf("清空包版本 %d 的文件路径失败：%v", pkg.ID, err))
			} else {
				s.logger.Info("SyncService", "CleanupOldPackageFiles", fmt.Sprintf("成功清空包版本 %d 的文件路径", pkg.ID))
			}
		}
	}

	return nil
}
