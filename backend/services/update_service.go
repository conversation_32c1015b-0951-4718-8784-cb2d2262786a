package services

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"school-package-system/models"
	"school-package-system/utils"
	"strconv"
	"strings"
	"time"
)

// UpdateInfo 更新信息
type UpdateInfo struct {
	HasUpdate      bool               `json:"hasUpdate"`
	CurrentVersion string             `json:"currentVersion"`
	LatestVersion  string             `json:"latestVersion"`
	DownloadURL    string             `json:"downloadUrl"`
	Changelog      string             `json:"changelog"`
	ReleaseDate    string             `json:"releaseDate"`
	FileSize       int64              `json:"fileSize"`
	CheckSum       string             `json:"checkSum"`
	UpdateData     *CentralUpdateData `json:"updateData,omitempty"` // 完整的更新数据
}

// CentralUpdateResponse 中央端更新响应
type CentralUpdateResponse struct {
	Code string             `json:"code"`
	Msg  string             `json:"msg"`
	Data *CentralUpdateData `json:"data"`
}

// CentralUpdateData 中央端更新数据
type CentralUpdateData struct {
	PackageClientUpdateId string                 `json:"packageClientUpdateId"`
	CreateTime            int64                  `json:"createTime"`
	ExtraInfo             map[string]interface{} `json:"extraInfo"`
	VersionNumber         int64                  `json:"versionNumber"`
	Type                  string                 `json:"type"`
	IsPushed              bool                   `json:"isPushed"`
	UpdateConfig          *UpdateConfig          `json:"updateConfig"`
}

// UpdateConfig 更新配置
type UpdateConfig struct {
	WebDistZipDownloadUrl          string `json:"webDistZipDownloadUrl"`
	SchoolClientLinuxDownloadUrl   string `json:"schoolClientLinuxDownloadUrl"`
	StudentClientDownloadUrl       string `json:"studentClientDownloadUrl"`
	SchoolClientWindowsDownloadUrl string `json:"schoolClientWindowsDownloadUrl"`
	SchoolUpdateDes                string `json:"schoolUpdateDes"`
	WebDistZipFileHash             string `json:"webDistZipFileHash"`
	SchoolClientLinuxFileHash      string `json:"schoolClientLinuxFileHash"`
	SchoolClientWindowsFileHash    string `json:"schoolClientWindowsFileHash"`
	StudentClientFileHash          string `json:"studentClientFileHash"`
	StudentClientUpdateDes         string `json:"studentClientUpdateDes"`
}

// UpdateService 更新服务
type UpdateService struct {
	logger         *utils.Logger
	currentVersion string
	centralAPIURL  string
	isChecking     bool
	lastCheckTime  time.Time
	autoUpdate     bool // 是否启用自动更新
}

// NewUpdateService 创建更新服务
func NewUpdateService(currentVersion string, logger *utils.Logger) *UpdateService {
	centralAPIURL := os.Getenv("CENTRAL_API_URL")
	if centralAPIURL == "" {
		centralAPIURL = "https://api.cdzyhd.com/system/erp"
	}

	// 检查是否启用自动更新
	autoUpdate := true // 默认启用自动更新
	if autoUpdateStr := os.Getenv("AUTO_UPDATE_ENABLED"); autoUpdateStr != "" {
		autoUpdate = autoUpdateStr == "true"
	}

	service := &UpdateService{
		logger:         logger,
		currentVersion: currentVersion,
		centralAPIURL:  centralAPIURL,
		isChecking:     false,
		autoUpdate:     autoUpdate,
	}

	// 启动时立即检查更新
	go service.checkOnStartup()

	return service
}

// CheckForUpdates 检查更新
func (us *UpdateService) CheckForUpdates() (*UpdateInfo, error) {
	if us.isChecking {
		return nil, fmt.Errorf("正在检查更新中，请稍后再试")
	}

	us.isChecking = true
	defer func() {
		us.isChecking = false
		us.lastCheckTime = time.Now()
	}()

	us.logger.Info("UpdateService", "CheckForUpdates", "开始检查程序更新")

	// 将当前版本转换为数字格式
	currentVersionNumber, err := us.parseVersionNumber(us.currentVersion)
	if err != nil {
		us.logger.Error("UpdateService", "CheckForUpdates",
			fmt.Sprintf("解析当前版本号失败: %v", err))
		return nil, fmt.Errorf("解析当前版本号失败: %v", err)
	}

	// 构建请求URL - 对接中央端API
	checkURL := fmt.Sprintf("%s/v1/packageSystem/packageClientUpdate/getLatestPushedVersion?currentVersionNumber=%d&type=school",
		us.centralAPIURL, currentVersionNumber)

	// 发送HTTP请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Get(checkURL)
	if err != nil {
		us.logger.Error("UpdateService", "CheckForUpdates",
			fmt.Sprintf("请求更新检查失败: %v", err))
		return nil, fmt.Errorf("请求更新检查失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		us.logger.Error("UpdateService", "CheckForUpdates",
			fmt.Sprintf("更新检查请求失败，状态码: %d", resp.StatusCode))
		return nil, fmt.Errorf("更新检查请求失败，状态码: %d", resp.StatusCode)
	}

	// 解析中央端响应
	var centralResp CentralUpdateResponse
	if err := json.NewDecoder(resp.Body).Decode(&centralResp); err != nil {
		us.logger.Error("UpdateService", "CheckForUpdates",
			fmt.Sprintf("解析更新信息失败: %v", err))
		return nil, fmt.Errorf("解析更新信息失败: %v", err)
	}

	// 转换为本地UpdateInfo格式
	updateInfo := &UpdateInfo{
		CurrentVersion: us.currentVersion,
	}

	if centralResp.Code == "000000" && centralResp.Data != nil {
		// 发现新版本
		updateInfo.HasUpdate = true
		updateInfo.LatestVersion = fmt.Sprintf("%d", centralResp.Data.VersionNumber)
		updateInfo.Changelog = centralResp.Data.UpdateConfig.SchoolUpdateDes
		updateInfo.UpdateData = centralResp.Data // 保存完整的更新数据

		// 根据操作系统选择下载URL和哈希（用于兼容性）
		if us.isWindows() {
			updateInfo.DownloadURL = centralResp.Data.UpdateConfig.SchoolClientWindowsDownloadUrl
			updateInfo.CheckSum = centralResp.Data.UpdateConfig.SchoolClientWindowsFileHash
		} else {
			updateInfo.DownloadURL = centralResp.Data.UpdateConfig.SchoolClientLinuxDownloadUrl
			updateInfo.CheckSum = centralResp.Data.UpdateConfig.SchoolClientLinuxFileHash
		}

		us.logger.Info("UpdateService", "CheckForUpdates",
			fmt.Sprintf("发现新版本: %s → %s", us.currentVersion, updateInfo.LatestVersion))
	} else {
		// 没有新版本
		updateInfo.HasUpdate = false
		us.logger.Info("UpdateService", "CheckForUpdates", "当前已是最新版本")
	}

	return updateInfo, nil
}

// DownloadUpdate 下载更新
func (us *UpdateService) DownloadUpdate(downloadURL string) (string, error) {
	return us.DownloadUpdateWithHash(downloadURL, "", "unknown")
}

// DownloadUpdateWithHash 下载更新并验证哈希
func (us *UpdateService) DownloadUpdateWithHash(downloadURL, expectedHash, version string) (string, error) {
	us.logger.Info("UpdateService", "DownloadUpdateWithHash",
		fmt.Sprintf("开始下载更新: %s", downloadURL))

	// 创建更新文件存储目录
	updateDir := "./updates"
	if err := os.MkdirAll(updateDir, 0755); err != nil {
		return "", fmt.Errorf("创建更新文件存储目录失败: %v", err)
	}

	// 生成临时文件名（包含版本号）
	var fileName string
	if us.isWindows() {
		fileName = fmt.Sprintf("school-package-system-v%s-%s.exe",
			version, time.Now().Format("20060102150405"))
	} else {
		fileName = fmt.Sprintf("school-package-system-v%s-%s",
			version, time.Now().Format("20060102150405"))
	}
	tempFilePath := filepath.Join(updateDir, fileName)

	// 下载文件
	if err := us.downloadFile(downloadURL, tempFilePath); err != nil {
		return "", fmt.Errorf("下载文件失败: %v", err)
	}

	// 验证文件哈希（如果提供了期望的哈希值）
	if expectedHash != "" {
		if err := us.verifyFileHash(tempFilePath, expectedHash); err != nil {
			// 删除下载的文件
			os.Remove(tempFilePath)
			return "", fmt.Errorf("文件哈希验证失败: %v", err)
		}
		us.logger.Info("UpdateService", "DownloadUpdateWithHash", "文件哈希验证通过")
	}

	us.logger.Info("UpdateService", "DownloadUpdateWithHash",
		fmt.Sprintf("更新下载完成: %s", tempFilePath))

	return tempFilePath, nil
}

// DownloadCompleteUpdate 下载完整更新包（学校端、web前端、学生端）
func (us *UpdateService) DownloadCompleteUpdate(updateData *CentralUpdateData, version string) (*CompleteUpdateFiles, error) {
	us.logger.Info("UpdateService", "DownloadCompleteUpdate", "开始下载完整更新包")

	result := &CompleteUpdateFiles{}

	// 创建更新文件存储目录
	updateDir := "./updates"
	if err := os.MkdirAll(updateDir, 0755); err != nil {
		return nil, fmt.Errorf("创建更新文件存储目录失败: %v", err)
	}

	// 1. 下载学校端可执行文件
	var schoolClientURL, schoolClientHash string
	if us.isWindows() {
		schoolClientURL = updateData.UpdateConfig.SchoolClientWindowsDownloadUrl
		schoolClientHash = updateData.UpdateConfig.SchoolClientWindowsFileHash
	} else {
		schoolClientURL = updateData.UpdateConfig.SchoolClientLinuxDownloadUrl
		schoolClientHash = updateData.UpdateConfig.SchoolClientLinuxFileHash
	}

	if schoolClientURL != "" {
		schoolClientPath, err := us.DownloadUpdateWithHash(schoolClientURL, schoolClientHash, version)
		if err != nil {
			return nil, fmt.Errorf("下载学校端可执行文件失败: %v", err)
		}
		result.SchoolClientPath = schoolClientPath
		us.logger.Info("UpdateService", "DownloadCompleteUpdate", "学校端可执行文件下载完成")
	}

	// 2. 下载web前端（可选）
	if updateData.UpdateConfig.WebDistZipDownloadUrl != "" {
		webDistFileName := fmt.Sprintf("web-dist-v%s-%s.zip", version, time.Now().Format("20060102150405"))
		webDistPath := filepath.Join(updateDir, webDistFileName)

		if err := us.downloadFile(updateData.UpdateConfig.WebDistZipDownloadUrl, webDistPath); err != nil {
			us.logger.Warn("UpdateService", "DownloadCompleteUpdate",
				fmt.Sprintf("下载web前端失败，但继续处理学校端更新: %v", err))
			// web前端下载失败不影响学校端更新，继续执行
		} else {
			result.WebDistPath = webDistPath
			us.logger.Info("UpdateService", "DownloadCompleteUpdate", "web前端下载完成")
		}
	} else {
		us.logger.Info("UpdateService", "DownloadCompleteUpdate", "跳过web前端下载（未提供下载URL）")
	}

	// 3. 下载学生端（可选）
	if updateData.UpdateConfig.StudentClientDownloadUrl != "" {
		studentClientFileName := fmt.Sprintf("student-client-v%s-%s.exe", version, time.Now().Format("20060102150405"))
		studentClientPath := filepath.Join(updateDir, studentClientFileName)

		if err := us.downloadFile(updateData.UpdateConfig.StudentClientDownloadUrl, studentClientPath); err != nil {
			us.logger.Warn("UpdateService", "DownloadCompleteUpdate",
				fmt.Sprintf("下载学生端失败，但继续处理学校端更新: %v", err))
			// 学生端下载失败不影响学校端更新，继续执行
		} else {
			// 验证学生端哈希
			if updateData.UpdateConfig.StudentClientFileHash != "" {
				if err := us.verifyFileHash(studentClientPath, updateData.UpdateConfig.StudentClientFileHash); err != nil {
					os.Remove(studentClientPath)
					us.logger.Warn("UpdateService", "DownloadCompleteUpdate",
						fmt.Sprintf("学生端文件哈希验证失败，但继续处理学校端更新: %v", err))
				} else {
					result.StudentClientPath = studentClientPath
					us.logger.Info("UpdateService", "DownloadCompleteUpdate", "学生端下载完成")
				}
			} else {
				result.StudentClientPath = studentClientPath
				us.logger.Info("UpdateService", "DownloadCompleteUpdate", "学生端下载完成（未验证哈希）")
			}
		}
	} else {
		us.logger.Info("UpdateService", "DownloadCompleteUpdate", "跳过学生端下载（未提供下载URL）")
	}

	us.logger.Info("UpdateService", "DownloadCompleteUpdate", "完整更新包下载完成")
	return result, nil
}

// CompleteUpdateFiles 完整更新文件
type CompleteUpdateFiles struct {
	SchoolClientPath  string `json:"schoolClientPath"`
	WebDistPath       string `json:"webDistPath"`
	StudentClientPath string `json:"studentClientPath"`
}

// downloadFile 下载文件
func (us *UpdateService) downloadFile(url, filePath string) error {
	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 10 * time.Minute, // 10分钟超时
	}

	// 发送请求
	resp, err := client.Get(url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载请求失败，状态码: %d", resp.StatusCode)
	}

	// 创建文件
	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 复制数据
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return err
	}

	return nil
}

// StartUpdate 启动更新
func (us *UpdateService) StartUpdate(newExePath string) error {
	us.logger.Info("UpdateService", "StartUpdate", "开始启动更新器")

	// 获取当前程序路径
	currentExe, err := os.Executable()
	if err != nil {
		return fmt.Errorf("获取当前程序路径失败: %v", err)
	}

	// 根据操作系统类型确定更新器路径
	var updaterPath string
	if us.isWindows() {
		updaterPath = "./updater.exe"
	} else {
		updaterPath = "./updater"
	}

	if _, err := os.Stat(updaterPath); err != nil {
		return fmt.Errorf("更新器不存在: %s", updaterPath)
	}

	// 启动更新器
	cmd := exec.Command(updaterPath,
		"--type=school",
		"--old="+currentExe,
		"--new="+newExePath,
		"--restart")

	us.logger.Info("UpdateService", "StartUpdate",
		fmt.Sprintf("启动更新器: %s", cmd.String()))

	if err := cmd.Start(); err != nil {
		return fmt.Errorf("启动更新器失败: %v", err)
	}

	us.logger.Info("UpdateService", "StartUpdate", "更新器启动成功，程序即将退出")

	// 延迟退出，确保日志写入
	go func() {
		time.Sleep(2 * time.Second)
		os.Exit(0)
	}()

	return nil
}

// GetUpdateStatus 获取更新状态
func (us *UpdateService) GetUpdateStatus() map[string]interface{} {
	return map[string]interface{}{
		"currentVersion": us.currentVersion,
		"isChecking":     us.isChecking,
		"lastCheckTime":  us.lastCheckTime,
	}
}

// ScheduleUpdateCheck 定时检查更新
func (us *UpdateService) ScheduleUpdateCheck() {
	// 获取检查间隔配置
	intervalConfig, err := models.GetConfig("update_check_interval")
	if err != nil {
		us.logger.Warn("UpdateService", "ScheduleUpdateCheck",
			"获取更新检查间隔配置失败，使用默认值")
	}

	interval := 12 * time.Hour // 默认12小时检查一次
	if intervalConfig != nil && intervalConfig.Value != "" {
		if hours, err := strconv.Atoi(intervalConfig.Value); err == nil {
			interval = time.Duration(hours) * time.Hour
		}
	}

	interval = 1 * time.Minute // 测试1分钟检测一次

	us.logger.Info("UpdateService", "ScheduleUpdateCheck",
		fmt.Sprintf("启动定时更新检查，间隔: %v", interval))

	ticker := time.NewTicker(interval)
	go func() {
		for range ticker.C {
			us.logger.Info("UpdateService", "ScheduleUpdateCheck", "执行定时更新检查")
			err := us.CheckAndAutoUpdate() // 检测和自动更新
			if err != nil {
				return
			}
		}
	}()
}

// ValidateVersion 验证版本兼容性
func (us *UpdateService) ValidateVersion(fromVersion, toVersion string) error {
	// 简单的版本兼容性检查
	// 这里可以根据实际需求实现更复杂的版本兼容性逻辑

	if fromVersion == toVersion {
		return fmt.Errorf("源版本和目标版本相同")
	}

	// 检查是否支持跨大版本升级
	// 例如：不允许从 1.x 直接升级到 3.x
	// 这里简化处理，实际可以根据版本号规则进行更严格的检查

	us.logger.Info("UpdateService", "ValidateVersion",
		fmt.Sprintf("版本兼容性检查通过: %s → %s", fromVersion, toVersion))

	return nil
}

// parseVersionNumber 解析版本号为数字格式
func (us *UpdateService) parseVersionNumber(version string) (int64, error) {
	// 如果版本号已经是数字格式（如25052901），直接转换
	if num, err := strconv.ParseInt(version, 10, 64); err == nil {
		return num, nil
	}

	// 如果是语义化版本（如1.0.0），转换为数字格式
	// 这里简化处理，实际可以根据需要实现更复杂的转换逻辑
	parts := strings.Split(version, ".")
	if len(parts) >= 3 {
		major, _ := strconv.Atoi(parts[0])
		minor, _ := strconv.Atoi(parts[1])
		patch, _ := strconv.Atoi(parts[2])

		// 转换为类似25052901的格式：年月日版本
		// 这里使用简单的转换：major*1000000 + minor*1000 + patch
		return int64(major*1000000 + minor*1000 + patch), nil
	}

	return 0, fmt.Errorf("无法解析版本号: %s", version)
}

// isWindows 检查是否为Windows系统
func (us *UpdateService) isWindows() bool {
	return strings.ToLower(runtime.GOOS) == "windows"
}

// calculateFileHash 计算文件哈希值（使用xxHash算法，与sync_service保持一致）
func (us *UpdateService) calculateFileHash(filePath string) (string, error) {
	return utils.CalculateFileHash(filePath)
}

// verifyFileHash 验证文件哈希值（使用xxHash算法，与sync_service保持一致）
func (us *UpdateService) verifyFileHash(filePath, expectedHash string) error {
	valid, err := utils.VerifyFileHash(filePath, expectedHash)
	if err != nil {
		return fmt.Errorf("验证文件哈希失败: %v", err)
	}

	if !valid {
		actualHash, _ := utils.CalculateFileHash(filePath)
		return fmt.Errorf("文件哈希验证失败: 期望 %s, 实际 %s", expectedHash, actualHash)
	}

	return nil
}

// checkOnStartup 启动时检查更新
func (us *UpdateService) checkOnStartup() {
	// 等待一段时间让系统完全启动
	time.Sleep(5 * time.Second)

	us.logger.Info("UpdateService", "checkOnStartup", "启动时检查更新")

	// 检查更新
	updateInfo, err := us.CheckForUpdates()
	if err != nil {
		us.logger.Error("UpdateService", "checkOnStartup",
			fmt.Sprintf("启动时检查更新失败: %v", err))
		return
	}

	if !updateInfo.HasUpdate {
		us.logger.Info("UpdateService", "checkOnStartup", "当前已是最新版本")
		return
	}

	us.logger.Info("UpdateService", "checkOnStartup",
		fmt.Sprintf("发现新版本: %s → %s", updateInfo.CurrentVersion, updateInfo.LatestVersion))

	// 如果启用了自动更新，则自动下载并更新
	if us.autoUpdate {
		us.logger.Info("UpdateService", "checkOnStartup", "开始自动更新")

		// 下载完整更新包
		completeFiles, err := us.DownloadCompleteUpdate(updateInfo.UpdateData, updateInfo.LatestVersion)
		if err != nil {
			us.logger.Error("UpdateService", "checkOnStartup",
				fmt.Sprintf("自动下载完整更新包失败: %v", err))
			return
		}

		// 构建下载完成的文件信息
		var downloadComponents []string
		if completeFiles.SchoolClientPath != "" {
			downloadComponents = append(downloadComponents, fmt.Sprintf("学校端=%s", completeFiles.SchoolClientPath))
		}
		if completeFiles.WebDistPath != "" {
			downloadComponents = append(downloadComponents, fmt.Sprintf("Web前端=%s", completeFiles.WebDistPath))
		} else {
			downloadComponents = append(downloadComponents, "Web前端=未下载")
		}
		if completeFiles.StudentClientPath != "" {
			downloadComponents = append(downloadComponents, fmt.Sprintf("学生端=%s", completeFiles.StudentClientPath))
		} else {
			downloadComponents = append(downloadComponents, "学生端=未下载")
		}

		downloadInfo := strings.Join(downloadComponents, ", ")
		us.logger.Info("UpdateService", "checkOnStartup",
			fmt.Sprintf("更新包下载完成: %s", downloadInfo))

		// 保存版本信息到数据库
		if err := us.saveVersionInfo(updateInfo.LatestVersion, completeFiles, updateInfo.UpdateData); err != nil {
			us.logger.Warn("UpdateService", "checkOnStartup",
				fmt.Sprintf("保存版本信息: %v", err))
		}

		// 启动更新（使用学校端可执行文件）
		if err := us.StartUpdate(completeFiles.SchoolClientPath); err != nil {
			us.logger.Error("UpdateService", "checkOnStartup",
				fmt.Sprintf("自动启动更新失败: %v", err))
			return
		}

		us.logger.Info("UpdateService", "checkOnStartup", "自动更新已启动，程序即将重启")
	} else {
		us.logger.Info("UpdateService", "checkOnStartup", "自动更新已禁用，请手动更新")
	}
}

// CheckAndAutoUpdate 检查并自动更新（可供外部调用）
func (us *UpdateService) CheckAndAutoUpdate() error {
	us.logger.Info("UpdateService", "CheckAndAutoUpdate", "开始检查并自动更新")

	// 检查更新
	updateInfo, err := us.CheckForUpdates()
	if err != nil {
		return fmt.Errorf("检查更新失败: %v", err)
	}

	if !updateInfo.HasUpdate {
		us.logger.Info("UpdateService", "CheckAndAutoUpdate", "当前已是最新版本")
		return nil
	}

	us.logger.Info("UpdateService", "CheckAndAutoUpdate",
		fmt.Sprintf("发现新版本: %s → %s", updateInfo.CurrentVersion, updateInfo.LatestVersion))

	// 如果启用了自动更新，则自动下载并更新
	if !us.autoUpdate {
		return fmt.Errorf("自动更新已禁用")
	}

	// 下载完整更新包
	completeFiles, err := us.DownloadCompleteUpdate(updateInfo.UpdateData, updateInfo.LatestVersion)
	if err != nil {
		return fmt.Errorf("下载完整更新包失败: %v", err)
	}

	// 构建下载完成的文件信息
	var downloadComponents []string
	if completeFiles.SchoolClientPath != "" {
		downloadComponents = append(downloadComponents, fmt.Sprintf("学校端=%s", completeFiles.SchoolClientPath))
	}
	if completeFiles.WebDistPath != "" {
		downloadComponents = append(downloadComponents, fmt.Sprintf("Web前端=%s", completeFiles.WebDistPath))
	} else {
		downloadComponents = append(downloadComponents, "Web前端=未下载")
	}
	if completeFiles.StudentClientPath != "" {
		downloadComponents = append(downloadComponents, fmt.Sprintf("学生端=%s", completeFiles.StudentClientPath))
	} else {
		downloadComponents = append(downloadComponents, "学生端=未下载")
	}

	downloadInfo := strings.Join(downloadComponents, ", ")
	us.logger.Info("UpdateService", "CheckAndAutoUpdate",
		fmt.Sprintf("更新包下载完成: %s", downloadInfo))

	// 保存版本信息到数据库
	if err := us.saveVersionInfo(updateInfo.LatestVersion, completeFiles, updateInfo.UpdateData); err != nil {
		us.logger.Warn("UpdateService", "CheckAndAutoUpdate",
			fmt.Sprintf("保存版本信息: %v", err))
	}

	// 启动更新（使用学校端可执行文件）
	if err := us.StartUpdate(completeFiles.SchoolClientPath); err != nil {
		return fmt.Errorf("启动更新失败: %v", err)
	}

	us.logger.Info("UpdateService", "CheckAndAutoUpdate", "自动更新已启动，程序即将重启")
	return nil
}

// SetAutoUpdate 设置自动更新开关
func (us *UpdateService) SetAutoUpdate(enabled bool) {
	us.autoUpdate = enabled
	us.logger.Info("UpdateService", "SetAutoUpdate",
		fmt.Sprintf("自动更新设置为: %t", enabled))
}

// IsAutoUpdateEnabled 检查是否启用自动更新
func (us *UpdateService) IsAutoUpdateEnabled() bool {
	return us.autoUpdate
}

// saveVersionInfo 保存版本信息到数据库
func (us *UpdateService) saveVersionInfo(version string, completeFiles *CompleteUpdateFiles, updateData *CentralUpdateData) error {
	// 转换为models包的类型
	modelCompleteFiles := &models.CompleteUpdateFiles{
		SchoolClientPath:  completeFiles.SchoolClientPath,
		WebDistPath:       completeFiles.WebDistPath,
		StudentClientPath: completeFiles.StudentClientPath,
	}

	var modelUpdateData *models.CentralUpdateData
	if updateData != nil {
		modelUpdateData = &models.CentralUpdateData{
			PackageClientUpdateId: updateData.PackageClientUpdateId,
			CreateTime:            updateData.CreateTime,
			ExtraInfo:             updateData.ExtraInfo,
			VersionNumber:         updateData.VersionNumber,
			Type:                  updateData.Type,
			IsPushed:              updateData.IsPushed,
		}

		if updateData.UpdateConfig != nil {
			modelUpdateData.UpdateConfig = &models.CentralUpdateConfig{
				WebDistZipDownloadUrl:          updateData.UpdateConfig.WebDistZipDownloadUrl,
				SchoolClientLinuxDownloadUrl:   updateData.UpdateConfig.SchoolClientLinuxDownloadUrl,
				StudentClientDownloadUrl:       updateData.UpdateConfig.StudentClientDownloadUrl,
				SchoolClientWindowsDownloadUrl: updateData.UpdateConfig.SchoolClientWindowsDownloadUrl,
				SchoolUpdateDes:                updateData.UpdateConfig.SchoolUpdateDes,
				WebDistZipFileHash:             updateData.UpdateConfig.WebDistZipFileHash,
				SchoolClientLinuxFileHash:      updateData.UpdateConfig.SchoolClientLinuxFileHash,
				SchoolClientWindowsFileHash:    updateData.UpdateConfig.SchoolClientWindowsFileHash,
				StudentClientFileHash:          updateData.UpdateConfig.StudentClientFileHash,
				StudentClientUpdateDes:         updateData.UpdateConfig.StudentClientUpdateDes,
			}
		}
	}

	return models.SaveVersionWithFiles(version, modelCompleteFiles, modelUpdateData)
}
