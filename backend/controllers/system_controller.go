package controllers

import (
	"net/http"
	"os"
	"runtime"
	"time"

	"school-package-system/models"
	"school-package-system/utils"

	"github.com/gin-gonic/gin"
)

// GetSystemStatus 获取系统状态
func GetSystemStatus(c *gin.Context) {
	// 获取系统信息
	currentVersion, _ := models.GetCurrentVersion()
	systemInfo := map[string]interface{}{
		"os":             runtime.GOOS,
		"arch":           runtime.GOARCH,
		"cpus":           runtime.NumCPU(),
		"version":        currentVersion,
		"buildVersion":   buildVersion, // 使用实际的构建版本
		"uptime":         time.Now().Unix() - startTime,
		"needsMigration": currentVersion != buildVersion,
	}

	// 获取内存使用情况
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	memoryInfo := map[string]interface{}{
		"allocated":  memStats.Alloc,
		"totalAlloc": memStats.TotalAlloc,
		"sys":        memStats.Sys,
		"numGC":      memStats.NumGC,
	}

	// 获取磁盘使用情况
	storagePath := os.Getenv("PACKAGE_STORAGE_PATH")
	if storagePath == "" {
		storagePath = "./packages"
	}

	// 确保存储目录存在
	if err := utils.EnsureDirectoryExists(storagePath); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "访问存储目录失败",
			"error":   err.Error(),
		})
		return
	}

	total, free, used, err := utils.GetDiskUsage(storagePath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取磁盘使用情况失败",
			"error":   err.Error(),
		})
		return
	}

	diskInfo := map[string]interface{}{
		"total":       total,
		"free":        free,
		"used":        used,
		"usedPercent": float64(used) / float64(total) * 100,
	}

	// 获取包版本统计
	syncedCount, err := models.GetPackageVersionCountByStatus(models.SyncStatusSynced)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取包版本数量失败",
			"error":   err.Error(),
		})
		return
	}

	syncingCount, err := models.GetPackageVersionCountByStatus(models.SyncStatusSyncing)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取包版本数量失败",
			"error":   err.Error(),
		})
		return
	}

	failedCount, err := models.GetPackageVersionCountByStatus(models.SyncStatusFailed)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取包版本数量失败",
			"error":   err.Error(),
		})
		return
	}

	notSyncedCount, err := models.GetPackageVersionCountByStatus(models.SyncStatusNotSynced)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取包版本数量失败",
			"error":   err.Error(),
		})
		return
	}

	packageInfo := map[string]interface{}{
		"synced":    syncedCount,
		"syncing":   syncingCount,
		"failed":    failedCount,
		"notSynced": notSyncedCount,
		"total":     syncedCount + syncingCount + failedCount + notSyncedCount,
	}

	// 获取同步状态
	syncStatus := map[string]interface{}{
		"isRunning":   false,
		"lastRunTime": time.Time{},
	}

	if SyncServiceInstance != nil {
		syncStatus = SyncServiceInstance.GetStatus()
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "成功",
		"data": gin.H{
			"system":  systemInfo,
			"memory":  memoryInfo,
			"disk":    diskInfo,
			"package": packageInfo,
			"sync":    syncStatus,
		},
	})
}

// 系统启动时间
var startTime int64

// 构建版本
var buildVersion string

// InitSystemController 初始化系统控制器
func InitSystemController() {
	startTime = time.Now().Unix()
}

// SetBuildVersion 设置构建版本
func SetBuildVersion(version string) {
	buildVersion = version
}
