package controllers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"school-package-system/models"
)

// DownloadLogRequest 下载日志请求
type DownloadLogRequest struct {
	PackageVersionID string `json:"packageVersionId" binding:"required"`
	StudentMAC       string `json:"studentMac" binding:"required"`
	StudentIP        string `json:"studentIp" binding:"required"`
	UserAgent        string `json:"userAgent"`
	SchoolID         string `json:"schoolId" binding:"required"`
}

// RecordDownload 记录下载
func RecordDownload(c *gin.Context) {
	var req DownloadLogRequest
	if err := c.ShouldBindJ<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证包版本是否存在
	packageVersion, err := models.GetPackageVersionByPackageVersionID(req.PackageVersionID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询包版本失败",
			"error":   err.Error(),
		})
		return
	}

	if packageVersion == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "包版本不存在",
		})
		return
	}

	// 验证学校ID是否匹配
	if packageVersion.SchoolID != req.SchoolID {
		c.JSON(http.StatusForbidden, gin.H{
			"code":    403,
			"message": "无权访问该包版本",
		})
		return
	}

	// 增加下载次数
	if err := models.IncrementPackageDownloadCount(req.PackageVersionID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新下载次数失败",
			"error":   err.Error(),
		})
		return
	}

	// 创建下载日志
	downloadLog := &models.DownloadLog{
		PackageVersionID: req.PackageVersionID,
		StudentMAC:       req.StudentMAC,
		StudentIP:        req.StudentIP,
		DownloadTime:     time.Now(),
		UserAgent:        req.UserAgent,
		SchoolID:         req.SchoolID,
	}

	if err := models.CreateDownloadLog(downloadLog); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建下载日志失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "下载记录成功",
		"data": gin.H{
			"downloadLogId": downloadLog.ID,
			"downloadCount": packageVersion.DownloadCount + 1,
		},
	})
}

// GetDownloadLogs 获取下载日志
func GetDownloadLogs(c *gin.Context) {
	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("pageSize", "20")
	packageVersionID := c.Query("packageVersionId")
	schoolID := c.Query("schoolId")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	var logs []models.DownloadLog
	var total int

	if packageVersionID != "" {
		// 根据包版本ID获取下载日志
		logs, err = models.GetDownloadLogsByPackageVersionID(packageVersionID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "获取下载日志失败",
				"error":   err.Error(),
			})
			return
		}
		total = len(logs)
	} else if schoolID != "" {
		// 根据学校ID获取下载日志
		logs, total, err = models.GetDownloadLogsBySchoolID(schoolID, page, pageSize)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "获取下载日志失败",
				"error":   err.Error(),
			})
			return
		}
	} else {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "必须提供packageVersionId或schoolId参数",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取下载日志成功",
		"data": gin.H{
			"logs":     logs,
			"total":    total,
			"page":     page,
			"pageSize": pageSize,
		},
	})
}

// GetPackageDownloadStats 获取包下载统计
func GetPackageDownloadStats(c *gin.Context) {
	packageVersionID := c.Param("packageVersionId")
	if packageVersionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "包版本ID不能为空",
		})
		return
	}

	// 获取包版本信息
	packageVersion, err := models.GetPackageVersionByPackageVersionID(packageVersionID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询包版本失败",
			"error":   err.Error(),
		})
		return
	}

	if packageVersion == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "包版本不存在",
		})
		return
	}

	// 获取下载日志
	logs, err := models.GetDownloadLogsByPackageVersionID(packageVersionID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取下载日志失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取下载统计成功",
		"data": gin.H{
			"packageVersion": packageVersion,
			"downloadCount":  packageVersion.DownloadCount,
			"downloadLogs":   logs,
		},
	})
}
