package controllers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"school-package-system/models"
	"school-package-system/services"
)

// 全局同步服务实例
var SyncServiceInstance *services.SyncService

// GetPackages 获取包版本列表
func GetPackages(c *gin.Context) {
	// 获取分页参数
	pageStr := c.<PERSON><PERSON>("page", "1")
	pageSizeStr := c.<PERSON><PERSON><PERSON>("pageSize", "10")
	syncStatusStr := c.<PERSON><PERSON><PERSON>("syncStatus", "-1")
	keyword := c.<PERSON><PERSON><PERSON>("keyword", "")
	experimentId := c.<PERSON>("experimentId", "")
	experimentName := c.<PERSON>("experimentName", "")
	experimentVersionName := c.<PERSON><PERSON>ult<PERSON>("experimentVersionName", "")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	syncStatus, err := strconv.Atoi(syncStatusStr)
	if err != nil {
		syncStatus = -1
	}

	// 构建查询参数
	params := models.PackageVersionQueryParams{
		Page:                  page,
		PageSize:              pageSize,
		SyncStatus:            syncStatus,
		Keyword:               keyword,
		ExperimentId:          experimentId,
		ExperimentName:        experimentName,
		ExperimentVersionName: experimentVersionName,
	}

	// 获取包版本列表
	packages, total, err := models.GetPackageVersionsWithFilter(params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取包版本列表失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取统计数据
	stats := gin.H{}

	// 获取总数
	stats["totalPackages"] = total

	// 获取已同步的包版本数量
	syncedCount, err := models.GetPackageVersionCountByStatus(models.SyncStatusSynced)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取已同步包版本数量失败",
			"error":   err.Error(),
		})
		return
	}
	stats["syncedPackages"] = syncedCount

	// 获取同步中的包版本数量
	syncingCount, err := models.GetPackageVersionCountByStatus(models.SyncStatusSyncing)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取同步中包版本数量失败",
			"error":   err.Error(),
		})
		return
	}
	stats["syncingPackages"] = syncingCount

	// 获取同步失败的包版本数量
	failedCount, err := models.GetPackageVersionCountByStatus(models.SyncStatusFailed)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取同步失败包版本数量失败",
			"error":   err.Error(),
		})
		return
	}
	stats["failedPackages"] = failedCount

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "成功",
		"data": gin.H{
			"total":    total,
			"page":     page,
			"pageSize": pageSize,
			"items":    packages,
			"packages": packages, // 添加packages字段，与前端期望的字段名一致
			"stats":    stats,    // 添加统计数据
		},
	})
}

// GetPackage 获取包版本详情
func GetPackage(c *gin.Context) {
	// 获取包版本ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的包版本ID",
			"error":   err.Error(),
		})
		return
	}

	// 获取包版本详情
	packageVersion, err := models.GetPackageVersionByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取包版本详情失败",
			"error":   err.Error(),
		})
		return
	}

	if packageVersion == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "未找到包版本",
		})
		return
	}

	// 获取同步日志
	logs, err := models.GetSyncLogsByPackageVersionID(packageVersion.PackageVersionID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取同步日志失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "成功",
		"data": gin.H{
			"packageVersion": packageVersion,
			"syncLogs":       logs,
		},
	})
}

// SyncPackage 手动同步包版本
func SyncPackage(c *gin.Context) {
	// 获取包版本ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的包版本ID",
			"error":   err.Error(),
		})
		return
	}

	// 获取包版本详情
	packageVersion, err := models.GetPackageVersionByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取包版本详情失败",
			"error":   err.Error(),
		})
		return
	}

	if packageVersion == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "未找到包版本",
		})
		return
	}

	// 检查同步服务是否已初始化
	if SyncServiceInstance == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "同步服务未初始化",
		})
		return
	}

	// 获取同步状态
	syncStatus := SyncServiceInstance.GetStatus()
	if syncStatus["isRunning"].(bool) {
		c.JSON(http.StatusConflict, gin.H{
			"code":    409,
			"message": "同步任务已在运行中",
			"data":    syncStatus,
		})
		return
	}

	// 更新同步状态为同步中
	if err := models.UpdatePackageVersionSyncStatus(packageVersion.ID, models.SyncStatusSyncing, packageVersion.FilePath, packageVersion.FileSize); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新同步状态失败",
			"error":   err.Error(),
		})
		return
	}

	// 记录同步日志
	models.AddSyncLog(packageVersion.PackageVersionID, packageVersion.PackagePushID, "manual_sync", 1, "已启动手动同步")

	// 触发手动同步
	if err := SyncServiceInstance.ManualSync(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "启动手动同步失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "同步任务已启动",
		"data":    syncStatus,
	})
}

// GetSyncStatus 获取同步状态
func GetSyncStatus(c *gin.Context) {
	// 检查同步服务是否已初始化
	if SyncServiceInstance == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "同步服务未初始化",
		})
		return
	}

	// 获取同步状态
	syncStatus := SyncServiceInstance.GetStatus()

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "成功",
		"data":    syncStatus,
	})
}

// ManualSyncAll 手动触发全局同步
func ManualSyncAll(c *gin.Context) {
	// 检查同步服务是否已初始化
	if SyncServiceInstance == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "同步服务未初始化",
		})
		return
	}

	// 获取同步状态
	syncStatus := SyncServiceInstance.GetStatus()
	if syncStatus["isRunning"].(bool) {
		c.JSON(http.StatusConflict, gin.H{
			"code":    409,
			"message": "同步任务已在运行中",
			"data":    syncStatus,
		})
		return
	}

	// 记录手动同步日志
	models.AddSystemLog("INFO", "PackageController", "ManualSyncAll", "用户触发手动全局同步")

	// 触发手动同步
	if err := SyncServiceInstance.ManualSync(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "启动手动同步失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "全局同步任务已启动",
		"data":    syncStatus,
	})
}
