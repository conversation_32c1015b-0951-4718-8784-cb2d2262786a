package controllers

import (
	"fmt"
	"net/http"
	"time"

	"school-package-system/models"
	"school-package-system/utils"

	"github.com/gin-gonic/gin"
)

// SessionLoginRequest 会话登录请求
type SessionLoginRequest struct {
	Username   string `json:"username" binding:"required"`
	Password   string `json:"password" binding:"required"`
	SchoolName string `json:"schoolName" binding:"required"`
}

// HandleSessionLogin 处理会话登录请求
func HandleSessionLogin(c *gin.Context) {
	var req SessionLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的请求参数",
			"error":   err.Error(),
		})
		return
	}

	// 尝试与中央平台认证
	centralToken, schoolId, schoolName, _, err := models.AuthenticateWithCentral(req.Username, req.Password, req.SchoolName)
	if err != nil {
		c.<PERSON>(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": err.<PERSON><PERSON>r(),
			"error":   err.<PERSON>rror(),
		})
		return
	}

	// 使用中央平台返回的真实schoolId和schoolName
	// 如果中央平台返回的schoolName为空，则使用请求中的schoolName
	if schoolName == "" {
		schoolName = req.SchoolName
	}

	// 打印调试信息
	fmt.Printf("登录处理: 使用SchoolID=%s, SchoolName=%s\n", schoolId, schoolName)

	// 检查会话是否存在于本地数据库
	session, err := models.GetSessionByUsername(req.Username)
	isFirstLogin := false
	if err != nil {
		// 如果会话不存在，创建新会话
		isFirstLogin = true
		// 生成本地JWT令牌 - 修改为长期有效（10年）
		localToken, err := utils.GenerateToken(0, req.Username, schoolId, 24*time.Hour*365*10)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "生成令牌失败",
				"error":   err.Error(),
			})
			return
		}

		newSession := &models.Session{
			Username:       req.Username,
			SchoolID:       schoolId,
			SchoolName:     schoolName,
			CentralToken:   centralToken,
			LocalToken:     localToken,
			TokenExpiresAt: time.Now().AddDate(10, 0, 0),
			LastLogin:      time.Now(),
		}

		if err := models.CreateSession(newSession); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "创建会话失败",
				"error":   err.Error(),
			})
			return
		}

		session = newSession
	} else {
		// 如果会话存在，更新令牌
		// 生成本地JWT令牌 - 修改为长期有效（10年）
		localToken, err := utils.GenerateToken(session.ID, req.Username, schoolId, 24*time.Hour*365*10)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "生成令牌失败",
				"error":   err.Error(),
			})
			return
		}

		session.SchoolID = schoolId
		session.SchoolName = schoolName
		session.CentralToken = centralToken
		session.LocalToken = localToken
		session.TokenExpiresAt = time.Now().AddDate(10, 0, 0)
		session.LastLogin = time.Now()

		if err := models.UpdateSession(session); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新会话失败",
				"error":   err.Error(),
			})
			return
		}
	}

	// 如果是首次登录，触发立即同步
	if isFirstLogin {
		go func() {
			// 等待一小段时间确保登录响应已发送
			time.Sleep(1 * time.Second)

			// 触发立即同步
			if SyncServiceInstance != nil {
				fmt.Printf("首次登录检测到，触发立即同步\n")
				if err := SyncServiceInstance.ManualSync(); err != nil {
					fmt.Printf("首次登录触发同步失败：%v\n", err)
				} else {
					fmt.Printf("首次登录同步任务已启动\n")
				}
			}
		}()
	}

	// 返回登录成功响应，格式与前端期望的一致
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "登录成功",
		"data": gin.H{
			"token": session.LocalToken,
			"user": gin.H{
				"id":         session.ID,
				"username":   session.Username,
				"schoolId":   session.SchoolID,
				"schoolName": session.SchoolName,
			},
		},
	})
}

// HandleSessionInfo 获取会话信息
func HandleSessionInfo(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未认证",
		})
		return
	}

	// 获取会话信息
	session, err := models.GetSessionByID(userID.(int64))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取会话信息失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "成功",
		"data": gin.H{
			"user": gin.H{
				"id":         session.ID,
				"username":   session.Username,
				"schoolId":   session.SchoolID,
				"schoolName": session.SchoolName,
				"lastLogin":  session.LastLogin,
			},
		},
	})
}

// HandleSessionLogout 处理会话登出请求
func HandleSessionLogout(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("userID")
	if exists {
		// 使会话token失效
		if err := models.InvalidateSessionToken(userID.(int64)); err != nil {
			// 记录错误但不影响响应
			fmt.Printf("登出时清理会话失败：%v\n", err)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "登出成功",
	})
}
