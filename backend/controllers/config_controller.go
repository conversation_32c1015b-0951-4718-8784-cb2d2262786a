package controllers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"school-package-system/models"
)

// ConfigRequest 配置请求
type ConfigRequest struct {
	Key   string `json:"key" binding:"required"`
	Value string `json:"value" binding:"required"`
}

// GetConfig 获取配置
func GetConfig(c *gin.Context) {
	// 获取所有配置
	configs, err := models.GetAllConfigs()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取配置失败",
			"error":   err.Error(),
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"code":    200,
		"message": "成功",
		"data":    configs,
	})
}

// UpdateConfig 更新配置
func UpdateConfig(c *gin.Context) {
	var req ConfigRequest
	if err := c.<PERSON>(&req); err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的请求参数",
			"error":   err.Error(),
		})
		return
	}

	// 检查配置是否存在
	config, err := models.GetConfig(req.Key)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "检查配置失败",
			"error":   err.Error(),
		})
		return
	}

	if config == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "未找到配置",
		})
		return
	}

	// 更新配置
	if err := models.UpdateConfig(req.Key, req.Value); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新配置失败",
			"error":   err.Error(),
		})
		return
	}

	// 记录系统日志
	models.AddSystemLog("INFO", "Config", "Update", "已更新配置: "+req.Key)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "配置更新成功",
	})
}
