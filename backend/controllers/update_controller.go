package controllers

import (
	"net/http"
	"school-package-system/models"
	"school-package-system/services"
	"school-package-system/utils"

	"github.com/gin-gonic/gin"
)

// UpdateServiceInstance 更新服务实例
var UpdateServiceInstance *services.UpdateService

// InitUpdateService 初始化更新服务
func InitUpdateService(currentVersion string, logger *utils.Logger) {
	UpdateServiceInstance = services.NewUpdateService(currentVersion, logger)

	// 启动定时检查更新
	UpdateServiceInstance.ScheduleUpdateCheck()
}

// CheckForUpdates 检查更新
func CheckForUpdates(c *gin.Context) {
	if UpdateServiceInstance == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新服务未初始化",
		})
		return
	}

	updateInfo, err := UpdateServiceInstance.CheckForUpdates()
	if err != nil {
		c.<PERSON><PERSON>(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "检查更新失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSO<PERSON>(http.StatusOK, gin.H{
		"code":    200,
		"message": "成功",
		"data":    updateInfo,
	})
}

// DownloadAndUpdate 下载并更新
func DownloadAndUpdate(c *gin.Context) {
	if UpdateServiceInstance == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新服务未初始化",
		})
		return
	}

	// 解析请求参数
	var req struct {
		DownloadURL string `json:"downloadUrl" binding:"required"`
		Version     string `json:"version" binding:"required"`
		CheckSum    string `json:"checkSum"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证版本兼容性
	currentVersion, _ := models.GetCurrentVersion()
	if err := UpdateServiceInstance.ValidateVersion(currentVersion, req.Version); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "版本兼容性检查失败",
			"error":   err.Error(),
		})
		return
	}

	// 下载更新（带哈希验证）
	newExePath, err := UpdateServiceInstance.DownloadUpdateWithHash(req.DownloadURL, req.CheckSum, req.Version)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "下载更新失败",
			"error":   err.Error(),
		})
		return
	}

	// 启动更新
	if err := UpdateServiceInstance.StartUpdate(newExePath); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "启动更新失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新已启动，程序即将重启",
	})
}

// DownloadCompleteUpdate 下载完整更新包
func DownloadCompleteUpdate(c *gin.Context) {
	if UpdateServiceInstance == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新服务未初始化",
		})
		return
	}

	// 首先检查更新
	updateInfo, err := UpdateServiceInstance.CheckForUpdates()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "检查更新失败",
			"error":   err.Error(),
		})
		return
	}

	if !updateInfo.HasUpdate {
		c.JSON(http.StatusOK, gin.H{
			"code":    200,
			"message": "当前已是最新版本",
			"data": gin.H{
				"hasUpdate": false,
			},
		})
		return
	}

	// 下载完整更新包
	completeFiles, err := UpdateServiceInstance.DownloadCompleteUpdate(updateInfo.UpdateData, updateInfo.LatestVersion)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "下载完整更新包失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "完整更新包下载成功",
		"data": gin.H{
			"hasUpdate":     true,
			"updateInfo":    updateInfo,
			"completeFiles": completeFiles,
		},
	})
}

// GetUpdateStatus 获取更新状态
func GetUpdateStatus(c *gin.Context) {
	if UpdateServiceInstance == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新服务未初始化",
		})
		return
	}

	status := UpdateServiceInstance.GetUpdateStatus()

	// 添加版本管理信息
	currentVersion, _ := models.GetCurrentVersion()
	status["databaseVersion"] = currentVersion

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "成功",
		"data":    status,
	})
}

// GetVersionHistory 获取版本历史
func GetVersionHistory(c *gin.Context) {
	versions, err := models.GetAllVersions()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取版本历史失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "成功",
		"data":    versions,
	})
}

// GetMigrationLogs 获取迁移日志
func GetMigrationLogs(c *gin.Context) {
	// 获取查询参数
	limit := 50 // 默认返回最近50条记录
	if limitParam := c.Query("limit"); limitParam != "" {
		if l, err := parseIntParam(limitParam); err == nil && l > 0 {
			limit = l
		}
	}

	logs, err := models.GetMigrationLogs(limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取迁移日志失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "成功",
		"data":    logs,
	})
}

// parseIntParam 解析整数参数
func parseIntParam(param string) (int, error) {
	var result int
	if param == "" {
		return 0, nil
	}

	// 简单的字符串转整数
	for _, char := range param {
		if char < '0' || char > '9' {
			return 0, gin.Error{Err: gin.Error{}.Err}
		}
		result = result*10 + int(char-'0')
	}

	return result, nil
}

// CheckAndAutoUpdate 检查并自动更新
func CheckAndAutoUpdate(c *gin.Context) {
	if UpdateServiceInstance == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新服务未初始化",
		})
		return
	}

	err := UpdateServiceInstance.CheckAndAutoUpdate()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "自动更新失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "自动更新已启动，程序即将重启",
	})
}

// SetAutoUpdate 设置自动更新开关
func SetAutoUpdate(c *gin.Context) {
	if UpdateServiceInstance == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新服务未初始化",
		})
		return
	}

	// 解析请求参数
	var req struct {
		Enabled bool `json:"enabled"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	UpdateServiceInstance.SetAutoUpdate(req.Enabled)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "自动更新设置已保存",
		"data": gin.H{
			"autoUpdateEnabled": req.Enabled,
		},
	})
}

// GetAutoUpdateStatus 获取自动更新状态
func GetAutoUpdateStatus(c *gin.Context) {
	if UpdateServiceInstance == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新服务未初始化",
		})
		return
	}

	enabled := UpdateServiceInstance.IsAutoUpdateEnabled()

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "成功",
		"data": gin.H{
			"autoUpdateEnabled": enabled,
		},
	})
}
