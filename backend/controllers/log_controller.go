package controllers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"school-package-system/models"
)

// GetSyncLogs 获取同步日志
func GetSyncLogs(c *gin.Context) {
	// 获取分页参数
	pageStr := c.<PERSON>("page", "1")
	pageSizeStr := c<PERSON>("pageSize", "10")
	packageVersionID := c.<PERSON><PERSON>("packageVersionId", "")
	action := c.<PERSON><PERSON>("action", "")
	statusStr := c.<PERSON><PERSON>ult<PERSON>uer<PERSON>("status", "-1")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	status, err := strconv.Atoi(statusStr)
	if err != nil {
		status = -1
	}

	// 获取同步日志
	logs, total, err := models.GetSyncLogs(page, pageSize, packageVersionID, action, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取同步日志失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "成功",
		"data": gin.H{
			"total":    total,
			"page":     page,
			"pageSize": pageSize,
			"items":    logs,
		},
	})
}

// GetSystemLogs 获取系统日志
func GetSystemLogs(c *gin.Context) {
	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("pageSize", "10")
	level := c.DefaultQuery("level", "")
	module := c.DefaultQuery("module", "")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 获取系统日志
	logs, total, err := models.GetSystemLogs(page, pageSize, level, module)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取系统日志失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "成功",
		"data": gin.H{
			"total":    total,
			"page":     page,
			"pageSize": pageSize,
			"items":    logs,
		},
	})
}

// GetRecentLogs 获取最近的日志
func GetRecentLogs(c *gin.Context) {
	// 获取最近的同步日志
	syncLogs, err := models.GetRecentSyncLogs(5)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取最近同步日志失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取最近的系统日志
	systemLogs, err := models.GetRecentSystemLogs(5)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取最近系统日志失败",
			"error":   err.Error(),
		})
		return
	}

	// 合并日志并转换为前端期望的格式
	logs := []gin.H{}

	// 添加同步日志
	for _, log := range syncLogs {
		logs = append(logs, gin.H{
			"id":        log.ID,
			"action":    log.Action,
			"message":   log.Message,
			"createdAt": log.CreatedAt,
			"type":      "sync",
		})
	}

	// 添加系统日志
	for _, log := range systemLogs {
		logs = append(logs, gin.H{
			"id":        log.ID,
			"action":    log.Module + "." + log.Action,
			"message":   log.Message,
			"createdAt": log.CreatedAt,
			"type":      "system",
			"level":     log.Level,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "成功",
		"data": gin.H{
			"syncLogs":   syncLogs,
			"systemLogs": systemLogs,
			"logs":       logs, // 添加前端期望的logs字段
		},
	})
}
