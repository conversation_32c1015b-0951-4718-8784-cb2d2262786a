package controllers

import (
	"fmt"
	"net/http"
	"strconv"

	"school-package-system/models"

	"github.com/gin-gonic/gin"
)

// StudentPackageResponse 学生端包响应
type StudentPackageResponse struct {
	ID                    int64  `json:"id"`
	PackageVersionID      string `json:"packageVersionId"`
	ExperimentID          string `json:"experimentId"`
	ExperimentName        string `json:"experimentName"`
	ExperimentVersionID   string `json:"experimentVersionId"`
	ExperimentVersionName string `json:"experimentVersionName"`
	VersionName           string `json:"versionName"`
	Version               string `json:"version"`
	VersionDesc           string `json:"versionDesc"`
	FileHash              string `json:"fileHash"`
	FileSize              int64  `json:"fileSize"`
	DownloadURL           string `json:"downloadUrl"`
	SyncStatus            int    `json:"syncStatus"`
	SchoolID              string `json:"schoolId"` // 添加学校ID
	Changelog             string `json:"changelog"`
	ReleaseDate           string `json:"releaseDate"`
}

// GetStudentSystemStatus 获取学生端系统状态
func GetStudentSystemStatus(c *gin.Context) {
	// 获取第一个会话的学校信息
	sessions, err := models.GetAllSessions()
	if err != nil || len(sessions) == 0 {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取学校信息失败",
			"error":   "未找到学校信息",
		})
		return
	}

	// 使用第一个会话的学校信息
	session := sessions[0]

	fmt.Println(session.SchoolID)

	// 返回系统状态
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "成功",
		"data": gin.H{
			"status":     "online",
			"schoolId":   session.SchoolID,
			"schoolName": session.SchoolName,
			"version":    "1.0.0",
		},
	})
}

// GetStudentPackages 获取学生端包列表
func GetStudentPackages(c *gin.Context) {
	// 获取第一个会话的学校信息
	sessions, err := models.GetAllSessions()
	if err != nil || len(sessions) == 0 {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取学校信息失败",
			"error":   "未找到学校信息",
		})
		return
	}

	// 使用第一个会话的学校ID
	schoolID := sessions[0].SchoolID

	// 获取已同步的包版本列表
	params := models.PackageVersionQueryParams{
		Page:       1,
		PageSize:   100,
		SyncStatus: models.SyncStatusSynced, // 只返回已同步完成的包
		SchoolID:   schoolID,                // 使用学校ID进行筛选
	}

	packages, _, err := models.GetPackageVersionsWithFilter(params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取包列表失败",
			"error":   err.Error(),
		})
		return
	}

	// 转换为学生端响应格式
	studentPackages := make([]StudentPackageResponse, 0, len(packages))
	for _, pkg := range packages {
		// 构建下载URL
		downloadURL := pkg.FilePath

		studentPackages = append(studentPackages, StudentPackageResponse{
			ID:                    pkg.ID,
			PackageVersionID:      pkg.PackageVersionID,
			ExperimentID:          pkg.ExperimentID,
			ExperimentName:        pkg.ExperimentName,
			ExperimentVersionID:   pkg.ExperimentVersionID,
			ExperimentVersionName: pkg.ExperimentVersionName, // 设置实验版本名称
			VersionName:           pkg.VersionName,
			Version:               pkg.Version,
			VersionDesc:           pkg.VersionDesc,
			FileHash:              pkg.FileHash,
			FileSize:              pkg.FileSize,
			DownloadURL:           downloadURL,
			SyncStatus:            pkg.SyncStatus,
			SchoolID:              schoolID,        // 设置学校ID
			Changelog:             pkg.VersionDesc, // 使用VersionDesc作为Changelog
			ReleaseDate:           pkg.CreatedAt.Format("2006-01-02"),
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "成功",
		"data":    studentPackages,
	})
}

// GetStudentPackage 获取学生端包详情
func GetStudentPackage(c *gin.Context) {
	// 获取第一个会话的学校信息
	sessions, err := models.GetAllSessions()
	if err != nil || len(sessions) == 0 {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取学校信息失败",
			"error":   "未找到学校信息",
		})
		return
	}

	// 使用第一个会话的学校ID
	schoolID := sessions[0].SchoolID

	// 获取包版本ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的包版本ID",
			"error":   err.Error(),
		})
		return
	}

	// 获取包版本详情
	packageVersion, err := models.GetPackageVersionByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取包版本详情失败",
			"error":   err.Error(),
		})
		return
	}

	if packageVersion == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "未找到包版本",
		})
		return
	}

	// 检查包版本是否属于该学校
	if packageVersion.SchoolID != schoolID {
		c.JSON(http.StatusForbidden, gin.H{
			"code":    403,
			"message": "无权访问该包版本",
		})
		return
	}

	// 构建下载URL
	downloadURL := "/packages/" + schoolID + "/" + packageVersion.FilePath

	// 转换为学生端响应格式
	studentPackage := StudentPackageResponse{
		ID:                    packageVersion.ID,
		PackageVersionID:      packageVersion.PackageVersionID,
		ExperimentID:          packageVersion.ExperimentID,
		ExperimentName:        packageVersion.ExperimentName,
		ExperimentVersionID:   packageVersion.ExperimentVersionID,
		ExperimentVersionName: packageVersion.ExperimentVersionName, // 设置实验版本名称
		VersionName:           packageVersion.VersionName,
		Version:               packageVersion.Version,
		VersionDesc:           packageVersion.VersionDesc,
		FileHash:              packageVersion.FileHash,
		FileSize:              packageVersion.FileSize,
		DownloadURL:           downloadURL,
		SyncStatus:            packageVersion.SyncStatus,
		SchoolID:              schoolID,                   // 设置学校ID
		Changelog:             packageVersion.VersionDesc, // 使用VersionDesc作为Changelog
		ReleaseDate:           packageVersion.CreatedAt.Format("2006-01-02"),
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "成功",
		"data":    studentPackage,
	})
}

// CheckStudentUpdate 检查学生端更新
func CheckStudentUpdate(c *gin.Context) {
	// 获取当前学校端版本
	currentVersion, err := models.GetCurrentVersion()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取当前版本失败",
			"error":   err.Error(),
		})
		return
	}

	// 从app_versions表中获取当前版本对应的学生端文件信息
	appVersion, err := models.GetAppVersionByVersion(currentVersion)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取版本信息失败",
			"error":   err.Error(),
		})
		return
	}

	// 检查是否有学生端更新文件
	if appVersion == nil {
		// 没有找到版本信息
		c.JSON(http.StatusOK, gin.H{
			"code":    "000001",
			"message": "未找到版本信息",
			"data":    nil,
		})
		return
	}

	if appVersion.StudentClientPath == "" {
		// 找到版本信息但没有学生端文件（仅学校端更新）
		c.JSON(http.StatusOK, gin.H{
			"code":    "000001",
			"message": "此版本仅包含学校端更新",
			"data":    nil,
		})
		return
	}

	// 有学生端更新，返回更新信息
	c.JSON(http.StatusOK, gin.H{
		"code":    "000000",
		"message": fmt.Sprintf("存在学生端版本 %s", currentVersion),
		"data": gin.H{
			"packageClientUpdateId": appVersion.PackageClientUpdateId,
			"versionNumber":         currentVersion,
			"createTime":            appVersion.InstalledAt.Unix() * 1000, // 转换为毫秒时间戳
			"type":                  "student",
			"isPushed":              true,
			"updateConfig": gin.H{
				"studentClientDownloadUrl": appVersion.StudentClientPath, // 相对路径
				"studentClientFileHash":    appVersion.StudentClientFileHash,
				"studentClientUpdateDes":   appVersion.StudentUpdateDescription,
			},
		},
	})
}
