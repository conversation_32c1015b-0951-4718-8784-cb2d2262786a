package models

import (
	"database/sql"
	"fmt"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

// AppVersion 应用版本模型
type AppVersion struct {
	ID                       int64     `json:"id"`
	Version                  string    `json:"version"`
	InstalledAt              time.Time `json:"installedAt"`
	IsCurrent                bool      `json:"isCurrent"`
	SchoolClientPath         string    `json:"schoolClientPath"`         // 学校端文件路径
	WebDistPath              string    `json:"webDistPath"`              // Web前端文件路径
	StudentClientPath        string    `json:"studentClientPath"`        // 学生端文件路径
	StudentClientUrlSuffix   string    `json:"studentClientUrlSuffix"`   // 学生端下载URL后缀
	SchoolClientFileHash     string    `json:"schoolClientFileHash"`     // 学校端文件哈希
	WebDistFileHash          string    `json:"webDistFileHash"`          // Web前端文件哈希
	StudentClientFileHash    string    `json:"studentClientFileHash"`    // 学生端文件哈希
	SchoolUpdateDescription  string    `json:"schoolUpdateDescription"`  // 学校端更新说明
	StudentUpdateDescription string    `json:"studentUpdateDescription"` // 学生端更新说明
	PackageClientUpdateId    string    `json:"packageClientUpdateId"`    // 中央端更新ID
}

// CompleteUpdateFiles 完整更新文件（从services包复制）
type CompleteUpdateFiles struct {
	SchoolClientPath  string `json:"schoolClientPath"`
	WebDistPath       string `json:"webDistPath"`
	StudentClientPath string `json:"studentClientPath"`
}

// CentralUpdateData 中央端更新数据（从services包复制）
type CentralUpdateData struct {
	PackageClientUpdateId string                 `json:"packageClientUpdateId"`
	CreateTime            int64                  `json:"createTime"`
	ExtraInfo             map[string]interface{} `json:"extraInfo"`
	VersionNumber         int64                  `json:"versionNumber"`
	Type                  string                 `json:"type"`
	IsPushed              bool                   `json:"isPushed"`
	UpdateConfig          *CentralUpdateConfig   `json:"updateConfig"`
}

// CentralUpdateConfig 中央端更新配置（从services包复制）
type CentralUpdateConfig struct {
	WebDistZipDownloadUrl          string `json:"webDistZipDownloadUrl"`
	SchoolClientLinuxDownloadUrl   string `json:"schoolClientLinuxDownloadUrl"`
	StudentClientDownloadUrl       string `json:"studentClientDownloadUrl"`
	SchoolClientWindowsDownloadUrl string `json:"schoolClientWindowsDownloadUrl"`
	SchoolUpdateDes                string `json:"schoolUpdateDes"`
	WebDistZipFileHash             string `json:"webDistZipFileHash"`
	SchoolClientLinuxFileHash      string `json:"schoolClientLinuxFileHash"`
	SchoolClientWindowsFileHash    string `json:"schoolClientWindowsFileHash"`
	StudentClientFileHash          string `json:"studentClientFileHash"`
	StudentClientUpdateDes         string `json:"studentClientUpdateDes"`
}

// MigrationLog 迁移日志模型
type MigrationLog struct {
	ID           int64     `json:"id"`
	FromVersion  string    `json:"fromVersion"`
	ToVersion    string    `json:"toVersion"`
	Description  string    `json:"description"`
	ExecutedAt   time.Time `json:"executedAt"`
	Status       string    `json:"status"`
	ErrorMessage string    `json:"errorMessage,omitempty"`
}

// GetCurrentVersion 获取当前版本
func GetCurrentVersion() (string, error) {
	var version string
	query := `SELECT version FROM app_versions WHERE is_current = TRUE ORDER BY installed_at DESC LIMIT 1`

	err := DB.QueryRow(query).Scan(&version)
	if err != nil {
		if err == sql.ErrNoRows {
			return "25052901", nil // 默认初始版本
		}
		return "", err
	}

	return version, nil
}

// GetAppVersionByVersion 根据版本号获取版本信息
func GetAppVersionByVersion(version string) (*AppVersion, error) {
	query := `SELECT id, version, installed_at, is_current,
			  COALESCE(school_client_path, '') as school_client_path,
			  COALESCE(web_dist_path, '') as web_dist_path,
			  COALESCE(student_client_path, '') as student_client_path,
			  COALESCE(student_client_url_suffix, '') as student_client_url_suffix,
			  COALESCE(school_client_file_hash, '') as school_client_file_hash,
			  COALESCE(web_dist_file_hash, '') as web_dist_file_hash,
			  COALESCE(student_client_file_hash, '') as student_client_file_hash,
			  COALESCE(school_update_description, '') as school_update_description,
			  COALESCE(student_update_description, '') as student_update_description,
			  COALESCE(package_client_update_id, '') as package_client_update_id
			  FROM app_versions WHERE version = ?`

	var appVersion AppVersion
	err := DB.QueryRow(query, version).Scan(
		&appVersion.ID,
		&appVersion.Version,
		&appVersion.InstalledAt,
		&appVersion.IsCurrent,
		&appVersion.SchoolClientPath,
		&appVersion.WebDistPath,
		&appVersion.StudentClientPath,
		&appVersion.StudentClientUrlSuffix,
		&appVersion.SchoolClientFileHash,
		&appVersion.WebDistFileHash,
		&appVersion.StudentClientFileHash,
		&appVersion.SchoolUpdateDescription,
		&appVersion.StudentUpdateDescription,
		&appVersion.PackageClientUpdateId,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // 版本不存在
		}
		return nil, err
	}

	return &appVersion, nil
}

// SetCurrentVersion 设置当前版本
func SetCurrentVersion(version string) error {
	tx, err := DB.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 将所有版本标记为非当前
	_, err = tx.Exec("UPDATE app_versions SET is_current = FALSE")
	if err != nil {
		return err
	}

	// 检查版本是否已存在
	var count int
	err = tx.QueryRow("SELECT COUNT(*) FROM app_versions WHERE version = ?", version).Scan(&count)
	if err != nil {
		return err
	}

	if count > 0 {
		// 更新现有版本为当前版本
		_, err = tx.Exec("UPDATE app_versions SET is_current = TRUE WHERE version = ?", version)
	} else {
		// 插入新版本记录
		_, err = tx.Exec(`
			INSERT INTO app_versions (version, is_current, installed_at)
			VALUES (?, TRUE, ?)
		`, version, time.Now())
	}

	if err != nil {
		return err
	}

	return tx.Commit()
}

// SaveVersionWithFiles 保存版本信息和文件路径
func SaveVersionWithFiles(version string, completeFiles *CompleteUpdateFiles, updateData *CentralUpdateData) error {
	tx, err := DB.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 检查版本是否已存在
	var existingCount int
	err = tx.QueryRow("SELECT COUNT(*) FROM app_versions WHERE version = ?", version).Scan(&existingCount)
	if err != nil {
		return err
	}

	if existingCount > 0 {
		// 版本已存在，不需要重复插入
		return fmt.Errorf("版本 %s 已存在", version)
	}

	// 注意：获取更新版本时不修改现有版本的is_current状态
	// 因为这只是下载了更新文件，还没有实际更新成功

	// 生成学生端下载URL后缀
	studentClientUrlSuffix := ""
	if completeFiles.StudentClientPath != "" {
		// 从完整路径中提取相对于updates目录的路径
		// 例如：./updates/student-client-20250529.exe -> /updates/student-client-20250529.exe
		// 或者：updates/student-client-20250529.exe -> /updates/student-client-20250529.exe
		if strings.HasPrefix(completeFiles.StudentClientPath, "./updates/") {
			studentClientUrlSuffix = strings.Replace(completeFiles.StudentClientPath, "./updates", "/updates", 1)
		} else if strings.HasPrefix(completeFiles.StudentClientPath, "updates/") {
			studentClientUrlSuffix = "/" + completeFiles.StudentClientPath
		} else {
			// 如果路径格式不符合预期，直接使用文件名
			studentClientUrlSuffix = "/updates/" + filepath.Base(completeFiles.StudentClientPath)
		}
	}

	// 插入新版本记录（is_current=FALSE，因为只是下载了文件，还没有实际更新成功）
	query := `
		INSERT INTO app_versions (
			version, is_current, installed_at, school_client_path, web_dist_path,
			student_client_path, student_client_url_suffix, school_client_file_hash,
			web_dist_file_hash, student_client_file_hash, school_update_description,
			student_update_description, package_client_update_id
		) VALUES (?, FALSE, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	var schoolClientFileHash, webDistFileHash, studentClientFileHash, schoolUpdateDes, studentUpdateDes, packageClientUpdateId string

	if updateData != nil {
		if updateData.UpdateConfig != nil {
			// 根据操作系统选择对应的哈希值
			if strings.ToLower(runtime.GOOS) == "windows" {
				schoolClientFileHash = updateData.UpdateConfig.SchoolClientWindowsFileHash
			} else {
				schoolClientFileHash = updateData.UpdateConfig.SchoolClientLinuxFileHash
			}
			studentClientFileHash = updateData.UpdateConfig.StudentClientFileHash
			webDistFileHash = updateData.UpdateConfig.WebDistZipFileHash
			schoolUpdateDes = updateData.UpdateConfig.SchoolUpdateDes
			studentUpdateDes = updateData.UpdateConfig.StudentClientUpdateDes
		}
		packageClientUpdateId = updateData.PackageClientUpdateId
	}

	_, err = tx.Exec(query,
		version,
		time.Now(),
		completeFiles.SchoolClientPath,
		completeFiles.WebDistPath,
		completeFiles.StudentClientPath,
		studentClientUrlSuffix,
		schoolClientFileHash,
		webDistFileHash,
		studentClientFileHash,
		schoolUpdateDes,
		studentUpdateDes,
		packageClientUpdateId,
	)

	if err != nil {
		return err
	}

	return tx.Commit()
}

// GetAllVersions 获取所有版本记录
func GetAllVersions() ([]AppVersion, error) {
	query := `SELECT id, version, installed_at, is_current,
			  COALESCE(school_client_path, '') as school_client_path,
			  COALESCE(web_dist_path, '') as web_dist_path,
			  COALESCE(student_client_path, '') as student_client_path,
			  COALESCE(student_client_url_suffix, '') as student_client_url_suffix,
			  COALESCE(school_client_file_hash, '') as school_client_file_hash,
			  COALESCE(web_dist_file_hash, '') as web_dist_file_hash,
			  COALESCE(student_client_file_hash, '') as student_client_file_hash,
			  COALESCE(school_update_description, '') as school_update_description,
		  COALESCE(student_update_description, '') as student_update_description,
			  COALESCE(package_client_update_id, '') as package_client_update_id
			  FROM app_versions ORDER BY installed_at DESC`

	rows, err := DB.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var versions []AppVersion
	for rows.Next() {
		var version AppVersion
		err := rows.Scan(
			&version.ID,
			&version.Version,
			&version.InstalledAt,
			&version.IsCurrent,
			&version.SchoolClientPath,
			&version.WebDistPath,
			&version.StudentClientPath,
			&version.StudentClientUrlSuffix,
			&version.SchoolClientFileHash,
			&version.WebDistFileHash,
			&version.StudentClientFileHash,
			&version.SchoolUpdateDescription,
			&version.StudentUpdateDescription,
			&version.PackageClientUpdateId,
		)
		if err != nil {
			return nil, err
		}
		versions = append(versions, version)
	}

	return versions, rows.Err()
}

// AddMigrationLog 添加迁移日志
func AddMigrationLog(fromVersion, toVersion, description, status string, errorMessage ...string) error {
	var errMsg string
	if len(errorMessage) > 0 {
		errMsg = errorMessage[0]
	}

	query := `INSERT INTO migration_logs (from_version, to_version, description, status, error_message, executed_at)
			  VALUES (?, ?, ?, ?, ?, ?)`

	_, err := DB.Exec(query, fromVersion, toVersion, description, status, errMsg, time.Now())
	return err
}

// GetMigrationLogs 获取迁移日志
func GetMigrationLogs(limit int) ([]MigrationLog, error) {
	query := `SELECT id, from_version, to_version, description, executed_at, status,
			  COALESCE(error_message, '') as error_message
			  FROM migration_logs ORDER BY executed_at DESC`

	if limit > 0 {
		query += " LIMIT ?"
	}

	var rows *sql.Rows
	var err error

	if limit > 0 {
		rows, err = DB.Query(query, limit)
	} else {
		rows, err = DB.Query(query)
	}

	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var logs []MigrationLog
	for rows.Next() {
		var log MigrationLog
		err := rows.Scan(
			&log.ID,
			&log.FromVersion,
			&log.ToVersion,
			&log.Description,
			&log.ExecutedAt,
			&log.Status,
			&log.ErrorMessage,
		)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log)
	}

	return logs, rows.Err()
}
