package models

import (
	"database/sql"
	"time"
)

// Config 配置模型
type Config struct {
	ID          int64     `json:"id"`
	Key         string    `json:"key"`
	Value       string    `json:"value"`
	Description string    `json:"description"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

// GetConfig 获取配置
func GetConfig(key string) (*Config, error) {
	config := &Config{}
	query := `SELECT id, key, value, description, updated_at 
			  FROM configs WHERE key = ?`
	
	err := DB.QueryRow(query, key).Scan(
		&config.ID,
		&config.Key,
		&config.Value,
		&config.Description,
		&config.UpdatedAt,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	
	return config, nil
}

// GetAllConfigs 获取所有配置
func GetAllConfigs() ([]Config, error) {
	query := `SELECT id, key, value, description, updated_at 
			  FROM configs ORDER BY key`
	
	rows, err := DB.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	// 解析结果
	configs := []Config{}
	for rows.Next() {
		config := Config{}
		err := rows.Scan(
			&config.ID,
			&config.Key,
			&config.Value,
			&config.Description,
			&config.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		configs = append(configs, config)
	}
	
	if err := rows.Err(); err != nil {
		return nil, err
	}
	
	return configs, nil
}

// UpdateConfig 更新配置
func UpdateConfig(key, value string) error {
	query := `UPDATE configs SET 
			  value = ?, 
			  updated_at = ? 
			  WHERE key = ?`
	
	_, err := DB.Exec(
		query,
		value,
		time.Now(),
		key,
	)
	
	return err
}

// CreateConfig 创建配置
func CreateConfig(key, value, description string) error {
	query := `INSERT INTO configs (key, value, description, updated_at) 
			  VALUES (?, ?, ?, ?)`
	
	_, err := DB.Exec(
		query,
		key,
		value,
		description,
		time.Now(),
	)
	
	return err
}
