package models

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

var DB *sql.DB

// InitDB 初始化数据库连接
func InitDB() error {
	dbPath := os.Getenv("DB_PATH")
	if dbPath == "" {
		dbPath = "./data/school-package.db"
	}

	// 确保数据库目录存在
	dbDir := filepath.Dir(dbPath)
	if err := os.MkdirAll(dbDir, 0755); err != nil {
		return fmt.Errorf("failed to create database directory: %w", err)
	}

	// 打开数据库连接
	var err error
	DB, err = sql.Open("sqlite3", dbPath)
	if err != nil {
		return fmt.Errorf("failed to open database: %w", err)
	}

	// 测试数据库连接
	if err := DB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	// 初始化数据库表
	if err := createTables(); err != nil {
		return fmt.Errorf("failed to create tables: %w", err)
	}

	log.Println("Database initialized successfully")
	return nil
}

// CloseDB 关闭数据库连接
func CloseDB() {
	if DB != nil {
		DB.Close()
	}
}

// createTables 创建数据库表
func createTables() error {
	// 配置表
	_, err := DB.Exec(`
	CREATE TABLE IF NOT EXISTS configs (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		key TEXT NOT NULL UNIQUE,
		value TEXT NOT NULL,
		description TEXT,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`)
	if err != nil {
		return err
	}

	// 会话表
	_, err = DB.Exec(`
	CREATE TABLE IF NOT EXISTS sessions (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		username TEXT NOT NULL UNIQUE,
		school_id TEXT NOT NULL,
		school_name TEXT NOT NULL,
		central_token TEXT,
		local_token TEXT,
		token_expires_at TIMESTAMP,
		last_login TIMESTAMP,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`)
	if err != nil {
		return err
	}

	// 包版本表
	_, err = DB.Exec(`
	CREATE TABLE IF NOT EXISTS package_versions (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		package_version_id TEXT NOT NULL UNIQUE,
		package_push_id TEXT NOT NULL,
		experiment_id TEXT NOT NULL,
		experiment_name TEXT NOT NULL DEFAULT '未知',
		experiment_version_id TEXT NOT NULL,
		experiment_version_name TEXT NOT NULL,
		version TEXT NOT NULL,
		version_name TEXT NOT NULL,
		version_desc TEXT,
		file_hash TEXT NOT NULL,
		file_path TEXT,
		file_size INTEGER DEFAULT 0,
		download_url TEXT NOT NULL,
		sync_status INTEGER DEFAULT 0,
		download_count INTEGER DEFAULT 0,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		school_id TEXT
	)`)
	if err != nil {
		return err
	}

	// 同步日志表
	_, err = DB.Exec(`
	CREATE TABLE IF NOT EXISTS sync_logs (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		package_version_id TEXT NOT NULL,
		package_push_id TEXT NOT NULL,
		action TEXT NOT NULL,
		status INTEGER NOT NULL,
		message TEXT,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`)
	if err != nil {
		return err
	}

	// 系统日志表
	_, err = DB.Exec(`
	CREATE TABLE IF NOT EXISTS system_logs (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		level TEXT NOT NULL,
		module TEXT NOT NULL,
		action TEXT NOT NULL,
		message TEXT NOT NULL,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`)
	if err != nil {
		return err
	}

	// 下载日志表
	_, err = DB.Exec(`
	CREATE TABLE IF NOT EXISTS download_logs (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		package_version_id TEXT NOT NULL,
		student_mac TEXT NOT NULL,
		student_ip TEXT NOT NULL,
		download_time TIMESTAMP NOT NULL,
		user_agent TEXT,
		school_id TEXT NOT NULL,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`)
	if err != nil {
		return err
	}

	// 应用版本管理表
	_, err = DB.Exec(`
	CREATE TABLE IF NOT EXISTS app_versions (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		version TEXT NOT NULL UNIQUE,
		installed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		is_current BOOLEAN DEFAULT FALSE,
		school_client_path TEXT DEFAULT '',
		web_dist_path TEXT DEFAULT '',
		student_client_path TEXT DEFAULT '',
		student_client_url_suffix TEXT DEFAULT '',
		school_client_file_hash TEXT DEFAULT '',
		web_dist_file_hash TEXT DEFAULT '',
		student_client_file_hash TEXT DEFAULT '',
		school_update_description TEXT DEFAULT '',
		student_update_description TEXT DEFAULT '',
		package_client_update_id TEXT DEFAULT ''
	)`)
	if err != nil {
		return err
	}

	// 迁移日志表
	_, err = DB.Exec(`
	CREATE TABLE IF NOT EXISTS migration_logs (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		from_version TEXT NOT NULL,
		to_version TEXT NOT NULL,
		description TEXT NOT NULL,
		executed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		status TEXT NOT NULL,
		error_message TEXT
	)`)
	if err != nil {
		return err
	}

	// 插入默认配置
	err = insertDefaultConfigs()
	if err != nil {
		return err
	}

	// 插入初始版本记录（需要在main.go中调用时传入当前版本）
	// 这里暂时不调用，将在main.go中单独调用

	return nil
}

// insertDefaultConfigs 插入默认配置
func insertDefaultConfigs() error {
	configs := []struct {
		Key         string
		Value       string
		Description string
	}{
		{"sync_interval", "30", "同步间隔（分钟）"},
		{"sync_retry_count", "3", "同步重试次数"},
		{"sync_retry_delay", "5", "同步重试延迟（分钟）"},
		{"max_storage_size", "10737418240", "最大存储大小（字节）"},
		{"log_level", "info", "日志级别"},
		{"max_log_size", "104857600", "最大日志大小（字节）"},
		{"max_log_backups", "5", "最大日志备份数"},
		{"max_log_age", "30", "最大日志保存天数"},
		{"update_check_interval", "12", "更新检查间隔（小时）"},
		{"auto_update_enabled", "true", "是否启用自动更新"},
		{"update_backup_retention", "3", "更新备份保留数量"},
	}

	for _, config := range configs {
		_, err := DB.Exec(
			"INSERT OR IGNORE INTO configs (key, value, description) VALUES (?, ?, ?)",
			config.Key, config.Value, config.Description,
		)
		if err != nil {
			return err
		}
	}

	return nil
}

// InsertInitialVersion 插入初始版本记录（导出函数，供main.go调用）
func InsertInitialVersion(currentVersion string) error {
	// 检查是否已有版本记录
	var count int
	err := DB.QueryRow("SELECT COUNT(*) FROM app_versions").Scan(&count)
	if err != nil {
		return err
	}

	// 如果已有版本记录，不需要插入初始版本
	if count > 0 {
		return nil
	}

	// 插入初始版本记录（使用传入的当前版本）
	query := `INSERT INTO app_versions (version, is_current, installed_at) VALUES (?, TRUE, ?)`

	_, err = DB.Exec(query, currentVersion, time.Now())
	if err != nil {
		return err
	}

	return nil
}
