package models

import (
	"time"
)

// SyncLog 同步日志模型
type SyncLog struct {
	ID               int64     `json:"id"`
	PackageVersionID string    `json:"packageVersionId"`
	PackagePushID    string    `json:"packagePushId"`
	Action           string    `json:"action"`
	Status           int       `json:"status"`
	Message          string    `json:"message"`
	CreatedAt        time.Time `json:"createdAt"`
}

// AddSyncLog 添加同步日志
func AddSyncLog(packageVersionID, packagePushID, action string, status int, message string) error {
	query := `INSERT INTO sync_logs (package_version_id, package_push_id, action, status, message, created_at) 
			  VALUES (?, ?, ?, ?, ?, ?)`

	_, err := DB.Exec(query, packageVersionID, packagePushID, action, status, message, time.Now())
	return err
}

// GetSyncLogs 获取同步日志
func GetSyncLogs(page, pageSize int, packageVersionID, action string, status int) ([]SyncLog, int, error) {
	// 构建查询条件
	whereClause := ""
	args := []interface{}{}

	if packageVersionID != "" {
		whereClause += " WHERE package_version_id = ?"
		args = append(args, packageVersionID)
	}

	if action != "" {
		if whereClause == "" {
			whereClause += " WHERE action = ?"
		} else {
			whereClause += " AND action = ?"
		}
		args = append(args, action)
	}

	if status >= 0 {
		if whereClause == "" {
			whereClause += " WHERE status = ?"
		} else {
			whereClause += " AND status = ?"
		}
		args = append(args, status)
	}

	// 获取总数
	countQuery := "SELECT COUNT(*) FROM sync_logs" + whereClause
	var total int
	err := DB.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	// 计算分页
	offset := (page - 1) * pageSize

	// 查询日志
	query := `SELECT id, package_version_id, package_push_id, action, status, message, created_at 
			  FROM sync_logs` + whereClause + ` 
			  ORDER BY created_at DESC 
			  LIMIT ? OFFSET ?`

	args = append(args, pageSize, offset)

	rows, err := DB.Query(query, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	// 解析结果
	logs := []SyncLog{}
	for rows.Next() {
		log := SyncLog{}
		err := rows.Scan(
			&log.ID,
			&log.PackageVersionID,
			&log.PackagePushID,
			&log.Action,
			&log.Status,
			&log.Message,
			&log.CreatedAt,
		)
		if err != nil {
			return nil, 0, err
		}
		logs = append(logs, log)
	}

	if err := rows.Err(); err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetRecentSyncLogs 获取最近的同步日志
func GetRecentSyncLogs(limit int) ([]SyncLog, error) {
	query := `SELECT id, package_version_id, package_push_id, action, status, message, created_at 
			  FROM sync_logs 
			  ORDER BY created_at DESC 
			  LIMIT ?`

	rows, err := DB.Query(query, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 解析结果
	logs := []SyncLog{}
	for rows.Next() {
		log := SyncLog{}
		err := rows.Scan(
			&log.ID,
			&log.PackageVersionID,
			&log.PackagePushID,
			&log.Action,
			&log.Status,
			&log.Message,
			&log.CreatedAt,
		)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return logs, nil
}

// GetSyncLogsByPackageVersionID 获取指定包版本的同步日志
func GetSyncLogsByPackageVersionID(packageVersionID string) ([]SyncLog, error) {
	query := `SELECT id, package_version_id, package_push_id, action, status, message, created_at 
			  FROM sync_logs 
			  WHERE package_version_id = ? 
			  ORDER BY created_at DESC`

	rows, err := DB.Query(query, packageVersionID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 解析结果
	logs := []SyncLog{}
	for rows.Next() {
		log := SyncLog{}
		err := rows.Scan(
			&log.ID,
			&log.PackageVersionID,
			&log.PackagePushID,
			&log.Action,
			&log.Status,
			&log.Message,
			&log.CreatedAt,
		)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return logs, nil
}
