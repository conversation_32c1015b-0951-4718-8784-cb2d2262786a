package models

import (
	"time"
)

// DownloadLog 下载日志模型
type DownloadLog struct {
	ID               int64     `json:"id"`
	PackageVersionID string    `json:"packageVersionId"` // 包版本ID
	StudentMAC       string    `json:"studentMac"`       // 学生端MAC地址
	StudentIP        string    `json:"studentIp"`        // 学生端IP地址
	DownloadTime     time.Time `json:"downloadTime"`     // 下载时间
	UserAgent        string    `json:"userAgent"`        // 用户代理
	SchoolID         string    `json:"schoolId"`         // 学校ID
	CreatedAt        time.Time `json:"createdAt"`
	UpdatedAt        time.Time `json:"updatedAt"`
}

// CreateDownloadLog 创建下载日志
func CreateDownloadLog(log *DownloadLog) error {
	query := `INSERT INTO download_logs (
			  package_version_id, student_mac, student_ip, download_time, 
			  user_agent, school_id, created_at, updated_at
			  ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`

	now := time.Now()
	log.CreatedAt = now
	log.UpdatedAt = now

	result, err := DB.Exec(
		query,
		log.PackageVersionID,
		log.StudentMAC,
		log.StudentIP,
		log.DownloadTime,
		log.UserAgent,
		log.SchoolID,
		log.CreatedAt,
		log.UpdatedAt,
	)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}

	log.ID = id
	return nil
}

// GetDownloadLogsByPackageVersionID 根据包版本ID获取下载日志
func GetDownloadLogsByPackageVersionID(packageVersionID string) ([]DownloadLog, error) {
	query := `SELECT id, package_version_id, student_mac, student_ip, download_time,
			  user_agent, school_id, created_at, updated_at
			  FROM download_logs
			  WHERE package_version_id = ?
			  ORDER BY download_time DESC`

	rows, err := DB.Query(query, packageVersionID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	logs := []DownloadLog{}
	for rows.Next() {
		log := DownloadLog{}
		err := rows.Scan(
			&log.ID,
			&log.PackageVersionID,
			&log.StudentMAC,
			&log.StudentIP,
			&log.DownloadTime,
			&log.UserAgent,
			&log.SchoolID,
			&log.CreatedAt,
			&log.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return logs, nil
}

// GetDownloadLogsBySchoolID 根据学校ID获取下载日志
func GetDownloadLogsBySchoolID(schoolID string, page, pageSize int) ([]DownloadLog, int, error) {
	// 获取总数
	countQuery := "SELECT COUNT(*) FROM download_logs WHERE school_id = ?"
	var total int
	err := DB.QueryRow(countQuery, schoolID).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	// 计算分页
	offset := (page - 1) * pageSize

	// 查询下载日志
	query := `SELECT id, package_version_id, student_mac, student_ip, download_time,
			  user_agent, school_id, created_at, updated_at
			  FROM download_logs
			  WHERE school_id = ?
			  ORDER BY download_time DESC
			  LIMIT ? OFFSET ?`

	rows, err := DB.Query(query, schoolID, pageSize, offset)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	logs := []DownloadLog{}
	for rows.Next() {
		log := DownloadLog{}
		err := rows.Scan(
			&log.ID,
			&log.PackageVersionID,
			&log.StudentMAC,
			&log.StudentIP,
			&log.DownloadTime,
			&log.UserAgent,
			&log.SchoolID,
			&log.CreatedAt,
			&log.UpdatedAt,
		)
		if err != nil {
			return nil, 0, err
		}
		logs = append(logs, log)
	}

	if err := rows.Err(); err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// IncrementPackageDownloadCount 增加包版本的下载次数
func IncrementPackageDownloadCount(packageVersionID string) error {
	query := `UPDATE package_versions SET 
			  download_count = download_count + 1
			  WHERE package_version_id = ?`

	_, err := DB.Exec(query, packageVersionID)
	return err
}
