package models

// 包同步状态常量
const (
	// 学校端同步状态
	SyncStatusNotSynced = 0 // 未同步
	SyncStatusSyncing   = 1 // 同步中
	SyncStatusSynced    = 2 // 已同步
	SyncStatusFailed    = 3 // 同步失败

	// 学生端同步状态（兼容）
	SyncStatusNone        = 0 // 未同步
	SyncStatusDownloading = 1 // 下载中
	SyncStatusDownloaded  = 2 // 已下载
	SyncStatusExtracting  = 3 // 解压中
	SyncStatusCompleted   = 4 // 已完成
)

// 操作系统类型常量
const (
	OSTypeWindows = "windows"
	OSTypeLinux   = "linux"
	OSTypeDarwin  = "darwin"
)

// 客户端类型常量
const (
	ClientTypeSchool  = "school"
	ClientTypeStudent = "student"
	ClientTypeTeacher = "teacher"
)
