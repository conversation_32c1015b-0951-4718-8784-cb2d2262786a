package middleware

import (
	"net/http"
	"strings"

	"school-package-system/models"
	"school-package-system/utils"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware 认证中间件
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取令牌
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "需要授权头信息",
			})
			c.Abort()
			return
		}

		// 解析令牌
		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			c.J<PERSON>(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "授权头格式必须为 Bearer {token}",
			})
			c.Abort()
			return
		}

		// 验证令牌
		tokenString := parts[1]
		valid, _, err := utils.ValidateToken(tokenString)
		if err != nil || !valid {
			c.<PERSON><PERSON>(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "无效或已过期的令牌",
				"error":   err.Error(),
			})
			c.Abort()
			return
		}

		// 检查数据库中的会话状态
		session, err := models.GetSessionByLocalToken(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "会话已失效，请重新登录",
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("userID", session.ID)
		c.Set("username", session.Username)
		c.Set("schoolID", session.SchoolID)

		c.Next()
	}
}

// OptionalAuthMiddleware 可选认证中间件
func OptionalAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取令牌
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		// 解析令牌
		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			c.Next()
			return
		}

		// 验证令牌
		tokenString := parts[1]
		valid, claims, err := utils.ValidateToken(tokenString)
		if err != nil || !valid {
			c.Next()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("userID", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("schoolID", claims.SchoolID)
		c.Set("authenticated", true)

		c.Next()
	}
}
