# 学校端后端Windows打包脚本 (PowerShell版本)
# 使用方法：在backend目录下执行 .\build-windows.ps1

Write-Host "开始构建学校端后端Windows版本..." -ForegroundColor Green

# 设置变量
$APP_NAME = "school-package-system"
$FRONTEND_DIR = "..\frontend"

# 检查前端目录是否存在
if (-not (Test-Path $FRONTEND_DIR)) {
    Write-Host "错误：未找到前端目录 $FRONTEND_DIR" -ForegroundColor Red
    Write-Host "请确保在正确的目录下执行此脚本" -ForegroundColor Red
    exit 1
}

# 构建前端
Write-Host "开始构建前端..." -ForegroundColor Yellow
Push-Location $FRONTEND_DIR

# 检查是否安装了Node.js和npm
if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
    Write-Host "错误：未找到 Node.js" -ForegroundColor Red
    Write-Host "请先安装 Node.js" -ForegroundColor Red
    exit 1
}

if (-not (Get-Command npm -ErrorAction SilentlyContinue)) {
    Write-Host "错误：未找到 npm" -ForegroundColor Red
    Write-Host "请先安装 npm" -ForegroundColor Red
    exit 1
}

# 安装前端依赖
Write-Host "安装前端依赖..." -ForegroundColor Yellow
npm install

# 构建前端
Write-Host "构建前端项目..." -ForegroundColor Yellow
npm run build

# 检查前端构建是否成功（前端配置为直接输出到 ..\backend\web\dist）
if (-not (Test-Path "..\backend\web\dist")) {
    Write-Host "错误：前端构建失败，未找到 ..\backend\web\dist 目录" -ForegroundColor Red
    exit 1
}

Write-Host "前端构建完成" -ForegroundColor Green

# 返回到backend目录
Pop-Location

# 检查前端构建文件（前端构建时已直接输出到 web/dist）
if (-not (Test-Path "web\dist")) {
    Write-Host "错误：未找到前端构建文件 web\dist" -ForegroundColor Red
    Write-Host "请确保前端已正确构建到 web\dist 目录" -ForegroundColor Red
    exit 1
}

Write-Host "前端文件已就绪" -ForegroundColor Green

# 构建Windows可执行文件
Write-Host "构建Windows可执行文件..." -ForegroundColor Yellow
$env:GOOS="windows"; $env:GOARCH="amd64"; $env:CGO_ENABLED="1"; $env:CC="gcc"; go build -o "$APP_NAME.exe"

if ($LASTEXITCODE -eq 0) {
    Write-Host "构建完成！" -ForegroundColor Green
    Write-Host "输出文件：$APP_NAME.exe" -ForegroundColor Green
} else {
    Write-Host "构建失败！" -ForegroundColor Red
    exit 1
}