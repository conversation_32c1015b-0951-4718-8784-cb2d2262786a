package main

import (
	"flag"
	"fmt"
	"io"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

// Updater 更新器结构体
type Updater struct {
	Type    string // "school" | "student"
	OldExe  string // 当前程序路径
	NewExe  string // 新程序路径
	Restart bool   // 是否重启
	logger  *log.Logger
}

func main() {
	var updater Updater
	flag.StringVar(&updater.Type, "type", "", "更新类型: school|student|teacher")
	flag.StringVar(&updater.OldExe, "old", "", "当前程序路径")
	flag.StringVar(&updater.NewExe, "new", "", "新程序路径")
	flag.BoolVar(&updater.Restart, "restart", false, "更新后重启")
	flag.Parse()

	// 参数验证
	if updater.Type == "" || updater.OldExe == "" || updater.NewExe == "" {
		flag.Usage()
		os.Exit(1)
	}

	// 验证支持的程序类型
	supportedTypes := []string{"school", "student", "teacher"}
	if !updater.isValidType(supportedTypes) {
		log.Fatalf("错误：type 参数必须是以下之一: %v", supportedTypes)
	}

	// 初始化日志
	updater.initLogger()

	// 执行更新
	if err := updater.Execute(); err != nil {
		updater.logger.Printf("更新失败: %v", err)
		os.Exit(1)
	}

	updater.logger.Printf("%s 程序更新成功完成", updater.Type)
}

// isValidType 检查程序类型是否有效
func (u *Updater) isValidType(supportedTypes []string) bool {
	for _, validType := range supportedTypes {
		if u.Type == validType {
			return true
		}
	}
	return false
}

// initLogger 初始化日志记录器
func (u *Updater) initLogger() {
	logFile := fmt.Sprintf("updater_%s.log", time.Now().Format("20060102_150405"))
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		log.Fatal("无法创建日志文件:", err)
	}

	// 同时输出到文件和控制台
	multiWriter := io.MultiWriter(os.Stdout, file)
	u.logger = log.New(multiWriter, "[UPDATER] ", log.LstdFlags)
}

// Execute 执行更新流程
func (u *Updater) Execute() error {
	u.logger.Printf("开始更新 %s 程序", u.Type)
	u.logger.Printf("当前程序: %s", u.OldExe)
	u.logger.Printf("新程序: %s", u.NewExe)

	// 1. 验证文件
	if err := u.validateFiles(); err != nil {
		return fmt.Errorf("文件验证失败: %v", err)
	}

	// 2. 等待原程序退出
	if err := u.waitForExit(); err != nil {
		return fmt.Errorf("等待程序退出失败: %v", err)
	}

	// 3. 备份原程序
	if err := u.backup(); err != nil {
		return fmt.Errorf("备份程序失败: %v", err)
	}

	// 4. 替换程序
	if err := u.replace(); err != nil {
		u.logger.Printf("程序替换失败，开始回滚")
		if rollbackErr := u.rollback(); rollbackErr != nil {
			return fmt.Errorf("替换失败且回滚失败: 替换错误=%v, 回滚错误=%v", err, rollbackErr)
		}
		return fmt.Errorf("程序替换失败: %v", err)
	}

	// 5. 重启程序（如果需要）
	if u.Restart {
		if err := u.restart(); err != nil {
			return fmt.Errorf("重启程序失败: %v", err)
		}
	}

	// 6. 清理临时文件
	u.cleanup()

	return nil
}

// validateFiles 验证文件
func (u *Updater) validateFiles() error {
	// 检查当前程序文件是否存在
	if _, err := os.Stat(u.OldExe); err != nil {
		return fmt.Errorf("当前程序文件不存在: %s", u.OldExe)
	}

	// 检查新程序文件是否存在
	if _, err := os.Stat(u.NewExe); err != nil {
		return fmt.Errorf("新程序文件不存在: %s", u.NewExe)
	}

	// 检查新程序文件大小
	newFileInfo, err := os.Stat(u.NewExe)
	if err != nil {
		return err
	}

	if newFileInfo.Size() == 0 {
		return fmt.Errorf("新程序文件大小为0")
	}

	u.logger.Printf("文件验证通过，新程序大小: %d 字节", newFileInfo.Size())
	return nil
}

// waitForExit 等待原程序退出
func (u *Updater) waitForExit() error {
	u.logger.Printf("等待原程序退出...")

	programName := filepath.Base(u.OldExe)
	// 移除扩展名用于进程检查
	if ext := filepath.Ext(programName); ext != "" {
		programName = strings.TrimSuffix(programName, ext)
	}

	for i := 0; i < 30; i++ { // 最多等待30秒
		if !u.isProcessRunning(programName) {
			u.logger.Printf("原程序已退出")
			return nil
		}
		u.logger.Printf("等待程序退出... (%d/30)", i+1)
		time.Sleep(1 * time.Second)
	}

	return fmt.Errorf("等待程序退出超时")
}

// isProcessRunning 检查进程是否还在运行
func (u *Updater) isProcessRunning(processName string) bool {
	var cmd *exec.Cmd

	switch runtime.GOOS {
	case "windows":
		cmd = exec.Command("tasklist", "/FI", fmt.Sprintf("IMAGENAME eq %s.exe", processName))
	case "linux", "darwin":
		cmd = exec.Command("pgrep", "-f", processName)
	default:
		u.logger.Printf("不支持的操作系统: %s", runtime.GOOS)
		return false
	}

	output, err := cmd.Output()
	if err != nil {
		return false
	}

	return strings.Contains(string(output), processName)
}

// backup 备份原程序
func (u *Updater) backup() error {
	backupPath := u.OldExe + ".backup"
	u.logger.Printf("备份原程序到: %s", backupPath)

	return u.copyFile(u.OldExe, backupPath)
}

// replace 替换程序
func (u *Updater) replace() error {
	u.logger.Printf("替换程序文件...")

	// 删除旧程序
	if err := os.Remove(u.OldExe); err != nil {
		return fmt.Errorf("删除旧程序失败: %v", err)
	}

	// 复制新程序
	if err := u.copyFile(u.NewExe, u.OldExe); err != nil {
		return fmt.Errorf("复制新程序失败: %v", err)
	}

	// 设置执行权限（Unix系统）
	if runtime.GOOS != "windows" {
		if err := os.Chmod(u.OldExe, 0755); err != nil {
			return fmt.Errorf("设置执行权限失败: %v", err)
		}
	}

	u.logger.Printf("程序替换完成")
	return nil
}

// rollback 回滚操作
func (u *Updater) rollback() error {
	backupPath := u.OldExe + ".backup"
	u.logger.Printf("回滚程序从备份: %s", backupPath)

	if _, err := os.Stat(backupPath); err != nil {
		return fmt.Errorf("备份文件不存在: %v", err)
	}

	return u.copyFile(backupPath, u.OldExe)
}

// restart 重启程序
func (u *Updater) restart() error {
	u.logger.Printf("重启程序: %s", u.OldExe)

	// 等待一下确保文件系统操作完成
	time.Sleep(1 * time.Second)

	var cmd *exec.Cmd
	if runtime.GOOS == "windows" {
		// Windows下使用start命令启动程序
		cmd = exec.Command("cmd", "/c", "start", "", u.OldExe)
	} else {
		// Unix系统直接启动
		cmd = exec.Command(u.OldExe)
	}

	if err := cmd.Start(); err != nil {
		return fmt.Errorf("启动程序失败: %v", err)
	}

	u.logger.Printf("程序重启成功")
	return nil
}

// cleanup 清理临时文件
func (u *Updater) cleanup() {
	u.logger.Printf("清理临时文件...")

	// 删除新程序文件（已经复制到目标位置）
	if err := os.Remove(u.NewExe); err != nil {
		u.logger.Printf("删除临时文件失败: %v", err)
	}

	// 可选：删除备份文件（保留一段时间后再删除）
	// backupPath := u.OldExe + ".backup"
	// os.Remove(backupPath)

	u.logger.Printf("清理完成")
}

// copyFile 复制文件
func (u *Updater) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return err
	}

	// 确保数据写入磁盘
	return destFile.Sync()
}
