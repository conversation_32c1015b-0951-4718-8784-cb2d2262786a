#!/bin/bash

# 更新器测试脚本

echo "=== 更新器测试脚本 ==="

# 检查是否已编译
if [ ! -f "bin/updater" ]; then
    echo "错误：未找到更新器程序，请先运行 ./build.sh"
    exit 1
fi

# 创建测试目录
TEST_DIR="test_env"
mkdir -p $TEST_DIR

# 创建模拟的旧程序
echo "创建测试程序..."
cat > $TEST_DIR/old_program.go << 'EOF'
package main

import (
    "fmt"
    "time"
)

func main() {
    fmt.Println("这是旧版本程序 v1.0.0")
    fmt.Println("程序运行中...")
    time.Sleep(2 * time.Second)
    fmt.Println("程序退出")
}
EOF

# 创建模拟的新程序
cat > $TEST_DIR/new_program.go << 'EOF'
package main

import (
    "fmt"
    "time"
)

func main() {
    fmt.Println("这是新版本程序 v2.0.0")
    fmt.Println("程序运行中...")
    time.Sleep(2 * time.Second)
    fmt.Println("程序退出")
}
EOF

# 编译测试程序
echo "编译测试程序..."
cd $TEST_DIR
go build -o old_program old_program.go
go build -o new_program new_program.go
cd ..

# 复制更新器到测试目录
cp bin/updater $TEST_DIR/

echo ""
echo "=== 开始测试更新流程 ==="

cd $TEST_DIR

# 启动旧程序（后台运行）
echo "1. 启动旧程序..."
./old_program &
OLD_PID=$!

# 等待程序启动
sleep 1

# 运行更新器
echo "2. 运行更新器..."
./updater --type=student --old=./old_program --new=./new_program --restart

# 等待更新完成
sleep 2

# 检查结果
echo ""
echo "=== 测试结果 ==="

if [ -f "old_program.backup" ]; then
    echo "✓ 备份文件已创建"
else
    echo "✗ 备份文件未创建"
fi

if [ -f "old_program" ]; then
    echo "✓ 程序文件存在"
    
    # 运行更新后的程序检查版本
    echo "3. 运行更新后的程序..."
    ./old_program
else
    echo "✗ 程序文件不存在"
fi

# 检查日志文件
if ls updater_*.log 1> /dev/null 2>&1; then
    echo "✓ 日志文件已生成"
    echo "最新日志内容："
    echo "=================="
    tail -10 updater_*.log
else
    echo "✗ 日志文件未生成"
fi

# 清理
cd ..
echo ""
echo "测试完成！测试文件保留在 $TEST_DIR 目录中"
echo "如需清理测试环境，请运行: rm -rf $TEST_DIR"
