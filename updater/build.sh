#!/bin/bash

# 通用更新器编译脚本

echo "开始编译通用更新器..."

# 创建输出目录
mkdir -p bin

# 编译当前平台
echo "编译当前平台版本..."
go build -o bin/updater

# 交叉编译 Windows
echo "编译 Windows 版本..."
GOOS=windows GOARCH=amd64 go build -o bin/updater.exe

# 交叉编译 Linux
echo "编译 Linux 版本..."
GOOS=linux GOARCH=amd64 go build -o bin/updater-linux

# 交叉编译 macOS
echo "编译 macOS 版本..."
GOOS=darwin GOARCH=amd64 go build -o bin/updater-macos

echo "编译完成！输出文件："
ls -la bin/

echo ""
echo "使用方法："
echo "  ./bin/updater --type=school --old=./old.exe --new=./new.exe --restart"
echo "  ./bin/updater --type=student --old=./old.exe --new=./new.exe --restart"
echo "  ./bin/updater --type=teacher --old=./old.exe --new=./new.exe --restart"
