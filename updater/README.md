# 通用更新器

一个简单稳健的程序更新器，支持多种类型程序的自动更新（学校端、学生端、教师端等）。

## 功能特点

- **极简稳健**：只负责文件替换和重启，避免复杂逻辑
- **跨平台支持**：支持 Windows、Linux、macOS
- **安全可靠**：完整的备份和回滚机制
- **详细日志**：记录更新过程的每个步骤

## 使用方法

### 基本用法

```bash
# 学校端更新
./updater --type=school --old=./school-package-system.exe --new=./new-school.exe --restart

# 学生端更新
./updater --type=student --old=./student-client.exe --new=./new-student.exe --restart

# 教师端更新
./updater --type=teacher --old=./teacher-client.exe --new=./new-teacher.exe --restart
```

### 参数说明

- `--type`: 更新类型，支持 `school`、`student`、`teacher`
- `--old`: 当前程序的路径
- `--new`: 新程序的路径
- `--restart`: 更新完成后是否重启程序（可选）

## 更新流程

1. **文件验证**：检查新旧程序文件是否存在且有效
2. **等待退出**：等待原程序完全退出（最多30秒）
3. **备份程序**：将当前程序备份为 `.backup` 文件
4. **替换程序**：用新程序替换当前程序
5. **重启程序**：如果指定了 `--restart` 参数，则重启程序
6. **清理文件**：删除临时的新程序文件

## 错误处理

- 如果程序替换失败，会自动从备份恢复
- 所有操作都有详细的日志记录
- 支持跨平台的进程检测

## 编译

```bash
# 编译当前平台
go build -o updater

# 交叉编译 Windows
GOOS=windows GOARCH=amd64 go build -o updater.exe

# 交叉编译 Linux
GOOS=linux GOARCH=amd64 go build -o updater

# 交叉编译 macOS
GOOS=darwin GOARCH=amd64 go build -o updater
```

## 日志文件

更新器会生成带时间戳的日志文件：`updater_YYYYMMDD_HHMMSS.log`

日志同时输出到控制台和文件，方便调试和问题排查。

## 注意事项

1. 确保更新器有足够的权限操作目标程序文件
2. 更新前确保原程序已经正常退出
3. 备份文件会保留，可用于手动回滚
4. 新程序文件在更新完成后会被自动删除
