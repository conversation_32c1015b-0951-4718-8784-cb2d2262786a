@echo off
REM 通用更新器编译脚本 (Windows)

echo 开始编译通用更新器...

REM 创建输出目录
if not exist bin mkdir bin

REM 编译当前平台 (Windows)
echo 编译 Windows 版本...
go build -o bin\updater.exe

REM 交叉编译 Linux
echo 编译 Linux 版本...
set GOOS=linux
set GOARCH=amd64
go build -o bin\updater-linux

REM 交叉编译 macOS
echo 编译 macOS 版本...
set GOOS=darwin
set GOARCH=amd64
go build -o bin\updater-macos

REM 重置环境变量
set GOOS=
set GOARCH=

echo 编译完成！输出文件：
dir bin\

echo.
echo 使用方法：
echo   .\bin\updater.exe --type=school --old=.\old.exe --new=.\new.exe --restart
echo   .\bin\updater.exe --type=student --old=.\old.exe --new=.\new.exe --restart
echo   .\bin\updater.exe --type=teacher --old=.\old.exe --new=.\new.exe --restart

pause
