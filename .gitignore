# Go 后端相关忽略
# 编译生成的二进制文件
/backend/school-package-system
/backend/main
*.exe
*.exe~
*.dll
*.so
*.dylib

# 测试二进制文件
*.test

# 覆盖率工具生成的文件
*.out
*.prof

# Go 依赖目录
/backend/vendor/
/backend/Godeps/
/backend/web/

# Go 模块缓存
/backend/go.sum

# 本地配置文件
/backend/.env
/backend/config.local.yaml
/backend/config.dev.yaml

# SQLite 数据库文件
/backend/*.db
/backend/*.sqlite
/backend/*.sqlite3

# 日志文件
/backend/logs/
/backend/*.log

# Vue 前端相关忽略
# 依赖目录
/frontend/node_modules/
/frontend/.pnp
/frontend/.pnp.js

# 构建输出
/frontend/dist/
/frontend/build/

# 本地环境文件
/frontend/.env.local
/frontend/.env.*.local

# 日志文件
/frontend/npm-debug.log*
/frontend/yarn-debug.log*
/frontend/yarn-error.log*
/frontend/pnpm-debug.log*

# 编辑器目录和文件
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store

# 缓存文件
.cache/
.temp/
.tmp/

# 上传的文件目录
/uploads/
/storage/
/public/uploads/

# 系统文件
.DS_Store
Thumbs.db

# 临时文件
*.tmp
*.bak
*.swp
*~.nib

# 包文件目录
/backend/packages/

# 学生端相关忽略
# 依赖目录
/student-client/node_modules/
/student-client/wailsjs/

# 构建输出
/student-client/build/
/student-client/dist/

# 本地环境文件
/student-client/.env.local
/student-client/.env.*.local

# 日志文件
/student-client/npm-debug.log*
/student-client/yarn-debug.log*
/student-client/yarn-error.log*
/student-client/pnpm-debug.log*

# SQLite 数据库文件
/student-client/*.db
/student-client/*.sqlite
/student-client/*.sqlite3

# 下载的包文件和解压目录
/student-client/downloads/
/student-client/packages/
/student-client/extracted/

# Wails 生成的文件
/student-client/frontend/wailsjs/
/student-client/frontend/dist

/student-client/frontend/node_modules
/student-client/student-client

# 更新器相关忽略
# 编译生成的二进制文件

backend/build/
/backend/build/

/updater/bin/
/updater/updater
/updater/updater.exe
/updater/updater-*

# 更新器日志文件
/updater/*.log
/updater/updater_*.log

# 更新器测试环境
/updater/test_env/

# 备份文件
*.backup

# 软链接 java-erp
/java-erp

# 软链接 fe-erp
/fe-erp1.0

/backend/updater
/backend/updater.exe

/backend/updates

/backend/dist/school-package-system-windows.zip
backend/dist/school-package-system-windows.zip


## 教师端
# 学生端相关忽略
# 依赖目录
/teacher-client/node_modules/
/teacher-client/wailsjs/

# 构建输出
/teacher-client/build/
/teacher-client/dist/

# 本地环境文件
/teacher-client/.env.local
/teacher-client/.env.*.local

# 日志文件
/teacher-client/npm-debug.log*
/teacher-client/yarn-debug.log*
/teacher-client/yarn-error.log*
/teacher-client/pnpm-debug.log*

# SQLite 数据库文件
/teacher-client/*.db
/teacher-client/*.sqlite
/teacher-client/*.sqlite3

# 下载的包文件和解压目录
/teacher-client/downloads/
/teacher-client/packages/
/teacher-client/extracted/

# Wails 生成的文件
/teacher-client/frontend/wailsjs/
/teacher-client/frontend/dist

/teacher-client/frontend/node_modules
/teacher-client/student-client

/teacher-client/updater
/teacher-client/updater.exe