# 教师端部署文档

## 概述

教师端是一个基于 Wails 的桌面应用程序，支持 Windows、macOS 和 Linux 平台。本文档详细说明了构建、打包和部署流程。

## 构建环境要求

### 基础环境
- **Go**: 1.19 或更高版本
- **Node.js**: 16 或更高版本
- **Wails CLI**: 最新版本
- **Git**: 用于版本控制

### 平台特定要求

#### Windows 构建
```bash
# 安装 TDM-GCC 或 MinGW-w64
# 下载地址: https://jmeubank.github.io/tdm-gcc/

# 或使用 Chocolatey 安装
choco install mingw

# 验证安装
gcc --version
```

#### macOS 构建
```bash
# 安装 Xcode Command Line Tools
xcode-select --install

# 安装跨平台编译工具 (可选)
brew install mingw-w64
```

#### Linux 构建
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install build-essential

# CentOS/RHEL
sudo yum groupinstall "Development Tools"

# 安装 GTK 开发库
sudo apt-get install libgtk-3-dev libwebkit2gtk-4.0-dev
```

## 构建流程

### 1. 准备构建环境

```bash
# 克隆项目
git clone <repository-url>
cd teacher-client

# 安装依赖
go mod tidy
cd frontend && npm ci && cd ..

# 验证环境
wails doctor
```

### 2. 版本管理

#### 更新版本号
```bash
# 在 main.go 中更新 BuildVersion
const BuildVersion = "********"

# 在 wails.json 中更新版本信息
{
  "version": "1.0.4",
  "info": {
    "productVersion": "********"
  }
}
```

#### 生成版本信息
```bash
# 创建版本信息文件
cat > version.txt << EOF
Version: ********
Build Date: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
Git Commit: $(git rev-parse --short HEAD)
Git Branch: $(git rev-parse --abbrev-ref HEAD)
EOF
```

### 3. 构建命令

#### 开发构建
```bash
# 包含调试信息的构建
wails build -debug

# 输出位置: build/bin/
```

#### 生产构建
```bash
# Windows 64位
wails build -platform windows/amd64 -clean

# macOS 64位 (Intel)
wails build -platform darwin/amd64 -clean

# macOS ARM64 (Apple Silicon)
wails build -platform darwin/arm64 -clean

# Linux 64位
wails build -platform linux/amd64 -clean
```

#### 批量构建脚本
```bash
#!/bin/bash
# build-all.sh

set -e

echo "开始批量构建教师端..."

# 清理旧构建
rm -rf build/

# 定义构建平台
platforms=(
    "windows/amd64"
    "darwin/amd64"
    "darwin/arm64"
    "linux/amd64"
)

# 构建各平台版本
for platform in "${platforms[@]}"; do
    echo "构建 $platform..."
    wails build -platform "$platform" -clean
    
    # 检查构建结果
    if [ $? -eq 0 ]; then
        echo "✅ $platform 构建成功"
    else
        echo "❌ $platform 构建失败"
        exit 1
    fi
done

echo "🎉 所有平台构建完成！"
ls -la build/bin/
```

### 4. 跨平台构建

#### 在 macOS 上构建 Windows 版本
```bash
# 设置环境变量
export CGO_ENABLED=1
export CC=x86_64-w64-mingw32-gcc
export CXX=x86_64-w64-mingw32-g++

# 构建
wails build -platform windows/amd64 \
  -ldflags '-extldflags "-static"' \
  -skipbindings \
  -clean
```

#### 在 Linux 上构建 Windows 版本
```bash
# 安装 MinGW
sudo apt-get install gcc-mingw-w64

# 设置环境变量
export CGO_ENABLED=1
export CC=x86_64-w64-mingw32-gcc
export CXX=x86_64-w64-mingw32-g++

# 构建
wails build -platform windows/amd64 -clean
```

## 打包和分发

### 1. Windows 打包

#### 创建安装包目录结构
```
teacher-client-windows/
├── teacher-client.exe          # 主程序
├── updater.exe                 # 更新器
├── .env                        # 配置文件
├── README.txt                  # 说明文档
└── install.bat                 # 安装脚本
```

#### 安装脚本 (install.bat)
```batch
@echo off
chcp 65001 > nul
echo 教师端包管理系统安装程序
echo.

REM 创建程序目录
set INSTALL_DIR=%PROGRAMFILES%\TeacherClient
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM 复制文件
echo 正在安装文件...
copy teacher-client.exe "%INSTALL_DIR%\"
copy updater.exe "%INSTALL_DIR%\"
copy .env "%INSTALL_DIR%\"
copy README.txt "%INSTALL_DIR%\"

REM 创建桌面快捷方式
echo 正在创建桌面快捷方式...
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\教师端包管理系统.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\teacher-client.exe'; $Shortcut.Save()}"

REM 创建开始菜单快捷方式
set START_MENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs
if not exist "%START_MENU%\教师端包管理系统" mkdir "%START_MENU%\教师端包管理系统"
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\教师端包管理系统\教师端包管理系统.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\teacher-client.exe'; $Shortcut.Save()}"

echo.
echo 安装完成！
echo 程序已安装到: %INSTALL_DIR%
echo 桌面快捷方式已创建
pause
```

#### 使用 NSIS 创建安装程序
```nsis
; teacher-client-installer.nsi
!define APP_NAME "教师端包管理系统"
!define APP_VERSION "1.0.4"
!define APP_PUBLISHER "成都智云鸿道信息技术有限公司"
!define APP_EXE "teacher-client.exe"

Name "${APP_NAME}"
OutFile "teacher-client-setup-${APP_VERSION}.exe"
InstallDir "$PROGRAMFILES\${APP_NAME}"

Section "MainSection" SEC01
    SetOutPath "$INSTDIR"
    File "teacher-client.exe"
    File "updater.exe"
    File ".env"
    File "README.txt"
    
    ; 创建快捷方式
    CreateDirectory "$SMPROGRAMS\${APP_NAME}"
    CreateShortCut "$SMPROGRAMS\${APP_NAME}\${APP_NAME}.lnk" "$INSTDIR\${APP_EXE}"
    CreateShortCut "$DESKTOP\${APP_NAME}.lnk" "$INSTDIR\${APP_EXE}"
    
    ; 写入注册表
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayName" "${APP_NAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "UninstallString" "$INSTDIR\uninstall.exe"
    
    ; 创建卸载程序
    WriteUninstaller "$INSTDIR\uninstall.exe"
SectionEnd

Section "Uninstall"
    Delete "$INSTDIR\*.*"
    RMDir "$INSTDIR"
    Delete "$SMPROGRAMS\${APP_NAME}\*.*"
    RMDir "$SMPROGRAMS\${APP_NAME}"
    Delete "$DESKTOP\${APP_NAME}.lnk"
    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}"
SectionEnd
```

### 2. macOS 打包

#### 创建 .app 包结构
```bash
# 创建应用包结构
mkdir -p "Teacher Client.app/Contents/MacOS"
mkdir -p "Teacher Client.app/Contents/Resources"

# 复制可执行文件
cp build/bin/teacher-client "Teacher Client.app/Contents/MacOS/"
cp updater "Teacher Client.app/Contents/MacOS/"

# 创建 Info.plist
cat > "Teacher Client.app/Contents/Info.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>teacher-client</string>
    <key>CFBundleIdentifier</key>
    <string>com.cdzyhd.teacher-client</string>
    <key>CFBundleName</key>
    <string>Teacher Client</string>
    <key>CFBundleVersion</key>
    <string>********</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0.4</string>
    <key>LSMinimumSystemVersion</key>
    <string>10.13</string>
</dict>
</plist>
EOF

# 设置可执行权限
chmod +x "Teacher Client.app/Contents/MacOS/teacher-client"
```

#### 创建 DMG 安装包
```bash
#!/bin/bash
# create-dmg.sh

APP_NAME="Teacher Client"
VERSION="1.0.4"
DMG_NAME="teacher-client-${VERSION}.dmg"

# 创建临时目录
mkdir -p dmg-temp
cp -R "${APP_NAME}.app" dmg-temp/

# 创建 DMG
hdiutil create -volname "${APP_NAME}" -srcfolder dmg-temp -ov -format UDZO "${DMG_NAME}"

# 清理临时文件
rm -rf dmg-temp

echo "DMG 创建完成: ${DMG_NAME}"
```

### 3. Linux 打包

#### 创建 DEB 包
```bash
#!/bin/bash
# create-deb.sh

APP_NAME="teacher-client"
VERSION="1.0.4"
ARCH="amd64"

# 创建包结构
mkdir -p "${APP_NAME}_${VERSION}_${ARCH}/DEBIAN"
mkdir -p "${APP_NAME}_${VERSION}_${ARCH}/usr/bin"
mkdir -p "${APP_NAME}_${VERSION}_${ARCH}/usr/share/applications"
mkdir -p "${APP_NAME}_${VERSION}_${ARCH}/usr/share/pixmaps"

# 复制文件
cp build/bin/teacher-client "${APP_NAME}_${VERSION}_${ARCH}/usr/bin/"
cp updater "${APP_NAME}_${VERSION}_${ARCH}/usr/bin/"

# 创建控制文件
cat > "${APP_NAME}_${VERSION}_${ARCH}/DEBIAN/control" << EOF
Package: teacher-client
Version: ${VERSION}
Section: education
Priority: optional
Architecture: ${ARCH}
Maintainer: 成都智云鸿道信息技术有限公司 <<EMAIL>>
Description: 教师端包管理系统
 用于管理和分发实验包的桌面应用程序
EOF

# 创建桌面文件
cat > "${APP_NAME}_${VERSION}_${ARCH}/usr/share/applications/teacher-client.desktop" << EOF
[Desktop Entry]
Name=教师端包管理系统
Comment=实验包管理工具
Exec=/usr/bin/teacher-client
Icon=teacher-client
Terminal=false
Type=Application
Categories=Education;
EOF

# 构建 DEB 包
dpkg-deb --build "${APP_NAME}_${VERSION}_${ARCH}"

echo "DEB 包创建完成: ${APP_NAME}_${VERSION}_${ARCH}.deb"
```

#### 创建 RPM 包
```spec
# teacher-client.spec
Name:           teacher-client
Version:        1.0.4
Release:        1%{?dist}
Summary:        教师端包管理系统

License:        Proprietary
URL:            https://www.cdzyhd.com
Source0:        %{name}-%{version}.tar.gz

BuildRequires:  gcc
Requires:       gtk3

%description
用于管理和分发实验包的桌面应用程序

%prep
%setup -q

%build
# 已预编译

%install
rm -rf $RPM_BUILD_ROOT
mkdir -p $RPM_BUILD_ROOT/usr/bin
mkdir -p $RPM_BUILD_ROOT/usr/share/applications

cp teacher-client $RPM_BUILD_ROOT/usr/bin/
cp updater $RPM_BUILD_ROOT/usr/bin/
cp teacher-client.desktop $RPM_BUILD_ROOT/usr/share/applications/

%files
/usr/bin/teacher-client
/usr/bin/updater
/usr/share/applications/teacher-client.desktop

%changelog
* Wed May 30 2025 Developer <<EMAIL>> - 1.0.4-1
- 初始版本
```

## 自动化部署

### 1. GitHub Actions 配置

```yaml
# .github/workflows/build.yml
name: Build and Release

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    strategy:
      matrix:
        platform: [ubuntu-latest, windows-latest, macos-latest]
    
    runs-on: ${{ matrix.platform }}
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Go
      uses: actions/setup-go@v3
      with:
        go-version: '1.19'
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '16'
    
    - name: Install Wails
      run: go install github.com/wailsapp/wails/v2/cmd/wails@latest
    
    - name: Install dependencies
      run: |
        go mod tidy
        cd frontend && npm ci
    
    - name: Build
      run: wails build -clean
    
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: teacher-client-${{ matrix.platform }}
        path: build/bin/
```

### 2. 发布脚本

```bash
#!/bin/bash
# release.sh

VERSION=$1
if [ -z "$VERSION" ]; then
    echo "用法: $0 <version>"
    echo "示例: $0 1.0.4"
    exit 1
fi

echo "开始发布版本 $VERSION..."

# 构建所有平台
./build-all.sh

# 创建发布目录
RELEASE_DIR="release-$VERSION"
mkdir -p "$RELEASE_DIR"

# 打包各平台版本
cd build/bin

# Windows
zip -r "../../$RELEASE_DIR/teacher-client-windows-$VERSION.zip" teacher-client.exe

# macOS
tar -czf "../../$RELEASE_DIR/teacher-client-macos-$VERSION.tar.gz" teacher-client

# Linux
tar -czf "../../$RELEASE_DIR/teacher-client-linux-$VERSION.tar.gz" teacher-client

cd ../..

echo "发布包已创建在 $RELEASE_DIR 目录"
ls -la "$RELEASE_DIR"
```

## 部署检查清单

### 构建前检查
- [ ] 更新版本号
- [ ] 更新变更日志
- [ ] 运行所有测试
- [ ] 检查依赖版本
- [ ] 验证配置文件

### 构建检查
- [ ] 所有平台构建成功
- [ ] 可执行文件大小合理
- [ ] 包含必要的依赖文件
- [ ] 验证文件签名 (如适用)

### 发布前检查
- [ ] 在目标平台测试运行
- [ ] 验证自动更新功能
- [ ] 检查安装/卸载流程
- [ ] 确认文档更新
- [ ] 准备发布说明

### 发布后检查
- [ ] 监控下载统计
- [ ] 收集用户反馈
- [ ] 监控错误报告
- [ ] 准备热修复 (如需要)
