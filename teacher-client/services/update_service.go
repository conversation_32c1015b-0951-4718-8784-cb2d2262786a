package services

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"teacher-client/models"
	"teacher-client/utils"
	"time"
)

// UpdateInfo 更新信息
type UpdateInfo struct {
	HasUpdate      bool               `json:"hasUpdate"`
	CurrentVersion string             `json:"currentVersion"`
	LatestVersion  string             `json:"latestVersion"`
	DownloadURL    string             `json:"downloadUrl"`
	Changelog      string             `json:"changelog"`
	ReleaseDate    string             `json:"releaseDate"`
	FileSize       int64              `json:"fileSize"`
	CheckSum       string             `json:"checkSum"`
	UpdateData     *CentralUpdateData `json:"updateData,omitempty"` // 完整的更新数据
}

// CentralUpdateResponse 中央端更新响应
type CentralUpdateResponse struct {
	Code string             `json:"code"`
	Msg  string             `json:"msg"`
	Data *CentralUpdateData `json:"data"`
}

// CentralUpdateData 中央端更新数据
type CentralUpdateData struct {
	PackageClientUpdateId string                 `json:"packageClientUpdateId"`
	CreateTime            int64                  `json:"createTime"`
	ExtraInfo             map[string]interface{} `json:"extraInfo"`
	VersionNumber         int64                  `json:"versionNumber"`
	Type                  string                 `json:"type"`
	IsPushed              bool                   `json:"isPushed"`
	UpdateConfig          *UpdateConfig          `json:"updateConfig"`
}

// UpdateConfig 更新配置
type UpdateConfig struct {
	TeacherClientDownloadUrl string `json:"teacherClientDownloadUrl"`
	TeacherClientFileHash    string `json:"teacherClientFileHash"`
	TeacherUpdateDes         string `json:"teacherUpdateDes"`
}

// UpdateService 更新服务
type UpdateService struct {
	logger         *utils.Logger
	currentVersion string
	centralAPIURL  string // 中央端API地址
	isChecking     bool
	lastCheckTime  time.Time
	autoUpdate     bool            // 是否启用自动更新
	ctx            context.Context // Wails上下文，用于发送事件
}

// UpdateServiceInstance 全局更新服务实例
var UpdateServiceInstance *UpdateService

// NewUpdateService 创建更新服务
func NewUpdateService(currentVersion string, logger *utils.Logger) *UpdateService {
	// 从配置中获取中央端API地址
	centralAPIURL := getCentralAPIURL()

	// 检查是否启用自动更新
	autoUpdate := true // 默认启用自动更新
	if autoUpdateStr := os.Getenv("AUTO_UPDATE_ENABLED"); autoUpdateStr != "" {
		autoUpdate = autoUpdateStr == "true"
	}

	service := &UpdateService{
		logger:         logger,
		currentVersion: currentVersion,
		centralAPIURL:  centralAPIURL,
		isChecking:     false,
		autoUpdate:     autoUpdate,
	}

	// 注意：不在这里启动更新检查，而是在app.startup中触发
	// 这样可以确保有Wails上下文来发送事件

	return service
}

// InitUpdateService 初始化更新服务
func InitUpdateService(currentVersion string, logger *utils.Logger) {
	UpdateServiceInstance = NewUpdateService(currentVersion, logger)
}

// SetWailsContext 设置Wails上下文
func SetWailsContext(ctx context.Context) {
	if UpdateServiceInstance != nil {
		UpdateServiceInstance.ctx = ctx
	}
}

// getCentralAPIURL 获取中央端API地址
func getCentralAPIURL() string {
	// 优先从环境变量获取中央服务器URL
	centralURL := os.Getenv("CENTRAL_API_URL")
	if centralURL == "" {
		// 如果环境变量没有，从配置中获取
		if savedURL, err := models.GetConfig("central_url"); err == nil && savedURL != "" {
			centralURL = savedURL
		} else {
			// 最后使用默认地址
			centralURL = "http://localhost:8900"
		}
	}

	return centralURL
}

// CheckForUpdates 检查更新
func (us *UpdateService) CheckForUpdates() (*UpdateInfo, error) {
	if us.isChecking {
		return nil, fmt.Errorf("正在检查更新中，请稍后再试")
	}

	us.isChecking = true
	defer func() {
		us.isChecking = false
		us.lastCheckTime = time.Now()
	}()

	us.logger.Info("UpdateService", "CheckForUpdates", "开始检查程序更新")

	// 检查中央端API地址是否可用
	if us.centralAPIURL == "" {
		return nil, fmt.Errorf("中央端API地址未配置")
	}

	// 将当前版本转换为数字格式
	currentVersionNumber, err := us.parseVersionNumber(us.currentVersion)
	if err != nil {
		us.logger.Error("UpdateService", "CheckForUpdates",
			fmt.Sprintf("解析当前版本号失败: %v", err))
		return nil, fmt.Errorf("解析当前版本号失败: %v", err)
	}

	// 构建请求URL - 对接中央端API，传递type=teacher
	checkURL := fmt.Sprintf("%s/v1/packageSystem/packageClientUpdate/getLatestPushedVersion?currentVersionNumber=%d&type=teacher",
		us.centralAPIURL, currentVersionNumber)

	us.logger.Info("UpdateService", "CheckForUpdates",
		fmt.Sprintf("检查更新URL: %s", checkURL))

	// 发送HTTP请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Get(checkURL)
	if err != nil {
		us.logger.Error("UpdateService", "CheckForUpdates",
			fmt.Sprintf("请求更新检查失败: %v", err))
		return nil, fmt.Errorf("请求更新检查失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		us.logger.Error("UpdateService", "CheckForUpdates",
			fmt.Sprintf("更新检查请求失败，状态码: %d", resp.StatusCode))
		return nil, fmt.Errorf("更新检查请求失败，状态码: %d", resp.StatusCode)
	}

	// 解析中央端响应
	var centralResp CentralUpdateResponse
	if err := json.NewDecoder(resp.Body).Decode(&centralResp); err != nil {
		us.logger.Error("UpdateService", "CheckForUpdates",
			fmt.Sprintf("解析更新信息失败: %v", err))
		return nil, fmt.Errorf("解析更新信息失败: %v", err)
	}

	// 转换为本地UpdateInfo格式
	updateInfo := &UpdateInfo{
		CurrentVersion: us.currentVersion,
	}

	if centralResp.Code == "000000" && centralResp.Data != nil {
		// 发现新版本
		updateInfo.HasUpdate = true
		updateInfo.LatestVersion = fmt.Sprintf("%d", centralResp.Data.VersionNumber)
		updateInfo.UpdateData = centralResp.Data

		// 设置下载信息
		if centralResp.Data.UpdateConfig != nil {
			updateInfo.DownloadURL = centralResp.Data.UpdateConfig.TeacherClientDownloadUrl
			updateInfo.CheckSum = centralResp.Data.UpdateConfig.TeacherClientFileHash
			updateInfo.Changelog = centralResp.Data.UpdateConfig.TeacherUpdateDes
		}

		us.logger.Info("UpdateService", "CheckForUpdates",
			fmt.Sprintf("发现新版本: %s → %s", us.currentVersion, updateInfo.LatestVersion))
	} else {
		// 没有新版本
		updateInfo.HasUpdate = false
		us.logger.Info("UpdateService", "CheckForUpdates", "当前已是最新版本")
	}

	return updateInfo, nil
}

// parseVersionNumber 解析版本号为数字格式
func (us *UpdateService) parseVersionNumber(version string) (int64, error) {
	// 移除可能的前缀和后缀
	version = strings.TrimSpace(version)
	version = strings.TrimPrefix(version, "v")
	version = strings.TrimPrefix(version, "V")

	// 尝试直接转换为数字
	if num, err := strconv.ParseInt(version, 10, 64); err == nil {
		return num, nil
	}

	// 如果包含点号，尝试移除点号后转换
	if strings.Contains(version, ".") {
		version = strings.ReplaceAll(version, ".", "")
		if num, err := strconv.ParseInt(version, 10, 64); err == nil {
			return num, nil
		}
	}

	return 0, fmt.Errorf("无法解析版本号: %s", version)
}

// isWindows 检查是否为Windows系统
func (us *UpdateService) isWindows() bool {
	return runtime.GOOS == "windows"
}

// checkOnStartup 启动时检查更新
func (us *UpdateService) checkOnStartup() {
	// 等待一段时间后再检查，确保应用完全启动
	time.Sleep(5 * time.Second)

	us.logger.Info("UpdateService", "checkOnStartup", "启动时检查更新")

	// 检查更新
	updateInfo, err := us.CheckForUpdates()
	if err != nil {
		us.logger.Error("UpdateService", "checkOnStartup",
			fmt.Sprintf("启动时检查更新失败: %v", err))
		return
	}

	if !updateInfo.HasUpdate {
		us.logger.Info("UpdateService", "checkOnStartup", "当前已是最新版本")
		return
	}

	us.logger.Info("UpdateService", "checkOnStartup",
		fmt.Sprintf("发现新版本: %s → %s", updateInfo.CurrentVersion, updateInfo.LatestVersion))

	// 如果启用了自动更新，则自动下载并更新
	if us.autoUpdate {
		us.logger.Info("UpdateService", "checkOnStartup", "开始自动更新")

		// 下载更新文件
		newExePath, err := us.DownloadUpdateWithHash(updateInfo.DownloadURL, updateInfo.CheckSum, updateInfo.LatestVersion)
		if err != nil {
			us.logger.Error("UpdateService", "checkOnStartup",
				fmt.Sprintf("自动下载更新失败: %v", err))
			return
		}

		us.logger.Info("UpdateService", "checkOnStartup",
			fmt.Sprintf("更新文件下载完成: %s", newExePath))

		// 保存版本信息到数据库
		if err := us.saveVersionInfo(updateInfo.LatestVersion, newExePath, updateInfo.UpdateData); err != nil {
			us.logger.Warn("UpdateService", "checkOnStartup",
				fmt.Sprintf("保存版本信息失败: %v", err))
		}

		// 启动更新
		if err := us.StartUpdate(newExePath); err != nil {
			us.logger.Error("UpdateService", "checkOnStartup",
				fmt.Sprintf("启动更新失败: %v", err))
			return
		}

		us.logger.Info("UpdateService", "checkOnStartup", "自动更新已启动，程序即将重启")
	}
}

// DownloadUpdateWithHash 下载更新文件并验证哈希
func (us *UpdateService) DownloadUpdateWithHash(downloadURL, expectedHash, version string) (string, error) {
	us.logger.Info("UpdateService", "DownloadUpdate",
		fmt.Sprintf("开始下载更新文件: %s", downloadURL))

	// 确保更新目录存在
	updateDir := "./updates"
	if err := utils.EnsureDir(updateDir); err != nil {
		return "", fmt.Errorf("创建更新目录失败: %v", err)
	}

	// 构建保存路径
	var filename string
	if us.isWindows() {
		filename = fmt.Sprintf("teacher-client-v%s.exe", version)
	} else {
		filename = fmt.Sprintf("teacher-client-v%s", version)
	}
	savePath := filepath.Join(updateDir, filename)

	// 教师端直接使用中央端提供的完整下载URL
	fullURL := downloadURL

	// 创建请求
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		return "", err
	}

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Minute} // 30分钟超时
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("下载失败，状态码: %d", resp.StatusCode)
	}

	// 创建临时文件
	tmpFile, err := os.CreateTemp(updateDir, "download-*.tmp")
	if err != nil {
		return "", err
	}
	tmpPath := tmpFile.Name()
	defer func() {
		tmpFile.Close()
		// 如果出错，删除临时文件
		if err != nil {
			os.Remove(tmpPath)
		}
	}()

	// 复制数据到临时文件
	_, err = io.Copy(tmpFile, resp.Body)
	if err != nil {
		return "", err
	}

	// 关闭临时文件
	if err := tmpFile.Close(); err != nil {
		return "", err
	}

	// 验证文件哈希
	if expectedHash != "" {
		us.logger.Info("UpdateService", "DownloadUpdate", "开始验证文件哈希...")
		hash, err := us.calculateFileHash(tmpPath)
		if err != nil {
			us.logger.Error("UpdateService", "DownloadUpdate",
				fmt.Sprintf("计算文件哈希失败: %v", err))
			return "", err
		}

		us.logger.Info("UpdateService", "DownloadUpdate",
			fmt.Sprintf("文件哈希验证: 期望=%s, 实际=%s", expectedHash, hash))
		if hash != expectedHash {
			us.logger.Error("UpdateService", "DownloadUpdate",
				fmt.Sprintf("文件哈希验证失败: 期望=%s, 实际=%s", expectedHash, hash))
			return "", fmt.Errorf("文件哈希验证失败，期望: %s，实际: %s", expectedHash, hash)
		}
		us.logger.Info("UpdateService", "DownloadUpdate", "文件哈希验证成功")
	}

	// 重命名临时文件
	if err := os.Rename(tmpPath, savePath); err != nil {
		return "", err
	}

	// 设置可执行权限（非Windows系统）
	if !us.isWindows() {
		if err := os.Chmod(savePath, 0755); err != nil {
			us.logger.Warn("UpdateService", "DownloadUpdate",
				fmt.Sprintf("设置可执行权限失败: %v", err))
		}
	}

	us.logger.Info("UpdateService", "DownloadUpdate",
		fmt.Sprintf("更新文件下载完成: %s", savePath))

	return savePath, nil
}

// calculateFileHash 计算文件哈希值（使用xxHash算法，与学校端保持一致）
func (us *UpdateService) calculateFileHash(filePath string) (string, error) {
	return utils.CalculateFileHash(filePath)
}

// StartUpdate 启动更新
func (us *UpdateService) StartUpdate(newExePath string) error {
	us.logger.Info("UpdateService", "StartUpdate", "开始启动更新器")

	// 获取当前程序路径
	currentExe, err := os.Executable()
	if err != nil {
		return fmt.Errorf("获取当前程序路径失败: %v", err)
	}

	// 根据操作系统类型确定更新器路径
	var updaterPath string
	if us.isWindows() {
		updaterPath = "./updater.exe"
	} else {
		updaterPath = "./updater"
	}

	if _, err := os.Stat(updaterPath); err != nil {
		return fmt.Errorf("更新器不存在: %s", updaterPath)
	}

	// 启动更新器
	cmd := exec.Command(updaterPath,
		"--type=teacher",
		"--old="+currentExe,
		"--new="+newExePath,
		"--restart")

	us.logger.Info("UpdateService", "StartUpdate",
		fmt.Sprintf("启动更新器: %s", cmd.String()))

	if err := cmd.Start(); err != nil {
		return fmt.Errorf("启动更新器失败: %v", err)
	}

	us.logger.Info("UpdateService", "StartUpdate", "更新器已启动，程序即将退出")

	// 退出当前程序，让更新器接管
	os.Exit(0)
	return nil
}

// saveVersionInfo 保存版本信息到数据库
func (us *UpdateService) saveVersionInfo(version, filePath string, updateData *CentralUpdateData) error {
	// 构建版本信息
	versionInfo := map[string]interface{}{
		"version":                    version,
		"teacher_client_path":        filePath,
		"teacher_client_file_hash":   updateData.UpdateConfig.TeacherClientFileHash,
		"teacher_update_description": updateData.UpdateConfig.TeacherUpdateDes,
		"package_client_update_id":   updateData.PackageClientUpdateId,
		"installed_at":               time.Now(),
		"is_current":                 0, // 新下载的版本暂时不设为当前版本
	}

	// 这里应该调用models包的方法保存到数据库
	// 由于当前models包可能还没有相关方法，先记录日志
	us.logger.Info("UpdateService", "saveVersionInfo",
		fmt.Sprintf("保存版本信息: %v", versionInfo))

	return nil
}

// CheckAndAutoUpdate 检查并自动更新（可供外部调用）
func (us *UpdateService) CheckAndAutoUpdate() error {
	us.logger.Info("UpdateService", "CheckAndAutoUpdate", "开始检查并自动更新")

	// 检查更新
	updateInfo, err := us.CheckForUpdates()
	if err != nil {
		return fmt.Errorf("检查更新失败: %v", err)
	}

	if !updateInfo.HasUpdate {
		us.logger.Info("UpdateService", "CheckAndAutoUpdate", "当前已是最新版本")
		return nil
	}

	us.logger.Info("UpdateService", "CheckAndAutoUpdate",
		fmt.Sprintf("发现新版本: %s → %s", updateInfo.CurrentVersion, updateInfo.LatestVersion))

	// 如果启用了自动更新，则自动下载并更新
	if !us.autoUpdate {
		return fmt.Errorf("自动更新已禁用")
	}

	// 下载更新文件
	newExePath, err := us.DownloadUpdateWithHash(updateInfo.DownloadURL, updateInfo.CheckSum, updateInfo.LatestVersion)
	if err != nil {
		return fmt.Errorf("下载更新失败: %v", err)
	}

	us.logger.Info("UpdateService", "CheckAndAutoUpdate",
		fmt.Sprintf("更新文件下载完成: %s", newExePath))

	// 保存版本信息到数据库
	if err := us.saveVersionInfo(updateInfo.LatestVersion, newExePath, updateInfo.UpdateData); err != nil {
		us.logger.Warn("UpdateService", "CheckAndAutoUpdate",
			fmt.Sprintf("保存版本信息失败: %v", err))
	}

	// 启动更新
	if err := us.StartUpdate(newExePath); err != nil {
		return fmt.Errorf("启动更新失败: %v", err)
	}

	us.logger.Info("UpdateService", "CheckAndAutoUpdate", "自动更新已启动，程序即将重启")
	return nil
}

// GetCurrentVersion 获取当前版本
func (us *UpdateService) GetCurrentVersion() string {
	return us.currentVersion
}

// GetLastCheckTime 获取最后检查时间
func (us *UpdateService) GetLastCheckTime() time.Time {
	return us.lastCheckTime
}

// IsChecking 是否正在检查更新
func (us *UpdateService) IsChecking() bool {
	return us.isChecking
}

// IsAutoUpdateEnabled 是否启用自动更新
func (us *UpdateService) IsAutoUpdateEnabled() bool {
	return us.autoUpdate
}
