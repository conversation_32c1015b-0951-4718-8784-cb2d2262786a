package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"teacher-client/models"
)

// SchoolClient 学校端客户端
type SchoolClient struct {
	BaseURL    string
	Port       string
	Token      string
	HTTPClient *http.Client
}

// APIResponse API响应结构
type APIResponse struct {
	Code    int             `json:"code"`
	Message string          `json:"message"`
	Data    json.RawMessage `json:"data"`
}

// NewSchoolClient 创建学校端客户端
func NewSchoolClient() (*SchoolClient, error) {
	// 从配置中获取服务器URL和端口
	serverURL, err := models.GetServerURL()
	if err != nil {
		return nil, err
	}

	serverPort, err := models.GetServerPort()
	if err != nil {
		return nil, err
	}

	return &SchoolClient{
		BaseURL: serverURL,
		Port:    serverPort,
		Token:   "", // 不需要令牌
		HTTPClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}, nil
}

// SetBaseURL 设置基础URL
func (c *SchoolClient) SetBaseURL(baseURL string) {
	c.BaseURL = baseURL
}

// SetPort 设置端口
func (c *SchoolClient) SetPort(port string) {
	c.Port = port
}

// SetToken 设置令牌
func (c *SchoolClient) SetToken(token string) {
	c.Token = token
}

// GetFullURL 获取完整URL
func (c *SchoolClient) GetFullURL(path string) string {
	return fmt.Sprintf("http://%s:%s%s", c.BaseURL, c.Port, path)
}

// Get 发送GET请求
func (c *SchoolClient) Get(path string) (*APIResponse, error) {
	fullURL := c.GetFullURL(path)

	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		return nil, err
	}

	// 添加认证头
	if c.Token != "" {
		req.Header.Add("Authorization", "Bearer "+c.Token)
	}

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, err
	}

	return &apiResp, nil
}

// Post 发送POST请求
func (c *SchoolClient) Post(path string, data interface{}) (*APIResponse, error) {
	fullURL := c.GetFullURL(path)

	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", fullURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}

	// 添加认证头
	if c.Token != "" {
		req.Header.Add("Authorization", "Bearer "+c.Token)
	}

	req.Header.Add("Content-Type", "application/json")

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, err
	}

	return &apiResp, nil
}

// CheckServerStatus 检查服务器状态
func (c *SchoolClient) CheckServerStatus() (map[string]interface{}, error) {
	resp, err := c.Get("/api/student/system/status")
	if err != nil {
		return nil, err
	}

	if resp.Code != 200 {
		return nil, fmt.Errorf("服务器返回错误: %s", resp.Message)
	}

	var data map[string]interface{}
	if err := json.Unmarshal(resp.Data, &data); err != nil {
		return nil, err
	}

	return data, nil
}

// GetSchoolInfo 获取学校信息
func (c *SchoolClient) GetSchoolInfo() (string, string, error) {
	resp, err := c.Get("/api/student/system/status")
	if err != nil {
		return "", "", err
	}

	if resp.Code != 200 {
		return "", "", fmt.Errorf("获取学校信息失败: %s", resp.Message)
	}

	var statusResp struct {
		Status     string `json:"status"`
		SchoolID   string `json:"schoolId"`
		SchoolName string `json:"schoolName"`
		Version    string `json:"version"`
	}

	if err := json.Unmarshal(resp.Data, &statusResp); err != nil {
		return "", "", err
	}

	// 保存学校名称
	if err := models.SetSchoolName(statusResp.SchoolName); err != nil {
		return "", "", err
	}

	// 保存学校ID
	if err := models.SetSchoolID(statusResp.SchoolID); err != nil {
		return "", "", err
	}

	return statusResp.SchoolID, statusResp.SchoolName, nil
}

// GetPackages 获取包列表
func (c *SchoolClient) GetPackages() ([]models.Package, error) {
	resp, err := c.Get("/api/student/packages")
	if err != nil {
		return nil, err
	}

	if resp.Code != 200 {
		return nil, fmt.Errorf("获取包列表失败: %s", resp.Message)
	}

	var packages []models.Package
	if err := json.Unmarshal(resp.Data, &packages); err != nil {
		return nil, err
	}

	// 获取学校ID
	schoolID, _, err := c.GetSchoolInfo()
	if err != nil {
		return nil, fmt.Errorf("获取学校信息失败: %v", err)
	}

	// 处理每个包的信息
	for i := range packages {
		// 设置学校ID（如果为空）
		if packages[i].SchoolID == "" {
			packages[i].SchoolID = schoolID
		}

	}

	return packages, nil
}

// GetPackage 获取包详情
func (c *SchoolClient) GetPackage(id int64) (*models.Package, error) {
	resp, err := c.Get(fmt.Sprintf("/api/student/packages/%d", id))
	if err != nil {
		return nil, err
	}

	if resp.Code != 200 {
		return nil, fmt.Errorf("获取包详情失败: %s", resp.Message)
	}

	var pkg models.Package
	if err := json.Unmarshal(resp.Data, &pkg); err != nil {
		return nil, err
	}

	// 获取学校ID
	schoolID, _, err := c.GetSchoolInfo()
	if err != nil {
		return nil, fmt.Errorf("获取学校信息失败: %v", err)
	}

	// 设置学校ID（如果为空）
	if pkg.SchoolID == "" {
		pkg.SchoolID = schoolID
	}

	// 修复downloadUrl重复问题
	downloadUrl := pkg.DownloadURL
	if strings.Contains(downloadUrl, "/packages/"+schoolID+"/packages/"+schoolID+"/") {
		// 修复重复的路径
		downloadUrl = strings.Replace(downloadUrl, "/packages/"+schoolID+"/packages/"+schoolID+"/", "/packages/"+schoolID+"/", 1)
		pkg.DownloadURL = downloadUrl
	}

	return &pkg, nil
}

// DownloadPackage 下载包文件
func (c *SchoolClient) DownloadPackage(downloadURL, savePath string) error {
	// 构建完整URL
	fullURL, err := url.JoinPath(c.GetFullURL(""), downloadURL)
	if err != nil {
		return err
	}

	// 创建请求
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		return err
	}

	// 添加认证头
	if c.Token != "" {
		req.Header.Add("Authorization", "Bearer "+c.Token)
	}

	// 发送请求
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载失败，状态码: %d", resp.StatusCode)
	}

	// 创建文件
	file, err := os.CreateTemp("./downloads", "package-*.tmp")
	if err != nil {
		return err
	}
	defer file.Close()

	// 写入文件
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return err
	}

	// 重命名文件
	if err := os.Rename(file.Name(), savePath); err != nil {
		return err
	}

	return nil
}
