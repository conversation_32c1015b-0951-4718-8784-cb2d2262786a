package services

import (
	"fmt"
	"io"
	"net/http"
	"time"

	"teacher-client/models"
	"teacher-client/utils"
)

// AuthService 认证服务
type AuthService struct {
	centralURL string
	session    *models.SimpleSession
}

// NewAuthService 创建认证服务
func NewAuthService(centralURL string) *AuthService {
	authService := &AuthService{
		centralURL: centralURL,
		session:    models.NewSession(),
	}

	// 尝试从数据库恢复会话
	authService.restoreSessionFromDB()

	return authService
}

// LoginRequest 登录请求
type LoginRequest struct {
	SchoolName string `json:"schoolName"`
	Username   string `json:"username"`
	Password   string `json:"password"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Token   string `json:"token"`
	Data    struct {
		SchoolID   string `json:"schoolId"`
		SchoolName string `json:"schoolName"`
		UserID     string `json:"userId"`
		Username   string `json:"username"`
		UserType   string `json:"userType"`
	} `json:"data"`
}

// Login 教师登录
func (as *AuthService) Login(schoolName, username, password string) (*LoginResponse, error) {
	// 使用学校端相同的中央端认证逻辑
	token, schoolID, realSchoolName, _, err := models.AuthenticateWithCentral(username, password, schoolName)
	if err != nil {
		return &LoginResponse{
			Success: false,
			Message: err.Error(),
		}, err
	}

	// 使用真实的学校名称，如果为空则使用输入的学校名称
	if realSchoolName == "" {
		realSchoolName = schoolName
	}

	// 保存认证信息到会话
	as.session.SetToken(token)
	as.session.SetSchoolID(schoolID)
	as.session.SetSchoolName(realSchoolName)
	as.session.SetUserID("") // 教师端暂时不需要用户ID
	as.session.SetUsername(username)

	// 持久化保存会话到数据库
	as.saveSessionToDB()

	utils.LogPrintf("教师端登录成功: %s@%s (SchoolID: %s)", username, realSchoolName, schoolID)

	// 构造返回响应
	loginResp := &LoginResponse{
		Success: true,
		Message: "登录成功",
		Token:   token,
		Data: struct {
			SchoolID   string `json:"schoolId"`
			SchoolName string `json:"schoolName"`
			UserID     string `json:"userId"`
			Username   string `json:"username"`
			UserType   string `json:"userType"`
		}{
			SchoolID:   schoolID,
			SchoolName: realSchoolName,
			UserID:     "",
			Username:   username,
			UserType:   "teacher",
		},
	}

	return loginResp, nil
}

// Logout 退出登录
func (as *AuthService) Logout() error {
	// 清除数据库中的会话
	as.clearSessionFromDB()

	// 清除内存中的会话
	as.session.Clear()
	utils.LogPrintf("教师端退出登录")
	return nil
}

// HandleAuthExpired 处理授权过期
func (as *AuthService) HandleAuthExpired() error {
	utils.LogPrintf("检测到授权过期，清除本地授权信息")

	// 清除数据库中的会话
	as.clearSessionFromDB()

	// 清除内存中的会话
	as.session.Clear()

	return nil
}

// IsLoggedIn 检查是否已登录
func (as *AuthService) IsLoggedIn() bool {
	return as.session.IsValid()
}

// GetToken 获取认证token
func (as *AuthService) GetToken() string {
	return as.session.GetToken()
}

// GetSchoolID 获取学校ID
func (as *AuthService) GetSchoolID() string {
	return as.session.GetSchoolID()
}

// GetSchoolName 获取学校名称
func (as *AuthService) GetSchoolName() string {
	return as.session.GetSchoolName()
}

// GetSchoolInfo 获取学校信息
func (as *AuthService) GetSchoolInfo() (schoolID, schoolName string) {
	return as.session.GetSchoolID(), as.session.GetSchoolName()
}

// GetUserInfo 获取用户信息
func (as *AuthService) GetUserInfo() (userID, username string) {
	return as.session.GetUserID(), as.session.GetUsername()
}

// RefreshToken 刷新token（如果需要的话）
func (as *AuthService) RefreshToken() error {
	// 这里可以实现token刷新逻辑
	// 目前先简单返回nil
	return nil
}

// MakeAuthenticatedRequest 发送带认证的请求
func (as *AuthService) MakeAuthenticatedRequest(method, url string, body io.Reader) (*http.Response, error) {
	if !as.IsLoggedIn() {
		return nil, fmt.Errorf("未登录，无法发送认证请求")
	}

	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, err
	}

	// 添加认证头
	req.Header.Set("Authorization", "Bearer "+as.GetToken())
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}

	// 检查HTTP状态码，特别是401授权过期
	if resp.StatusCode == 401 {
		resp.Body.Close() // 关闭响应体
		// 处理授权过期
		as.HandleAuthExpired()
		return nil, &models.AuthExpiredError{Message: "授权已过期，请重新登录"}
	}

	return resp, nil
}

// ValidateToken 验证token是否有效
func (as *AuthService) ValidateToken() error {
	if !as.IsLoggedIn() {
		return fmt.Errorf("未登录")
	}

	// 使用学校端相同的token验证逻辑
	token := as.session.GetToken()
	isValid, err := models.ValidateCentralToken(token)
	if err != nil {
		as.session.Clear() // 清除无效的会话
		return fmt.Errorf("验证token失败: %v", err)
	}

	if !isValid {
		as.session.Clear() // 清除无效的会话
		return fmt.Errorf("token无效")
	}

	return nil
}

// GetCentralURL 获取中央端URL
func (as *AuthService) GetCentralURL() string {
	return as.centralURL
}

// restoreSessionFromDB 从数据库恢复会话
func (as *AuthService) restoreSessionFromDB() {
	// 尝试获取最新的会话记录
	sessions, err := models.GetAllSessions()
	if err != nil || len(sessions) == 0 {
		utils.LogPrintf("没有找到已保存的会话")
		return
	}

	// 获取最新的会话（按更新时间排序）
	var latestSession *models.Session
	for i := range sessions {
		if latestSession == nil || sessions[i].UpdatedAt.After(latestSession.UpdatedAt) {
			latestSession = &sessions[i]
		}
	}

	// 检查会话是否过期
	if latestSession.TokenExpiresAt.Before(time.Now()) {
		utils.LogPrintf("已保存的会话已过期")
		// 删除过期的会话
		models.DeleteSession(latestSession.ID)
		return
	}

	// 验证中央端token是否仍然有效
	isValid, err := models.ValidateCentralToken(latestSession.CentralToken)
	if err != nil || !isValid {
		utils.LogPrintf("已保存的中央端token无效")
		// 删除无效的会话
		models.DeleteSession(latestSession.ID)
		return
	}

	// 恢复会话到内存
	as.session.SetToken(latestSession.CentralToken)
	as.session.SetSchoolID(latestSession.SchoolID)
	as.session.SetSchoolName(latestSession.SchoolName)
	as.session.SetUserID("")
	as.session.SetUsername(latestSession.Username)

	utils.LogPrintf("成功恢复会话: %s@%s", latestSession.Username, latestSession.SchoolName)
}

// saveSessionToDB 保存会话到数据库
func (as *AuthService) saveSessionToDB() {
	if !as.session.IsValid() {
		return
	}

	// 检查是否已存在相同用户名的会话
	existingSession, err := models.GetSessionByUsername(as.session.GetUsername())
	if err == nil && existingSession != nil {
		// 更新现有会话
		existingSession.SchoolID = as.session.GetSchoolID()
		existingSession.SchoolName = as.session.GetSchoolName()
		existingSession.CentralToken = as.session.GetToken()
		existingSession.TokenExpiresAt = time.Now().AddDate(10, 0, 0) // 10年有效期
		existingSession.LastLogin = time.Now()

		if err := models.UpdateSession(existingSession); err != nil {
			utils.LogPrintf("更新会话失败: %v", err)
		} else {
			utils.LogPrintf("会话已更新到数据库")
		}
	} else {
		// 创建新会话
		newSession := &models.Session{
			Username:       as.session.GetUsername(),
			SchoolID:       as.session.GetSchoolID(),
			SchoolName:     as.session.GetSchoolName(),
			CentralToken:   as.session.GetToken(),
			LocalToken:     "",                           // 教师端不需要本地token
			TokenExpiresAt: time.Now().AddDate(10, 0, 0), // 10年有效期
			LastLogin:      time.Now(),
		}

		if err := models.CreateSession(newSession); err != nil {
			utils.LogPrintf("创建会话失败: %v", err)
		} else {
			utils.LogPrintf("会话已保存到数据库")
		}
	}
}

// clearSessionFromDB 清除数据库中的会话
func (as *AuthService) clearSessionFromDB() {
	if as.session.GetUsername() == "" {
		return
	}

	// 删除用户名对应的会话
	session, err := models.GetSessionByUsername(as.session.GetUsername())
	if err == nil && session != nil {
		if err := models.DeleteSession(session.ID); err != nil {
			utils.LogPrintf("删除会话失败: %v", err)
		} else {
			utils.LogPrintf("会话已从数据库删除")
		}
	}
}
