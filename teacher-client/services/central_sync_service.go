package services

import (
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"teacher-client/models"
	"teacher-client/utils"
)

// CentralSyncService 中央端同步服务
type CentralSyncService struct {
	authService *AuthService
	centralURL  string
}

// NewCentralSyncService 创建中央端同步服务
func NewCentralSyncService(authService *AuthService, centralURL string) *CentralSyncService {
	return &CentralSyncService{
		authService: authService,
		centralURL:  centralURL,
	}
}

// PackageListResponse 包列表响应
type PackageListResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    []struct {
		PackageVersionID      string `json:"packageVersionId"`
		PackagePushID         string `json:"packagePushId"`
		ExperimentID          string `json:"experimentId"`
		ExperimentName        string `json:"experimentName"`
		ExperimentVersionID   string `json:"experimentVersionId"`
		ExperimentVersionName string `json:"experimentVersionName"`
		Version               string `json:"version"`
		VersionName           string `json:"versionName"`
		VersionDesc           string `json:"versionDesc"`
		FileHash              string `json:"fileHash"`
		FileSize              int64  `json:"fileSize"`
		DownloadURL           string `json:"downloadUrl"`
		SchoolID              string `json:"schoolId"`
	} `json:"data"`
}

// GetPackages 从中央端获取包列表
func (css *CentralSyncService) GetPackages() ([]*models.Package, error) {
	if !css.authService.IsLoggedIn() {
		return nil, fmt.Errorf("未登录，无法获取包列表")
	}

	// 使用学校端相同的逻辑获取推送列表
	token := css.authService.GetToken()
	schoolID := css.authService.GetSchoolID()

	// 获取推送列表
	pushes, err := models.GetPendingPushes(token, schoolID)
	if err != nil {
		// 检查是否是授权过期错误
		if authErr, ok := err.(*models.AuthExpiredError); ok {
			// 处理授权过期
			css.authService.HandleAuthExpired()
			return nil, fmt.Errorf("授权已过期: %v", authErr)
		}
		return nil, fmt.Errorf("获取推送列表失败: %v", err)
	}

	// 将推送转换为包列表
	var packages []*models.Package
	for _, push := range pushes {
		if len(push.PackageVersion) == 0 {
			continue
		}

		pkgVersion := push.PackageVersion[0]

		// 获取实验信息
		expName := "未知实验"
		expVersionName := "未知版本"
		if len(push.ExperimentVersion) > 0 {
			expName = push.ExperimentVersion[0].ExperimentName
			expVersionName = push.ExperimentVersion[0].Name
		}

		// 创建包对象
		pkg := &models.Package{
			PackageVersionID:      push.PackageVersionID,
			ExperimentID:          pkgVersion.ExperimentID,
			ExperimentName:        expName,
			ExperimentVersionID:   pkgVersion.ExperimentVersionID,
			ExperimentVersionName: expVersionName,
			Version:               pkgVersion.Version,
			VersionName:           pkgVersion.VersionName,
			VersionDesc:           pkgVersion.VersionDesc,
			FileHash:              pkgVersion.FileHash,
			FileSize:              pkgVersion.FileInfo.Size,
			DownloadURL:           pkgVersion.DownloadURL,
			SyncStatus:            models.SyncStatusNone,
			SchoolID:              schoolID,
			CreatedAt:             time.Now(),
			UpdatedAt:             time.Now(),
		}

		packages = append(packages, pkg)
	}

	utils.LogPrintf("从中央端获取到 %d 个包", len(packages))
	return packages, nil
}

// DownloadPackage 从中央端下载包文件
func (css *CentralSyncService) DownloadPackage(pkg *models.Package, progressCallback func(downloaded, total int64)) error {
	if !css.authService.IsLoggedIn() {
		return fmt.Errorf("未登录，无法下载包")
	}

	// 构建下载URL
	downloadURL := pkg.DownloadURL
	if !strings.HasPrefix(downloadURL, "http") {
		// 如果是相对路径，则拼接中央端URL
		downloadURL = fmt.Sprintf("%s%s", strings.TrimRight(css.centralURL, "/"), downloadURL)
	}

	// 发送下载请求
	resp, err := css.authService.MakeAuthenticatedRequest("GET", downloadURL, nil)
	if err != nil {
		return fmt.Errorf("下载包请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载包失败，状态码: %d", resp.StatusCode)
	}

	// 创建下载目录
	downloadDir := utils.GetDownloadDir()
	if err := utils.EnsureDir(downloadDir); err != nil {
		return fmt.Errorf("创建下载目录失败: %v", err)
	}

	// 生成文件名
	fileName := fmt.Sprintf("%s_%s.zip", pkg.ExperimentName, pkg.PackageVersionID)
	filePath := fmt.Sprintf("%s/%s", downloadDir, fileName)

	// 创建文件
	file, err := utils.CreateFile(filePath)
	if err != nil {
		return fmt.Errorf("创建下载文件失败: %v", err)
	}
	defer file.Close()

	// 获取文件大小
	contentLength := resp.ContentLength
	if contentLength <= 0 {
		contentLength = pkg.FileSize
	}

	// 下载文件
	var downloaded int64
	buffer := make([]byte, 32*1024) // 32KB buffer

	for {
		n, err := resp.Body.Read(buffer)
		if n > 0 {
			if _, writeErr := file.Write(buffer[:n]); writeErr != nil {
				return fmt.Errorf("写入文件失败: %v", writeErr)
			}
			downloaded += int64(n)

			// 调用进度回调
			if progressCallback != nil {
				progressCallback(downloaded, contentLength)
			}
		}

		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("读取下载数据失败: %v", err)
		}
	}

	// 验证文件哈希
	if pkg.FileHash != "" {
		fileHash, err := utils.CalculateFileHash(filePath)
		if err != nil {
			return fmt.Errorf("计算文件哈希失败: %v", err)
		}

		if fileHash != pkg.FileHash {
			// 删除损坏的文件
			utils.RemoveFile(filePath)
			return fmt.Errorf("文件哈希验证失败，期望: %s，实际: %s", pkg.FileHash, fileHash)
		}
	}

	utils.LogPrintf("包下载完成: %s, 文件路径: %s", pkg.ExperimentName, filePath)
	return nil
}

// CheckServerStatus 检查中央端服务器状态
func (css *CentralSyncService) CheckServerStatus() (map[string]interface{}, error) {
	// 简单的连接测试，尝试获取推送列表来验证连接
	if !css.authService.IsLoggedIn() {
		return map[string]interface{}{
			"status":  "offline",
			"message": "未登录",
		}, nil
	}

	// 尝试获取推送列表来测试连接
	token := css.authService.GetToken()
	schoolID := css.authService.GetSchoolID()

	_, err := models.GetPendingPushes(token, schoolID)
	if err != nil {
		// 检查是否是授权过期错误
		if authErr, ok := err.(*models.AuthExpiredError); ok {
			// 处理授权过期
			css.authService.HandleAuthExpired()
			return map[string]interface{}{
				"status":  "auth_expired",
				"message": fmt.Sprintf("授权已过期: %v", authErr),
			}, nil
		}
		return map[string]interface{}{
			"status":  "offline",
			"message": fmt.Sprintf("连接失败: %v", err),
		}, nil
	}

	return map[string]interface{}{
		"status":  "online",
		"message": "连接正常",
	}, nil
}
