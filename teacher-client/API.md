# 教师端 API 接口文档

## 概述

教师端直接与中央服务器通信，使用 RESTful API 进行数据交换。所有接口都使用 JSON 格式进行数据传输。

## 基础信息

- **基础URL**: `https://api.cdzyhd.com/system/erp` (生产环境)
- **基础URL**: `http://localhost:8900` (开发环境)
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`

## 认证机制

### 1. 登录认证

#### 接口地址
```
POST /v1/packageSystem/packageSystemSchool/login
```

#### 请求参数
```json
{
    "username": "admin",
    "password": "password123",
    "schoolName": "测试学校"
}
```

#### 响应格式
```json
{
    "code": "000000",
    "msg": "登录成功",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refreshToken": "refresh_token_string",
        "expiresIn": 3600,
        "userInfo": {
            "id": "123456",
            "username": "admin",
            "schoolName": "测试学校",
            "schoolId": "school_001"
        }
    }
}
```

#### 错误响应
```json
{
    "code": "000001",
    "msg": "用户名或密码错误",
    "data": null
}
```

### 2. Token 使用

所有需要认证的接口都需要在请求头中携带 token：

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 实验包管理接口

### 1. 获取推送列表

#### 接口地址
```
POST /v1/packageSystem/packagePush/oneSchoolPushList
```

#### 请求头
```http
Authorization: Bearer <token>
Content-Type: application/json
```

#### 请求参数
```json
{}
```

#### 响应格式
```json
{
    "code": "000000",
    "msg": "获取成功",
    "data": [
        {
            "packagePushId": "push_001",
            "experimentId": "exp_001",
            "experimentName": "数据结构实验",
            "experimentVersionId": "version_001",
            "experimentVersionName": "v1.0.0",
            "packageVersionId": "pkg_001",
            "packageVersionName": "v1.0.0",
            "packageSize": 1048576,
            "packageHash": "abc123def456",
            "downloadUrl": "http://package-system-file.cdzyhd.com/packages/exp_001.zip",
            "isPushed": true,
            "pushTime": 1640995200000,
            "description": "实验包描述信息"
        }
    ]
}
```

### 2. 下载实验包

实验包下载使用推送列表中提供的 `downloadUrl` 直接下载，无需额外接口。

#### 下载流程
1. 从推送列表获取 `downloadUrl`
2. 使用 HTTP GET 请求下载文件
3. 验证文件哈希值 (`packageHash`)
4. 解压到指定目录

#### 示例代码
```go
// 下载文件
resp, err := http.Get(downloadUrl)
if err != nil {
    return err
}
defer resp.Body.Close()

// 保存到本地
file, err := os.Create(localPath)
if err != nil {
    return err
}
defer file.Close()

_, err = io.Copy(file, resp.Body)
return err
```

## 自动更新接口

### 1. 检查更新

#### 接口地址
```
GET /v1/packageSystem/packageClientUpdate/getLatestPushedVersion?currentVersionNumber={version}&type=teacher
```

#### 请求参数
- `currentVersionNumber`: 当前版本号 (数字格式，如: 25053004)
- `type`: 客户端类型，固定值 `teacher`

#### 响应格式 (有新版本)
```json
{
    "code": "000000",
    "msg": "找到新版本",
    "data": {
        "packageClientUpdateId": "595028652979130368",
        "createTime": 1748617885968,
        "extraInfo": {},
        "versionNumber": 25053004,
        "type": "teacher",
        "isPushed": true,
        "updateConfig": {
            "teacherClientDownloadUrl": "http://package-system-file.cdzyhd.com/teacher-client/teacher-app/2025-05-30/teacher-client.exe",
            "teacherClientFileHash": "1329c45561cf2270",
            "teacherUpdateDes": "修复已知问题，优化用户体验"
        }
    }
}
```

#### 响应格式 (无新版本)
```json
{
    "code": "000001",
    "msg": "当前已是最新版本",
    "data": null
}
```

### 2. 更新流程

1. **检查更新**: 调用检查更新接口
2. **下载文件**: 使用 `teacherClientDownloadUrl` 下载新版本
3. **验证哈希**: 使用 `teacherClientFileHash` 验证文件完整性
4. **启动更新器**: 调用更新器程序完成替换
5. **重启应用**: 更新器完成后重启应用

## 错误码说明

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 000000 | 成功 | 正常处理 |
| 000001 | 业务失败 | 根据具体消息处理 |
| 401001 | 未授权 | 重新登录 |
| 401002 | Token 过期 | 刷新 Token 或重新登录 |
| 403001 | 权限不足 | 联系管理员 |
| 404001 | 资源不存在 | 检查请求参数 |
| 500001 | 服务器内部错误 | 稍后重试 |

## 请求示例

### 使用 curl 测试

#### 登录
```bash
curl -X POST "http://localhost:8900/v1/packageSystem/packageSystemSchool/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "password123",
    "schoolName": "测试学校"
  }'
```

#### 获取推送列表
```bash
curl -X POST "http://localhost:8900/v1/packageSystem/packagePush/oneSchoolPushList" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{}'
```

#### 检查更新
```bash
curl -X GET "http://localhost:8900/v1/packageSystem/packageClientUpdate/getLatestPushedVersion?currentVersionNumber=25053001&type=teacher"
```

### 使用 Go 代码

#### 登录示例
```go
type LoginRequest struct {
    Username   string `json:"username"`
    Password   string `json:"password"`
    SchoolName string `json:"schoolName"`
}

func login(apiURL, username, password, schoolName string) (*LoginResponse, error) {
    loginReq := LoginRequest{
        Username:   username,
        Password:   password,
        SchoolName: schoolName,
    }
    
    reqBody, err := json.Marshal(loginReq)
    if err != nil {
        return nil, err
    }
    
    resp, err := http.Post(
        apiURL+"/v1/packageSystem/packageSystemSchool/login",
        "application/json",
        bytes.NewBuffer(reqBody),
    )
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    var loginResp LoginResponse
    err = json.NewDecoder(resp.Body).Decode(&loginResp)
    return &loginResp, err
}
```

#### 带认证的请求示例
```go
func makeAuthenticatedRequest(apiURL, endpoint, token string) (*http.Response, error) {
    req, err := http.NewRequest("POST", apiURL+endpoint, strings.NewReader("{}"))
    if err != nil {
        return nil, err
    }
    
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Authorization", "Bearer "+token)
    
    client := &http.Client{Timeout: 30 * time.Second}
    return client.Do(req)
}
```

## 数据模型

### 用户信息
```go
type UserInfo struct {
    ID         string `json:"id"`
    Username   string `json:"username"`
    SchoolName string `json:"schoolName"`
    SchoolID   string `json:"schoolId"`
}
```

### 实验包信息
```go
type PackageInfo struct {
    PackagePushID          string `json:"packagePushId"`
    ExperimentID           string `json:"experimentId"`
    ExperimentName         string `json:"experimentName"`
    ExperimentVersionID    string `json:"experimentVersionId"`
    ExperimentVersionName  string `json:"experimentVersionName"`
    PackageVersionID       string `json:"packageVersionId"`
    PackageVersionName     string `json:"packageVersionName"`
    PackageSize           int64  `json:"packageSize"`
    PackageHash           string `json:"packageHash"`
    DownloadURL           string `json:"downloadUrl"`
    IsPushed              bool   `json:"isPushed"`
    PushTime              int64  `json:"pushTime"`
    Description           string `json:"description"`
}
```

### 更新信息
```go
type UpdateInfo struct {
    PackageClientUpdateID string                 `json:"packageClientUpdateId"`
    CreateTime           int64                  `json:"createTime"`
    ExtraInfo            map[string]interface{} `json:"extraInfo"`
    VersionNumber        int64                  `json:"versionNumber"`
    Type                 string                 `json:"type"`
    IsPushed             bool                   `json:"isPushed"`
    UpdateConfig         *UpdateConfig          `json:"updateConfig"`
}

type UpdateConfig struct {
    TeacherClientDownloadUrl string `json:"teacherClientDownloadUrl"`
    TeacherClientFileHash    string `json:"teacherClientFileHash"`
    TeacherUpdateDes         string `json:"teacherUpdateDes"`
}
```

## 最佳实践

### 1. 错误处理
```go
func handleAPIResponse(resp *http.Response) error {
    if resp.StatusCode != http.StatusOK {
        return fmt.Errorf("API请求失败，状态码: %d", resp.StatusCode)
    }
    
    var apiResp APIResponse
    if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
        return fmt.Errorf("解析响应失败: %w", err)
    }
    
    if apiResp.Code != "000000" {
        return fmt.Errorf("业务错误: %s", apiResp.Msg)
    }
    
    return nil
}
```

### 2. 超时控制
```go
client := &http.Client{
    Timeout: 30 * time.Second,
}
```

### 3. 重试机制
```go
func retryRequest(fn func() error, maxRetries int) error {
    var err error
    for i := 0; i < maxRetries; i++ {
        if err = fn(); err == nil {
            return nil
        }
        
        if i < maxRetries-1 {
            time.Sleep(time.Duration(i+1) * time.Second)
        }
    }
    return err
}
```

### 4. 日志记录
```go
func logAPICall(method, url string, statusCode int, duration time.Duration) {
    logger.Info("API", "Request", 
        fmt.Sprintf("%s %s - %d (%v)", method, url, statusCode, duration))
}
```
