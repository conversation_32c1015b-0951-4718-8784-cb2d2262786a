{"name": "教师端包管理系统", "outputfilename": "teacher-client", "frontend:install": "npm install", "frontend:build": "npm run build", "frontend:dev:watcher": "npm run dev", "frontend:dev:serverUrl": "auto", "author": {"name": "Package System Team", "email": "<EMAIL>"}, "info": {"companyName": "School Package System", "productName": "教师端包管理系统", "productVersion": "1.0.0", "copyright": "© 2025 School Package System", "comments": "学校端包管理系统的配套教师客户端"}, "bindings": {"ts": {"structs": {"models.Package": {"name": "PackageInfo"}}}}}