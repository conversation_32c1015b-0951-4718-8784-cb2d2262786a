2025/05/30 21:52:57 logger.go:109: 日志系统初始化成功
2025/05/30 21:52:57 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 21:52:57 db.go:47: Database initialized successfully
2025/05/30 21:52:57 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25052901, 程序版本: 25053002
2025/05/30 21:52:57 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 开始线性迁移: 25052901 → 25053002
2025/05/30 21:52:57 logger.go:127: [INFO] [StudentClient] [VersionManager] [backupDatabase] 数据库备份完成: backup/student-client_20250530_215257.db
2025/05/30 21:52:57 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 需要执行 1 个迁移
2025/05/30 21:52:57 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 执行迁移 1/1: 25053002
2025/05/30 21:52:57 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 开始执行迁移: 25053002 - 升级到25053002版本
2025/05/30 21:52:57 logger.go:127: [INFO] [StudentClient] [Migration] [25053002] 开始执行版本升级
2025/05/30 21:52:57 logger.go:127: [INFO] [StudentClient] [Migration] [25053002] 版本升级完成
2025/05/30 21:52:57 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 迁移完成: 25053002
2025/05/30 21:52:57 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 线性迁移完成: 25052901 → 25053002
2025/05/30 21:52:57 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 21:53:07 logger.go:109: 日志系统初始化成功
2025/05/30 21:53:07 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 21:53:07 db.go:47: Database initialized successfully
2025/05/30 21:53:07 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 21:53:07 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 21:53:07 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 21:53:11 logger.go:109: 日志系统初始化成功
2025/05/30 21:53:11 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 21:53:11 db.go:47: Database initialized successfully
2025/05/30 21:53:11 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 21:53:11 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 21:53:11 logger.go:109: 日志系统初始化成功
2025/05/30 21:53:11 app.go:54: 应用启动
2025/05/30 21:53:11 db.go:47: Database initialized successfully
2025/05/30 21:53:21 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 21:53:21 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 21:53:21 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://127.0.0.1:18080/api/student/update/check
2025/05/30 21:53:21 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 学校端返回：当前已是最新版本
2025/05/30 21:53:21 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 当前已是最新版本: 25053002
2025/05/30 21:55:42 app.go:73: 应用关闭
2025/05/30 21:55:42 logger.go:117: 关闭日志系统
2025/05/30 21:59:27 logger.go:109: 日志系统初始化成功
2025/05/30 21:59:27 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 21:59:27 db.go:47: Database initialized successfully
2025/05/30 21:59:27 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 21:59:27 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 21:59:27 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 21:59:34 logger.go:109: 日志系统初始化成功
2025/05/30 21:59:34 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 21:59:34 db.go:47: Database initialized successfully
2025/05/30 21:59:34 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 21:59:34 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 21:59:34 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 21:59:37 logger.go:109: 日志系统初始化成功
2025/05/30 21:59:37 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 21:59:37 db.go:47: Database initialized successfully
2025/05/30 21:59:37 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 21:59:37 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 21:59:37 logger.go:109: 日志系统初始化成功
2025/05/30 21:59:37 app.go:54: 应用启动
2025/05/30 21:59:37 db.go:47: Database initialized successfully
2025/05/30 21:59:47 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 21:59:47 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 21:59:47 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://127.0.0.1:18080/api/student/update/check
2025/05/30 21:59:47 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 学校端返回：当前已是最新版本
2025/05/30 21:59:47 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 当前已是最新版本: 25053002
2025/05/30 22:01:40 logger.go:109: 日志系统初始化成功
2025/05/30 22:01:40 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:01:40 db.go:47: Database initialized successfully
2025/05/30 22:01:40 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:01:40 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:01:40 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:01:43 logger.go:109: 日志系统初始化成功
2025/05/30 22:01:43 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:01:43 db.go:47: Database initialized successfully
2025/05/30 22:01:43 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:01:43 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:01:43 logger.go:109: 日志系统初始化成功
2025/05/30 22:01:43 app.go:54: 应用启动
2025/05/30 22:01:43 db.go:47: Database initialized successfully
2025/05/30 22:01:53 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 22:01:53 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 22:01:53 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://127.0.0.1:18080/api/student/update/check
2025/05/30 22:01:53 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 学校端返回：当前已是最新版本
2025/05/30 22:01:53 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 当前已是最新版本: 25053002
2025/05/30 22:02:01 logger.go:109: 日志系统初始化成功
2025/05/30 22:02:01 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:02:01 db.go:47: Database initialized successfully
2025/05/30 22:02:01 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:02:01 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:02:01 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:02:04 logger.go:109: 日志系统初始化成功
2025/05/30 22:02:04 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:02:04 db.go:47: Database initialized successfully
2025/05/30 22:02:04 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:02:04 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:02:04 logger.go:109: 日志系统初始化成功
2025/05/30 22:02:04 app.go:54: 应用启动
2025/05/30 22:02:04 db.go:47: Database initialized successfully
2025/05/30 22:02:14 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 22:02:14 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 22:02:14 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://127.0.0.1:18080/api/student/update/check
2025/05/30 22:02:14 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 学校端返回：当前已是最新版本
2025/05/30 22:02:14 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 当前已是最新版本: 25053002
2025/05/30 22:02:52 app.go:73: 应用关闭
2025/05/30 22:02:52 logger.go:117: 关闭日志系统
2025/05/30 22:03:20 logger.go:109: 日志系统初始化成功
2025/05/30 22:03:20 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:03:20 db.go:47: Database initialized successfully
2025/05/30 22:03:20 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:03:20 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:03:20 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:03:27 logger.go:109: 日志系统初始化成功
2025/05/30 22:03:27 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:03:27 db.go:47: Database initialized successfully
2025/05/30 22:03:27 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:03:27 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:03:27 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:03:29 logger.go:109: 日志系统初始化成功
2025/05/30 22:03:29 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:03:29 db.go:47: Database initialized successfully
2025/05/30 22:03:29 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:03:29 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:03:30 logger.go:109: 日志系统初始化成功
2025/05/30 22:03:30 app.go:54: 应用启动
2025/05/30 22:03:30 db.go:47: Database initialized successfully
2025/05/30 22:03:39 logger.go:127: 教师端登录成功: 1@黑龙江农业职业技术学院 (SchoolID: 588328868260089856)
2025/05/30 22:03:39 logger.go:142: 获取包列表
2025/05/30 22:03:39 logger.go:142: 本地没有包，尝试从中央端同步包列表
2025/05/30 22:03:40 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:03:40 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:03:40 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 22:03:40 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 22:03:40 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://127.0.0.1:18080/api/student/update/check
2025/05/30 22:03:40 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 学校端返回：当前已是最新版本
2025/05/30 22:03:40 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 当前已是最新版本: 25053002
2025/05/30 22:03:40 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:03:40 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:03:40 logger.go:127: 从中央端获取到 9 个包
2025/05/30 22:03:40 logger.go:142: 正在获取本地包列表...
2025/05/30 22:03:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:03:40 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:03:40 logger.go:127: 从中央端获取到 9 个包
2025/05/30 22:03:40 logger.go:142: 正在获取本地包列表...
2025/05/30 22:03:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:03:40 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:03:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:03:40 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:03:40 logger.go:127: 创建包信息失败: UNIQUE constraint failed: packages.package_version_id
2025/05/30 22:03:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:03:40 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:03:40 logger.go:127: 创建包信息失败: UNIQUE constraint failed: packages.package_version_id
2025/05/30 22:03:40 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:03:40 logger.go:127: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/30 22:03:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:03:40 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:03:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:03:40 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:03:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:03:40 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:03:40 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:03:40 logger.go:127: 发现新实验版本: 黄继光安卓端 (589448104990740480)
2025/05/30 22:03:40 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:03:40 logger.go:127: 发现新实验版本: 甲午海战虚拟仿真体验教学软件 (589448093531901952)
2025/05/30 22:03:40 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:03:40 logger.go:127: 发现新实验版本: 航母编队虚拟仿真体验教学软件 (589448093070528512)
2025/05/30 22:03:40 logger.go:127: 从中央端同步包列表完成，共同步 9 个包
2025/05/30 22:03:40 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:03:40 logger.go:127: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/30 22:03:40 logger.go:127: 创建包信息失败: UNIQUE constraint failed: packages.package_version_id
2025/05/30 22:03:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:03:40 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:03:40 logger.go:127: 创建包信息失败: UNIQUE constraint failed: packages.package_version_id
2025/05/30 22:03:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:03:40 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:03:40 logger.go:127: 创建包信息失败: UNIQUE constraint failed: packages.package_version_id
2025/05/30 22:03:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:03:40 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:03:40 logger.go:127: 创建包信息失败: UNIQUE constraint failed: packages.package_version_id
2025/05/30 22:03:40 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:03:40 logger.go:127: 发现新实验版本: 黄继光安卓端 (589448104990740480)
2025/05/30 22:03:40 logger.go:127: 创建包信息失败: UNIQUE constraint failed: packages.package_version_id
2025/05/30 22:03:40 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:03:40 logger.go:127: 发现新实验版本: 甲午海战虚拟仿真体验教学软件 (589448093531901952)
2025/05/30 22:03:40 logger.go:127: 创建包信息失败: UNIQUE constraint failed: packages.package_version_id
2025/05/30 22:03:40 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:03:40 logger.go:127: 发现新实验版本: 航母编队虚拟仿真体验教学软件 (589448093070528512)
2025/05/30 22:03:40 logger.go:127: 创建包信息失败: UNIQUE constraint failed: packages.package_version_id
2025/05/30 22:03:40 logger.go:127: 从中央端同步包列表完成，共同步 9 个包
2025/05/30 22:03:40 logger.go:127: 返回包列表，共 5 个包
2025/05/30 22:04:40 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:04:40 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:04:40 logger.go:127: 从中央端获取到 9 个包
2025/05/30 22:04:40 logger.go:142: 正在获取本地包列表...
2025/05/30 22:04:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:04:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:04:40 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:04:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:04:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:04:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:04:40 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:04:40 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:04:40 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:04:40 logger.go:127: 从中央端同步包列表完成，共同步 9 个包
2025/05/30 22:05:35 logger.go:142: 获取包列表
2025/05/30 22:05:35 logger.go:127: 返回包列表，共 5 个包
2025/05/30 22:05:40 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:05:40 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:05:41 logger.go:127: 从中央端获取到 9 个包
2025/05/30 22:05:41 logger.go:142: 正在获取本地包列表...
2025/05/30 22:05:41 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:05:41 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:05:41 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:05:41 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:05:41 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:05:41 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:05:41 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:05:41 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:05:41 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:05:41 logger.go:127: 从中央端同步包列表完成，共同步 9 个包
2025/05/30 22:06:40 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:06:40 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:06:40 logger.go:127: 从中央端获取到 9 个包
2025/05/30 22:06:40 logger.go:142: 正在获取本地包列表...
2025/05/30 22:06:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:06:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:06:40 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:06:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:06:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:06:40 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:06:40 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:06:40 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:06:40 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:06:40 logger.go:127: 从中央端同步包列表完成，共同步 9 个包
2025/05/30 22:07:14 app.go:73: 应用关闭
2025/05/30 22:07:14 logger.go:117: 关闭日志系统
2025/05/30 22:09:57 logger.go:109: 日志系统初始化成功
2025/05/30 22:09:57 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:09:57 db.go:47: Database initialized successfully
2025/05/30 22:09:57 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25052901, 程序版本: 25053002
2025/05/30 22:09:57 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 开始线性迁移: 25052901 → 25053002
2025/05/30 22:09:57 logger.go:127: [WARN] [StudentClient] [VersionManager] [Migration] 创建数据库备份失败: 复制数据库文件失败: open data/student-client.db: no such file or directory
2025/05/30 22:09:57 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 需要执行 1 个迁移
2025/05/30 22:09:57 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 执行迁移 1/1: 25053002
2025/05/30 22:09:57 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 开始执行迁移: 25053002 - 升级到25053002版本
2025/05/30 22:09:57 logger.go:127: [INFO] [StudentClient] [Migration] [25053002] 开始执行版本升级
2025/05/30 22:09:57 logger.go:127: [INFO] [StudentClient] [Migration] [25053002] 版本升级完成
2025/05/30 22:09:57 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 迁移完成: 25053002
2025/05/30 22:09:57 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 线性迁移完成: 25052901 → 25053002
2025/05/30 22:09:57 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:10:04 logger.go:109: 日志系统初始化成功
2025/05/30 22:10:04 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:10:04 db.go:47: Database initialized successfully
2025/05/30 22:10:04 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:10:04 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:10:04 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:10:07 logger.go:109: 日志系统初始化成功
2025/05/30 22:10:07 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:10:07 db.go:47: Database initialized successfully
2025/05/30 22:10:07 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:10:07 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:10:07 logger.go:109: 日志系统初始化成功
2025/05/30 22:10:07 app.go:54: 应用启动
2025/05/30 22:10:07 db.go:47: Database initialized successfully
2025/05/30 22:10:17 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 22:10:17 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 22:10:17 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://127.0.0.1:18080/api/student/update/check
2025/05/30 22:10:17 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 学校端返回：当前已是最新版本
2025/05/30 22:10:17 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 当前已是最新版本: 25053002
2025/05/30 22:10:41 logger.go:127: 教师端登录成功: 1@黑龙江农业职业技术学院 (SchoolID: 588328868260089856)
2025/05/30 22:10:41 logger.go:142: 获取包列表
2025/05/30 22:10:41 logger.go:142: 本地没有包，尝试从中央端同步包列表
2025/05/30 22:10:42 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:10:42 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:10:42 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:10:42 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:10:42 logger.go:127: 从中央端获取到 9 个包
2025/05/30 22:10:42 logger.go:142: 正在获取本地包列表...
2025/05/30 22:10:42 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:10:42 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:10:42 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:10:42 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:10:42 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:10:42 logger.go:127: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/30 22:10:42 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:10:42 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:10:42 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:10:42 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:10:42 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:10:42 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:10:42 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:10:42 logger.go:127: 发现新实验版本: 黄继光安卓端 (589448104990740480)
2025/05/30 22:10:42 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:10:42 logger.go:127: 发现新实验版本: 甲午海战虚拟仿真体验教学软件 (589448093531901952)
2025/05/30 22:10:42 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:10:42 logger.go:127: 发现新实验版本: 航母编队虚拟仿真体验教学软件 (589448093070528512)
2025/05/30 22:10:42 logger.go:127: 从中央端同步包列表完成，共同步 9 个包
2025/05/30 22:10:42 logger.go:127: 返回包列表，共 5 个包
2025/05/30 22:10:42 logger.go:127: 从中央端获取到 9 个包
2025/05/30 22:10:42 logger.go:142: 正在获取本地包列表...
2025/05/30 22:10:42 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:10:42 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:10:42 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:10:42 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:10:42 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:10:42 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:10:42 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:10:42 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:10:42 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:10:42 logger.go:127: 从中央端同步包列表完成，共同步 9 个包
2025/05/30 22:11:42 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:11:42 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:11:43 logger.go:127: 从中央端获取到 9 个包
2025/05/30 22:11:43 logger.go:142: 正在获取本地包列表...
2025/05/30 22:11:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:11:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:11:43 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:11:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:11:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:11:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:11:43 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:11:43 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:11:43 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:11:43 logger.go:127: 从中央端同步包列表完成，共同步 9 个包
2025/05/30 22:12:42 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:12:42 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:12:43 logger.go:127: 从中央端获取到 9 个包
2025/05/30 22:12:43 logger.go:142: 正在获取本地包列表...
2025/05/30 22:12:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:12:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:12:43 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:12:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:12:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:12:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:12:43 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:12:43 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:12:43 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:12:43 logger.go:127: 从中央端同步包列表完成，共同步 9 个包
2025/05/30 22:13:42 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:13:42 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:13:43 logger.go:127: 从中央端获取到 9 个包
2025/05/30 22:13:43 logger.go:142: 正在获取本地包列表...
2025/05/30 22:13:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:13:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:13:43 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:13:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:13:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:13:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:13:43 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:13:43 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:13:43 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:13:43 logger.go:127: 从中央端同步包列表完成，共同步 9 个包
2025/05/30 22:14:42 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:14:42 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:14:42 logger.go:127: 从中央端获取到 9 个包
2025/05/30 22:14:42 logger.go:142: 正在获取本地包列表...
2025/05/30 22:14:42 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:14:42 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:14:42 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:14:42 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:14:42 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:14:42 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:14:42 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:14:42 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:14:42 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:14:42 logger.go:127: 从中央端同步包列表完成，共同步 9 个包
2025/05/30 22:15:42 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:15:42 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:15:43 logger.go:127: 从中央端获取到 9 个包
2025/05/30 22:15:43 logger.go:142: 正在获取本地包列表...
2025/05/30 22:15:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:15:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:15:43 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:15:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:15:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:15:43 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:15:43 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:15:43 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:15:43 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:15:43 logger.go:127: 从中央端同步包列表完成，共同步 9 个包
2025/05/30 22:15:49 logger.go:109: 日志系统初始化成功
2025/05/30 22:15:49 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:15:49 db.go:47: Database initialized successfully
2025/05/30 22:15:49 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:15:49 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:15:49 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:15:50 app.go:73: 应用关闭
2025/05/30 22:15:50 logger.go:117: 关闭日志系统
2025/05/30 22:23:12 logger.go:109: 日志系统初始化成功
2025/05/30 22:23:12 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:23:12 db.go:47: Database initialized successfully
2025/05/30 22:23:12 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:23:12 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:23:12 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:24:18 logger.go:109: 日志系统初始化成功
2025/05/30 22:24:18 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:24:18 db.go:47: Database initialized successfully
2025/05/30 22:24:18 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25052901, 程序版本: 25053002
2025/05/30 22:24:18 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 开始线性迁移: 25052901 → 25053002
2025/05/30 22:24:18 logger.go:127: [WARN] [StudentClient] [VersionManager] [Migration] 创建数据库备份失败: 复制数据库文件失败: open data/student-client.db: no such file or directory
2025/05/30 22:24:18 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 需要执行 1 个迁移
2025/05/30 22:24:18 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 执行迁移 1/1: 25053002
2025/05/30 22:24:18 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 开始执行迁移: 25053002 - 升级到25053002版本
2025/05/30 22:24:18 logger.go:127: [INFO] [StudentClient] [Migration] [25053002] 开始执行版本升级
2025/05/30 22:24:18 logger.go:127: [INFO] [StudentClient] [Migration] [25053002] 版本升级完成
2025/05/30 22:24:18 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 迁移完成: 25053002
2025/05/30 22:24:18 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 线性迁移完成: 25052901 → 25053002
2025/05/30 22:24:18 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:24:24 logger.go:109: 日志系统初始化成功
2025/05/30 22:24:24 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:24:24 db.go:47: Database initialized successfully
2025/05/30 22:24:24 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:24:24 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:24:24 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:24:27 logger.go:109: 日志系统初始化成功
2025/05/30 22:24:27 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:24:27 db.go:47: Database initialized successfully
2025/05/30 22:24:27 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:24:27 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:24:27 logger.go:109: 日志系统初始化成功
2025/05/30 22:24:27 app.go:54: 应用启动
2025/05/30 22:24:27 db.go:47: Database initialized successfully
2025/05/30 22:24:37 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 22:24:37 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 22:24:37 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://127.0.0.1:18080/api/student/update/check
2025/05/30 22:24:37 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 学校端返回：当前已是最新版本
2025/05/30 22:24:37 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 当前已是最新版本: 25053002
2025/05/30 22:25:14 logger.go:109: 日志系统初始化成功
2025/05/30 22:25:14 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:25:14 db.go:47: Database initialized successfully
2025/05/30 22:25:14 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:25:14 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:25:14 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:25:20 logger.go:109: 日志系统初始化成功
2025/05/30 22:25:20 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:25:20 db.go:47: Database initialized successfully
2025/05/30 22:25:20 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:25:20 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:25:20 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:25:23 logger.go:109: 日志系统初始化成功
2025/05/30 22:25:23 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:25:23 db.go:47: Database initialized successfully
2025/05/30 22:25:23 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:25:23 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:25:23 logger.go:109: 日志系统初始化成功
2025/05/30 22:25:23 app.go:54: 应用启动
2025/05/30 22:25:23 db.go:47: Database initialized successfully
2025/05/30 22:25:33 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 22:25:33 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 22:25:33 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://127.0.0.1:18080/api/student/update/check
2025/05/30 22:25:33 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 学校端返回：当前已是最新版本
2025/05/30 22:25:33 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 当前已是最新版本: 25053002
2025/05/30 22:30:52 logger.go:109: 日志系统初始化成功
2025/05/30 22:30:52 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:30:52 db.go:47: Database initialized successfully
2025/05/30 22:30:52 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:30:52 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:30:52 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:31:18 logger.go:109: 日志系统初始化成功
2025/05/30 22:31:18 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:31:18 db.go:47: Database initialized successfully
2025/05/30 22:31:18 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:31:18 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:31:18 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:31:26 logger.go:109: 日志系统初始化成功
2025/05/30 22:31:26 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:31:26 db.go:47: Database initialized successfully
2025/05/30 22:31:26 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:31:26 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:31:26 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:31:28 logger.go:109: 日志系统初始化成功
2025/05/30 22:31:28 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:31:28 db.go:47: Database initialized successfully
2025/05/30 22:31:28 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:31:28 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:31:28 logger.go:109: 日志系统初始化成功
2025/05/30 22:31:28 app.go:63: 应用启动
2025/05/30 22:31:28 db.go:47: Database initialized successfully
2025/05/30 22:31:38 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 22:31:38 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 22:31:38 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://127.0.0.1:18080/api/student/update/check
2025/05/30 22:31:38 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 学校端返回：当前已是最新版本
2025/05/30 22:31:38 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 当前已是最新版本: 25053002
2025/05/30 22:31:45 logger.go:127: 教师端登录成功: 1@黑龙江农业职业技术学院 (SchoolID: 588328868260089856)
2025/05/30 22:31:45 logger.go:142: 获取包列表
2025/05/30 22:31:45 logger.go:142: 本地没有包，尝试从中央端同步包列表
2025/05/30 22:31:45 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:31:45 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:31:45 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:31:45 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:31:46 logger.go:127: 从中央端获取到 9 个包
2025/05/30 22:31:46 logger.go:142: 正在获取本地包列表...
2025/05/30 22:31:46 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:31:46 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:31:46 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:31:46 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:31:46 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:31:46 logger.go:127: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/30 22:31:46 logger.go:127: 从中央端获取到 9 个包
2025/05/30 22:31:46 logger.go:142: 正在获取本地包列表...
2025/05/30 22:31:46 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:31:46 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:31:46 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:31:46 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:31:46 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:31:46 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:31:46 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:31:46 logger.go:127: 发现新实验版本: 黄继光安卓端 (589448104990740480)
2025/05/30 22:31:46 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:31:46 logger.go:127: 发现新实验版本: 甲午海战虚拟仿真体验教学软件 (589448093531901952)
2025/05/30 22:31:46 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:31:46 logger.go:127: 发现新实验版本: 航母编队虚拟仿真体验教学软件 (589448093070528512)
2025/05/30 22:31:46 logger.go:127: 从中央端同步包列表完成，共同步 9 个包
2025/05/30 22:31:46 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:31:46 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:31:46 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:31:46 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:31:46 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:31:46 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:31:46 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:31:46 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:31:46 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:31:46 logger.go:127: 从中央端同步包列表完成，共同步 9 个包
2025/05/30 22:31:46 logger.go:127: 返回包列表，共 5 个包
2025/05/30 22:31:59 logger.go:142: 获取包列表
2025/05/30 22:31:59 logger.go:127: 返回包列表，共 5 个包
2025/05/30 22:32:10 logger.go:142: 开始验证文件哈希...
2025/05/30 22:32:10 logger.go:127: 文件哈希验证: 期望=c5d4d2155909bcf8, 实际=c5d4d2155909bcf8
2025/05/30 22:32:10 logger.go:142: 文件哈希验证成功
2025/05/30 22:32:10 logger.go:127: 教师端下载完成: 包版本ID=593427432036372480
2025/05/30 22:32:10 logger.go:142: 成功发送最终进度到通道
2025/05/30 22:32:10 logger.go:142: 调用进度处理函数发送最终进度
2025/05/30 22:32:10 logger.go:127: 开始解压包 ID: 3, 解压ID: extract-3-1748615530548945000
2025/05/30 22:32:10 logger.go:127: 创建解压目录: 学校ID=588328868260089856, 实验ID=592348408270946304, 实验版本ID=592362717994553344
2025/05/30 22:32:10 logger.go:127: 解压目录创建成功: extracted/588328868260089856/592348408270946304/592362717994553344
2025/05/30 22:32:10 logger.go:127: 开始解压，共 2 个文件
2025/05/30 22:32:10 logger.go:127: 检测到GBK编码的文件名: Ѫս�潭.exe
2025/05/30 22:32:10 logger.go:127: 解码后的文件名: 血战湘江.exe
2025/05/30 22:32:10 logger.go:127: 正在处理第 1 个文件: 血战湘江.exe
2025/05/30 22:32:10 logger.go:127: 解压进度: 0.00% (0/2)
2025/05/30 22:32:10 logger.go:127: 成功发送进度到通道
2025/05/30 22:32:10 logger.go:127: 调用进度处理函数
2025/05/30 22:32:10 logger.go:127: 发送解压进度: 0.00% (0/2)
2025/05/30 22:32:10 logger.go:127: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344
2025/05/30 22:32:10 logger.go:127: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/血战湘江.exe
2025/05/30 22:32:10 logger.go:127: 打开zip中的文件: 血战湘江.exe
2025/05/30 22:32:10 logger.go:127: 复制文件内容: 血战湘江.exe
2025/05/30 22:32:10 logger.go:127: 复制了 650752 字节
2025/05/30 22:32:10 logger.go:127: 检测到GBK编码的文件名: UnityPlayer.dll
2025/05/30 22:32:10 logger.go:127: 解码后的文件名: UnityPlayer.dll
2025/05/30 22:32:10 logger.go:127: 正在处理第 2 个文件: UnityPlayer.dll
2025/05/30 22:32:10 logger.go:127: 解压进度: 50.00% (1/2)
2025/05/30 22:32:10 logger.go:127: 成功发送进度到通道
2025/05/30 22:32:10 logger.go:127: 调用进度处理函数
2025/05/30 22:32:10 logger.go:127: 发送解压进度: 50.00% (1/2)
2025/05/30 22:32:10 logger.go:127: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344
2025/05/30 22:32:10 logger.go:127: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/UnityPlayer.dll
2025/05/30 22:32:10 logger.go:127: 打开zip中的文件: UnityPlayer.dll
2025/05/30 22:32:10 logger.go:127: 复制文件内容: UnityPlayer.dll
2025/05/30 22:32:10 logger.go:142: 再次调用进度处理函数发送最终进度
2025/05/30 22:32:10 logger.go:127: 复制了 24294856 字节
2025/05/30 22:32:10 logger.go:142: 开始查找可执行文件...
2025/05/30 22:32:10 logger.go:127: 开始在目录 extracted/588328868260089856/592348408270946304/592362717994553344 中查找可执行文件
2025/05/30 22:32:10 logger.go:127: 找到可执行文件: 血战湘江.exe (层级: 0, 大小: 650752 字节)
2025/05/30 22:32:10 logger.go:127: 找到 1 个可执行文件
2025/05/30 22:32:10 logger.go:127: 检查可执行文件: 血战湘江.exe, 层级: 0, 大小: 650752 字节
2025/05/30 22:32:10 logger.go:127: 选择更浅层级的文件: 血战湘江.exe (层级: 0)
2025/05/30 22:32:10 logger.go:127: 最终选择的可执行文件: 血战湘江.exe (层级: 0, 大小: 650752 字节)
2025/05/30 22:32:10 logger.go:127: 找到可执行文件: extracted/588328868260089856/592348408270946304/592362717994553344/血战湘江.exe
2025/05/30 22:32:10 logger.go:142: 解压完成，发送最终进度
2025/05/30 22:32:10 logger.go:142: 成功发送最终进度到通道
2025/05/30 22:32:10 logger.go:142: 调用进度处理函数发送最终进度
2025/05/30 22:32:10 logger.go:127: 发送解压进度: 100.00% (2/2)
2025/05/30 22:32:10 logger.go:142: 再次调用进度处理函数发送最终进度
2025/05/30 22:32:10 logger.go:127: 发送解压进度: 100.00% (2/2)
2025/05/30 22:32:11 logger.go:127: 解压成功完成
2025/05/30 22:32:11 logger.go:142: 获取包列表
2025/05/30 22:32:11 logger.go:127: 返回包列表，共 5 个包
2025/05/30 22:32:12 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:32:12 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:32:12 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:32:12 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:32:12 logger.go:127: 从中央端获取到 9 个包
2025/05/30 22:32:12 logger.go:142: 正在获取本地包列表...
2025/05/30 22:32:12 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:32:12 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:32:12 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:32:12 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:32:12 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:32:12 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:32:12 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:32:12 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:32:12 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:32:12 logger.go:127: 从中央端同步包列表完成，共同步 9 个包
2025/05/30 22:32:12 logger.go:127: 从中央端获取到 9 个包
2025/05/30 22:32:12 logger.go:142: 正在获取本地包列表...
2025/05/30 22:32:12 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:32:12 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:32:12 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:32:12 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:32:12 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:32:12 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:32:12 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:32:12 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:32:12 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:32:12 logger.go:127: 从中央端同步包列表完成，共同步 9 个包
2025/05/30 22:32:14 logger.go:127: 开始运行包: ID=3, 名称=冠军精神虚拟仿真馆, 版本=1
2025/05/30 22:32:14 logger.go:127: 检查可执行文件: extracted/588328868260089856/592348408270946304/592362717994553344/血战湘江.exe
2025/05/30 22:32:14 logger.go:127: 可执行文件存在: extracted/588328868260089856/592348408270946304/592362717994553344/血战湘江.exe, 大小: 650752 字节, 修改时间: 2025-05-30 22:32:10
2025/05/30 22:32:14 logger.go:127: 执行文件绝对路径: /Users/<USER>/code/go/packageSystem_school/teacher-client/extracted/588328868260089856/592348408270946304/592362717994553344/血战湘江.exe
2025/05/30 22:32:14 logger.go:127: 工作目录: extracted/588328868260089856/592348408270946304/592362717994553344
2025/05/30 22:32:14 logger.go:127: 使用macOS方式启动程序
2025/05/30 22:32:14 logger.go:127: 检测到Windows可执行文件(.exe)，尝试使用Wine运行
2025/05/30 22:32:14 logger.go:127: 在macOS上无法直接运行Windows可执行文件(.exe)，且未安装Wine
2025/05/30 22:32:52 logger.go:142: 获取包列表
2025/05/30 22:32:52 logger.go:127: 返回包列表，共 5 个包
2025/05/30 22:33:12 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:33:12 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:33:12 logger.go:127: 从中央端获取到 9 个包
2025/05/30 22:33:12 logger.go:142: 正在获取本地包列表...
2025/05/30 22:33:12 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:33:12 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:33:12 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:33:12 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:33:12 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:33:12 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:33:12 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:33:12 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:33:12 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:33:12 logger.go:127: 从中央端同步包列表完成，共同步 9 个包
2025/05/30 22:49:58 logger.go:109: 日志系统初始化成功
2025/05/30 22:49:58 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:49:58 db.go:47: Database initialized successfully
2025/05/30 22:49:58 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:49:58 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:49:58 logger.go:127: 没有找到已保存的会话
2025/05/30 22:49:58 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:51:36 logger.go:109: 日志系统初始化成功
2025/05/30 22:51:36 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:51:36 db.go:47: Database initialized successfully
2025/05/30 22:51:36 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25052901, 程序版本: 25053002
2025/05/30 22:51:36 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 开始线性迁移: 25052901 → 25053002
2025/05/30 22:51:36 logger.go:127: [WARN] [StudentClient] [VersionManager] [Migration] 创建数据库备份失败: 复制数据库文件失败: open data/student-client.db: no such file or directory
2025/05/30 22:51:36 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 需要执行 1 个迁移
2025/05/30 22:51:36 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 执行迁移 1/1: 25053002
2025/05/30 22:51:36 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 开始执行迁移: 25053002 - 升级到25053002版本
2025/05/30 22:51:36 logger.go:127: [INFO] [StudentClient] [Migration] [25053002] 开始执行版本升级
2025/05/30 22:51:36 logger.go:127: [INFO] [StudentClient] [Migration] [25053002] 版本升级完成
2025/05/30 22:51:36 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 迁移完成: 25053002
2025/05/30 22:51:36 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 线性迁移完成: 25052901 → 25053002
2025/05/30 22:51:36 logger.go:127: 没有找到已保存的会话
2025/05/30 22:51:36 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:51:43 logger.go:109: 日志系统初始化成功
2025/05/30 22:51:43 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:51:43 db.go:47: Database initialized successfully
2025/05/30 22:51:43 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:51:43 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:51:43 logger.go:127: 没有找到已保存的会话
2025/05/30 22:51:43 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:51:46 logger.go:109: 日志系统初始化成功
2025/05/30 22:51:46 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:51:46 db.go:47: Database initialized successfully
2025/05/30 22:51:46 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:51:46 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:51:46 logger.go:127: 没有找到已保存的会话
2025/05/30 22:51:46 logger.go:109: 日志系统初始化成功
2025/05/30 22:51:46 app.go:63: 应用启动
2025/05/30 22:51:46 db.go:47: Database initialized successfully
2025/05/30 22:51:56 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 22:51:56 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 22:51:56 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://127.0.0.1:18080/api/student/update/check
2025/05/30 22:51:56 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 学校端返回：当前已是最新版本
2025/05/30 22:51:56 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 当前已是最新版本: 25053002
2025/05/30 22:52:00 logger.go:127: 创建会话失败: no such table: sessions
2025/05/30 22:52:00 logger.go:127: 教师端登录成功: 1@黑龙江农业职业技术学院 (SchoolID: 588328868260089856)
2025/05/30 22:52:00 logger.go:142: 获取包列表
2025/05/30 22:52:00 logger.go:142: 本地没有包，尝试从中央端同步包列表
2025/05/30 22:52:01 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:52:01 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:52:01 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:52:01 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:52:01 logger.go:127: 从中央端获取到 10 个包
2025/05/30 22:52:01 logger.go:142: 正在获取本地包列表...
2025/05/30 22:52:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:52:01 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:52:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:52:01 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:52:01 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:52:01 logger.go:127: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/30 22:52:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:52:01 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:52:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:52:01 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:52:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:52:01 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:52:01 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:52:01 logger.go:127: 发现新实验版本: 黄继光安卓端 (589448104990740480)
2025/05/30 22:52:01 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:52:01 logger.go:127: 发现新实验版本: 甲午海战虚拟仿真体验教学软件 (589448093531901952)
2025/05/30 22:52:01 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:52:01 logger.go:127: 发现新实验版本: 航母编队虚拟仿真体验教学软件 (589448093070528512)
2025/05/30 22:52:01 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 22:52:01 logger.go:127: 发现新实验版本: 黄继光安卓端 (589448104990740480)
2025/05/30 22:52:01 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 22:52:01 logger.go:127: 从中央端获取到 10 个包
2025/05/30 22:52:01 logger.go:142: 正在获取本地包列表...
2025/05/30 22:52:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:52:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:52:01 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:52:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:52:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:52:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:52:01 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:52:01 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:52:01 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:52:01 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 22:52:01 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 22:52:01 logger.go:127: 返回包列表，共 5 个包
2025/05/30 22:52:16 logger.go:127: 没有找到已保存的会话
2025/05/30 22:52:23 logger.go:127: 没有找到已保存的会话
2025/05/30 22:52:24 logger.go:127: 没有找到已保存的会话
2025/05/30 22:54:05 logger.go:109: 日志系统初始化成功
2025/05/30 22:54:05 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:54:05 db.go:47: Database initialized successfully
2025/05/30 22:54:05 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:54:05 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:54:05 logger.go:127: 没有找到已保存的会话
2025/05/30 22:54:05 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:54:07 logger.go:109: 日志系统初始化成功
2025/05/30 22:54:07 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:54:07 db.go:47: Database initialized successfully
2025/05/30 22:54:07 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:54:07 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:54:07 logger.go:127: 没有找到已保存的会话
2025/05/30 22:54:07 logger.go:109: 日志系统初始化成功
2025/05/30 22:54:07 app.go:63: 应用启动
2025/05/30 22:54:07 db.go:47: Database initialized successfully
2025/05/30 22:54:17 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 22:54:17 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 22:54:17 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://127.0.0.1:18080/api/student/update/check
2025/05/30 22:54:17 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 学校端返回：当前已是最新版本
2025/05/30 22:54:17 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 当前已是最新版本: 25053002
2025/05/30 22:54:27 logger.go:109: 日志系统初始化成功
2025/05/30 22:54:27 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:54:27 db.go:47: Database initialized successfully
2025/05/30 22:54:27 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:54:27 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:54:27 logger.go:127: 没有找到已保存的会话
2025/05/30 22:54:27 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:54:29 logger.go:109: 日志系统初始化成功
2025/05/30 22:54:29 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:54:29 db.go:47: Database initialized successfully
2025/05/30 22:54:29 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:54:29 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:54:29 logger.go:127: 没有找到已保存的会话
2025/05/30 22:54:29 logger.go:109: 日志系统初始化成功
2025/05/30 22:54:29 app.go:63: 应用启动
2025/05/30 22:54:29 db.go:47: Database initialized successfully
2025/05/30 22:54:39 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 22:54:39 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 22:54:39 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://127.0.0.1:18080/api/student/update/check
2025/05/30 22:54:39 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 学校端返回：当前已是最新版本
2025/05/30 22:54:39 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 当前已是最新版本: 25053002
2025/05/30 22:55:50 app.go:82: 应用关闭
2025/05/30 22:55:50 logger.go:117: 关闭日志系统
2025/05/30 22:55:57 logger.go:109: 日志系统初始化成功
2025/05/30 22:55:57 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:55:57 db.go:47: Database initialized successfully
2025/05/30 22:55:57 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:55:57 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:55:57 logger.go:127: 没有找到已保存的会话
2025/05/30 22:55:57 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:56:59 logger.go:109: 日志系统初始化成功
2025/05/30 22:56:59 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:56:59 db.go:47: Database initialized successfully
2025/05/30 22:56:59 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25052901, 程序版本: 25053002
2025/05/30 22:56:59 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 开始线性迁移: 25052901 → 25053002
2025/05/30 22:56:59 logger.go:127: [WARN] [StudentClient] [VersionManager] [Migration] 创建数据库备份失败: 复制数据库文件失败: open data/student-client.db: no such file or directory
2025/05/30 22:56:59 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 需要执行 1 个迁移
2025/05/30 22:56:59 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 执行迁移 1/1: 25053002
2025/05/30 22:56:59 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 开始执行迁移: 25053002 - 升级到25053002版本
2025/05/30 22:56:59 logger.go:127: [INFO] [StudentClient] [Migration] [25053002] 开始执行版本升级
2025/05/30 22:56:59 logger.go:127: [INFO] [StudentClient] [Migration] [25053002] 版本升级完成
2025/05/30 22:56:59 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 迁移完成: 25053002
2025/05/30 22:56:59 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 线性迁移完成: 25052901 → 25053002
2025/05/30 22:56:59 logger.go:127: 没有找到已保存的会话
2025/05/30 22:56:59 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:57:05 logger.go:109: 日志系统初始化成功
2025/05/30 22:57:05 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:57:05 db.go:47: Database initialized successfully
2025/05/30 22:57:05 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:57:05 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:57:05 logger.go:127: 没有找到已保存的会话
2025/05/30 22:57:05 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:57:07 logger.go:109: 日志系统初始化成功
2025/05/30 22:57:07 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:57:07 db.go:47: Database initialized successfully
2025/05/30 22:57:07 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:57:07 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:57:07 logger.go:127: 没有找到已保存的会话
2025/05/30 22:57:07 logger.go:109: 日志系统初始化成功
2025/05/30 22:57:07 app.go:63: 应用启动
2025/05/30 22:57:07 db.go:47: Database initialized successfully
2025/05/30 22:57:17 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 22:57:17 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 22:57:17 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://127.0.0.1:18080/api/student/update/check
2025/05/30 22:57:17 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 学校端返回：当前已是最新版本
2025/05/30 22:57:17 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 当前已是最新版本: 25053002
2025/05/30 22:57:20 logger.go:127: 创建会话失败: no such table: sessions
2025/05/30 22:57:20 logger.go:127: 教师端登录成功: 1@黑龙江农业职业技术学院 (SchoolID: 588328868260089856)
2025/05/30 22:57:21 logger.go:142: 获取包列表
2025/05/30 22:57:21 logger.go:142: 本地没有包，尝试从中央端同步包列表
2025/05/30 22:57:21 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:57:21 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:57:21 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:57:21 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:57:21 logger.go:127: 从中央端获取到 10 个包
2025/05/30 22:57:21 logger.go:142: 正在获取本地包列表...
2025/05/30 22:57:21 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:57:21 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:57:21 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:57:21 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:57:21 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:57:21 logger.go:127: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/30 22:57:21 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:57:21 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:57:21 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:57:21 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:57:21 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:57:21 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 22:57:21 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:57:21 logger.go:127: 发现新实验版本: 黄继光安卓端 (589448104990740480)
2025/05/30 22:57:21 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:57:21 logger.go:127: 发现新实验版本: 甲午海战虚拟仿真体验教学软件 (589448093531901952)
2025/05/30 22:57:21 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:57:21 logger.go:127: 发现新实验版本: 航母编队虚拟仿真体验教学软件 (589448093070528512)
2025/05/30 22:57:21 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 22:57:21 logger.go:127: 发现新实验版本: 黄继光安卓端 (589448104990740480)
2025/05/30 22:57:21 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 22:57:22 logger.go:127: 从中央端获取到 10 个包
2025/05/30 22:57:22 logger.go:142: 正在获取本地包列表...
2025/05/30 22:57:22 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:57:22 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:57:22 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:57:22 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:57:22 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:57:22 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:57:22 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:57:22 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:57:22 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:57:22 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 22:57:22 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 22:57:22 logger.go:127: 返回包列表，共 5 个包
2025/05/30 22:57:25 logger.go:127: 没有找到已保存的会话
2025/05/30 22:57:25 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:57:25 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:57:26 logger.go:127: 从中央端获取到 10 个包
2025/05/30 22:57:26 logger.go:142: 正在获取本地包列表...
2025/05/30 22:57:26 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:57:26 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:57:26 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:57:26 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:57:26 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:57:26 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:57:26 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:57:26 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:57:26 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:57:26 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 22:57:26 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 22:57:39 logger.go:142: 获取包列表
2025/05/30 22:57:39 logger.go:127: 返回包列表，共 5 个包
2025/05/30 22:57:41 app.go:82: 应用关闭
2025/05/30 22:57:41 logger.go:117: 关闭日志系统
2025/05/30 22:57:46 logger.go:109: 日志系统初始化成功
2025/05/30 22:57:46 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:57:46 db.go:47: Database initialized successfully
2025/05/30 22:57:46 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:57:46 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:57:46 logger.go:127: 没有找到已保存的会话
2025/05/30 22:57:46 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:57:54 logger.go:109: 日志系统初始化成功
2025/05/30 22:57:54 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:57:54 db.go:47: Database initialized successfully
2025/05/30 22:57:54 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:57:54 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:57:54 logger.go:127: 没有找到已保存的会话
2025/05/30 22:57:54 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:57:56 logger.go:109: 日志系统初始化成功
2025/05/30 22:57:56 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:57:56 db.go:47: Database initialized successfully
2025/05/30 22:57:56 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:57:56 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:57:56 logger.go:127: 没有找到已保存的会话
2025/05/30 22:57:56 logger.go:109: 日志系统初始化成功
2025/05/30 22:57:56 app.go:63: 应用启动
2025/05/30 22:57:56 db.go:47: Database initialized successfully
2025/05/30 22:58:06 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 22:58:06 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 22:58:06 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://127.0.0.1:18080/api/student/update/check
2025/05/30 22:58:06 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 学校端返回：当前已是最新版本
2025/05/30 22:58:06 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 当前已是最新版本: 25053002
2025/05/30 22:58:12 logger.go:127: 创建会话失败: no such table: sessions
2025/05/30 22:58:12 logger.go:127: 教师端登录成功: 1@黑龙江农业职业技术学院 (SchoolID: 588328868260089856)
2025/05/30 22:58:12 logger.go:142: 获取包列表
2025/05/30 22:58:12 logger.go:127: 返回包列表，共 5 个包
2025/05/30 22:58:12 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:58:12 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:58:13 logger.go:127: 从中央端获取到 10 个包
2025/05/30 22:58:13 logger.go:142: 正在获取本地包列表...
2025/05/30 22:58:13 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:58:13 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:58:13 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:58:13 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:58:13 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:58:13 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:58:13 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:58:13 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:58:13 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:58:13 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 22:58:13 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 22:58:17 app.go:82: 应用关闭
2025/05/30 22:58:17 logger.go:117: 关闭日志系统
2025/05/30 22:58:22 logger.go:109: 日志系统初始化成功
2025/05/30 22:58:22 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:58:22 db.go:47: Database initialized successfully
2025/05/30 22:58:22 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:58:22 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:58:22 logger.go:127: 没有找到已保存的会话
2025/05/30 22:58:22 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:58:28 logger.go:109: 日志系统初始化成功
2025/05/30 22:58:28 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:58:28 db.go:47: Database initialized successfully
2025/05/30 22:58:28 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:58:28 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:58:28 logger.go:127: 没有找到已保存的会话
2025/05/30 22:58:28 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:58:30 logger.go:109: 日志系统初始化成功
2025/05/30 22:58:30 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:58:30 db.go:47: Database initialized successfully
2025/05/30 22:58:30 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:58:30 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:58:30 logger.go:127: 没有找到已保存的会话
2025/05/30 22:58:30 logger.go:109: 日志系统初始化成功
2025/05/30 22:58:30 app.go:63: 应用启动
2025/05/30 22:58:30 db.go:47: Database initialized successfully
2025/05/30 22:58:33 app.go:82: 应用关闭
2025/05/30 22:58:33 logger.go:117: 关闭日志系统
2025/05/30 22:58:36 logger.go:109: 日志系统初始化成功
2025/05/30 22:58:36 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:58:36 db.go:47: Database initialized successfully
2025/05/30 22:58:36 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:58:36 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:58:36 logger.go:127: 没有找到已保存的会话
2025/05/30 22:58:36 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:58:43 logger.go:109: 日志系统初始化成功
2025/05/30 22:58:43 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:58:43 db.go:47: Database initialized successfully
2025/05/30 22:58:43 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:58:43 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:58:43 logger.go:127: 没有找到已保存的会话
2025/05/30 22:58:43 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:58:45 logger.go:109: 日志系统初始化成功
2025/05/30 22:58:45 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:58:45 db.go:47: Database initialized successfully
2025/05/30 22:58:45 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:58:45 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:58:45 logger.go:127: 没有找到已保存的会话
2025/05/30 22:58:45 logger.go:109: 日志系统初始化成功
2025/05/30 22:58:45 app.go:63: 应用启动
2025/05/30 22:58:45 db.go:47: Database initialized successfully
2025/05/30 22:58:55 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 22:58:55 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 22:58:55 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://127.0.0.1:18080/api/student/update/check
2025/05/30 22:58:55 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 学校端返回：当前已是最新版本
2025/05/30 22:58:55 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 当前已是最新版本: 25053002
2025/05/30 22:58:59 logger.go:127: 创建会话失败: no such table: sessions
2025/05/30 22:58:59 logger.go:127: 教师端登录成功: 1@黑龙江农业职业技术学院 (SchoolID: 588328868260089856)
2025/05/30 22:58:59 logger.go:142: 获取包列表
2025/05/30 22:58:59 logger.go:127: 返回包列表，共 5 个包
2025/05/30 22:59:00 logger.go:142: 开始从中央端同步包列表
2025/05/30 22:59:00 logger.go:142: 正在从中央端获取包列表...
2025/05/30 22:59:01 logger.go:127: 从中央端获取到 10 个包
2025/05/30 22:59:01 logger.go:142: 正在获取本地包列表...
2025/05/30 22:59:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 22:59:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 22:59:01 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 22:59:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 22:59:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 22:59:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 22:59:01 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 22:59:01 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 22:59:01 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 22:59:01 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 22:59:01 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 22:59:04 app.go:82: 应用关闭
2025/05/30 22:59:04 logger.go:117: 关闭日志系统
2025/05/30 22:59:09 logger.go:109: 日志系统初始化成功
2025/05/30 22:59:09 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:59:09 db.go:47: Database initialized successfully
2025/05/30 22:59:09 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:59:09 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:59:09 logger.go:127: 没有找到已保存的会话
2025/05/30 22:59:09 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:59:15 logger.go:109: 日志系统初始化成功
2025/05/30 22:59:15 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:59:15 db.go:47: Database initialized successfully
2025/05/30 22:59:15 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:59:15 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:59:15 logger.go:127: 没有找到已保存的会话
2025/05/30 22:59:15 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 22:59:17 logger.go:109: 日志系统初始化成功
2025/05/30 22:59:17 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 22:59:17 db.go:47: Database initialized successfully
2025/05/30 22:59:17 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 22:59:17 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 22:59:17 logger.go:127: 没有找到已保存的会话
2025/05/30 22:59:17 logger.go:109: 日志系统初始化成功
2025/05/30 22:59:17 app.go:63: 应用启动
2025/05/30 22:59:17 db.go:47: Database initialized successfully
2025/05/30 22:59:19 app.go:82: 应用关闭
2025/05/30 22:59:19 logger.go:117: 关闭日志系统
2025/05/30 23:03:27 logger.go:109: 日志系统初始化成功
2025/05/30 23:03:27 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:03:27 db.go:47: Database initialized successfully
2025/05/30 23:03:27 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:03:27 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:03:27 logger.go:127: 没有找到已保存的会话
2025/05/30 23:03:27 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 23:03:50 logger.go:109: 日志系统初始化成功
2025/05/30 23:03:50 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:03:50 db.go:47: Database initialized successfully
2025/05/30 23:03:50 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25052901, 程序版本: 25053002
2025/05/30 23:03:50 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 开始线性迁移: 25052901 → 25053002
2025/05/30 23:03:50 logger.go:127: [WARN] [StudentClient] [VersionManager] [Migration] 创建数据库备份失败: 复制数据库文件失败: open data/student-client.db: no such file or directory
2025/05/30 23:03:50 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 需要执行 1 个迁移
2025/05/30 23:03:50 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 执行迁移 1/1: 25053002
2025/05/30 23:03:50 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 开始执行迁移: 25053002 - 升级到25053002版本
2025/05/30 23:03:50 logger.go:127: [INFO] [StudentClient] [Migration] [25053002] 开始执行版本升级
2025/05/30 23:03:50 logger.go:127: [INFO] [StudentClient] [Migration] [25053002] 版本升级完成
2025/05/30 23:03:50 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 迁移完成: 25053002
2025/05/30 23:03:50 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 线性迁移完成: 25052901 → 25053002
2025/05/30 23:03:50 logger.go:127: 没有找到已保存的会话
2025/05/30 23:03:50 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 23:03:58 logger.go:109: 日志系统初始化成功
2025/05/30 23:03:58 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:03:58 db.go:47: Database initialized successfully
2025/05/30 23:03:58 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:03:58 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:03:58 logger.go:127: 没有找到已保存的会话
2025/05/30 23:03:58 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 23:04:00 logger.go:109: 日志系统初始化成功
2025/05/30 23:04:00 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:04:00 db.go:47: Database initialized successfully
2025/05/30 23:04:00 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:04:00 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:04:00 logger.go:127: 没有找到已保存的会话
2025/05/30 23:04:01 logger.go:109: 日志系统初始化成功
2025/05/30 23:04:01 app.go:64: 应用启动
2025/05/30 23:04:01 db.go:47: Database initialized successfully
2025/05/30 23:04:11 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 23:04:11 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 23:04:11 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://127.0.0.1:18080/api/student/update/check
2025/05/30 23:04:11 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 学校端返回：当前已是最新版本
2025/05/30 23:04:11 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 当前已是最新版本: 25053002
2025/05/30 23:04:49 logger.go:127: 会话已保存到数据库
2025/05/30 23:04:49 logger.go:127: 教师端登录成功: 1@黑龙江农业职业技术学院 (SchoolID: 588328868260089856)
2025/05/30 23:04:50 logger.go:142: 获取包列表
2025/05/30 23:04:50 logger.go:142: 本地没有包，尝试从中央端同步包列表
2025/05/30 23:04:50 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:04:50 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:04:50 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:04:50 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:04:50 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:04:50 logger.go:142: 正在获取本地包列表...
2025/05/30 23:04:50 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:04:50 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 23:04:50 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:04:50 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 23:04:50 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:04:50 logger.go:127: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/30 23:04:50 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:04:50 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 23:04:50 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:04:50 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 23:04:50 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:04:50 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 23:04:50 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:04:50 logger.go:127: 发现新实验版本: 黄继光安卓端 (589448104990740480)
2025/05/30 23:04:50 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:04:50 logger.go:127: 发现新实验版本: 甲午海战虚拟仿真体验教学软件 (589448093531901952)
2025/05/30 23:04:50 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:04:50 logger.go:127: 发现新实验版本: 航母编队虚拟仿真体验教学软件 (589448093070528512)
2025/05/30 23:04:50 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:04:50 logger.go:127: 发现新实验版本: 黄继光安卓端 (589448104990740480)
2025/05/30 23:04:50 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:04:50 logger.go:127: 返回包列表，共 5 个包
2025/05/30 23:04:50 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:04:50 logger.go:142: 正在获取本地包列表...
2025/05/30 23:04:50 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:04:50 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:04:50 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:04:50 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:04:50 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:04:50 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:04:50 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:04:50 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:04:50 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:04:50 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:04:50 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:05:04 logger.go:142: 获取包列表
2025/05/30 23:05:04 logger.go:127: 返回包列表，共 5 个包
2025/05/30 23:05:12 app.go:83: 应用关闭
2025/05/30 23:05:12 logger.go:117: 关闭日志系统
2025/05/30 23:05:18 logger.go:109: 日志系统初始化成功
2025/05/30 23:05:18 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:05:18 db.go:47: Database initialized successfully
2025/05/30 23:05:18 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:05:18 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:05:18 logger.go:127: 成功恢复会话: 1@黑龙江农业职业技术学院
2025/05/30 23:05:18 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 23:05:25 logger.go:109: 日志系统初始化成功
2025/05/30 23:05:25 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:05:25 db.go:47: Database initialized successfully
2025/05/30 23:05:25 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:05:25 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:05:25 logger.go:127: 成功恢复会话: 1@黑龙江农业职业技术学院
2025/05/30 23:05:25 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 23:05:27 logger.go:109: 日志系统初始化成功
2025/05/30 23:05:27 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:05:27 db.go:47: Database initialized successfully
2025/05/30 23:05:27 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:05:27 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:05:27 logger.go:127: 成功恢复会话: 1@黑龙江农业职业技术学院
2025/05/30 23:05:27 logger.go:109: 日志系统初始化成功
2025/05/30 23:05:27 app.go:64: 应用启动
2025/05/30 23:05:27 db.go:47: Database initialized successfully
2025/05/30 23:05:27 logger.go:142: 获取包列表
2025/05/30 23:05:27 logger.go:127: 返回包列表，共 5 个包
2025/05/30 23:05:28 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:05:28 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:05:28 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:05:28 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:05:28 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:05:28 logger.go:142: 正在获取本地包列表...
2025/05/30 23:05:28 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:05:28 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:05:28 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:05:28 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:05:28 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:05:28 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:05:28 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:05:28 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:05:28 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:05:28 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:05:28 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:05:28 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:05:28 logger.go:142: 正在获取本地包列表...
2025/05/30 23:05:28 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:05:28 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:05:28 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:05:28 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:05:28 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:05:28 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:05:28 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:05:28 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:05:28 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:05:28 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:05:28 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:05:37 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 23:05:37 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 23:05:37 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://127.0.0.1:18080/api/student/update/check
2025/05/30 23:05:37 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 学校端返回：当前已是最新版本
2025/05/30 23:05:37 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 当前已是最新版本: 25053002
2025/05/30 23:06:30 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:06:30 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:06:31 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:06:31 logger.go:142: 正在获取本地包列表...
2025/05/30 23:06:31 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:06:31 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:06:31 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:06:31 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:06:31 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:06:31 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:06:31 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:06:31 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:06:31 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:06:31 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:06:31 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:07:05 logger.go:109: 日志系统初始化成功
2025/05/30 23:07:05 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:07:05 db.go:47: Database initialized successfully
2025/05/30 23:07:05 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:07:05 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:07:05 logger.go:127: 成功恢复会话: 1@黑龙江农业职业技术学院
2025/05/30 23:07:05 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 23:07:07 logger.go:109: 日志系统初始化成功
2025/05/30 23:07:07 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:07:07 db.go:47: Database initialized successfully
2025/05/30 23:07:07 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:07:07 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:07:07 logger.go:127: 成功恢复会话: 1@黑龙江农业职业技术学院
2025/05/30 23:07:07 logger.go:109: 日志系统初始化成功
2025/05/30 23:07:07 app.go:64: 应用启动
2025/05/30 23:07:07 db.go:47: Database initialized successfully
2025/05/30 23:07:07 logger.go:142: 获取包列表
2025/05/30 23:07:07 logger.go:127: 返回包列表，共 5 个包
2025/05/30 23:07:08 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:07:08 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:07:08 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:07:08 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:07:08 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:07:08 logger.go:142: 正在获取本地包列表...
2025/05/30 23:07:08 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:07:08 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:07:08 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:07:08 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:07:08 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:07:08 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:07:08 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:07:08 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:07:08 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:07:08 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:07:08 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:07:08 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:07:08 logger.go:142: 正在获取本地包列表...
2025/05/30 23:07:08 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:07:08 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:07:08 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:07:08 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:07:08 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:07:08 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:07:08 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:07:08 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:07:08 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:07:08 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:07:08 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:07:11 app.go:83: 应用关闭
2025/05/30 23:07:11 logger.go:117: 关闭日志系统
2025/05/30 23:07:25 logger.go:109: 日志系统初始化成功
2025/05/30 23:07:25 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:07:25 db.go:47: Database initialized successfully
2025/05/30 23:07:25 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:07:25 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:07:25 logger.go:127: 成功恢复会话: 1@黑龙江农业职业技术学院
2025/05/30 23:07:25 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 23:07:50 logger.go:109: 日志系统初始化成功
2025/05/30 23:07:50 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:07:50 db.go:47: Database initialized successfully
2025/05/30 23:07:50 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:07:50 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:07:50 logger.go:127: 成功恢复会话: 1@黑龙江农业职业技术学院
2025/05/30 23:07:50 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 23:07:56 logger.go:109: 日志系统初始化成功
2025/05/30 23:07:56 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:07:56 db.go:47: Database initialized successfully
2025/05/30 23:07:56 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:07:56 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:07:56 logger.go:127: 成功恢复会话: 1@黑龙江农业职业技术学院
2025/05/30 23:07:56 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 23:07:58 logger.go:109: 日志系统初始化成功
2025/05/30 23:07:58 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:07:58 db.go:47: Database initialized successfully
2025/05/30 23:07:58 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:07:58 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:07:58 logger.go:127: 成功恢复会话: 1@黑龙江农业职业技术学院
2025/05/30 23:07:58 logger.go:109: 日志系统初始化成功
2025/05/30 23:07:58 app.go:64: 应用启动
2025/05/30 23:07:58 db.go:47: Database initialized successfully
2025/05/30 23:07:59 logger.go:142: 获取包列表
2025/05/30 23:07:59 logger.go:127: 返回包列表，共 5 个包
2025/05/30 23:07:59 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:07:59 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:07:59 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:07:59 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:07:59 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:07:59 logger.go:142: 正在获取本地包列表...
2025/05/30 23:07:59 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:07:59 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:07:59 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:07:59 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:07:59 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:07:59 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:07:59 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:07:59 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:07:59 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:07:59 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:07:59 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:07:59 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:07:59 logger.go:142: 正在获取本地包列表...
2025/05/30 23:07:59 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:07:59 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:07:59 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:07:59 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:07:59 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:07:59 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:07:59 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:07:59 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:07:59 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:07:59 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:07:59 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:08:04 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:08:04 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:08:05 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:08:05 logger.go:142: 正在获取本地包列表...
2025/05/30 23:08:05 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:08:05 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:08:05 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:08:05 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:08:05 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:08:05 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:08:05 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:08:05 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:08:05 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:08:05 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:08:05 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:08:08 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 23:08:08 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 23:08:08 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://127.0.0.1:18080/api/student/update/check
2025/05/30 23:08:08 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 学校端返回：当前已是最新版本
2025/05/30 23:08:08 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 当前已是最新版本: 25053002
2025/05/30 23:08:24 logger.go:142: 获取包列表
2025/05/30 23:08:24 logger.go:127: 返回包列表，共 5 个包
2025/05/30 23:08:51 logger.go:142: 获取包列表
2025/05/30 23:08:51 logger.go:127: 返回包列表，共 5 个包
2025/05/30 23:09:00 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:09:00 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:09:01 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:09:01 logger.go:142: 正在获取本地包列表...
2025/05/30 23:09:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:09:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:09:01 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:09:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:09:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:09:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:09:01 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:09:01 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:09:01 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:09:01 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:09:01 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:10:03 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:10:03 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:10:03 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:10:03 logger.go:142: 正在获取本地包列表...
2025/05/30 23:10:03 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:10:03 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:10:03 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:10:03 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:10:03 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:10:03 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:10:03 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:10:03 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:10:03 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:10:03 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:10:03 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:11:02 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:11:02 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:11:03 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:11:03 logger.go:142: 正在获取本地包列表...
2025/05/30 23:11:03 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:11:03 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:11:03 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:11:03 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:11:03 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:11:03 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:11:03 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:11:03 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:11:03 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:11:03 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:11:03 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:12:03 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:12:03 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:12:03 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:12:03 logger.go:142: 正在获取本地包列表...
2025/05/30 23:12:03 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:12:03 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:12:03 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:12:03 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:12:03 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:12:03 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:12:03 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:12:03 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:12:03 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:12:03 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:12:03 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:13:09 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:13:09 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:13:09 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:13:09 logger.go:142: 正在获取本地包列表...
2025/05/30 23:13:09 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:13:09 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:13:09 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:13:09 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:13:09 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:13:09 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:13:09 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:13:09 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:13:09 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:13:09 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:13:09 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:14:10 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:14:10 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:14:11 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:14:11 logger.go:142: 正在获取本地包列表...
2025/05/30 23:14:11 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:14:11 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:14:11 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:14:11 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:14:11 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:14:11 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:14:11 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:14:11 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:14:11 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:14:11 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:14:11 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:15:11 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:15:11 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:15:11 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:15:11 logger.go:142: 正在获取本地包列表...
2025/05/30 23:15:11 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:15:11 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:15:11 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:15:11 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:15:11 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:15:11 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:15:11 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:15:11 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:15:11 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:15:11 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:15:11 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:15:35 logger.go:109: 日志系统初始化成功
2025/05/30 23:15:35 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:15:35 db.go:47: Database initialized successfully
2025/05/30 23:15:35 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:15:35 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:15:35 logger.go:127: 成功恢复会话: 1@黑龙江农业职业技术学院
2025/05/30 23:15:35 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 23:15:38 logger.go:109: 日志系统初始化成功
2025/05/30 23:15:38 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:15:38 db.go:47: Database initialized successfully
2025/05/30 23:15:38 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:15:38 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:15:38 logger.go:127: 成功恢复会话: 1@黑龙江农业职业技术学院
2025/05/30 23:15:38 logger.go:109: 日志系统初始化成功
2025/05/30 23:15:38 app.go:64: 应用启动
2025/05/30 23:15:38 db.go:47: Database initialized successfully
2025/05/30 23:15:38 logger.go:142: 获取包列表
2025/05/30 23:15:38 logger.go:127: 返回包列表，共 5 个包
2025/05/30 23:15:39 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:15:39 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:15:39 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:15:39 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:15:39 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:15:39 logger.go:142: 正在获取本地包列表...
2025/05/30 23:15:39 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:15:39 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:15:39 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:15:39 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:15:39 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:15:39 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:15:39 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:15:39 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:15:39 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:15:39 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:15:39 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:15:39 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:15:39 logger.go:142: 正在获取本地包列表...
2025/05/30 23:15:39 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:15:39 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:15:39 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:15:39 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:15:39 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:15:39 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:15:39 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:15:39 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:15:39 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:15:39 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:15:39 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:15:44 logger.go:109: 日志系统初始化成功
2025/05/30 23:15:44 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:15:44 db.go:47: Database initialized successfully
2025/05/30 23:15:44 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:15:44 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:15:44 logger.go:127: 成功恢复会话: 1@黑龙江农业职业技术学院
2025/05/30 23:15:44 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 23:15:46 logger.go:109: 日志系统初始化成功
2025/05/30 23:15:46 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:15:46 db.go:47: Database initialized successfully
2025/05/30 23:15:46 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:15:46 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:15:46 logger.go:127: 成功恢复会话: 1@黑龙江农业职业技术学院
2025/05/30 23:15:46 logger.go:109: 日志系统初始化成功
2025/05/30 23:15:46 app.go:64: 应用启动
2025/05/30 23:15:46 db.go:47: Database initialized successfully
2025/05/30 23:15:47 logger.go:142: 获取包列表
2025/05/30 23:15:47 logger.go:127: 返回包列表，共 5 个包
2025/05/30 23:15:47 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:15:47 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:15:47 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:15:47 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:15:47 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:15:47 logger.go:142: 正在获取本地包列表...
2025/05/30 23:15:47 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:15:47 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:15:47 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:15:47 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:15:47 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:15:47 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:15:47 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:15:47 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:15:47 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:15:47 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:15:47 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:15:47 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:15:47 logger.go:142: 正在获取本地包列表...
2025/05/30 23:15:47 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:15:47 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:15:47 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:15:47 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:15:47 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:15:47 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:15:47 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:15:47 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:15:47 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:15:47 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:15:47 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:15:56 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 23:15:56 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 23:15:56 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://localhost:8900/v1/packageSystem/packageClientUpdate/getLatestPushedVersion?currentVersionNumber=25053002&type=teacher
2025/05/30 23:15:57 logger.go:127: [ERROR] [StudentClient] [UpdateService] [CheckForUpdates] 解析更新信息失败: json: cannot unmarshal number into Go struct field CentralUpdateData.data.versionNumber of type string
2025/05/30 23:15:57 logger.go:127: [WARN] [UpdateService] [checkUpdateOnStartup] 检查更新失败: 解析更新信息失败: json: cannot unmarshal number into Go struct field CentralUpdateData.data.versionNumber of type string
2025/05/30 23:15:57 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 无法连接学校端，跳过更新检查
2025/05/30 23:15:58 logger.go:109: 日志系统初始化成功
2025/05/30 23:15:58 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:15:58 db.go:47: Database initialized successfully
2025/05/30 23:15:58 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:15:58 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:15:58 logger.go:127: 成功恢复会话: 1@黑龙江农业职业技术学院
2025/05/30 23:15:58 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 23:16:00 logger.go:109: 日志系统初始化成功
2025/05/30 23:16:00 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:16:00 db.go:47: Database initialized successfully
2025/05/30 23:16:00 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:16:00 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:16:00 logger.go:127: 成功恢复会话: 1@黑龙江农业职业技术学院
2025/05/30 23:16:00 logger.go:109: 日志系统初始化成功
2025/05/30 23:16:00 app.go:64: 应用启动
2025/05/30 23:16:00 db.go:47: Database initialized successfully
2025/05/30 23:16:01 logger.go:142: 获取包列表
2025/05/30 23:16:01 logger.go:127: 返回包列表，共 5 个包
2025/05/30 23:16:01 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:16:01 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:16:01 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:16:01 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:16:01 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:16:01 logger.go:142: 正在获取本地包列表...
2025/05/30 23:16:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:16:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:16:01 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:16:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:16:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:16:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:16:01 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:16:01 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:16:01 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:16:01 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:16:01 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:16:01 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:16:01 logger.go:142: 正在获取本地包列表...
2025/05/30 23:16:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:16:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:16:01 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:16:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:16:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:16:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:16:01 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:16:01 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:16:01 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:16:01 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:16:01 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:16:10 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 23:16:10 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 23:16:10 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://localhost:8900/v1/packageSystem/packageClientUpdate/getLatestPushedVersion?currentVersionNumber=25053002&type=teacher
2025/05/30 23:16:11 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 发现新版本: 25053002 → 25053004
2025/05/30 23:16:11 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 发现新版本: 25053002 → 25053004
2025/05/30 23:16:11 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 开始倒计时，10秒后自动更新
2025/05/30 23:16:21 logger.go:127: [INFO] [UpdateService] [startUpdateCountdown] 倒计时结束，开始下载更新
2025/05/30 23:16:21 logger.go:127: [INFO] [UpdateService] [executeUpdate] 开始下载更新文件: http://package-system-file.cdzyhd.com/teacher-client/teacher-app/2025-05-30/1601befc-b59d-4f5a-8c23-302e9dc1274c_teacher-client.exe
2025/05/30 23:16:21 logger.go:127: [INFO] [StudentClient] [UpdateService] [DownloadUpdate] 开始下载更新文件: http://package-system-file.cdzyhd.com/teacher-client/teacher-app/2025-05-30/1601befc-b59d-4f5a-8c23-302e9dc1274c_teacher-client.exe
2025/05/30 23:16:22 logger.go:127: [INFO] [StudentClient] [UpdateService] [DownloadUpdate] 开始验证文件哈希...
2025/05/30 23:16:22 logger.go:127: [INFO] [StudentClient] [UpdateService] [DownloadUpdate] 文件哈希验证: 期望=1329c45561cf2270, 实际=1329c45561cf2270
2025/05/30 23:16:22 logger.go:127: [INFO] [StudentClient] [UpdateService] [DownloadUpdate] 文件哈希验证成功
2025/05/30 23:16:22 logger.go:127: [INFO] [StudentClient] [UpdateService] [DownloadUpdate] 更新文件下载完成: updates/teacher-client-v25053004
2025/05/30 23:16:22 logger.go:127: [INFO] [UpdateService] [executeUpdate] 更新文件下载完成: updates/teacher-client-v25053004
2025/05/30 23:16:22 logger.go:127: [INFO] [UpdateService] [executeUpdate] 开始自动更新
2025/05/30 23:16:22 logger.go:127: [INFO] [StudentClient] [UpdateService] [StartUpdate] 开始启动更新器
2025/05/30 23:16:22 logger.go:127: [ERROR] [UpdateService] [executeUpdate] 启动更新失败: 更新器不存在: ./updater
2025/05/30 23:17:01 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:17:01 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:17:01 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:17:01 logger.go:142: 正在获取本地包列表...
2025/05/30 23:17:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:17:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:17:01 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:17:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:17:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:17:01 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:17:01 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:17:01 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:17:01 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:17:01 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:17:01 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:17:28 app.go:83: 应用关闭
2025/05/30 23:17:35 logger.go:109: 日志系统初始化成功
2025/05/30 23:17:35 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:17:35 db.go:47: Database initialized successfully
2025/05/30 23:17:35 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25052901, 程序版本: 25053002
2025/05/30 23:17:35 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 开始线性迁移: 25052901 → 25053002
2025/05/30 23:17:35 logger.go:127: [WARN] [StudentClient] [VersionManager] [Migration] 创建数据库备份失败: 复制数据库文件失败: open data/student-client.db: no such file or directory
2025/05/30 23:17:35 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 需要执行 1 个迁移
2025/05/30 23:17:35 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 执行迁移 1/1: 25053002
2025/05/30 23:17:35 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 开始执行迁移: 25053002 - 升级到25053002版本
2025/05/30 23:17:35 logger.go:127: [INFO] [StudentClient] [Migration] [25053002] 开始执行版本升级
2025/05/30 23:17:35 logger.go:127: [INFO] [StudentClient] [Migration] [25053002] 版本升级完成
2025/05/30 23:17:35 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 迁移完成: 25053002
2025/05/30 23:17:35 logger.go:127: [INFO] [StudentClient] [VersionManager] [Migration] 线性迁移完成: 25052901 → 25053002
2025/05/30 23:17:35 logger.go:127: 没有找到已保存的会话
2025/05/30 23:17:35 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 23:17:42 logger.go:109: 日志系统初始化成功
2025/05/30 23:17:42 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:17:42 db.go:47: Database initialized successfully
2025/05/30 23:17:42 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:17:42 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:17:42 logger.go:127: 没有找到已保存的会话
2025/05/30 23:17:42 typescriptify.go:1008: Please rename returned type or consider adding bindings config to your wails.json
2025/05/30 23:17:44 logger.go:109: 日志系统初始化成功
2025/05/30 23:17:44 logger.go:127: 教师端包管理系统启动，版本: 25053002
2025/05/30 23:17:44 db.go:47: Database initialized successfully
2025/05/30 23:17:44 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 数据库版本: 25053002, 程序版本: 25053002
2025/05/30 23:17:44 logger.go:127: [INFO] [StudentClient] [VersionManager] [CheckAndMigrate] 版本一致，无需迁移
2025/05/30 23:17:44 logger.go:127: 没有找到已保存的会话
2025/05/30 23:17:44 logger.go:109: 日志系统初始化成功
2025/05/30 23:17:44 app.go:64: 应用启动
2025/05/30 23:17:44 db.go:47: Database initialized successfully
2025/05/30 23:17:54 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
2025/05/30 23:17:54 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
2025/05/30 23:17:54 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 检查更新URL: http://localhost:8900/v1/packageSystem/packageClientUpdate/getLatestPushedVersion?currentVersionNumber=25053002&type=teacher
2025/05/30 23:17:54 logger.go:127: [INFO] [StudentClient] [UpdateService] [CheckForUpdates] 发现新版本: 25053002 → 25053004
2025/05/30 23:17:54 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 发现新版本: 25053002 → 25053004
2025/05/30 23:17:54 logger.go:127: [INFO] [UpdateService] [checkUpdateOnStartup] 开始倒计时，10秒后自动更新
2025/05/30 23:17:59 logger.go:127: 会话已保存到数据库
2025/05/30 23:17:59 logger.go:127: 教师端登录成功: 1@黑龙江农业职业技术学院 (SchoolID: 588328868260089856)
2025/05/30 23:17:59 logger.go:142: 获取包列表
2025/05/30 23:17:59 logger.go:142: 本地没有包，尝试从中央端同步包列表
2025/05/30 23:17:59 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:17:59 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:17:59 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:17:59 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:18:00 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:18:00 logger.go:142: 正在获取本地包列表...
2025/05/30 23:18:00 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:18:00 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 23:18:00 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:18:00 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 23:18:00 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:18:00 logger.go:127: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/30 23:18:00 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:18:00 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 23:18:00 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:18:00 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 23:18:00 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:18:00 logger.go:127: 发现新实验版本: 算器的奇幻旅行 (592382805766311936)
2025/05/30 23:18:00 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:18:00 logger.go:127: 发现新实验版本: 黄继光安卓端 (589448104990740480)
2025/05/30 23:18:00 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:18:00 logger.go:127: 发现新实验版本: 甲午海战虚拟仿真体验教学软件 (589448093531901952)
2025/05/30 23:18:00 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:18:00 logger.go:127: 发现新实验版本: 航母编队虚拟仿真体验教学软件 (589448093070528512)
2025/05/30 23:18:00 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:18:00 logger.go:127: 发现新实验版本: 黄继光安卓端 (589448104990740480)
2025/05/30 23:18:00 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:18:00 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:18:00 logger.go:142: 正在获取本地包列表...
2025/05/30 23:18:00 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:18:00 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:18:00 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:18:00 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:18:00 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:18:00 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:18:00 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:18:00 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:18:00 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:18:00 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:18:00 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:18:00 logger.go:127: 返回包列表，共 5 个包
2025/05/30 23:18:04 logger.go:127: [INFO] [UpdateService] [startUpdateCountdown] 倒计时结束，开始下载更新
2025/05/30 23:18:04 logger.go:127: [INFO] [UpdateService] [executeUpdate] 开始下载更新文件: http://package-system-file.cdzyhd.com/teacher-client/teacher-app/2025-05-30/1601befc-b59d-4f5a-8c23-302e9dc1274c_teacher-client.exe
2025/05/30 23:18:04 logger.go:127: [INFO] [StudentClient] [UpdateService] [DownloadUpdate] 开始下载更新文件: http://package-system-file.cdzyhd.com/teacher-client/teacher-app/2025-05-30/1601befc-b59d-4f5a-8c23-302e9dc1274c_teacher-client.exe
2025/05/30 23:18:06 logger.go:127: [INFO] [StudentClient] [UpdateService] [DownloadUpdate] 开始验证文件哈希...
2025/05/30 23:18:06 logger.go:127: [INFO] [StudentClient] [UpdateService] [DownloadUpdate] 文件哈希验证: 期望=1329c45561cf2270, 实际=1329c45561cf2270
2025/05/30 23:18:06 logger.go:127: [INFO] [StudentClient] [UpdateService] [DownloadUpdate] 文件哈希验证成功
2025/05/30 23:18:06 logger.go:127: [INFO] [StudentClient] [UpdateService] [DownloadUpdate] 更新文件下载完成: updates/teacher-client-v25053004
2025/05/30 23:18:06 logger.go:127: [INFO] [UpdateService] [executeUpdate] 更新文件下载完成: updates/teacher-client-v25053004
2025/05/30 23:18:06 logger.go:127: [INFO] [UpdateService] [executeUpdate] 开始自动更新
2025/05/30 23:18:06 logger.go:127: [INFO] [StudentClient] [UpdateService] [StartUpdate] 开始启动更新器
2025/05/30 23:18:06 logger.go:127: [ERROR] [UpdateService] [executeUpdate] 启动更新失败: 更新器不存在: ./updater
2025/05/30 23:19:00 logger.go:142: 开始从中央端同步包列表
2025/05/30 23:19:00 logger.go:142: 正在从中央端获取包列表...
2025/05/30 23:19:00 logger.go:127: 从中央端获取到 10 个包
2025/05/30 23:19:00 logger.go:142: 正在获取本地包列表...
2025/05/30 23:19:00 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593401509065003008
2025/05/30 23:19:00 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593426936600989696
2025/05/30 23:19:00 logger.go:127: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 593427432036372480
2025/05/30 23:19:00 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593431083157032960
2025/05/30 23:19:00 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593433073954066432
2025/05/30 23:19:00 logger.go:127: 处理远程包: 算器的奇幻旅行, 实验版本ID: 592382805766311936, 包版本ID: 593594123240345600
2025/05/30 23:19:00 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 593606097810493440
2025/05/30 23:19:00 logger.go:127: 处理远程包: 甲午海战虚拟仿真体验教学软件, 实验版本ID: 589448093531901952, 包版本ID: 593752457780137984
2025/05/30 23:19:00 logger.go:127: 处理远程包: 航母编队虚拟仿真体验教学软件, 实验版本ID: 589448093070528512, 包版本ID: 593753929259421696
2025/05/30 23:19:00 logger.go:127: 处理远程包: 黄继光安卓端, 实验版本ID: 589448104990740480, 包版本ID: 595020539236454400
2025/05/30 23:19:00 logger.go:127: 从中央端同步包列表完成，共同步 10 个包
2025/05/30 23:19:35 app.go:83: 应用关闭
2025/05/30 23:19:35 logger.go:117: 关闭日志系统
