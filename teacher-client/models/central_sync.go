package models

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"teacher-client/utils"
	"time"
)

// PackageVersionEntity 包版本实体
type PackageVersionEntity struct {
	ID struct {
		Timestamp int64 `json:"timestamp"`
		Date      int64 `json:"date"`
	} `json:"_id"`
	PackageVersionID      string      `json:"packageVersionId"`
	CreateTime            int64       `json:"createTime"`
	ExtraInfo             interface{} `json:"extraInfo"`
	ExperimentID          string      `json:"experimentId"`
	ExperimentVersionID   string      `json:"experimentVersionId"`
	ExperimentVersionName string      `json:"experimentVersionName"`
	FileHash              string      `json:"fileHash"`
	FileInfo              struct {
		OriginalName     string `json:"originalName"`
		Extension        string `json:"extension"`
		Size             int64  `json:"size"`
		LastModifiedDate string `json:"lastModifiedDate"`
		SizeFormatted    string `json:"sizeFormatted"`
		LastModified     int64  `json:"lastModified"`
		Type             string `json:"type"`
	} `json:"fileInfo"`
	DownloadURL     string   `json:"downloadUrl"`
	Version         string   `json:"version"`
	VersionName     string   `json:"versionName"`
	VersionDesc     string   `json:"versionDesc"`
	PushedSchoolIds []string `json:"pushedSchoolIds"`
	Class           string   `json:"_class"`
}

// ExperimentVersionEntity 实验版本实体
type ExperimentVersionEntity struct {
	ID struct {
		Timestamp int64 `json:"timestamp"`
		Date      int64 `json:"date"`
	} `json:"_id"`
	ExperimentVersionID   string      `json:"experimentVersionId"`
	ExperimentVersionName string      `json:"experimentVersionName"`
	CreateTime            int64       `json:"createTime"`
	ExtraInfo             interface{} `json:"extraInfo"`
	ExperimentID          string      `json:"experimentId"`
	ExperimentName        string      `json:"experimentName"`
	Name                  string      `json:"name"`
	AsMainVersion         bool        `json:"asMainVersion"`
	Description           string      `json:"description"`
	Enable                bool        `json:"enable"`
	SchoolIds             []string    `json:"schoolIds"`
	Class                 string      `json:"_class"`
}

// PackagePushItem 包推送项
type PackagePushItem struct {
	ID struct {
		Timestamp int64 `json:"timestamp"`
		Date      int64 `json:"date"`
	} `json:"_id"`
	PackagePushID     string                    `json:"packagePushId"`
	CreateTime        int64                     `json:"createTime"`
	ExtraInfo         interface{}               `json:"extraInfo"`
	PackageVersionID  string                    `json:"packageVersionId"`
	SchoolID          string                    `json:"schoolId"`
	SchoolAccept      bool                      `json:"schoolAccept"`
	SchoolDownload    bool                      `json:"schoolDownload"`
	Class             string                    `json:"_class"`
	PackageVersion    []PackageVersionEntity    `json:"packageVersion"`
	ExperimentVersion []ExperimentVersionEntity `json:"experimentVersion"`
}

// PackagePushResponse 包推送响应
type PackagePushResponse struct {
	Code string            `json:"code"`
	Msg  string            `json:"msg"`
	Data []PackagePushItem `json:"data"`
}

// UpdatePushStatusRequest 更新推送状态请求（旧版）
type UpdatePushStatusRequest struct {
	PackagePushID  string `json:"packagePushId"`
	AcceptStatus   int    `json:"acceptStatus"`
	DownloadStatus int    `json:"downloadStatus"`
}

// UpdatePushStatusResponse 更新推送状态响应（旧版）
type UpdatePushStatusResponse struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Success bool `json:"success"`
	} `json:"data"`
}

// AddPushLogRequest 添加推送日志请求
type AddPushLogRequest struct {
	PackagePushID string `json:"packagePushId"` // 所属推送id
	Type          string `json:"type"`          // 类型 accept 接受了推送，download 完成了下载
	Message       string `json:"message"`       // 消息
	Mac           string `json:"mac,omitempty"` // 设备id（可选）
	IP            string `json:"ip,omitempty"`  // ip地址（可选）
}

// AddPushLogResponse 添加推送日志响应
type AddPushLogResponse struct {
	Code string      `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// GetPendingPushes 获取待处理的推送
func GetPendingPushes(token, schoolID string) ([]PackagePushItem, error) {
	centralAPIURL := os.Getenv("CENTRAL_API_URL")
	if centralAPIURL == "" {
		return nil, errors.New("中央平台 API URL 未配置")
	}

	pushURL := fmt.Sprintf("%s/v1/packageSystem/packagePush/oneSchoolPushList", centralAPIURL)

	// 准备空的请求体
	requestBody := []byte("{}")

	// 准备请求
	req, err := http.NewRequest("POST", pushURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, err
	}

	// 添加请求头
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", token))
	req.Header.Add("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{
		Timeout: time.Duration(30) * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 解析响应
	var pushResponse PackagePushResponse
	if err := json.NewDecoder(resp.Body).Decode(&pushResponse); err != nil {
		return nil, err
	}

	// 检查响应状态
	if pushResponse.Code != "000000" {
		return nil, fmt.Errorf("获取推送列表失败: %s", pushResponse.Msg)
	}

	// 获取所有推送记录
	return pushResponse.Data, nil
}

// UpdatePushStatus 更新推送状态
func UpdatePushStatus(token string, packagePushID string, acceptStatus, downloadStatus int) error {
	centralAPIURL := os.Getenv("CENTRAL_API_URL")
	if centralAPIURL == "" {
		return errors.New("中央平台 API URL 未配置")
	}

	// 构建请求URL
	updateURL := fmt.Sprintf("%s/v1/packageSystem/packagePushLog/addPushLog", centralAPIURL)

	// 确定操作类型和消息
	var logType string
	var message string

	if acceptStatus == 1 {
		logType = "accept"
		message = "学校端已接受推送"
	}

	if downloadStatus == 1 {
		logType = "download"
		message = "学校端已完成下载"
	}

	// 如果没有状态变更，则不需要发送请求
	if logType == "" {
		return nil
	}

	// 获取设备MAC地址和IP地址
	mac, _ := utils.GetMacAddress()
	ip, _ := utils.GetLocalIP()

	// 准备请求体
	addLogRequest := AddPushLogRequest{
		PackagePushID: packagePushID,
		Type:          logType,
		Message:       message,
		Mac:           mac,
		IP:            ip,
	}

	requestBody, err := json.Marshal(addLogRequest)
	if err != nil {
		return err
	}

	// 准备请求
	req, err := http.NewRequest("POST", updateURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return err
	}

	// 添加请求头
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", token))
	req.Header.Add("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{
		Timeout: time.Duration(30) * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// 解析响应
	var addLogResponse AddPushLogResponse
	if err := json.NewDecoder(resp.Body).Decode(&addLogResponse); err != nil {
		return err
	}

	// 检查响应状态
	if addLogResponse.Code != "000000" {
		return fmt.Errorf("更新推送状态失败: %s", addLogResponse.Msg)
	}

	return nil
}

// DownloadPackage 下载包文件
func DownloadPackage(token, downloadURL, packageId string, fileHash string, fileSize int64, storagePath string) (string, error) {
	// 确保存储目录存在
	if err := os.MkdirAll(storagePath, 0755); err != nil {
		return "", err
	}

	// 生成文件名
	fileName := filepath.Base(downloadURL)
	//if fileName == "" || fileName == "." {
	//	fileName = fmt.Sprintf("package_%s.zip", time.Now().Format("20060102150405"))
	//}
	fileName = fmt.Sprintf("%s.zip", packageId) // 用packageId重命名包名称

	filePath := filepath.Join(storagePath, fileName)

	// 准备请求
	req, err := http.NewRequest("GET", downloadURL, nil)
	if err != nil {
		return "", err
	}

	// 添加令牌到请求头
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", token))

	// 发送请求
	client := &http.Client{
		Timeout: time.Duration(3600) * time.Second, // 长超时，支持大文件下载
	}

	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("download failed with status: %s", resp.Status)
	}

	// 创建文件
	file, err := os.Create(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 下载文件
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		// 删除不完整的文件
		file.Close()
		os.Remove(filePath)
		return "", err
	}

	// 关闭文件以便后续操作
	file.Close()

	return filePath, nil
}
