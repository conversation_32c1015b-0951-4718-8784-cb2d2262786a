package models

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"path/filepath"

	_ "github.com/mattn/go-sqlite3"
)

var DB *sql.DB

// InitDB 初始化数据库连接
func InitDB() error {
	dbPath := "./data/teacher-client.db"

	// 确保数据库目录存在
	dbDir := filepath.Dir(dbPath)
	if err := os.MkdirAll(dbDir, 0755); err != nil {
		return fmt.Errorf("failed to create database directory: %w", err)
	}

	// 打开数据库连接
	var err error
	DB, err = sql.Open("sqlite3", dbPath)
	if err != nil {
		return fmt.Errorf("failed to open database: %w", err)
	}

	// 测试数据库连接
	if err := DB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	// 初始化数据库表
	if err := createTables(); err != nil {
		return fmt.Errorf("failed to create tables: %w", err)
	}

	// 插入默认配置
	if err := InsertDefaultConfigs(); err != nil {
		return fmt.Errorf("failed to insert default configs: %w", err)
	}

	log.Println("Database initialized successfully")
	return nil
}

// CloseDB 关闭数据库连接
func CloseDB() {
	if DB != nil {
		DB.Close()
	}
}

// createTables 创建数据库表
func createTables() error {
	// 配置表
	_, err := DB.Exec(`
	CREATE TABLE IF NOT EXISTS configs (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		key TEXT NOT NULL UNIQUE,
		value TEXT NOT NULL,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`)
	if err != nil {
		return err
	}

	// 包版本表
	_, err = DB.Exec(`
	CREATE TABLE IF NOT EXISTS packages (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		package_version_id TEXT NOT NULL UNIQUE,
		experiment_id TEXT NOT NULL,
		experiment_name TEXT NOT NULL,
		experiment_version_id TEXT NOT NULL,
		experiment_version_name TEXT NOT NULL,
		version TEXT NOT NULL,
		version_name TEXT NOT NULL,
		version_desc TEXT,
		file_hash TEXT NOT NULL,
		file_path TEXT,
		file_size INTEGER DEFAULT 0,
		download_url TEXT NOT NULL,
		local_path TEXT,
		extract_path TEXT,
		executable_path TEXT,
		sync_status INTEGER DEFAULT 0,
		download_count INTEGER DEFAULT 0,
		school_id TEXT NOT NULL DEFAULT '',
		last_run TIMESTAMP,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`)
	if err != nil {
		return err
	}

	// 同步日志表
	_, err = DB.Exec(`
	CREATE TABLE IF NOT EXISTS sync_logs (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		package_id INTEGER DEFAULT 0,
		experiment_version_id TEXT DEFAULT '',
		action TEXT NOT NULL,
		status INTEGER NOT NULL,
		message TEXT,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`)
	if err != nil {
		return err
	}

	// 应用版本表
	_, err = DB.Exec(`
	CREATE TABLE IF NOT EXISTS app_versions (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		version TEXT NOT NULL UNIQUE,
		installed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		is_current BOOLEAN DEFAULT FALSE,
		student_client_path TEXT DEFAULT '',
		student_client_file_hash TEXT DEFAULT '',
		student_update_description TEXT DEFAULT '',
		package_client_update_id TEXT DEFAULT ''
	)`)
	if err != nil {
		return err
	}

	// 迁移日志表
	_, err = DB.Exec(`
	CREATE TABLE IF NOT EXISTS migration_logs (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		from_version TEXT NOT NULL,
		to_version TEXT NOT NULL,
		description TEXT NOT NULL,
		executed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		status TEXT NOT NULL,
		error_message TEXT
	)`)
	if err != nil {
		return err
	}

	// 会话表（用于持久化登录状态）
	_, err = DB.Exec(`
	CREATE TABLE IF NOT EXISTS sessions (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		username TEXT NOT NULL,
		school_id TEXT NOT NULL,
		school_name TEXT NOT NULL,
		central_token TEXT NOT NULL,
		local_token TEXT DEFAULT '',
		token_expires_at DATETIME NOT NULL,
		last_login DATETIME DEFAULT CURRENT_TIMESTAMP,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	)`)
	if err != nil {
		return err
	}

	// 检查并添加新字段（用于数据库升级）
	err = addColumnIfNotExists("sync_logs", "experiment_version_id", "TEXT DEFAULT ''")
	if err != nil {
		return err
	}

	return nil
}

// addColumnIfNotExists 如果字段不存在则添加字段
func addColumnIfNotExists(tableName, columnName, columnDef string) error {
	// 检查字段是否存在
	query := `PRAGMA table_info(` + tableName + `)`
	rows, err := DB.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	columnExists := false
	for rows.Next() {
		var cid int
		var name, dataType string
		var notNull, pk int
		var defaultValue interface{}

		err := rows.Scan(&cid, &name, &dataType, &notNull, &defaultValue, &pk)
		if err != nil {
			return err
		}

		if name == columnName {
			columnExists = true
			break
		}
	}

	// 如果字段不存在，则添加
	if !columnExists {
		alterQuery := fmt.Sprintf("ALTER TABLE %s ADD COLUMN %s %s", tableName, columnName, columnDef)
		_, err = DB.Exec(alterQuery)
		if err != nil {
			return err
		}
		log.Printf("添加字段 %s.%s 成功", tableName, columnName)
	}

	return nil
}
