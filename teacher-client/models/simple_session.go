package models

import (
	"sync"
	"time"
)

// SimpleSession 简单会话管理器（内存存储）
type SimpleSession struct {
	token      string
	schoolID   string
	schoolName string
	userID     string
	username   string
	expiresAt  time.Time
	mutex      sync.RWMutex
}

// NewSession 创建新的会话管理器
func NewSession() *SimpleSession {
	return &SimpleSession{}
}

// SetToken 设置认证token
func (s *SimpleSession) SetToken(token string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.token = token
	// 设置token过期时间为24小时后
	s.expiresAt = time.Now().Add(24 * time.Hour)
}

// GetToken 获取认证token
func (s *SimpleSession) GetToken() string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.token
}

// SetSchoolID 设置学校ID
func (s *SimpleSession) SetSchoolID(schoolID string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.schoolID = schoolID
}

// GetSchoolID 获取学校ID
func (s *SimpleSession) GetSchoolID() string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.schoolID
}

// SetSchoolName 设置学校名称
func (s *SimpleSession) SetSchoolName(schoolName string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.schoolName = schoolName
}

// GetSchoolName 获取学校名称
func (s *SimpleSession) GetSchoolName() string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.schoolName
}

// SetUserID 设置用户ID
func (s *SimpleSession) SetUserID(userID string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.userID = userID
}

// GetUserID 获取用户ID
func (s *SimpleSession) GetUserID() string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.userID
}

// SetUsername 设置用户名
func (s *SimpleSession) SetUsername(username string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.username = username
}

// GetUsername 获取用户名
func (s *SimpleSession) GetUsername() string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.username
}

// IsValid 检查会话是否有效
func (s *SimpleSession) IsValid() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	// 检查token是否存在且未过期
	return s.token != "" && time.Now().Before(s.expiresAt)
}

// Clear 清除会话信息
func (s *SimpleSession) Clear() {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	s.token = ""
	s.schoolID = ""
	s.schoolName = ""
	s.userID = ""
	s.username = ""
	s.expiresAt = time.Time{}
}

// Refresh 刷新会话过期时间
func (s *SimpleSession) Refresh() {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	if s.token != "" {
		s.expiresAt = time.Now().Add(24 * time.Hour)
	}
}

// GetSessionInfo 获取会话信息
func (s *SimpleSession) GetSessionInfo() map[string]interface{} {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	return map[string]interface{}{
		"token":      s.token,
		"schoolId":   s.schoolID,
		"schoolName": s.schoolName,
		"userId":     s.userID,
		"username":   s.username,
		"expiresAt":  s.expiresAt,
		"isValid":    s.IsValid(),
	}
}
