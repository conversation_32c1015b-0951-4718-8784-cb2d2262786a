package models

import (
	"time"
)

// SystemLog 系统日志模型
type SystemLog struct {
	ID        int64     `json:"id"`
	Level     string    `json:"level"`
	Module    string    `json:"module"`
	Action    string    `json:"action"`
	Message   string    `json:"message"`
	CreatedAt time.Time `json:"createdAt"`
}

// AddSystemLog 添加系统日志
func AddSystemLog(level, module, action, message string) error {
	query := `INSERT INTO system_logs (level, module, action, message, created_at) 
			  VALUES (?, ?, ?, ?, ?)`

	_, err := DB.Exec(query, level, module, action, message, time.Now())
	return err
}

// GetSystemLogs 获取系统日志
func GetSystemLogs(page, pageSize int, level, module string) ([]SystemLog, int, error) {
	// 构建查询条件
	whereClause := ""
	args := []interface{}{}

	if level != "" {
		whereClause += " WHERE level = ?"
		args = append(args, level)
	}

	if module != "" {
		if whereClause == "" {
			whereClause += " WHERE module = ?"
		} else {
			whereClause += " AND module = ?"
		}
		args = append(args, module)
	}

	// 获取总数
	countQuery := "SELECT COUNT(*) FROM system_logs" + whereClause
	var total int
	err := DB.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	// 计算分页
	offset := (page - 1) * pageSize

	// 查询日志
	query := `SELECT id, level, module, action, message, created_at 
			  FROM system_logs` + whereClause + ` 
			  ORDER BY created_at DESC 
			  LIMIT ? OFFSET ?`

	args = append(args, pageSize, offset)

	rows, err := DB.Query(query, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	// 解析结果
	logs := []SystemLog{}
	for rows.Next() {
		log := SystemLog{}
		err := rows.Scan(
			&log.ID,
			&log.Level,
			&log.Module,
			&log.Action,
			&log.Message,
			&log.CreatedAt,
		)
		if err != nil {
			return nil, 0, err
		}
		logs = append(logs, log)
	}

	if err := rows.Err(); err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetRecentSystemLogs 获取最近的系统日志
func GetRecentSystemLogs(limit int) ([]SystemLog, error) {
	query := `SELECT id, level, module, action, message, created_at 
			  FROM system_logs 
			  ORDER BY created_at DESC 
			  LIMIT ?`

	rows, err := DB.Query(query, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 解析结果
	logs := []SystemLog{}
	for rows.Next() {
		log := SystemLog{}
		err := rows.Scan(
			&log.ID,
			&log.Level,
			&log.Module,
			&log.Action,
			&log.Message,
			&log.CreatedAt,
		)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return logs, nil
}
