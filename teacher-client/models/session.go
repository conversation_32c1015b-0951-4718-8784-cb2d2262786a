package models

import (
	"database/sql"
	"errors"
	"time"
)

// Session 会话模型，用于存储登录用户的信息
type Session struct {
	ID             int64     `json:"id"`
	Username       string    `json:"username"`
	SchoolID       string    `json:"schoolId"`
	SchoolName     string    `json:"schoolName"`
	CentralToken   string    `json:"-"` // 中央平台令牌，不返回给客户端
	LocalToken     string    `json:"token"`
	TokenExpiresAt time.Time `json:"tokenExpiresAt,omitempty"`
	LastLogin      time.Time `json:"lastLogin,omitempty"`
	CreatedAt      time.Time `json:"createdAt"`
	UpdatedAt      time.Time `json:"updatedAt"`
}

// GetSessionByUsername 通过用户名获取会话
func GetSessionByUsername(username string) (*Session, error) {
	session := &Session{}
	query := `SELECT id, username, school_id, school_name, central_token, local_token, token_expires_at, last_login, created_at, updated_at
			  FROM sessions WHERE username = ?`

	err := DB.QueryRow(query, username).Scan(
		&session.ID,
		&session.Username,
		&session.SchoolID,
		&session.SchoolName,
		&session.CentralToken,
		&session.LocalToken,
		&session.TokenExpiresAt,
		&session.LastLogin,
		&session.CreatedAt,
		&session.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New("session not found")
		}
		return nil, err
	}

	return session, nil
}

// GetSessionByID 通过ID获取会话
func GetSessionByID(id int64) (*Session, error) {
	session := &Session{}
	query := `SELECT id, username, school_id, school_name, central_token, local_token, token_expires_at, last_login, created_at, updated_at
			  FROM sessions WHERE id = ?`

	err := DB.QueryRow(query, id).Scan(
		&session.ID,
		&session.Username,
		&session.SchoolID,
		&session.SchoolName,
		&session.CentralToken,
		&session.LocalToken,
		&session.TokenExpiresAt,
		&session.LastLogin,
		&session.CreatedAt,
		&session.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New("session not found")
		}
		return nil, err
	}

	return session, nil
}

// CreateSession 创建会话
func CreateSession(session *Session) error {
	query := `INSERT INTO sessions (username, school_id, school_name, central_token, local_token, token_expires_at, last_login, created_at, updated_at)
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`

	now := time.Now()
	session.CreatedAt = now
	session.UpdatedAt = now

	result, err := DB.Exec(
		query,
		session.Username,
		session.SchoolID,
		session.SchoolName,
		session.CentralToken,
		session.LocalToken,
		session.TokenExpiresAt,
		session.LastLogin,
		session.CreatedAt,
		session.UpdatedAt,
	)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}

	session.ID = id
	return nil
}

// UpdateSession 更新会话信息
func UpdateSession(session *Session) error {
	query := `UPDATE sessions SET
			  username = ?,
			  school_id = ?,
			  school_name = ?,
			  central_token = ?,
			  local_token = ?,
			  token_expires_at = ?,
			  last_login = ?,
			  updated_at = ?
			  WHERE id = ?`

	session.UpdatedAt = time.Now()

	_, err := DB.Exec(
		query,
		session.Username,
		session.SchoolID,
		session.SchoolName,
		session.CentralToken,
		session.LocalToken,
		session.TokenExpiresAt,
		session.LastLogin,
		session.UpdatedAt,
		session.ID,
	)

	return err
}

// GetSessionByLocalToken 通过本地令牌获取会话
func GetSessionByLocalToken(token string) (*Session, error) {
	session := &Session{}
	query := `SELECT id, username, school_id, school_name, central_token, local_token, token_expires_at, last_login, created_at, updated_at
			  FROM sessions WHERE local_token = ?`

	err := DB.QueryRow(query, token).Scan(
		&session.ID,
		&session.Username,
		&session.SchoolID,
		&session.SchoolName,
		&session.CentralToken,
		&session.LocalToken,
		&session.TokenExpiresAt,
		&session.LastLogin,
		&session.CreatedAt,
		&session.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New("session not found")
		}
		return nil, err
	}

	// 检查令牌是否过期
	if time.Now().After(session.TokenExpiresAt) {
		return nil, errors.New("token expired")
	}

	return session, nil
}

// GetSessionBySchoolID 通过学校ID获取会话
func GetSessionBySchoolID(schoolID string) (*Session, error) {
	session := &Session{}
	query := `SELECT id, username, school_id, school_name, central_token, local_token, token_expires_at, last_login, created_at, updated_at
			  FROM sessions WHERE school_id = ? LIMIT 1`

	err := DB.QueryRow(query, schoolID).Scan(
		&session.ID,
		&session.Username,
		&session.SchoolID,
		&session.SchoolName,
		&session.CentralToken,
		&session.LocalToken,
		&session.TokenExpiresAt,
		&session.LastLogin,
		&session.CreatedAt,
		&session.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New("session not found")
		}
		return nil, err
	}

	return session, nil
}

// GetSessionBySchoolName 通过学校名称获取会话
func GetSessionBySchoolName(schoolName string) (*Session, error) {
	session := &Session{}
	query := `SELECT id, username, school_id, school_name, central_token, local_token, token_expires_at, last_login, created_at, updated_at
			  FROM sessions WHERE school_name = ? LIMIT 1`

	err := DB.QueryRow(query, schoolName).Scan(
		&session.ID,
		&session.Username,
		&session.SchoolID,
		&session.SchoolName,
		&session.CentralToken,
		&session.LocalToken,
		&session.TokenExpiresAt,
		&session.LastLogin,
		&session.CreatedAt,
		&session.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New("session not found")
		}
		return nil, err
	}

	return session, nil
}

// GetAllSessions 获取所有会话
func GetAllSessions() ([]Session, error) {
	query := `SELECT id, username, school_id, school_name, central_token, local_token, token_expires_at, last_login, created_at, updated_at
			  FROM sessions`

	rows, err := DB.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 解析结果
	sessions := []Session{}
	for rows.Next() {
		session := Session{}
		err := rows.Scan(
			&session.ID,
			&session.Username,
			&session.SchoolID,
			&session.SchoolName,
			&session.CentralToken,
			&session.LocalToken,
			&session.TokenExpiresAt,
			&session.LastLogin,
			&session.CreatedAt,
			&session.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		sessions = append(sessions, session)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return sessions, nil
}

// DeleteSession 删除会话
func DeleteSession(id int64) error {
	query := `DELETE FROM sessions WHERE id = ?`
	_, err := DB.Exec(query, id)
	return err
}

// InvalidateSessionToken 使会话token失效
func InvalidateSessionToken(id int64) error {
	query := `UPDATE sessions SET local_token = '', token_expires_at = ? WHERE id = ?`
	pastTime := time.Now().Add(-1 * time.Hour)
	_, err := DB.Exec(query, pastTime, id)
	return err
}
