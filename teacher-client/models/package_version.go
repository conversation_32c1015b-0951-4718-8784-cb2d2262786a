package models

import (
	"database/sql"
	"strings"
	"time"
)

// PackageVersion 包版本模型
type PackageVersion struct {
	ID                    int64     `json:"id"`
	PackageVersionID      string    `json:"packageVersionId"`
	PackagePushID         string    `json:"packagePushId"`
	ExperimentID          string    `json:"experimentId"`
	ExperimentName        string    `json:"experimentName"`        // 实验名称
	ExperimentVersionID   string    `json:"experimentVersionId"`   // 实验版本id
	ExperimentVersionName string    `json:"experimentVersionName"` // 实验版本名称
	Version               string    `json:"version"`
	VersionName           string    `json:"versionName"`
	VersionDesc           string    `json:"versionDesc"`
	FileHash              string    `json:"fileHash"`
	FilePath              string    `json:"filePath"`
	FileSize              int64     `json:"fileSize"`
	DownloadURL           string    `json:"downloadUrl"`
	SyncStatus            int       `json:"syncStatus"`
	DownloadCount         int       `json:"downloadCount"`
	SchoolID              string    `json:"schoolId"` // 学校ID
	CreatedAt             time.Time `json:"createdAt"`
	UpdatedAt             time.Time `json:"updatedAt"`
}

// GetPackageVersionByID 通过ID获取包版本
func GetPackageVersionByID(id int64) (*PackageVersion, error) {
	packageVersion := &PackageVersion{}
	query := `SELECT id, package_version_id, package_push_id, experiment_id, experiment_name, experiment_version_id,experiment_version_name,
			  version, version_name, version_desc, file_hash, file_path, file_size, download_url,
			  sync_status, download_count, school_id, created_at, updated_at
			  FROM package_versions WHERE id = ?`

	err := DB.QueryRow(query, id).Scan(
		&packageVersion.ID,
		&packageVersion.PackageVersionID,
		&packageVersion.PackagePushID,
		&packageVersion.ExperimentID,
		&packageVersion.ExperimentName,
		&packageVersion.ExperimentVersionID,
		&packageVersion.ExperimentVersionName,
		&packageVersion.Version,
		&packageVersion.VersionName,
		&packageVersion.VersionDesc,
		&packageVersion.FileHash,
		&packageVersion.FilePath,
		&packageVersion.FileSize,
		&packageVersion.DownloadURL,
		&packageVersion.SyncStatus,
		&packageVersion.DownloadCount,
		&packageVersion.SchoolID,
		&packageVersion.CreatedAt,
		&packageVersion.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return packageVersion, nil
}

// GetPackageVersionByPackageVersionID 通过包版本ID获取包版本
func GetPackageVersionByPackageVersionID(packageVersionID string) (*PackageVersion, error) {
	packageVersion := &PackageVersion{}
	query := `SELECT id, package_version_id, package_push_id, experiment_id, experiment_name, experiment_version_id,experiment_version_name,
			  version, version_name, version_desc, file_hash, file_path, file_size, download_url,
			  sync_status, download_count, school_id, created_at, updated_at
			  FROM package_versions WHERE package_version_id = ?`

	err := DB.QueryRow(query, packageVersionID).Scan(
		&packageVersion.ID,
		&packageVersion.PackageVersionID,
		&packageVersion.PackagePushID,
		&packageVersion.ExperimentID,
		&packageVersion.ExperimentName,
		&packageVersion.ExperimentVersionID,
		&packageVersion.ExperimentVersionName,
		&packageVersion.Version,
		&packageVersion.VersionName,
		&packageVersion.VersionDesc,
		&packageVersion.FileHash,
		&packageVersion.FilePath,
		&packageVersion.FileSize,
		&packageVersion.DownloadURL,
		&packageVersion.SyncStatus,
		&packageVersion.DownloadCount,
		&packageVersion.SchoolID,
		&packageVersion.CreatedAt,
		&packageVersion.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return packageVersion, nil
}

// PackageVersionQueryParams 包版本查询参数
type PackageVersionQueryParams struct {
	Page                  int    // 页码
	PageSize              int    // 每页数量
	SyncStatus            int    // 同步状态
	Keyword               string // 关键词（版本名称/版本号）
	ExperimentId          string // 实验ID
	ExperimentName        string // 实验名称
	ExperimentVersionName string // 实验版本名称
	SchoolID              string // 学校ID
}

// GetPackageVersions 获取包版本列表（仅按同步状态筛选，向后兼容）
func GetPackageVersions(page, pageSize int, syncStatus int) ([]PackageVersion, int, error) {
	params := PackageVersionQueryParams{
		Page:       page,
		PageSize:   pageSize,
		SyncStatus: syncStatus,
	}
	return GetPackageVersionsWithFilter(params)
}

// GetPackageVersionsWithFilter 获取包版本列表（支持多条件筛选）
func GetPackageVersionsWithFilter(params PackageVersionQueryParams) ([]PackageVersion, int, error) {
	// 构建查询条件
	whereClause := ""
	args := []interface{}{}
	conditions := []string{}

	// 同步状态筛选
	if params.SyncStatus >= 0 {
		conditions = append(conditions, "sync_status = ?")
		args = append(args, params.SyncStatus)
	}

	// 关键词筛选（版本名称或版本号）
	if params.Keyword != "" {
		conditions = append(conditions, "(version_name LIKE ? OR version LIKE ?)")
		args = append(args, "%"+params.Keyword+"%", "%"+params.Keyword+"%")
	}

	// 实验ID筛选
	if params.ExperimentId != "" {
		conditions = append(conditions, "experiment_id = ?")
		args = append(args, params.ExperimentId)
	}

	// 实验名称筛选
	if params.ExperimentName != "" {
		conditions = append(conditions, "experiment_name LIKE ?")
		args = append(args, "%"+params.ExperimentName+"%")
	}

	// 实验版本名称筛选
	if params.ExperimentVersionName != "" {
		conditions = append(conditions, "experiment_version_name LIKE ?")
		args = append(args, "%"+params.ExperimentVersionName+"%")
	}

	// 学校ID筛选
	if params.SchoolID != "" {
		conditions = append(conditions, "school_id = ?")
		args = append(args, params.SchoolID)
	}

	// 组合所有条件
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	// 获取总数
	countQuery := "SELECT COUNT(*) FROM package_versions" + whereClause
	var total int
	err := DB.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	// 计算分页
	offset := (params.Page - 1) * params.PageSize

	// 查询包版本
	query := `SELECT id, package_version_id, package_push_id, experiment_id, experiment_name, experiment_version_id,experiment_version_name,
			  version, version_name, version_desc, file_hash, file_path, file_size, download_url,
			  sync_status, download_count, school_id, created_at, updated_at
			  FROM package_versions` + whereClause + `
			  ORDER BY updated_at DESC
			  LIMIT ? OFFSET ?`

	args = append(args, params.PageSize, offset)

	rows, err := DB.Query(query, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	// 解析结果
	packageVersions := []PackageVersion{}
	for rows.Next() {
		packageVersion := PackageVersion{}
		err := rows.Scan(
			&packageVersion.ID,
			&packageVersion.PackageVersionID,
			&packageVersion.PackagePushID,
			&packageVersion.ExperimentID,
			&packageVersion.ExperimentName,
			&packageVersion.ExperimentVersionID,
			&packageVersion.ExperimentVersionName,
			&packageVersion.Version,
			&packageVersion.VersionName,
			&packageVersion.VersionDesc,
			&packageVersion.FileHash,
			&packageVersion.FilePath,
			&packageVersion.FileSize,
			&packageVersion.DownloadURL,
			&packageVersion.SyncStatus,
			&packageVersion.DownloadCount,
			&packageVersion.SchoolID,
			&packageVersion.CreatedAt,
			&packageVersion.UpdatedAt,
		)
		if err != nil {
			return nil, 0, err
		}
		packageVersions = append(packageVersions, packageVersion)
	}

	if err := rows.Err(); err != nil {
		return nil, 0, err
	}

	return packageVersions, total, nil
}

// CreatePackageVersion 创建包版本
func CreatePackageVersion(packageVersion *PackageVersion) error {
	query := `INSERT INTO package_versions (
			  package_version_id, package_push_id, experiment_id, experiment_name, experiment_version_id,experiment_version_name,
			  version, version_name, version_desc, file_hash, file_path, file_size,
			  download_url, sync_status, download_count, school_id, created_at, updated_at
			  ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	now := time.Now()
	packageVersion.CreatedAt = now
	packageVersion.UpdatedAt = now

	result, err := DB.Exec(
		query,
		packageVersion.PackageVersionID,
		packageVersion.PackagePushID,
		packageVersion.ExperimentID,
		packageVersion.ExperimentName,
		packageVersion.ExperimentVersionID,
		packageVersion.ExperimentVersionName,
		packageVersion.Version,
		packageVersion.VersionName,
		packageVersion.VersionDesc,
		packageVersion.FileHash,
		packageVersion.FilePath,
		packageVersion.FileSize,
		packageVersion.DownloadURL,
		packageVersion.SyncStatus,
		packageVersion.DownloadCount,
		packageVersion.SchoolID,
		packageVersion.CreatedAt,
		packageVersion.UpdatedAt,
	)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}

	packageVersion.ID = id
	return nil
}

// UpdatePackageVersion 更新包版本
func UpdatePackageVersion(packageVersion *PackageVersion) error {
	query := `UPDATE package_versions SET
			  package_push_id = ?,
			  experiment_id = ?,
			  experiment_name = ?,
			  experiment_version_id = ?,
			  version = ?,
			  version_name = ?,
			  version_desc = ?,
			  file_hash = ?,
			  file_path = ?,
			  file_size = ?,
			  download_url = ?,
			  sync_status = ?,
			  download_count = ?,
			  school_id = ?,
			  updated_at = ?
			  WHERE id = ?`

	packageVersion.UpdatedAt = time.Now()

	_, err := DB.Exec(
		query,
		packageVersion.PackagePushID,
		packageVersion.ExperimentID,
		packageVersion.ExperimentName,
		packageVersion.ExperimentVersionID,
		packageVersion.ExperimentVersionName,
		packageVersion.Version,
		packageVersion.VersionName,
		packageVersion.VersionDesc,
		packageVersion.FileHash,
		packageVersion.FilePath,
		packageVersion.FileSize,
		packageVersion.DownloadURL,
		packageVersion.SyncStatus,
		packageVersion.DownloadCount,
		packageVersion.SchoolID,
		packageVersion.UpdatedAt,
		packageVersion.ID,
	)

	return err
}

// UpdatePackageVersionSyncStatus 更新包版本同步状态
func UpdatePackageVersionSyncStatus(id int64, syncStatus int, filePath string, fileSize int64) error {
	query := `UPDATE package_versions SET
			  sync_status = ?,
			  file_path = ?,
			  file_size = ?,
			  updated_at = ?
			  WHERE id = ?`

	_, err := DB.Exec(
		query,
		syncStatus,
		filePath,
		fileSize,
		time.Now(),
		id,
	)

	return err
}

// GetRecentPackageVersions 获取最近的包版本
func GetRecentPackageVersions(limit int) ([]PackageVersion, error) {
	query := `SELECT id, package_version_id, package_push_id, experiment_id, experiment_name, experiment_version_id,
			  version, version_name, version_desc, file_hash, file_path, file_size, download_url,
			  sync_status, download_count, school_id, created_at, updated_at
			  FROM package_versions
			  ORDER BY updated_at DESC
			  LIMIT ?`

	rows, err := DB.Query(query, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 解析结果
	packageVersions := []PackageVersion{}
	for rows.Next() {
		packageVersion := PackageVersion{}
		err := rows.Scan(
			&packageVersion.ID,
			&packageVersion.PackageVersionID,
			&packageVersion.PackagePushID,
			&packageVersion.ExperimentID,
			&packageVersion.ExperimentName,
			&packageVersion.ExperimentVersionID,
			&packageVersion.ExperimentVersionName,
			&packageVersion.Version,
			&packageVersion.VersionName,
			&packageVersion.VersionDesc,
			&packageVersion.FileHash,
			&packageVersion.FilePath,
			&packageVersion.FileSize,
			&packageVersion.DownloadURL,
			&packageVersion.SyncStatus,
			&packageVersion.DownloadCount,
			&packageVersion.SchoolID,
			&packageVersion.CreatedAt,
			&packageVersion.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		packageVersions = append(packageVersions, packageVersion)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return packageVersions, nil
}

// GetPackageVersionsByExperimentVersionID 根据实验版本ID获取所有包版本，按PackageVersionID倒序排列
func GetPackageVersionsByExperimentVersionID(experimentVersionID string) ([]PackageVersion, error) {
	query := `SELECT id, package_version_id, package_push_id, experiment_id, experiment_name, experiment_version_id, experiment_version_name,
			  version, version_name, version_desc, file_hash, file_path, file_size, download_url,
			  sync_status, download_count, school_id, created_at, updated_at
			  FROM package_versions
			  WHERE experiment_version_id = ?
			  ORDER BY CAST(package_version_id AS INTEGER) DESC`

	rows, err := DB.Query(query, experimentVersionID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 解析结果
	packageVersions := []PackageVersion{}
	for rows.Next() {
		packageVersion := PackageVersion{}
		err := rows.Scan(
			&packageVersion.ID,
			&packageVersion.PackageVersionID,
			&packageVersion.PackagePushID,
			&packageVersion.ExperimentID,
			&packageVersion.ExperimentName,
			&packageVersion.ExperimentVersionID,
			&packageVersion.ExperimentVersionName,
			&packageVersion.Version,
			&packageVersion.VersionName,
			&packageVersion.VersionDesc,
			&packageVersion.FileHash,
			&packageVersion.FilePath,
			&packageVersion.FileSize,
			&packageVersion.DownloadURL,
			&packageVersion.SyncStatus,
			&packageVersion.DownloadCount,
			&packageVersion.SchoolID,
			&packageVersion.CreatedAt,
			&packageVersion.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		packageVersions = append(packageVersions, packageVersion)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return packageVersions, nil
}

// ClearPackageVersionFilePaths 清空包版本的文件路径，但保留记录
func ClearPackageVersionFilePaths(id int64) error {
	query := `UPDATE package_versions SET
			  file_path = ''
			  WHERE id = ?`
	_, err := DB.Exec(query, id)
	return err
}
