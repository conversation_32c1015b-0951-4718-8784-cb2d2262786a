package models

import (
	"time"
)

// 同步日志状态常量
const (
	LogStatusSuccess = 0 // 成功
	LogStatusRunning = 1 // 进行中
	LogStatusFailed  = 2 // 失败
)

// SyncLog 同步日志模型
type SyncLog struct {
	ID                  int64     `json:"id"`
	PackageID           int64     `json:"packageId"`
	ExperimentVersionID string    `json:"experimentVersionId"`
	Action              string    `json:"action"`
	Status              int       `json:"status"`
	Message             string    `json:"message"`
	CreatedAt           time.Time `json:"createdAt"`
}

// AddSyncLog 添加同步日志（兼容旧版本，基于包ID）
func AddSyncLog(packageID int64, action string, status int, message string) error {
	// 获取包的实验版本ID
	pkg, err := GetPackageByID(packageID)
	if err != nil || pkg == nil {
		// 如果获取失败，使用空的实验版本ID
		return AddSyncLogByExperimentVersion("", action, status, message)
	}

	query := `INSERT INTO sync_logs (package_id, experiment_version_id, action, status, message, created_at)
			  VALUES (?, ?, ?, ?, ?, ?)`

	_, err = DB.Exec(query, packageID, pkg.ExperimentVersionID, action, status, message, time.Now())
	return err
}

// AddSyncLogByExperimentVersion 基于实验版本ID添加同步日志
func AddSyncLogByExperimentVersion(experimentVersionID string, action string, status int, message string) error {
	query := `INSERT INTO sync_logs (package_id, experiment_version_id, action, status, message, created_at)
			  VALUES (?, ?, ?, ?, ?, ?)`

	_, err := DB.Exec(query, 0, experimentVersionID, action, status, message, time.Now())
	return err
}

// GetSyncLogsByPackageID 获取指定包的同步日志
func GetSyncLogsByPackageID(packageID int64) ([]*SyncLog, error) {
	query := `SELECT id, package_id, COALESCE(experiment_version_id, '') as experiment_version_id, action, status, message, created_at
			  FROM sync_logs
			  WHERE package_id = ?
			  ORDER BY created_at DESC`

	rows, err := DB.Query(query, packageID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	logs := []*SyncLog{}
	for rows.Next() {
		log := &SyncLog{}
		err := rows.Scan(
			&log.ID,
			&log.PackageID,
			&log.ExperimentVersionID,
			&log.Action,
			&log.Status,
			&log.Message,
			&log.CreatedAt,
		)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return logs, nil
}

// GetSyncLogsByExperimentVersionID 获取指定实验版本的同步日志
func GetSyncLogsByExperimentVersionID(experimentVersionID string) ([]*SyncLog, error) {
	query := `SELECT id, package_id, COALESCE(experiment_version_id, '') as experiment_version_id, action, status, message, created_at
			  FROM sync_logs
			  WHERE experiment_version_id = ?
			  ORDER BY created_at DESC`

	rows, err := DB.Query(query, experimentVersionID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	logs := []*SyncLog{}
	for rows.Next() {
		log := &SyncLog{}
		err := rows.Scan(
			&log.ID,
			&log.PackageID,
			&log.ExperimentVersionID,
			&log.Action,
			&log.Status,
			&log.Message,
			&log.CreatedAt,
		)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return logs, nil
}

// GetAllSyncLogs 获取所有同步日志
func GetAllSyncLogs() ([]*SyncLog, error) {
	query := `SELECT id, package_id, COALESCE(experiment_version_id, '') as experiment_version_id, action, status, message, created_at
			  FROM sync_logs
			  ORDER BY created_at DESC`

	rows, err := DB.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	logs := []*SyncLog{}
	for rows.Next() {
		log := &SyncLog{}
		err := rows.Scan(
			&log.ID,
			&log.PackageID,
			&log.ExperimentVersionID,
			&log.Action,
			&log.Status,
			&log.Message,
			&log.CreatedAt,
		)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return logs, nil
}

// GetRecentSyncLogs 获取最近的同步日志
func GetRecentSyncLogs(limit int) ([]*SyncLog, error) {
	query := `SELECT id, package_id, COALESCE(experiment_version_id, '') as experiment_version_id, action, status, message, created_at
			  FROM sync_logs
			  ORDER BY created_at DESC
			  LIMIT ?`

	rows, err := DB.Query(query, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	logs := []*SyncLog{}
	for rows.Next() {
		log := &SyncLog{}
		err := rows.Scan(
			&log.ID,
			&log.PackageID,
			&log.ExperimentVersionID,
			&log.Action,
			&log.Status,
			&log.Message,
			&log.CreatedAt,
		)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return logs, nil
}

// GetSyncLogsByAction 获取指定操作的同步日志
func GetSyncLogsByAction(action string) ([]*SyncLog, error) {
	query := `SELECT id, package_id, COALESCE(experiment_version_id, '') as experiment_version_id, action, status, message, created_at
			  FROM sync_logs
			  WHERE action = ?
			  ORDER BY created_at DESC`

	rows, err := DB.Query(query, action)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	logs := []*SyncLog{}
	for rows.Next() {
		log := &SyncLog{}
		err := rows.Scan(
			&log.ID,
			&log.PackageID,
			&log.ExperimentVersionID,
			&log.Action,
			&log.Status,
			&log.Message,
			&log.CreatedAt,
		)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return logs, nil
}

// GetSyncLogsByStatus 获取指定状态的同步日志
func GetSyncLogsByStatus(status int) ([]*SyncLog, error) {
	query := `SELECT id, package_id, COALESCE(experiment_version_id, '') as experiment_version_id, action, status, message, created_at
			  FROM sync_logs
			  WHERE status = ?
			  ORDER BY created_at DESC`

	rows, err := DB.Query(query, status)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	logs := []*SyncLog{}
	for rows.Next() {
		log := &SyncLog{}
		err := rows.Scan(
			&log.ID,
			&log.PackageID,
			&log.ExperimentVersionID,
			&log.Action,
			&log.Status,
			&log.Message,
			&log.CreatedAt,
		)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return logs, nil
}

// GetSyncLogsByActionAndStatus 获取指定操作和状态的同步日志，支持分页
func GetSyncLogsByActionAndStatus(action string, status int, page, pageSize int) ([]*SyncLog, error) {
	query := `SELECT id, package_id, COALESCE(experiment_version_id, '') as experiment_version_id, action, status, message, created_at
			  FROM sync_logs
			  WHERE action = ? AND status = ?
			  ORDER BY created_at DESC
			  LIMIT ? OFFSET ?`

	offset := (page - 1) * pageSize
	rows, err := DB.Query(query, action, status, pageSize, offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	logs := []*SyncLog{}
	for rows.Next() {
		log := &SyncLog{}
		err := rows.Scan(
			&log.ID,
			&log.PackageID,
			&log.ExperimentVersionID,
			&log.Action,
			&log.Status,
			&log.Message,
			&log.CreatedAt,
		)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return logs, nil
}

// CountSyncLogsByActionAndStatus 统计指定操作和状态的同步日志数量
func CountSyncLogsByActionAndStatus(action string, status int) (int, error) {
	query := `SELECT COUNT(*) FROM sync_logs WHERE action = ? AND status = ?`

	var count int
	err := DB.QueryRow(query, action, status).Scan(&count)
	if err != nil {
		return 0, err
	}

	return count, nil
}
