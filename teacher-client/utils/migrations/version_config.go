package migrations

import "fmt"

// VersionConfig 版本配置
type VersionConfig struct {
	CurrentVersion string               `json:"currentVersion"`
	VersionPath    []string             `json:"versionPath"`
	MigrationMap   map[string]Migration `json:"-"`
}

// GetVersionConfig 获取版本配置 - 这是唯一需要修改的地方
func GetVersionConfig() *VersionConfig {
	// ===== 新增版本时，只需要修改这里 =====

	// 1. 定义版本升级路径（按时间顺序，最后一个就是当前版本）
	versionPath := []string{
		"25052901", // 初始版本
		"25053001", // 新增版本
		// 新版本在这里继续添加...
	}

	// 2. 注册迁移实例（每个版本对应一个迁移）
	migrationMap := map[string]Migration{
		"25053001": &Migration_25052901_to_25053001{},
		// 新迁移在这里继续添加...
	}

	// ===== 修改结束 =====

	// 自动获取当前版本（versionPath的最后一个元素）
	currentVersion := versionPath[len(versionPath)-1]
	//currentVersion = "25052901"

	return &VersionConfig{
		CurrentVersion: currentVersion,
		VersionPath:    versionPath,
		MigrationMap:   migrationMap,
	}
}

// GetCurrentVersion 获取当前程序版本
func GetCurrentVersion() string {
	return GetVersionConfig().CurrentVersion
}

// GetMigrations 获取所有迁移
func GetMigrations() []Migration {
	config := GetVersionConfig()
	migrations := make([]Migration, 0, len(config.MigrationMap))

	// 按版本路径顺序返回迁移
	for i := 1; i < len(config.VersionPath); i++ {
		version := config.VersionPath[i]
		if migration, exists := config.MigrationMap[version]; exists {
			migrations = append(migrations, migration)
		}
	}

	return migrations
}

// ValidateConfig 验证配置一致性
func ValidateConfig() error {
	config := GetVersionConfig()

	// 检查版本路径不为空
	if len(config.VersionPath) == 0 {
		return fmt.Errorf("版本路径不能为空")
	}

	// 检查当前版本是否在版本路径中
	found := false
	for _, version := range config.VersionPath {
		if version == config.CurrentVersion {
			found = true
			break
		}
	}
	if !found {
		return fmt.Errorf("当前版本 %s 不在版本路径中", config.CurrentVersion)
	}

	// 检查迁移映射的一致性
	for version := range config.MigrationMap {
		found := false
		for _, pathVersion := range config.VersionPath {
			if pathVersion == version {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("迁移版本 %s 不在版本路径中", version)
		}
	}

	return nil
}
