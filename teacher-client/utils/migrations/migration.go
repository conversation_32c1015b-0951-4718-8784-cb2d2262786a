package migrations

import (
	"database/sql"
)

// Logger 日志接口
type Logger interface {
	Info(module, action, message string)
	Error(module, action, message string)
	Warn(module, action, message string)
}

// MigrationContext 迁移上下文
type MigrationContext struct {
	DB        *sql.DB
	Logger    Logger
	DataDir   string
	ConfigDir string
	BackupDir string
	TempDir   string
}

// Migration 迁移接口
type Migration interface {
	// Version 返回目标版本号
	Version() string
	
	// Description 返回迁移描述
	Description() string
	
	// Execute 执行迁移
	Execute(ctx *MigrationContext) error
	
	// Rollback 回滚迁移
	Rollback(ctx *MigrationContext) error
}
