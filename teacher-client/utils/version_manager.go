package utils

import (
	"database/sql"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"teacher-client/utils/migrations"
	"time"
)

// VersionManager 版本管理器
type VersionManager struct {
	currentVersion string
	migrations     []migrations.Migration
	ctx            *migrations.MigrationContext
	logger         *Logger
	db             *sql.DB
}

// LoggerAdapter 适配器，将utils.Logger适配为migrations.Logger
type LoggerAdapter struct {
	logger *Logger
}

func (la *LoggerAdapter) Info(module, action, message string) {
	la.logger.Info(module, action, message)
}

func (la *LoggerAdapter) Error(module, action, message string) {
	la.logger.Error(module, action, message)
}

func (la *LoggerAdapter) Warn(module, action, message string) {
	la.logger.Warn(module, action, message)
}

// NewVersionManager 创建版本管理器
func NewVersionManager(currentVersion string, logger *Logger, db *sql.DB) *VersionManager {
	loggerAdapter := &LoggerAdapter{logger: logger}

	return &VersionManager{
		currentVersion: currentVersion,
		logger:         logger,
		db:             db,
		ctx: &migrations.MigrationContext{
			DB:        db,
			Logger:    loggerAdapter,
			DataDir:   "./data",
			ConfigDir: "./config",
			BackupDir: "./backup",
			TempDir:   "./temp",
		},
	}
}

// RegisterMigrations 注册所有迁移
func (vm *VersionManager) RegisterMigrations() {
	// 使用集中配置获取所有迁移
	vm.migrations = migrations.GetMigrations()
}

// CheckAndMigrate 检查并执行迁移
func (vm *VersionManager) CheckAndMigrate() error {
	// 注册所有迁移
	vm.RegisterMigrations()

	// 获取数据库中记录的版本
	dbVersion, err := vm.getCurrentVersion()
	if err != nil {
		return fmt.Errorf("获取数据库版本失败: %v", err)
	}

	vm.logger.Info("VersionManager", "CheckAndMigrate",
		fmt.Sprintf("数据库版本: %s, 程序版本: %s", dbVersion, vm.currentVersion))

	// 如果版本相同，无需迁移
	if dbVersion == vm.currentVersion {
		vm.logger.Info("VersionManager", "CheckAndMigrate", "版本一致，无需迁移")
		return nil
	}

	// 执行线性迁移
	return vm.executeLinearMigration(dbVersion, vm.currentVersion)
}

// executeLinearMigration 执行线性迁移
func (vm *VersionManager) executeLinearMigration(fromVersion, toVersion string) error {
	vm.logger.Info("VersionManager", "Migration",
		fmt.Sprintf("开始线性迁移: %s → %s", fromVersion, toVersion))

	// 创建数据库备份
	if err := vm.backupDatabase(); err != nil {
		vm.logger.Warn("VersionManager", "Migration",
			fmt.Sprintf("创建数据库备份失败: %v", err))
	}

	// 获取需要执行的迁移
	migrationsToRun, err := vm.getMigrationsToRun(fromVersion, toVersion)
	if err != nil {
		return fmt.Errorf("获取迁移列表失败: %v", err)
	}

	if len(migrationsToRun) == 0 {
		vm.logger.Info("VersionManager", "Migration", "没有需要执行的迁移")
		return nil
	}

	vm.logger.Info("VersionManager", "Migration",
		fmt.Sprintf("需要执行 %d 个迁移", len(migrationsToRun)))

	// 逐步执行迁移
	for i, migration := range migrationsToRun {
		vm.logger.Info("VersionManager", "Migration",
			fmt.Sprintf("执行迁移 %d/%d: %s", i+1, len(migrationsToRun), migration.Version()))

		if err := vm.executeMigration(migration); err != nil {
			vm.logger.Error("VersionManager", "Migration",
				fmt.Sprintf("迁移失败: %s - %v", migration.Version(), err))

			// 记录失败日志
			vm.addMigrationLog(fromVersion, migration.Version(),
				migration.Description(), "failed", err.Error())

			return fmt.Errorf("迁移失败 %s: %v", migration.Version(), err)
		}

		// 更新版本记录
		if err := vm.setCurrentVersion(migration.Version()); err != nil {
			return fmt.Errorf("更新版本记录失败: %v", err)
		}

		// 记录成功日志
		vm.addMigrationLog(fromVersion, migration.Version(),
			migration.Description(), "success")

		vm.logger.Info("VersionManager", "Migration",
			fmt.Sprintf("迁移完成: %s", migration.Version()))
	}

	vm.logger.Info("VersionManager", "Migration",
		fmt.Sprintf("线性迁移完成: %s → %s", fromVersion, toVersion))

	return nil
}

// executeMigration 执行单个迁移
func (vm *VersionManager) executeMigration(migration migrations.Migration) error {
	vm.logger.Info("VersionManager", "Migration",
		fmt.Sprintf("开始执行迁移: %s - %s", migration.Version(), migration.Description()))

	if err := migration.Execute(vm.ctx); err != nil {
		// 记录失败日志
		vm.addMigrationLog("", migration.Version(),
			migration.Description(), "failed", err.Error())

		// 尝试回滚
		if rollbackErr := migration.Rollback(vm.ctx); rollbackErr != nil {
			vm.logger.Error("VersionManager", "Migration",
				fmt.Sprintf("回滚失败: %v", rollbackErr))
		}

		return fmt.Errorf("迁移失败: %v", err)
	}

	return nil
}

// getMigrationsToRun 获取需要执行的迁移列表
func (vm *VersionManager) getMigrationsToRun(fromVersion, toVersion string) ([]migrations.Migration, error) {
	config := migrations.GetVersionConfig()

	// 找到起始版本和目标版本在版本路径中的位置
	fromIndex := -1
	toIndex := -1

	for i, version := range config.VersionPath {
		if version == fromVersion {
			fromIndex = i
		}
		if version == toVersion {
			toIndex = i
		}
	}

	if fromIndex == -1 {
		return nil, fmt.Errorf("起始版本 %s 不在版本路径中", fromVersion)
	}
	if toIndex == -1 {
		return nil, fmt.Errorf("目标版本 %s 不在版本路径中", toVersion)
	}
	if fromIndex >= toIndex {
		return nil, fmt.Errorf("起始版本 %s 必须早于目标版本 %s", fromVersion, toVersion)
	}

	// 获取需要执行的迁移
	var migrationsToRun []migrations.Migration
	for i := fromIndex + 1; i <= toIndex; i++ {
		version := config.VersionPath[i]
		if migration, exists := config.MigrationMap[version]; exists {
			migrationsToRun = append(migrationsToRun, migration)
		}
	}

	return migrationsToRun, nil
}

// getCurrentVersion 获取数据库中记录的当前版本
func (vm *VersionManager) getCurrentVersion() (string, error) {
	var version string
	query := `SELECT version FROM app_versions WHERE is_current = 1 LIMIT 1`
	err := vm.db.QueryRow(query).Scan(&version)

	if err == sql.ErrNoRows {
		// 如果没有记录，说明是首次运行，返回初始版本
		config := migrations.GetVersionConfig()
		if len(config.VersionPath) > 0 {
			initialVersion := config.VersionPath[0]
			// 插入初始版本记录
			if err := vm.setCurrentVersion(initialVersion); err != nil {
				return "", fmt.Errorf("设置初始版本失败: %v", err)
			}
			return initialVersion, nil
		}
		return "", fmt.Errorf("版本路径为空")
	}

	if err != nil {
		return "", fmt.Errorf("查询当前版本失败: %v", err)
	}

	return version, nil
}

// setCurrentVersion 设置当前版本
func (vm *VersionManager) setCurrentVersion(version string) error {
	tx, err := vm.db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 将所有版本标记为非当前
	_, err = tx.Exec("UPDATE app_versions SET is_current = 0")
	if err != nil {
		return err
	}

	// 检查版本是否已存在
	var count int
	err = tx.QueryRow("SELECT COUNT(*) FROM app_versions WHERE version = ?", version).Scan(&count)
	if err != nil {
		return err
	}

	if count > 0 {
		// 更新现有版本为当前版本
		_, err = tx.Exec("UPDATE app_versions SET is_current = 1 WHERE version = ?", version)
	} else {
		// 插入新版本记录
		_, err = tx.Exec(`
			INSERT INTO app_versions (version, is_current, installed_at)
			VALUES (?, 1, ?)
		`, version, time.Now())
	}

	if err != nil {
		return err
	}

	return tx.Commit()
}

// addMigrationLog 添加迁移日志
func (vm *VersionManager) addMigrationLog(fromVersion, toVersion, description, status string, errorMessage ...string) {
	var errMsg string
	if len(errorMessage) > 0 {
		errMsg = errorMessage[0]
	}

	query := `
		INSERT INTO migration_logs (from_version, to_version, description, status, error_message, executed_at)
		VALUES (?, ?, ?, ?, ?, ?)
	`

	_, err := vm.db.Exec(query, fromVersion, toVersion, description, status, errMsg, time.Now())
	if err != nil {
		vm.logger.Error("VersionManager", "addMigrationLog",
			fmt.Sprintf("添加迁移日志失败: %v", err))
	}
}

// backupDatabase 备份数据库
func (vm *VersionManager) backupDatabase() error {
	// 确保备份目录存在
	if err := EnsureDirectoryExists(vm.ctx.BackupDir); err != nil {
		return fmt.Errorf("创建备份目录失败: %v", err)
	}

	// 生成备份文件名
	timestamp := time.Now().Format("20060102_150405")
	backupPath := filepath.Join(vm.ctx.BackupDir, fmt.Sprintf("student-client_%s.db", timestamp))

	// 复制数据库文件
	dbPath := filepath.Join(vm.ctx.DataDir, "student-client.db")
	if err := vm.copyFile(dbPath, backupPath); err != nil {
		return fmt.Errorf("复制数据库文件失败: %v", err)
	}

	vm.logger.Info("VersionManager", "backupDatabase",
		fmt.Sprintf("数据库备份完成: %s", backupPath))

	return nil
}

// copyFile 复制文件
func (vm *VersionManager) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	return err
}
