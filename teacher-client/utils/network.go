package utils

import (
	"errors"
	"net"
	"strings"
)

// GetLocalIP 获取本机IP地址
func GetLocalIP() (string, error) {
	// 获取所有网络接口
	interfaces, err := net.Interfaces()
	if err != nil {
		return "", err
	}

	// 遍历所有网络接口
	for _, iface := range interfaces {
		// 忽略回环接口和未启用的接口
		if iface.Flags&net.FlagLoopback != 0 || iface.Flags&net.FlagUp == 0 {
			continue
		}

		// 获取接口的地址
		addrs, err := iface.Addrs()
		if err != nil {
			continue
		}

		// 遍历所有地址
		for _, addr := range addrs {
			// 检查是否为IP地址
			var ip net.IP
			switch v := addr.(type) {
			case *net.IPNet:
				ip = v.IP
			case *net.IPAddr:
				ip = v.IP
			}

			// 忽略IPv6地址和回环地址
			if ip == nil || ip.IsLoopback() || ip.To4() == nil {
				continue
			}

			// 返回第一个有效的IPv4地址
			return ip.String(), nil
		}
	}

	return "", errors.New("未找到有效的IP地址")
}

// GetMacAddress 获取本机MAC地址
func GetMacAddress() (string, error) {
	// 获取所有网络接口
	interfaces, err := net.Interfaces()
	if err != nil {
		return "", err
	}

	// 遍历所有网络接口
	for _, iface := range interfaces {
		// 忽略回环接口和未启用的接口
		if iface.Flags&net.FlagLoopback != 0 || iface.Flags&net.FlagUp == 0 {
			continue
		}

		// 获取MAC地址
		macAddr := iface.HardwareAddr.String()
		if macAddr != "" {
			// 格式化MAC地址
			return strings.ToUpper(macAddr), nil
		}
	}

	return "", errors.New("未找到有效的MAC地址")
}

// GetNetworkInfo 获取网络信息（MAC地址和IP地址）
func GetNetworkInfo() (mac, ip string, err error) {
	mac, err = GetMacAddress()
	if err != nil {
		return "", "", err
	}

	ip, err = GetLocalIP()
	if err != nil {
		return "", "", err
	}

	return mac, ip, nil
}
