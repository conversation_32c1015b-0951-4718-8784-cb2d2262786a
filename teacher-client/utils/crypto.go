package utils

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"

	"golang.org/x/crypto/bcrypt"
)

// HashPassword 使用bcrypt对密码进行哈希
func HashPassword(password string) (string, error) {
	// 生成bcrypt哈希
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	
	// 返回哈希的字符串表示
	return string(hashedBytes), nil
}

// CheckPasswordHash 验证密码与哈希是否匹配
func CheckPasswordHash(password, hash string) bool {
	// 比较密码与哈希
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// GenerateRandomToken 生成随机令牌
func GenerateRandomToken(length int) (string, error) {
	// 生成随机字节
	b := make([]byte, length)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	
	// 转换为base64字符串
	return base64.URLEncoding.EncodeToString(b), nil
}

// GenerateSecureKey 生成安全密钥
func GenerateSecureKey() (string, error) {
	// 生成32字节的随机密钥
	key, err := GenerateRandomToken(32)
	if err != nil {
		return "", fmt.Errorf("failed to generate secure key: %v", err)
	}
	
	return key, nil
}
