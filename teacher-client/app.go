package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/wailsapp/wails/v2/pkg/runtime"

	"teacher-client/models"
	"teacher-client/services"
	"teacher-client/utils"
)

// App 应用结构体
type App struct {
	ctx             context.Context
	extractServices map[int64]*services.ExtractService // 解压服务管理器
	extractMutex    sync.RWMutex                       // 解压服务锁
	authService     *services.AuthService              // 认证服务
}

// NewApp 创建新的应用实例
func NewApp() *App {
	// 优先从环境变量获取中央服务器URL
	centralURL := os.Getenv("CENTRAL_API_URL")
	if centralURL == "" {
		// 如果环境变量没有，从配置中获取
		if savedURL, err := models.GetConfig("central_url"); err == nil && savedURL != "" {
			centralURL = savedURL
		} else {
			// 最后使用默认地址
			centralURL = "http://localhost:8900"
		}
	}

	// 保存到配置中，确保一致性
	models.SetConfig("central_url", centralURL)

	return &App{
		extractServices: make(map[int64]*services.ExtractService),
		authService:     services.NewAuthService(centralURL),
	}
}

// startup 应用启动时调用
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx

	// 确保必要的目录存在
	ensureDirectories()

	// 初始化日志系统
	if err := utils.InitLogger(); err != nil {
		fmt.Printf("初始化日志系统失败: %v\n", err)
	}

	log.Println("应用启动")

	// 初始化数据库
	if err := models.InitDB(); err != nil {
		log.Printf("初始化数据库失败: %v", err)
	}

	// 设置更新服务的Wails上下文
	services.SetWailsContext(ctx)

	// 启动后延迟检查更新（给前端时间加载）
	go func() {
		time.Sleep(10 * time.Second) // 等待Wails完全启动后10秒
		a.checkUpdateOnStartup()
	}()
}

// shutdown 应用关闭时调用
func (a *App) shutdown(ctx context.Context) {
	log.Println("应用关闭")

	// 关闭数据库连接
	models.CloseDB()

	// 关闭日志系统
	utils.CloseLogger()
}

// Greet 测试方法，可以从前端调用
func (a *App) Greet(name string) string {
	return fmt.Sprintf("你好 %s, 欢迎使用教师端包管理系统!", name)
}

// ===== 认证相关API =====

// Login 教师登录
func (a *App) Login(schoolName, username, password string) map[string]interface{} {
	resp, err := a.authService.Login(schoolName, username, password)
	if err != nil {
		return map[string]interface{}{
			"success": false,
			"message": err.Error(),
		}
	}

	return map[string]interface{}{
		"success": resp.Success,
		"message": resp.Message,
		"data": map[string]interface{}{
			"schoolId":   resp.Data.SchoolID,
			"schoolName": resp.Data.SchoolName,
			"userId":     resp.Data.UserID,
			"username":   resp.Data.Username,
			"userType":   resp.Data.UserType,
		},
	}
}

// Logout 退出登录
func (a *App) Logout() map[string]interface{} {
	err := a.authService.Logout()
	if err != nil {
		return map[string]interface{}{
			"success": false,
			"message": err.Error(),
		}
	}

	return map[string]interface{}{
		"success": true,
		"message": "退出登录成功",
	}
}

// IsLoggedIn 检查是否已登录
func (a *App) IsLoggedIn() bool {
	return a.authService.IsLoggedIn()
}

// GetAuthInfo 获取认证信息
func (a *App) GetAuthInfo() map[string]interface{} {
	if !a.authService.IsLoggedIn() {
		return map[string]interface{}{
			"isLoggedIn": false,
		}
	}

	schoolID, schoolName := a.authService.GetSchoolInfo()
	userID, username := a.authService.GetUserInfo()

	return map[string]interface{}{
		"isLoggedIn": true,
		"schoolId":   schoolID,
		"schoolName": schoolName,
		"userId":     userID,
		"username":   username,
	}
}

// ValidateAuth 验证认证状态
func (a *App) ValidateAuth() map[string]interface{} {
	err := a.authService.ValidateToken()
	if err != nil {
		return map[string]interface{}{
			"valid":   false,
			"message": err.Error(),
		}
	}

	return map[string]interface{}{
		"valid":   true,
		"message": "认证有效",
	}
}

// GetServerStatus 获取中央端服务器状态
func (a *App) GetServerStatus(centralURL string) map[string]interface{} {
	// 如果提供了中央端URL，则更新配置并重新创建认证服务
	if centralURL != "" {
		if err := models.SetConfig("central_url", centralURL); err != nil {
			return map[string]interface{}{
				"status":  "error",
				"message": "保存中央端地址失败",
			}
		}
		// 更新认证服务的中央端URL
		a.authService = services.NewAuthService(centralURL)
	}
	// 如果没有提供URL，使用当前的认证服务，不要重新创建

	// 检查是否已登录
	if !a.authService.IsLoggedIn() {
		// 先尝试连接服务器，确保服务器可用
		syncService := services.NewCentralSyncService(a.authService, a.authService.GetCentralURL())
		_, err := syncService.CheckServerStatus()
		if err != nil {
			// 连接失败，检查是否有已下载的包
			packages, pkgErr := models.GetAllPackages()
			if pkgErr == nil && len(packages) > 0 {
				// 有已下载的包，返回离线状态但允许使用
				return map[string]interface{}{
					"status":    "offline",
					"message":   "中央端服务器连接失败，使用离线模式",
					"allowSkip": true, // 允许跳过连接
				}
			}

			// 没有可用包，需要连接服务器
			return map[string]interface{}{
				"status":    "offline",
				"message":   "中央端服务器连接失败",
				"allowSkip": false, // 不允许跳过连接
			}
		}

		// 服务器连接正常，但需要登录
		return map[string]interface{}{
			"status":      "need_login",
			"message":     "需要登录",
			"requireAuth": true,
		}
	}

	// 创建中央端同步服务
	syncService := services.NewCentralSyncService(a.authService, a.authService.GetCentralURL())

	// 检查中央端服务器状态
	status, err := syncService.CheckServerStatus()
	if err != nil {
		// 检查是否有本地包可以离线使用
		packages, pkgErr := models.GetAllPackages()
		if pkgErr == nil && len(packages) > 0 {
			// 有已下载的包，返回离线状态但允许使用
			return map[string]interface{}{
				"status":    "offline",
				"message":   "中央端服务器连接失败，使用离线模式",
				"allowSkip": true, // 允许跳过连接
			}
		}

		// 没有可用包，需要连接服务器
		return map[string]interface{}{
			"status":    "offline",
			"message":   "中央端服务器连接失败",
			"allowSkip": false, // 不允许跳过连接
		}
	}

	// 连接成功，同步包信息
	go a.syncPackagesFromCentral()

	return map[string]interface{}{
		"status":  "online",
		"message": "中央端服务器连接正常",
		"data":    status,
	}
}

// syncPackagesFromCentral 从中央端同步包列表
func (a *App) syncPackagesFromCentral() {
	utils.LogPrintln("开始从中央端同步包列表")

	if !a.authService.IsLoggedIn() {
		utils.LogPrintf("未登录，无法同步包列表")
		return
	}

	// 创建中央端同步服务
	syncService := services.NewCentralSyncService(a.authService, a.authService.GetCentralURL())

	// 获取远程包列表
	utils.LogPrintln("正在从中央端获取包列表...")
	remotePackages, err := syncService.GetPackages()
	if err != nil {
		utils.LogPrintf("从中央端获取包列表失败: %v", err)
		return
	}

	// 获取本地包列表
	utils.LogPrintln("正在获取本地包列表...")
	localPackages, err := models.GetAllPackages()
	if err != nil {
		utils.LogPrintf("获取本地包列表失败: %v", err)
		return
	}

	// 按实验版本ID分组本地包
	localExperimentVersionMap := make(map[string][]*models.Package)
	for _, pkg := range localPackages {
		localExperimentVersionMap[pkg.ExperimentVersionID] = append(localExperimentVersionMap[pkg.ExperimentVersionID], pkg)
	}

	// 同步包列表
	for _, remotePkg := range remotePackages {
		utils.LogPrintf("处理远程包: %s, 实验版本ID: %s, 包版本ID: %s",
			remotePkg.ExperimentName, remotePkg.ExperimentVersionID, remotePkg.PackageVersionID)

		// 检查该实验版本是否已存在本地包
		localPkgs, experimentExists := localExperimentVersionMap[remotePkg.ExperimentVersionID]

		if experimentExists {
			// 实验版本已存在，检查是否有相同的包版本ID
			var existingPkg *models.Package
			for _, pkg := range localPkgs {
				if pkg.PackageVersionID == remotePkg.PackageVersionID {
					existingPkg = pkg
					break
				}
			}

			if existingPkg != nil {
				// 相同包版本已存在，检查是否需要更新信息
				if existingPkg.Version != remotePkg.Version || existingPkg.FileHash != remotePkg.FileHash {
					utils.LogPrintf("更新包信息: %s, 版本: %s -> %s",
						remotePkg.ExperimentName, existingPkg.Version, remotePkg.Version)

					// 更新包信息
					existingPkg.Version = remotePkg.Version
					existingPkg.VersionName = remotePkg.VersionName
					existingPkg.VersionDesc = remotePkg.VersionDesc
					existingPkg.FileHash = remotePkg.FileHash
					existingPkg.FileSize = remotePkg.FileSize
					existingPkg.DownloadURL = remotePkg.DownloadURL
					existingPkg.UpdatedAt = time.Now()

					// 保存更新
					if err := models.UpdatePackage(existingPkg); err != nil {
						utils.LogPrintf("更新包信息失败: %v", err)
					} else {
						// 添加同步日志
						models.AddSyncLogByExperimentVersion(existingPkg.ExperimentVersionID, "update", models.LogStatusSuccess, "更新包信息: "+remotePkg.Version)
					}
				}
			} else {
				// 新的包版本，添加到该实验版本
				utils.LogPrintf("发现实验版本 %s 的新包版本: %s",
					remotePkg.ExperimentVersionID, remotePkg.PackageVersionID)

				// 保存新包
				if err := models.CreatePackage(remotePkg); err != nil {
					utils.LogPrintf("创建包信息失败: %v", err)
				} else {
					// 添加同步日志
					models.AddSyncLogByExperimentVersion(remotePkg.ExperimentVersionID, "create", models.LogStatusSuccess, "发现新包版本: "+remotePkg.PackageVersionID)

					// 清理该实验版本的旧包版本（只清理未下载的）
					a.cleanupOldPackageVersions(remotePkg.ExperimentVersionID)
				}
			}
		} else {
			// 全新的实验版本
			utils.LogPrintf("发现新实验版本: %s (%s)",
				remotePkg.ExperimentName, remotePkg.ExperimentVersionID)

			// 保存新包
			if err := models.CreatePackage(remotePkg); err != nil {
				utils.LogPrintf("创建包信息失败: %v", err)
			} else {
				// 添加同步日志
				models.AddSyncLogByExperimentVersion(remotePkg.ExperimentVersionID, "create", models.LogStatusSuccess, "发现新实验版本: "+remotePkg.ExperimentName)
			}
		}
	}

	utils.LogPrintf("从中央端同步包列表完成，共同步 %d 个包", len(remotePackages))
}

// syncSchoolInfo 同步学校信息
func (a *App) syncSchoolInfo() {
	// 创建学校端客户端
	client, err := services.NewSchoolClient()
	if err != nil {
		log.Printf("创建客户端失败: %v", err)
		return
	}

	// 获取当前保存的学校ID
	oldSchoolID, err := models.GetSchoolID()
	if err != nil {
		log.Printf("获取学校ID失败: %v", err)
		oldSchoolID = ""
	}

	// 获取学校信息
	schoolID, schoolName, err := client.GetSchoolInfo()
	if err != nil {
		log.Printf("获取学校信息失败: %v", err)
		return
	}

	// 如果学校ID变化了，则清理旧的包信息
	if oldSchoolID != "" && oldSchoolID != schoolID {
		log.Printf("学校ID变化，从 %s 变为 %s，清理旧的包信息", oldSchoolID, schoolID)

		// 清理数据库中的包信息
		if err := models.ClearAllPackages(); err != nil {
			log.Printf("清理包信息失败: %v", err)
		} else {
			log.Printf("清理包信息成功")
		}

		// 清理下载和解压目录
		cleanDirectories()
	}

	log.Printf("同步学校信息成功，学校ID: %s，学校名称: %s", schoolID, schoolName)

	// 同步包列表
	go a.syncPackages()
}

// syncPackages 同步包列表
func (a *App) syncPackages() {
	utils.LogPrintln("开始同步包列表")

	// 创建学校端客户端
	client, err := services.NewSchoolClient()
	if err != nil {
		utils.LogPrintf("创建客户端失败: %v", err)
		return
	}

	// 获取远程包列表
	utils.LogPrintln("正在获取远程包列表...")
	remotePackages, err := client.GetPackages()
	if err != nil {
		utils.LogPrintf("获取远程包列表失败: %v", err)
		return
	}

	// 获取本地包列表
	utils.LogPrintln("正在获取本地包列表...")
	localPackages, err := models.GetAllPackages()
	if err != nil {
		utils.LogPrintf("获取本地包列表失败: %v", err)
		return
	}

	// 按实验版本ID分组本地包
	localExperimentVersionMap := make(map[string][]*models.Package)
	for _, pkg := range localPackages {
		localExperimentVersionMap[pkg.ExperimentVersionID] = append(localExperimentVersionMap[pkg.ExperimentVersionID], pkg)
	}

	// 同步包列表
	for _, remotePkg := range remotePackages {
		utils.LogPrintf("处理远程包: %s, 实验版本ID: %s, 包版本ID: %s",
			remotePkg.ExperimentName, remotePkg.ExperimentVersionID, remotePkg.PackageVersionID)

		// 检查该实验版本是否已存在本地包
		localPkgs, experimentExists := localExperimentVersionMap[remotePkg.ExperimentVersionID]

		if experimentExists {
			// 实验版本已存在，检查是否有相同的包版本ID
			var existingPkg *models.Package
			for _, pkg := range localPkgs {
				if pkg.PackageVersionID == remotePkg.PackageVersionID {
					existingPkg = pkg
					break
				}
			}

			if existingPkg != nil {
				// 相同包版本已存在，检查是否需要更新信息
				if existingPkg.Version != remotePkg.Version || existingPkg.FileHash != remotePkg.FileHash {
					utils.LogPrintf("更新包信息: %s, 版本: %s -> %s",
						remotePkg.ExperimentName, existingPkg.Version, remotePkg.Version)

					// 更新包信息
					existingPkg.Version = remotePkg.Version
					existingPkg.VersionName = remotePkg.VersionName
					existingPkg.VersionDesc = remotePkg.VersionDesc
					existingPkg.FileHash = remotePkg.FileHash
					existingPkg.FileSize = remotePkg.FileSize
					existingPkg.DownloadURL = remotePkg.DownloadURL
					existingPkg.UpdatedAt = time.Now()

					// 保存更新
					if err := models.UpdatePackage(existingPkg); err != nil {
						utils.LogPrintf("更新包信息失败: %v", err)
					} else {
						// 添加同步日志
						models.AddSyncLogByExperimentVersion(existingPkg.ExperimentVersionID, "update", models.LogStatusSuccess, "更新包信息: "+remotePkg.Version)
					}
				}
			} else {
				// 新的包版本，添加到该实验版本
				utils.LogPrintf("发现实验版本 %s 的新包版本: %s",
					remotePkg.ExperimentVersionID, remotePkg.PackageVersionID)

				// 创建新包
				newPkg := &models.Package{
					PackageVersionID:      remotePkg.PackageVersionID,
					ExperimentID:          remotePkg.ExperimentID,
					ExperimentName:        remotePkg.ExperimentName,
					ExperimentVersionID:   remotePkg.ExperimentVersionID,
					ExperimentVersionName: remotePkg.ExperimentVersionName,
					Version:               remotePkg.Version,
					VersionName:           remotePkg.VersionName,
					VersionDesc:           remotePkg.VersionDesc,
					FileHash:              remotePkg.FileHash,
					FileSize:              remotePkg.FileSize,
					DownloadURL:           remotePkg.DownloadURL,
					SyncStatus:            models.SyncStatusNone,
					CreatedAt:             time.Now(),
					UpdatedAt:             time.Now(),
					SchoolID:              remotePkg.SchoolID,
				}

				// 保存新包
				if err := models.CreatePackage(newPkg); err != nil {
					utils.LogPrintf("创建包信息失败: %v", err)
				} else {
					// 添加同步日志
					models.AddSyncLogByExperimentVersion(newPkg.ExperimentVersionID, "create", models.LogStatusSuccess, "发现新包版本: "+newPkg.PackageVersionID)

					// 清理该实验版本的旧包版本（只清理未下载的）
					a.cleanupOldPackageVersions(remotePkg.ExperimentVersionID)
				}
			}
		} else {
			// 全新的实验版本
			utils.LogPrintf("发现新实验版本: %s (%s)",
				remotePkg.ExperimentName, remotePkg.ExperimentVersionID)

			// 创建新包
			newPkg := &models.Package{
				PackageVersionID:      remotePkg.PackageVersionID,
				ExperimentID:          remotePkg.ExperimentID,
				ExperimentName:        remotePkg.ExperimentName,
				ExperimentVersionID:   remotePkg.ExperimentVersionID,
				ExperimentVersionName: remotePkg.ExperimentVersionName,
				Version:               remotePkg.Version,
				VersionName:           remotePkg.VersionName,
				VersionDesc:           remotePkg.VersionDesc,
				FileHash:              remotePkg.FileHash,
				FileSize:              remotePkg.FileSize,
				DownloadURL:           remotePkg.DownloadURL,
				SyncStatus:            models.SyncStatusNone,
				CreatedAt:             time.Now(),
				UpdatedAt:             time.Now(),
				SchoolID:              remotePkg.SchoolID,
			}

			// 保存新包
			if err := models.CreatePackage(newPkg); err != nil {
				utils.LogPrintf("创建包信息失败: %v", err)
			} else {
				// 添加同步日志
				models.AddSyncLogByExperimentVersion(newPkg.ExperimentVersionID, "create", models.LogStatusSuccess, "发现新实验版本: "+newPkg.ExperimentName)
			}
		}
	}

	utils.LogPrintf("同步包列表完成，共同步 %d 个包", len(remotePackages))
}

// GetPackages 获取包列表（优先显示已下载的包，未下载的显示最新版本）
func (a *App) GetPackages() ([]*models.Package, error) {
	utils.LogPrintln("获取包列表")

	// 获取应该显示的包列表（优先显示已下载的包）
	displayPackages, err := models.GetDisplayPackagesByExperimentVersion()
	if err != nil {
		utils.LogPrintf("获取包列表失败: %v", err)
		return nil, err
	}

	// 如果本地没有包，则尝试从中央端同步包列表
	if len(displayPackages) == 0 {
		utils.LogPrintln("本地没有包，尝试从中央端同步包列表")

		// 检查是否已登录
		if !a.authService.IsLoggedIn() {
			utils.LogPrintf("未登录，无法同步包列表")
			return displayPackages, nil // 返回空列表，不报错
		}

		// 创建中央端同步服务
		syncService := services.NewCentralSyncService(a.authService, a.authService.GetCentralURL())

		// 尝试检查中央端服务器状态
		_, err := syncService.CheckServerStatus()
		if err != nil {
			utils.LogPrintf("连接中央端服务器失败: %v, 返回空列表", err)
			return displayPackages, nil // 返回空列表，不报错
		}

		// 同步包列表
		a.syncPackagesFromCentral()

		// 重新获取应该显示的包列表
		displayPackages, err = models.GetDisplayPackagesByExperimentVersion()
		if err != nil {
			utils.LogPrintf("同步后重新获取包列表失败: %v", err)
			return nil, err
		}
	}

	utils.LogPrintf("返回包列表，共 %d 个包", len(displayPackages))
	return displayPackages, nil
}

// CheckPackageUpdates 检查包是否有更新
func (a *App) CheckPackageUpdates(id int64) (map[string]interface{}, error) {
	// 获取当前包详情
	currentPkg, err := models.GetPackageByID(id)
	if err != nil {
		return nil, err
	}

	if currentPkg == nil {
		return nil, fmt.Errorf("包不存在")
	}

	// 检查是否有更新
	updatePkg, err := models.CheckForUpdates(currentPkg.ExperimentVersionID, currentPkg.PackageVersionID)
	if err != nil {
		return nil, err
	}

	result := map[string]interface{}{
		"hasUpdate": updatePkg != nil,
	}

	if updatePkg != nil {
		result["updatePackage"] = map[string]interface{}{
			"id":                    updatePkg.ID,
			"packageVersionId":      updatePkg.PackageVersionID,
			"experimentId":          updatePkg.ExperimentID,
			"experimentName":        updatePkg.ExperimentName,
			"experimentVersionId":   updatePkg.ExperimentVersionID,
			"experimentVersionName": updatePkg.ExperimentVersionName,
			"version":               updatePkg.Version,
			"versionName":           updatePkg.VersionName,
			"versionDesc":           updatePkg.VersionDesc,
			"fileHash":              updatePkg.FileHash,
			"fileSize":              updatePkg.FileSize,
			"downloadUrl":           updatePkg.DownloadURL,
			"syncStatus":            updatePkg.SyncStatus,
			"schoolId":              updatePkg.SchoolID,
		}
	}

	return result, nil
}

// GetPackage 获取包详情
func (a *App) GetPackage(id int64) (*models.Package, error) {
	// 从本地获取包详情
	return models.GetPackageByID(id)
}

// GetPackageDetail 获取包详情和同步日志
func (a *App) GetPackageDetail(id int64) (map[string]interface{}, error) {
	// 获取包详情
	pkg, err := models.GetPackageByID(id)
	if err != nil {
		return nil, err
	}

	if pkg == nil {
		return nil, fmt.Errorf("包不存在")
	}

	// 获取该包的同步日志
	logs, err := models.GetSyncLogsByPackageID(pkg.ID)
	if err != nil {
		return nil, err
	}

	// 转换日志格式
	syncLogs := make([]map[string]interface{}, 0, len(logs))
	for _, log := range logs {
		logItem := map[string]interface{}{
			"id":         log.ID,
			"action":     log.Action,
			"status":     log.Status,
			"statusText": getStatusText(log.Status),
			"message":    log.Message,
			"createdAt":  log.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		syncLogs = append(syncLogs, logItem)
	}

	// 构建响应
	result := map[string]interface{}{
		"packageDetail": map[string]interface{}{
			"id":                    pkg.ID,
			"packageVersionId":      pkg.PackageVersionID,
			"experimentId":          pkg.ExperimentID,
			"experimentName":        pkg.ExperimentName,
			"experimentVersionId":   pkg.ExperimentVersionID,
			"experimentVersionName": pkg.ExperimentVersionName,
			"version":               pkg.Version,
			"versionName":           pkg.VersionName,
			"versionDesc":           pkg.VersionDesc,
			"fileHash":              pkg.FileHash,
			"fileSize":              pkg.FileSize,
			"downloadUrl":           pkg.DownloadURL,
			"syncStatus":            pkg.SyncStatus,
			"downloadCount":         pkg.DownloadCount,
			"schoolId":              pkg.SchoolID,
			"lastRun":               pkg.LastRun,
			"createdAt":             pkg.CreatedAt.Format("2006-01-02 15:04:05"),
			"updatedAt":             pkg.UpdatedAt.Format("2006-01-02 15:04:05"),
		},
		"syncLogs": syncLogs,
	}

	return result, nil
}

// DownloadPackage 下载包
func (a *App) DownloadPackage(id int64) (string, error) {
	// 获取包详情
	pkg, err := a.GetPackage(id)
	if err != nil {
		return "", err
	}

	// 创建下载服务
	downloadService, err := services.NewDownloadService()
	if err != nil {
		return "", err
	}

	// 创建进度通道
	progressChan := make(chan services.DownloadProgress, 10)

	// 生成唯一的下载ID
	downloadID := fmt.Sprintf("download-%d-%d", id, time.Now().UnixNano())
	utils.LogPrintf("生成下载ID: %s, 包ID: %d", downloadID, id)

	// 启动下载
	go func() {
		defer close(progressChan)
		utils.LogPrintf("开始下载goroutine, 下载ID: %s", downloadID)

		// 更新包状态为下载中
		if err := models.UpdatePackageStatus(pkg.ID, models.SyncStatusDownloading); err != nil {
			// 发送错误事件
			runtime.EventsEmit(a.ctx, downloadID, services.DownloadProgress{
				Total:      0,
				Downloaded: 0,
				Percentage: 0,
				Speed:      0,
				Filename:   "",
				Error:      err.Error(),
			})
			return
		}

		// 添加同步日志
		if err := models.AddSyncLog(pkg.ID, "download", models.LogStatusRunning, "开始下载"); err != nil {
			// 发送错误事件
			runtime.EventsEmit(a.ctx, downloadID, services.DownloadProgress{
				Total:      0,
				Downloaded: 0,
				Percentage: 0,
				Speed:      0,
				Filename:   "",
				Error:      err.Error(),
			})
			return
		}

		// 创建一个处理进度更新的函数
		progressHandler := func(progress services.DownloadProgress) {
			// 通过事件系统发送进度更新
			utils.LogPrintf("发送下载进度事件: %s, 进度: %.2f%%, 速度: %.2f KB/s",
				downloadID, progress.Percentage, progress.Speed/1024)
			runtime.EventsEmit(a.ctx, downloadID, progress)
		}

		// 下载包
		if err := downloadService.DownloadPackage(pkg, progressChan, progressHandler); err != nil {
			// 回退包状态到未下载
			models.UpdatePackageStatus(pkg.ID, models.SyncStatusNone)

			// 清除本地路径（如果有部分下载的文件）
			if pkg.LocalPath != "" {
				if removeErr := os.Remove(pkg.LocalPath); removeErr != nil && !os.IsNotExist(removeErr) {
					utils.LogPrintf("清理部分下载文件失败: %s, 错误: %v", pkg.LocalPath, removeErr)
				}
				// 清除数据库中的本地路径
				models.UpdatePackageLocalPath(pkg.ID, "")
			}

			// 添加同步日志
			models.AddSyncLog(pkg.ID, "download", models.LogStatusFailed, fmt.Sprintf("下载失败: %v", err))

			// 发送错误事件
			runtime.EventsEmit(a.ctx, downloadID, services.DownloadProgress{
				Total:      0,
				Downloaded: 0,
				Percentage: 0,
				Speed:      0,
				Filename:   "",
				Error:      err.Error(),
			})
			return
		}

		// 发送完成事件
		runtime.EventsEmit(a.ctx, downloadID, services.DownloadProgress{
			Total:      pkg.FileSize,
			Downloaded: pkg.FileSize,
			Percentage: 100,
			Speed:      0,
			Filename:   filepath.Base(pkg.LocalPath),
			Completed:  true,
		})
	}()

	// 返回下载ID，前端可以使用这个ID来监听进度更新事件
	return downloadID, nil
}

// ExtractPackage 解压包
func (a *App) ExtractPackage(id int64) (string, error) {
	// 获取包详情
	pkg, err := models.GetPackageByID(id)
	if err != nil {
		return "", err
	}

	if pkg == nil {
		return "", fmt.Errorf("包不存在")
	}

	// 检查包是否已经在解压中
	a.extractMutex.Lock()
	if _, exists := a.extractServices[id]; exists {
		a.extractMutex.Unlock()
		return "", fmt.Errorf("包正在解压中，请勿重复操作")
	}

	// 检查包状态是否适合解压
	if pkg.SyncStatus == models.SyncStatusExtracting {
		a.extractMutex.Unlock()
		return "", fmt.Errorf("包正在解压中")
	}

	if pkg.SyncStatus != models.SyncStatusDownloaded {
		a.extractMutex.Unlock()
		return "", fmt.Errorf("包状态不正确，当前状态: %d", pkg.SyncStatus)
	}

	// 创建解压服务
	extractService := services.NewExtractService()

	// 将解压服务添加到管理器中
	a.extractServices[id] = extractService
	a.extractMutex.Unlock()

	// 创建进度通道，增大缓冲区大小
	progressChan := make(chan services.ExtractProgress, 100)

	// 生成唯一的解压ID
	extractID := fmt.Sprintf("extract-%d-%d", id, time.Now().UnixNano())

	// 启动解压
	go func() {
		defer close(progressChan)
		defer func() {
			// 解压完成后从管理器中移除
			a.extractMutex.Lock()
			delete(a.extractServices, id)
			a.extractMutex.Unlock()
		}()

		utils.LogPrintf("开始解压包 ID: %d, 解压ID: %s", id, extractID)

		// 创建一个处理进度更新的函数
		progressHandler := func(progress services.ExtractProgress) {
			// 通过事件系统发送进度更新
			utils.LogPrintf("发送解压进度: %.2f%% (%d/%d)", progress.Percentage, progress.Extracted, progress.Total)
			runtime.EventsEmit(a.ctx, extractID, progress)
		}

		// 创建一个超时通道
		timeoutChan := make(chan bool, 1)

		// 创建一个完成通道
		doneChan := make(chan error, 1)

		// 启动一个goroutine执行解压
		go func() {
			err := extractService.ExtractPackage(pkg, progressChan, progressHandler)
			doneChan <- err
		}()

		// 启动一个goroutine监控超时
		go func() {
			// 设置超时时间为5分钟
			time.Sleep(5 * time.Minute)
			timeoutChan <- true
		}()

		// 等待解压完成或超时
		select {
		case err := <-doneChan:
			// 解压完成
			if err != nil {
				// 解压失败
				utils.LogPrintf("解压失败: %v", err)
				models.AddSyncLog(pkg.ID, "extract", models.LogStatusFailed, fmt.Sprintf("解压失败: %v", err))

				// 发送错误事件
				errorProgress := services.ExtractProgress{
					Total:       0,
					Extracted:   0,
					Percentage:  0,
					CurrentFile: "",
					Error:       err.Error(),
				}
				utils.LogPrintf("发送解压错误: %s", err.Error())
				runtime.EventsEmit(a.ctx, extractID, errorProgress)
			} else {
				// 解压成功
				utils.LogPrintf("解压成功完成")
				// 解压服务已经发送了完成事件，这里不需要再发送
				// 只需要确保有足够的时间让前端接收到事件
				time.Sleep(100 * time.Millisecond)
			}

		case <-timeoutChan:
			// 解压超时
			utils.LogPrintf("解压超时")
			models.AddSyncLog(pkg.ID, "extract", models.LogStatusFailed, "解压超时，请重试")

			// 发送超时错误事件
			errorProgress := services.ExtractProgress{
				Total:       0,
				Extracted:   0,
				Percentage:  0,
				CurrentFile: "",
				Error:       "解压超时，请重试",
			}
			utils.LogPrintf("发送解压超时错误")
			runtime.EventsEmit(a.ctx, extractID, errorProgress)
		}
	}()

	// 返回解压ID，前端可以使用这个ID来监听进度更新事件
	return extractID, nil
}

// RunPackage 运行包
func (a *App) RunPackage(id int64) error {
	// 获取包详情
	pkg, err := models.GetPackageByID(id)
	if err != nil {
		return err
	}

	if pkg == nil {
		return fmt.Errorf("包不存在")
	}

	// 创建运行服务
	runService := services.NewRunService()

	// 运行包
	return runService.RunPackage(pkg)
}

// CreateShortcut 创建桌面快捷方式
func (a *App) CreateShortcut(id int64) error {
	// 获取包详情
	pkg, err := models.GetPackageByID(id)
	if err != nil {
		return err
	}

	if pkg == nil {
		return fmt.Errorf("包不存在")
	}

	// 创建运行服务
	runService := services.NewRunService()

	// 创建桌面快捷方式
	return runService.CreateShortcut(pkg)
}

// GetSyncLogs 获取同步日志
func (a *App) GetSyncLogs(page, pageSize int, action string, status int) (map[string]interface{}, error) {
	var logs []*models.SyncLog
	var err error
	var total int

	// 根据筛选条件获取日志
	if action != "" && status != -1 {
		// 同时按操作和状态筛选
		logs, err = models.GetSyncLogsByActionAndStatus(action, status, page, pageSize)
		if err != nil {
			return nil, err
		}
		total, err = models.CountSyncLogsByActionAndStatus(action, status)
		if err != nil {
			return nil, err
		}
	} else if action != "" {
		// 按操作筛选
		logs, err = models.GetSyncLogsByAction(action)
		if err != nil {
			return nil, err
		}
		total = len(logs)
		// 手动分页
		if (page-1)*pageSize < len(logs) {
			end := page * pageSize
			if end > len(logs) {
				end = len(logs)
			}
			logs = logs[(page-1)*pageSize : end]
		} else {
			logs = []*models.SyncLog{}
		}
	} else if status != -1 {
		// 按状态筛选
		logs, err = models.GetSyncLogsByStatus(status)
		if err != nil {
			return nil, err
		}
		total = len(logs)
		// 手动分页
		if (page-1)*pageSize < len(logs) {
			end := page * pageSize
			if end > len(logs) {
				end = len(logs)
			}
			logs = logs[(page-1)*pageSize : end]
		} else {
			logs = []*models.SyncLog{}
		}
	} else {
		// 获取所有日志
		logs, err = models.GetAllSyncLogs()
		if err != nil {
			return nil, err
		}
		total = len(logs)
		// 手动分页
		if (page-1)*pageSize < len(logs) {
			end := page * pageSize
			if end > len(logs) {
				end = len(logs)
			}
			logs = logs[(page-1)*pageSize : end]
		} else {
			logs = []*models.SyncLog{}
		}
	}

	// 获取包信息，用于填充日志中的实验名称
	packageMap := make(map[int64]*models.Package)
	for _, log := range logs {
		if log.PackageID > 0 {
			if _, exists := packageMap[log.PackageID]; !exists {
				pkg, err := models.GetPackageByID(log.PackageID)
				if err == nil && pkg != nil {
					packageMap[log.PackageID] = pkg
				}
			}
		}
	}

	// 构建响应
	result := make([]map[string]interface{}, 0, len(logs))
	for _, log := range logs {
		// 将数字状态码转换为字符串状态
		var statusText string
		switch log.Status {
		case models.LogStatusSuccess:
			statusText = "success"
		case models.LogStatusRunning:
			statusText = "running"
		case models.LogStatusFailed:
			statusText = "failed"
		default:
			statusText = "unknown"
		}

		// 转换操作为前端使用的格式
		var actionType, typeText string
		switch log.Action {
		case "sync":
			actionType = "sync"
			typeText = "同步"
		case "download":
			actionType = "download"
			typeText = "下载"
		case "update":
			actionType = "update"
			typeText = "更新"
		case "extract":
			actionType = "extract"
			typeText = "解压"
		case "run":
			actionType = "run"
			typeText = "运行"
		case "create":
			actionType = "download" // create操作在前端显示为下载
			typeText = "下载"
		case "shortcut":
			actionType = "shortcut"
			typeText = "快捷方式"
		default:
			actionType = "other"
			typeText = log.Action
		}

		item := map[string]interface{}{
			"id":         log.ID,
			"packageId":  log.PackageID,
			"action":     log.Action,
			"type":       actionType,
			"typeText":   typeText,
			"status":     statusText,
			"statusText": getStatusText(log.Status),
			"message":    log.Message,
			"time":       log.CreatedAt.Format("2006-01-02 15:04:05"),
		}

		// 添加包信息
		if pkg, exists := packageMap[log.PackageID]; exists {
			item["experimentName"] = pkg.ExperimentName
			item["versionName"] = pkg.VersionName + " v" + pkg.Version
		} else {
			item["experimentName"] = "未知实验"
			item["versionName"] = "未知版本"
		}

		result = append(result, item)
	}

	return map[string]interface{}{
		"logs":  result,
		"total": total,
	}, nil
}

// SyncNow 立即同步（教师端从中央服务器同步）
func (a *App) SyncNow() (string, error) {
	// 生成唯一的同步ID
	syncID := fmt.Sprintf("sync-%d", time.Now().UnixNano())

	go func() {
		// 发送同步开始事件
		runtime.EventsEmit(a.ctx, syncID, map[string]interface{}{
			"percentage":     10,
			"currentTask":    "正在连接中央服务器...",
			"completedTasks": 0,
			"totalTasks":     2,
		})

		// 检查是否已登录
		if !a.authService.IsLoggedIn() {
			runtime.EventsEmit(a.ctx, syncID, map[string]interface{}{
				"percentage":     0,
				"currentTask":    "未登录，无法同步",
				"completedTasks": 0,
				"totalTasks":     2,
				"error":          "请先登录中央服务器",
			})
			return
		}

		// 创建中央端同步服务
		syncService := services.NewCentralSyncService(a.authService, a.authService.GetCentralURL())

		// 检查中央端服务器状态
		_, err := syncService.CheckServerStatus()
		if err != nil {
			runtime.EventsEmit(a.ctx, syncID, map[string]interface{}{
				"percentage":     0,
				"currentTask":    "连接中央服务器失败",
				"completedTasks": 0,
				"totalTasks":     2,
				"error":          err.Error(),
			})
			return
		}

		// 获取学校信息
		schoolName := a.authService.GetSchoolName()

		// 添加同步日志
		models.AddSyncLog(0, "sync", models.LogStatusRunning, "开始从中央服务器同步: "+schoolName)

		// 同步包列表
		runtime.EventsEmit(a.ctx, syncID, map[string]interface{}{
			"percentage":     70,
			"currentTask":    "正在从中央服务器同步实验包列表...",
			"completedTasks": 1,
			"totalTasks":     2,
		})

		// 从中央服务器获取包列表
		remotePackages, err := syncService.GetPackages()
		if err != nil {
			runtime.EventsEmit(a.ctx, syncID, map[string]interface{}{
				"percentage":     70,
				"currentTask":    "获取实验包列表失败",
				"completedTasks": 1,
				"totalTasks":     2,
				"error":          err.Error(),
			})
			models.AddSyncLog(0, "sync", models.LogStatusFailed, "获取实验包列表失败: "+err.Error())
			return
		}

		// 获取本地包列表
		localPackages, err := models.GetAllPackages()
		if err != nil {
			runtime.EventsEmit(a.ctx, syncID, map[string]interface{}{
				"percentage":     70,
				"currentTask":    "获取本地包列表失败",
				"completedTasks": 2,
				"totalTasks":     3,
				"error":          err.Error(),
			})
			models.AddSyncLog(0, "sync", models.LogStatusFailed, "获取本地包列表失败: "+err.Error())
			return
		}

		// 创建本地包的映射，用于快速查找
		localPackageMap := make(map[string]*models.Package)
		for _, pkg := range localPackages {
			localPackageMap[pkg.PackageVersionID] = pkg
		}

		// 同步包列表
		updatedCount := 0
		newCount := 0
		for _, remotePkg := range remotePackages {
			// 检查本地是否存在该包
			localPkg, exists := localPackageMap[remotePkg.PackageVersionID]
			if exists {
				// 如果本地已存在，检查是否需要更新
				if localPkg.Version != remotePkg.Version || localPkg.FileHash != remotePkg.FileHash {
					// 需要更新
					utils.LogPrintf("发现包更新: %s, 版本: %s -> %s", remotePkg.ExperimentName, localPkg.Version, remotePkg.Version)

					// 使用认证服务中的学校ID
					schoolID := a.authService.GetSchoolID()

					// 处理exp字段
					experimentVersionName := remotePkg.ExperimentVersionName

					// 更新包信息
					localPkg.Version = remotePkg.Version
					localPkg.VersionName = remotePkg.VersionName
					localPkg.VersionDesc = remotePkg.VersionDesc
					localPkg.FileHash = remotePkg.FileHash
					localPkg.FileSize = remotePkg.FileSize
					localPkg.DownloadURL = remotePkg.DownloadURL
					localPkg.ExperimentVersionName = experimentVersionName
					localPkg.SchoolID = schoolID
					localPkg.SyncStatus = models.SyncStatusNone // 重置同步状态

					// 保存更新
					if err := models.UpdatePackage(localPkg); err != nil {
						utils.LogPrintf("更新包信息失败: %v", err)
					} else {
						// 添加同步日志
						models.AddSyncLog(localPkg.ID, "update", models.LogStatusSuccess, "发现新版本: "+remotePkg.Version)
						updatedCount++
					}
				}
			} else {
				// 如果本地不存在，创建新包
				utils.LogPrintf("发现新包: %s, 版本: %s", remotePkg.ExperimentName, remotePkg.Version)

				// 使用认证服务中的学校ID
				schoolID := a.authService.GetSchoolID()

				// 处理exp字段
				experimentVersionName := remotePkg.ExperimentVersionName

				// 创建新包
				newPkg := &models.Package{
					PackageVersionID:      remotePkg.PackageVersionID,
					ExperimentID:          remotePkg.ExperimentID,
					ExperimentName:        remotePkg.ExperimentName,
					ExperimentVersionID:   remotePkg.ExperimentVersionID,
					ExperimentVersionName: experimentVersionName,
					Version:               remotePkg.Version,
					VersionName:           remotePkg.VersionName,
					VersionDesc:           remotePkg.VersionDesc,
					FileHash:              remotePkg.FileHash,
					FileSize:              remotePkg.FileSize,
					DownloadURL:           remotePkg.DownloadURL,
					SyncStatus:            models.SyncStatusNone,
					SchoolID:              schoolID,
					CreatedAt:             time.Now(),
					UpdatedAt:             time.Now(),
				}

				// 保存新包
				if err := models.CreatePackage(newPkg); err != nil {
					utils.LogPrintf("创建包信息失败: %v", err)
				} else {
					// 添加同步日志
					models.AddSyncLog(newPkg.ID, "create", models.LogStatusSuccess, "发现新包: "+newPkg.ExperimentName)
					newCount++
				}
			}
		}

		// 同步完成
		runtime.EventsEmit(a.ctx, syncID, map[string]interface{}{
			"percentage":     100,
			"currentTask":    "同步完成",
			"completedTasks": 2,
			"totalTasks":     2,
			"result": map[string]interface{}{
				"updatedCount": updatedCount,
				"newCount":     newCount,
				"totalCount":   len(remotePackages),
			},
		})

		// 添加同步日志
		models.AddSyncLog(0, "sync", models.LogStatusSuccess, fmt.Sprintf("同步完成，共同步 %d 个包，新增 %d 个，更新 %d 个", len(remotePackages), newCount, updatedCount))
	}()

	return syncID, nil
}

// GetDownloads 获取下载列表
func (a *App) GetDownloads() (map[string]interface{}, error) {
	// 获取所有包
	packages, err := models.GetAllPackages()
	if err != nil {
		return nil, err
	}

	// 分类
	activeDownloads := make([]map[string]interface{}, 0)
	completedDownloads := make([]map[string]interface{}, 0)

	for _, pkg := range packages {
		// 跳过未下载的包
		if pkg.SyncStatus == models.SyncStatusNone {
			continue
		}

		item := map[string]interface{}{
			"id":             pkg.ID,
			"experimentName": pkg.ExperimentName,
			"versionName":    pkg.VersionName,
			"version":        pkg.Version,
			"fileSize":       pkg.FileSize,
		}

		if pkg.SyncStatus == models.SyncStatusDownloading {
			// 下载中
			item["status"] = "downloading"
			item["statusText"] = "下载中"
			item["progress"] = 0 // 实际应该从某处获取进度
			activeDownloads = append(activeDownloads, item)
		} else if pkg.SyncStatus == models.SyncStatusExtracting {
			// 解压中
			item["status"] = "extracting"
			item["statusText"] = "解压中"
			item["progress"] = 0 // 实际应该从某处获取进度
			activeDownloads = append(activeDownloads, item)
		} else if pkg.SyncStatus == models.SyncStatusDownloaded || pkg.SyncStatus == models.SyncStatusCompleted {
			// 已完成
			item["status"] = "success"
			item["statusText"] = "已完成"
			item["completedTime"] = pkg.UpdatedAt.Format("2006-01-02 15:04:05")
			completedDownloads = append(completedDownloads, item)
		} else if pkg.SyncStatus == models.SyncStatusFailed {
			// 失败
			item["status"] = "failed"
			item["statusText"] = "失败"
			item["completedTime"] = pkg.UpdatedAt.Format("2006-01-02 15:04:05")
			completedDownloads = append(completedDownloads, item)
		}
	}

	return map[string]interface{}{
		"activeDownloads":    activeDownloads,
		"completedDownloads": completedDownloads,
	}, nil
}

// CancelDownload 取消下载
func (a *App) CancelDownload(id int64) error {
	// 获取包详情
	pkg, err := models.GetPackageByID(id)
	if err != nil {
		return err
	}

	if pkg == nil {
		return fmt.Errorf("包不存在")
	}

	// 检查包是否正在下载
	if pkg.SyncStatus != models.SyncStatusDownloading {
		return fmt.Errorf("包不在下载中")
	}

	// 更新包状态为未下载（回退到下载前的状态）
	if err := models.UpdatePackageStatus(pkg.ID, models.SyncStatusNone); err != nil {
		return err
	}

	// 清除本地路径（如果有部分下载的文件）
	if pkg.LocalPath != "" {
		if err := os.Remove(pkg.LocalPath); err != nil && !os.IsNotExist(err) {
			utils.LogPrintf("清理部分下载文件失败: %s, 错误: %v", pkg.LocalPath, err)
		}
		// 清除数据库中的本地路径
		if err := models.UpdatePackageLocalPath(pkg.ID, ""); err != nil {
			utils.LogPrintf("清除本地路径失败: %v", err)
		}
	}

	// 添加同步日志
	if err := models.AddSyncLog(pkg.ID, "download", models.LogStatusFailed, "用户取消下载"); err != nil {
		return err
	}

	return nil
}

// CancelExtract 取消解压
func (a *App) CancelExtract(id int64) error {
	// 获取包详情
	pkg, err := models.GetPackageByID(id)
	if err != nil {
		return err
	}

	if pkg == nil {
		return fmt.Errorf("包不存在")
	}

	// 检查包是否正在解压
	if pkg.SyncStatus != models.SyncStatusExtracting {
		return fmt.Errorf("包不在解压中")
	}

	utils.LogPrintf("用户取消解压包 ID: %d", id)

	// 获取解压服务并发送取消信号
	a.extractMutex.RLock()
	extractService, exists := a.extractServices[id]
	a.extractMutex.RUnlock()

	if exists && extractService != nil {
		// 发送取消信号到解压服务
		extractService.CancelExtract()
		utils.LogPrintf("已发送取消信号到解压服务")
	}

	// 更新包状态为已下载（重置到解压前的状态）
	if err := models.UpdatePackageStatus(pkg.ID, models.SyncStatusDownloaded); err != nil {
		return err
	}

	// 清除解压路径和可执行文件路径
	if err := models.UpdatePackageExtractPath(pkg.ID, ""); err != nil {
		return err
	}
	if err := models.UpdatePackageExecutablePath(pkg.ID, ""); err != nil {
		return err
	}

	// 如果有部分解压的文件，尝试清理
	if pkg.ExtractPath != "" {
		if err := os.RemoveAll(pkg.ExtractPath); err != nil && !os.IsNotExist(err) {
			utils.LogPrintf("清理部分解压文件失败: %s, 错误: %v", pkg.ExtractPath, err)
		} else {
			utils.LogPrintf("已清理部分解压文件: %s", pkg.ExtractPath)
		}
	}

	// 添加同步日志
	if err := models.AddSyncLog(pkg.ID, "extract", models.LogStatusFailed, "用户取消解压"); err != nil {
		return err
	}

	// 从管理器中移除解压服务
	a.extractMutex.Lock()
	delete(a.extractServices, id)
	a.extractMutex.Unlock()

	utils.LogPrintf("取消解压完成，包状态已重置为已下载")
	return nil
}

// ClearCompletedDownloads 清除已完成的下载
func (a *App) ClearCompletedDownloads() error {
	// 获取所有包
	packages, err := models.GetAllPackages()
	if err != nil {
		return err
	}

	// 清除已完成的下载记录
	for _, pkg := range packages {
		if pkg.SyncStatus == models.SyncStatusDownloaded || pkg.SyncStatus == models.SyncStatusCompleted || pkg.SyncStatus == models.SyncStatusFailed {
			// 重置包状态为未下载
			if err := models.UpdatePackageStatus(pkg.ID, models.SyncStatusNone); err != nil {
				return err
			}

			// 清除本地路径和解压路径
			if err := models.UpdatePackageLocalPath(pkg.ID, ""); err != nil {
				return err
			}
			if err := models.UpdatePackageExtractPath(pkg.ID, ""); err != nil {
				return err
			}
			if err := models.UpdatePackageExecutablePath(pkg.ID, ""); err != nil {
				return err
			}

			// 添加同步日志
			if err := models.AddSyncLog(pkg.ID, "clear", models.LogStatusSuccess, "清除下载记录"); err != nil {
				return err
			}
		}
	}

	return nil
}

// DeletePackageDownload 删除指定包的下载文件并重置状态
func (a *App) DeletePackageDownload(id int64) error {
	// 获取包详情
	pkg, err := models.GetPackageByID(id)
	if err != nil {
		return err
	}

	if pkg == nil {
		return fmt.Errorf("包不存在")
	}

	// 检查包状态，只允许删除已下载、已解压或已完成的包
	if pkg.SyncStatus != models.SyncStatusDownloaded &&
		pkg.SyncStatus != models.SyncStatusCompleted {
		return fmt.Errorf("只能删除已下载或已完成的包")
	}

	utils.LogPrintf("开始删除包下载: ID=%d, 实验=%s, 状态=%d", pkg.ID, pkg.ExperimentName, pkg.SyncStatus)

	// 删除本地下载文件
	if pkg.LocalPath != "" {
		if err := os.Remove(pkg.LocalPath); err != nil && !os.IsNotExist(err) {
			utils.LogPrintf("删除本地下载文件失败: %s, 错误: %v", pkg.LocalPath, err)
			// 不返回错误，继续清理其他文件
		} else {
			utils.LogPrintf("已删除本地下载文件: %s", pkg.LocalPath)
		}
	}

	// 删除解压目录
	if pkg.ExtractPath != "" {
		if err := os.RemoveAll(pkg.ExtractPath); err != nil && !os.IsNotExist(err) {
			utils.LogPrintf("删除解压目录失败: %s, 错误: %v", pkg.ExtractPath, err)
			// 不返回错误，继续清理其他文件
		} else {
			utils.LogPrintf("已删除解压目录: %s", pkg.ExtractPath)
		}
	}

	// 重置包状态为未下载
	if err := models.UpdatePackageStatus(pkg.ID, models.SyncStatusNone); err != nil {
		return fmt.Errorf("更新包状态失败: %v", err)
	}

	// 清除数据库中的文件路径
	if err := models.UpdatePackageLocalPath(pkg.ID, ""); err != nil {
		return fmt.Errorf("清除本地路径失败: %v", err)
	}
	if err := models.UpdatePackageExtractPath(pkg.ID, ""); err != nil {
		return fmt.Errorf("清除解压路径失败: %v", err)
	}
	if err := models.UpdatePackageExecutablePath(pkg.ID, ""); err != nil {
		return fmt.Errorf("清除可执行文件路径失败: %v", err)
	}

	// 添加同步日志
	if err := models.AddSyncLog(pkg.ID, "delete", models.LogStatusSuccess, "删除下载文件并重置状态"); err != nil {
		utils.LogPrintf("添加同步日志失败: %v", err)
		// 不返回错误，因为主要操作已完成
	}

	utils.LogPrintf("删除包下载完成: ID=%d, 已重置为未下载状态", pkg.ID)
	return nil
}

// cleanDirectories 清理下载和解压目录
func cleanDirectories() {
	dirs := []string{
		"./downloads",
		"./extracted",
	}

	for _, dir := range dirs {
		// 删除目录中的所有文件
		if err := os.RemoveAll(dir); err != nil {
			log.Printf("删除目录失败 %s: %v\n", dir, err)
		}

		// 重新创建目录
		if err := os.MkdirAll(dir, 0755); err != nil {
			log.Printf("创建目录失败 %s: %v\n", dir, err)
		}
	}
}

// SetCentralURL 设置中央服务器URL（教师端专用）
func (a *App) SetCentralURL(centralURL string) error {
	// 保存中央服务器URL配置
	if err := models.SetConfig("central_url", centralURL); err != nil {
		return err
	}

	// 更新认证服务的中央端URL
	a.authService = services.NewAuthService(centralURL)

	return nil
}

// GetCentralURL 获取中央服务器URL（教师端专用）
func (a *App) GetCentralURL() (string, error) {
	// 获取中央服务器URL
	centralURL, err := models.GetConfig("central_url")
	if err != nil {
		return "", err
	}

	if centralURL == "" {
		// 默认中央服务器地址
		centralURL = "http://localhost:8900"
	}

	return centralURL, nil
}

// TestCentralConnection 测试中央服务器连接（不影响当前认证状态）
func (a *App) TestCentralConnection(centralURL string) map[string]interface{} {
	if centralURL == "" {
		return map[string]interface{}{
			"status":  "error",
			"message": "请输入中央服务器地址",
		}
	}

	// 直接发送HTTP请求测试连接，不创建认证服务
	client := &http.Client{Timeout: 10 * time.Second}

	// 测试基本连接 - 尝试访问根路径或任何不需要认证的路径
	// 由于中央服务器的API都需要认证，我们只测试服务器是否可达
	resp, err := client.Get(centralURL)
	if err != nil {
		return map[string]interface{}{
			"status":  "offline",
			"message": "连接失败: " + err.Error(),
		}
	}
	defer resp.Body.Close()

	// 只要能连接到服务器就算成功，不管返回什么状态码
	// 因为中央服务器可能会返回404（没有根路径处理）或其他状态码
	if resp.StatusCode >= 200 && resp.StatusCode < 500 {
		return map[string]interface{}{
			"status":  "online",
			"message": "连接成功",
		}
	}

	return map[string]interface{}{
		"status":  "offline",
		"message": fmt.Sprintf("服务器响应异常: %d", resp.StatusCode),
	}
}

// CheckCentralServerConfigValid 检查中央服务器配置是否有效（教师端专用）
func (a *App) CheckCentralServerConfigValid() (bool, error) {
	// 检查是否已登录
	if !a.authService.IsLoggedIn() {
		return false, nil
	}

	// 尝试连接中央服务器
	status := a.GetServerStatus("")
	return status["status"] == "online", nil
}

// cleanupOldPackageVersions 清理指定实验版本的旧包版本
// 只清理未下载的旧包版本，保留已下载的包
func (a *App) cleanupOldPackageVersions(experimentVersionID string) {
	utils.LogPrintf("开始清理实验版本 %s 的未下载旧包版本", experimentVersionID)

	// 获取该实验版本的所有旧包（除了最新版本）
	oldPackages, err := models.GetOldPackagesByExperimentVersion(experimentVersionID)
	if err != nil {
		utils.LogPrintf("获取旧包版本失败: %v", err)
		return
	}

	if len(oldPackages) == 0 {
		utils.LogPrintf("实验版本 %s 没有旧包版本需要清理", experimentVersionID)
		return
	}

	utils.LogPrintf("发现 %d 个旧包版本，检查清理策略", len(oldPackages))

	for _, oldPkg := range oldPackages {
		utils.LogPrintf("检查旧包: ID=%d, PackageVersionID=%s, 实验=%s, 状态=%d",
			oldPkg.ID, oldPkg.PackageVersionID, oldPkg.ExperimentName, oldPkg.SyncStatus)

		// 只清理未下载的包，保留已下载的包
		if oldPkg.SyncStatus == models.SyncStatusDownloaded || oldPkg.SyncStatus == models.SyncStatusCompleted {
			// 已下载或已完成的包：完全保留，不做任何清理
			utils.LogPrintf("保留已下载包: ID=%d, PackageVersionID=%s (状态=%d)",
				oldPkg.ID, oldPkg.PackageVersionID, oldPkg.SyncStatus)
			continue
		}

		// 未下载的包：清理文件和数据库记录
		utils.LogPrintf("清理未下载包: ID=%d, PackageVersionID=%s (状态=%d)",
			oldPkg.ID, oldPkg.PackageVersionID, oldPkg.SyncStatus)

		// 删除本地下载文件（如果有）
		if oldPkg.LocalPath != "" {
			if err := os.Remove(oldPkg.LocalPath); err != nil && !os.IsNotExist(err) {
				utils.LogPrintf("删除本地文件失败: %s, 错误: %v", oldPkg.LocalPath, err)
			} else {
				utils.LogPrintf("已删除本地文件: %s", oldPkg.LocalPath)
			}
		}

		// 删除解压目录（如果有）
		if oldPkg.ExtractPath != "" {
			if err := os.RemoveAll(oldPkg.ExtractPath); err != nil && !os.IsNotExist(err) {
				utils.LogPrintf("删除解压目录失败: %s, 错误: %v", oldPkg.ExtractPath, err)
			} else {
				utils.LogPrintf("已删除解压目录: %s", oldPkg.ExtractPath)
			}
		}

		// 删除数据库记录
		if err := models.DeletePackage(oldPkg.ID); err != nil {
			utils.LogPrintf("删除包记录失败: ID=%d, 错误: %v", oldPkg.ID, err)
		} else {
			utils.LogPrintf("已删除包记录: ID=%d", oldPkg.ID)
		}
	}

	utils.LogPrintf("实验版本 %s 的未下载旧包版本清理完成", experimentVersionID)
}

// CheckForUpdates 检查程序更新
func (a *App) CheckForUpdates() (map[string]interface{}, error) {
	if services.UpdateServiceInstance == nil {
		return nil, fmt.Errorf("更新服务未初始化")
	}

	updateInfo, err := services.UpdateServiceInstance.CheckForUpdates()
	if err != nil {
		return nil, err
	}

	result := map[string]interface{}{
		"hasUpdate":      updateInfo.HasUpdate,
		"currentVersion": updateInfo.CurrentVersion,
		"latestVersion":  updateInfo.LatestVersion,
		"downloadUrl":    updateInfo.DownloadURL,
		"changelog":      updateInfo.Changelog,
		"fileSize":       updateInfo.FileSize,
		"checkSum":       updateInfo.CheckSum,
	}

	return result, nil
}

// DownloadUpdate 下载程序更新
func (a *App) DownloadUpdate(downloadURL, checkSum, version string) (string, error) {
	if services.UpdateServiceInstance == nil {
		return "", fmt.Errorf("更新服务未初始化")
	}

	return services.UpdateServiceInstance.DownloadUpdateWithHash(downloadURL, checkSum, version)
}

// StartUpdate 启动程序更新
func (a *App) StartUpdate(newExePath string) error {
	if services.UpdateServiceInstance == nil {
		return fmt.Errorf("更新服务未初始化")
	}

	return services.UpdateServiceInstance.StartUpdate(newExePath)
}

// CheckAndAutoUpdate 检查并自动更新
func (a *App) CheckAndAutoUpdate() error {
	if services.UpdateServiceInstance == nil {
		return fmt.Errorf("更新服务未初始化")
	}

	return services.UpdateServiceInstance.CheckAndAutoUpdate()
}

// GetUpdateStatus 获取更新状态
func (a *App) GetUpdateStatus() (map[string]interface{}, error) {
	if services.UpdateServiceInstance == nil {
		return nil, fmt.Errorf("更新服务未初始化")
	}

	status := map[string]interface{}{
		"currentVersion": BuildVersion,
		"autoUpdate":     true, // 学生端默认启用自动更新
	}

	return status, nil
}

// GetCurrentVersion 获取当前程序版本
func (a *App) GetCurrentVersion() string {
	return BuildVersion
}

// IsDevelopmentMode 检查是否为开发模式
func (a *App) IsDevelopmentMode() bool {
	devMode := os.Getenv("DEVELOPMENT_MODE")
	return strings.ToLower(devMode) == "true"
}

// checkUpdateOnStartup 启动时检查更新
func (a *App) checkUpdateOnStartup() {
	// 记录到日志文件
	utils.LogPrintf("[INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新")

	// 发送启动更新检查事件
	runtime.EventsEmit(a.ctx, "update-check-start", map[string]interface{}{
		"message": "正在检查程序更新...",
		"time":    time.Now().Format("15:04:05"),
	})

	// 检查更新服务是否可用
	if services.UpdateServiceInstance == nil {
		utils.LogPrintf("[ERROR] [UpdateService] [checkUpdateOnStartup] 更新服务未初始化")
		runtime.EventsEmit(a.ctx, "update-check-error", map[string]interface{}{
			"message": "更新服务未初始化",
			"time":    time.Now().Format("15:04:05"),
		})
		return
	}

	// 执行更新检查
	updateInfo, err := services.UpdateServiceInstance.CheckForUpdates()
	if err != nil {
		utils.LogPrintf("[WARN] [UpdateService] [checkUpdateOnStartup] 检查更新失败: %v", err)
		// 检查更新失败时不显示错误，静默处理，不影响正常使用
		utils.LogPrintf("[INFO] [UpdateService] [checkUpdateOnStartup] 无法连接学校端，跳过更新检查")
		return
	}

	if !updateInfo.HasUpdate {
		// 没有更新
		utils.LogPrintf("[INFO] [UpdateService] [checkUpdateOnStartup] 当前已是最新版本: %s", updateInfo.CurrentVersion)
		runtime.EventsEmit(a.ctx, "update-check-complete", map[string]interface{}{
			"hasUpdate": false,
			"message":   "当前已是最新版本",
			"version":   updateInfo.CurrentVersion,
			"time":      time.Now().Format("15:04:05"),
		})
		return
	}

	// 发现更新
	utils.LogPrintf("[INFO] [UpdateService] [checkUpdateOnStartup] 发现新版本: %s → %s", updateInfo.CurrentVersion, updateInfo.LatestVersion)
	runtime.EventsEmit(a.ctx, "update-check-complete", map[string]interface{}{
		"hasUpdate":      true,
		"message":        fmt.Sprintf("发现新版本: %s → %s", updateInfo.CurrentVersion, updateInfo.LatestVersion),
		"currentVersion": updateInfo.CurrentVersion,
		"latestVersion":  updateInfo.LatestVersion,
		"changelog":      updateInfo.Changelog,
		"downloadUrl":    updateInfo.DownloadURL,
		"checkSum":       updateInfo.CheckSum,
		"time":           time.Now().Format("15:04:05"),
	})

	// 开始倒计时，10秒后自动更新
	utils.LogPrintf("[INFO] [UpdateService] [checkUpdateOnStartup] 开始倒计时，10秒后自动更新")
	a.startUpdateCountdown(updateInfo)
}

// startUpdateCountdown 开始更新倒计时
func (a *App) startUpdateCountdown(updateInfo *services.UpdateInfo) {
	go func() {
		// 倒计时10秒
		for i := 10; i > 0; i-- {
			runtime.EventsEmit(a.ctx, "update-countdown", map[string]interface{}{
				"message":   fmt.Sprintf("发现新版本，%d秒后将自动更新", i),
				"countdown": i,
				"time":      time.Now().Format("15:04:05"),
			})
			time.Sleep(1 * time.Second)
		}

		// 倒计时结束，开始下载和更新
		utils.LogPrintf("[INFO] [UpdateService] [startUpdateCountdown] 倒计时结束，开始下载更新")
		a.executeUpdate(updateInfo)
	}()
}

// executeUpdate 执行更新下载和安装
func (a *App) executeUpdate(updateInfo *services.UpdateInfo) {
	// 开始下载更新
	utils.LogPrintf("[INFO] [UpdateService] [executeUpdate] 开始下载更新文件: %s", updateInfo.DownloadURL)
	runtime.EventsEmit(a.ctx, "update-download-start", map[string]interface{}{
		"message": "开始下载更新文件...",
		"time":    time.Now().Format("15:04:05"),
	})

	// 下载更新文件
	newExePath, err := services.UpdateServiceInstance.DownloadUpdateWithHash(
		updateInfo.DownloadURL,
		updateInfo.CheckSum,
		updateInfo.LatestVersion,
	)
	if err != nil {
		utils.LogPrintf("[ERROR] [UpdateService] [executeUpdate] 下载更新失败: %v", err)
		runtime.EventsEmit(a.ctx, "update-download-error", map[string]interface{}{
			"message": fmt.Sprintf("下载更新失败: %v", err),
			"time":    time.Now().Format("15:04:05"),
		})
		return
	}

	// 下载完成
	utils.LogPrintf("[INFO] [UpdateService] [executeUpdate] 更新文件下载完成: %s", newExePath)
	runtime.EventsEmit(a.ctx, "update-download-complete", map[string]interface{}{
		"message":  "更新文件下载完成",
		"filePath": newExePath,
		"time":     time.Now().Format("15:04:05"),
	})

	// 自动开始更新
	utils.LogPrintf("[INFO] [UpdateService] [executeUpdate] 开始自动更新")
	runtime.EventsEmit(a.ctx, "update-start", map[string]interface{}{
		"message": "正在启动更新器，程序即将重启...",
		"time":    time.Now().Format("15:04:05"),
	})

	// 启动更新
	if err := services.UpdateServiceInstance.StartUpdate(newExePath); err != nil {
		utils.LogPrintf("[ERROR] [UpdateService] [executeUpdate] 启动更新失败: %v", err)
		runtime.EventsEmit(a.ctx, "update-error", map[string]interface{}{
			"message": fmt.Sprintf("启动更新失败: %v", err),
			"time":    time.Now().Format("15:04:05"),
		})
		return
	}

	utils.LogPrintf("[INFO] [UpdateService] [executeUpdate] 更新器已启动，程序即将重启")
}

// StartUpdateNow 立即开始更新
func (a *App) StartUpdateNow(newExePath string) error {
	if services.UpdateServiceInstance == nil {
		return fmt.Errorf("更新服务未初始化")
	}

	// 发送更新开始事件
	runtime.EventsEmit(a.ctx, "update-start", map[string]interface{}{
		"message": "正在启动更新器，程序即将重启...",
		"time":    time.Now().Format("15:04:05"),
	})

	// 延迟一下让前端接收到事件
	time.Sleep(1 * time.Second)

	return services.UpdateServiceInstance.StartUpdate(newExePath)
}

// GetUpdateLogs 获取更新日志
func (a *App) GetUpdateLogs() ([]map[string]interface{}, error) {
	// 读取最近的日志文件
	logFiles, err := filepath.Glob("./logs/student-client-*.log")
	if err != nil {
		return nil, err
	}

	if len(logFiles) == 0 {
		return []map[string]interface{}{}, nil
	}

	// 读取最新的日志文件
	latestLogFile := logFiles[len(logFiles)-1]
	content, err := os.ReadFile(latestLogFile)
	if err != nil {
		return nil, err
	}

	// 解析日志内容，提取更新相关的日志
	lines := strings.Split(string(content), "\n")
	var updateLogs []map[string]interface{}

	for _, line := range lines {
		if strings.Contains(line, "UpdateService") || strings.Contains(line, "VersionManager") {
			// 简单解析日志行
			parts := strings.SplitN(line, "] ", 4)
			if len(parts) >= 4 {
				updateLogs = append(updateLogs, map[string]interface{}{
					"time":    parts[0][1:], // 移除开头的 [
					"level":   parts[1][1:], // 移除开头的 [
					"module":  parts[2][1:], // 移除开头的 [
					"message": parts[3],
				})
			}
		}
	}

	// 只返回最近的50条日志
	if len(updateLogs) > 50 {
		updateLogs = updateLogs[len(updateLogs)-50:]
	}

	return updateLogs, nil
}

// getStatusText 获取状态文本
func getStatusText(status int) string {
	switch status {
	case models.LogStatusSuccess:
		return "成功"
	case models.LogStatusRunning:
		return "进行中"
	case models.LogStatusFailed:
		return "失败"
	default:
		return "未知"
	}
}
