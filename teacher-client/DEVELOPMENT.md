# 教师端开发指南

## 快速开始

### 环境准备
```bash
# 1. 安装 Go (1.19+)
go version

# 2. 安装 Node.js (16+)
node --version
npm --version

# 3. 安装 Wails CLI
go install github.com/wailsapp/wails/v2/cmd/wails@latest

# 4. 验证安装
wails doctor
```

### 项目初始化
```bash
# 克隆项目
git clone <repository-url>
cd teacher-client

# 安装 Go 依赖
go mod tidy

# 安装前端依赖
cd frontend
npm install
cd ..

# 首次运行
wails dev
```

## 开发工作流

### 1. 日常开发
```bash
# 启动开发服务器
wails dev

# 在另一个终端查看日志
tail -f logs/teacher-client.log

# 前端热重载会自动生效
# 后端代码修改需要重启 wails dev
```

### 2. 添加新功能

#### 后端 API 开发
```go
// 1. 在 app.go 中添加新方法
func (a *App) NewFeature(param string) (*Result, error) {
    // 业务逻辑
    return &Result{}, nil
}

// 2. 在对应的 service 中实现具体逻辑
func (s *SomeService) ProcessNewFeature(param string) error {
    // 实现细节
    return nil
}

// 3. 添加单元测试
func TestNewFeature(t *testing.T) {
    // 测试用例
}
```

#### 前端页面开发
```javascript
// 1. 创建新的 Vue 组件
// frontend/src/views/NewFeature.vue
<template>
  <div class="new-feature">
    <!-- UI 内容 -->
  </div>
</template>

<script>
export default {
  name: 'NewFeature',
  methods: {
    async callBackend() {
      try {
        const result = await window.go.main.App.NewFeature('param')
        console.log(result)
      } catch (error) {
        this.$message.error('操作失败: ' + error)
      }
    }
  }
}
</script>
```

#### 路由配置
```javascript
// frontend/src/router/index.js
{
  path: '/new-feature',
  name: 'NewFeature',
  component: () => import('../views/NewFeature.vue')
}
```

### 3. 数据库迁移

#### 创建新迁移
```go
// utils/migrations/migration_25053002.go
package migrations

import (
    "database/sql"
    "fmt"
)

type Migration25053002 struct {
    db *sql.DB
}

func NewMigration25053002(db *sql.DB) *Migration25053002 {
    return &Migration25053002{db: db}
}

func (m *Migration25053002) Version() string {
    return "25053002"
}

func (m *Migration25053002) Up() error {
    queries := []string{
        `ALTER TABLE experiments ADD COLUMN new_field TEXT DEFAULT ''`,
        `CREATE INDEX idx_experiments_new_field ON experiments(new_field)`,
    }
    
    for _, query := range queries {
        if _, err := m.db.Exec(query); err != nil {
            return fmt.Errorf("执行迁移失败: %w", err)
        }
    }
    
    return nil
}

func (m *Migration25053002) Down() error {
    // 回滚操作
    _, err := m.db.Exec(`ALTER TABLE experiments DROP COLUMN new_field`)
    return err
}
```

#### 注册迁移
```go
// utils/version_manager.go
func (vm *VersionManager) getAllMigrations() []Migration {
    return []Migration{
        migrations.NewMigration25053001(vm.db),
        migrations.NewMigration25053002(vm.db), // 新增
        // ...
    }
}
```

### 4. 测试开发

#### 单元测试
```go
// services/auth_service_test.go
package services

import (
    "testing"
    "teacher-client/models"
)

func TestAuthService_Login(t *testing.T) {
    // 设置测试数据库
    models.InitTestDB()
    defer models.CloseTestDB()
    
    service := NewAuthService("http://test-api.com")
    
    tests := []struct {
        name     string
        username string
        password string
        wantErr  bool
    }{
        {"valid credentials", "test", "password", false},
        {"invalid credentials", "test", "wrong", true},
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            err := service.Login(tt.username, tt.password, "test-school")
            if (err != nil) != tt.wantErr {
                t.Errorf("Login() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}
```

#### 集成测试
```go
// integration_test.go
//go:build integration
// +build integration

package main

import (
    "testing"
    "time"
)

func TestFullWorkflow(t *testing.T) {
    // 启动测试应用
    app := NewApp()
    
    // 测试登录
    result, err := app.Login("test", "password", "test-school")
    if err != nil {
        t.Fatalf("登录失败: %v", err)
    }
    
    // 测试同步
    err = app.SyncPackages()
    if err != nil {
        t.Fatalf("同步失败: %v", err)
    }
    
    // 验证数据
    library, err := app.GetLibrary()
    if err != nil {
        t.Fatalf("获取库失败: %v", err)
    }
    
    if len(library) == 0 {
        t.Error("库为空")
    }
}
```

## 构建和部署

### 开发构建
```bash
# 开发模式构建（包含调试信息）
wails build -debug

# 检查构建产物
ls -la build/bin/
```

### 生产构建
```bash
# Windows 构建
wails build -platform windows/amd64 -clean

# macOS 构建
wails build -platform darwin/amd64 -clean

# Linux 构建
wails build -platform linux/amd64 -clean
```

### 跨平台构建
```bash
# 在 macOS 上构建 Windows 版本
export CGO_ENABLED=1
export CC=x86_64-w64-mingw32-gcc
export CXX=x86_64-w64-mingw32-g++
wails build -platform windows/amd64 -ldflags '-extldflags "-static"' -skipbindings
```

### 构建脚本
```bash
#!/bin/bash
# build.sh

set -e

echo "开始构建教师端..."

# 清理旧的构建
rm -rf build/

# 安装依赖
echo "安装 Go 依赖..."
go mod tidy

echo "安装前端依赖..."
cd frontend
npm ci
cd ..

# 构建不同平台版本
platforms=("windows/amd64" "darwin/amd64" "linux/amd64")

for platform in "${platforms[@]}"; do
    echo "构建 $platform..."
    wails build -platform "$platform" -clean
done

echo "构建完成！"
ls -la build/bin/
```

## 调试技巧

### 1. 后端调试
```go
// 使用结构化日志
logger.Info("AuthService", "Login", 
    fmt.Sprintf("用户 %s 尝试登录", username))

// 添加调试断点（开发模式）
if os.Getenv("DEBUG") == "true" {
    fmt.Printf("Debug: %+v\n", data)
}

// 使用 context 传递调试信息
ctx = context.WithValue(ctx, "trace_id", generateTraceID())
```

### 2. 前端调试
```javascript
// 开发模式下的调试输出
if (import.meta.env.DEV) {
    console.log('Debug info:', data)
}

// 使用 Vue DevTools
// 在浏览器中按 F12 打开开发者工具

// 调试 Go 方法调用
const debugCall = async (method, ...args) => {
    console.log(`Calling ${method} with:`, args)
    try {
        const result = await window.go.main.App[method](...args)
        console.log(`${method} result:`, result)
        return result
    } catch (error) {
        console.error(`${method} error:`, error)
        throw error
    }
}
```

### 3. 网络调试
```bash
# 使用 curl 测试 API
curl -X POST "http://localhost:8900/v1/packageSystem/packageSystemSchool/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"password","schoolName":"test"}'

# 使用 tcpdump 抓包
sudo tcpdump -i any -w network.pcap host api.cdzyhd.com

# 使用 Wireshark 分析网络包
wireshark network.pcap
```

## 性能优化

### 1. 内存优化
```go
// 使用对象池
var bufferPool = sync.Pool{
    New: func() interface{} {
        return make([]byte, 32*1024) // 32KB buffer
    },
}

// 及时释放大对象
func processLargeFile(filePath string) error {
    data, err := os.ReadFile(filePath)
    if err != nil {
        return err
    }
    defer func() {
        data = nil // 帮助 GC
    }()
    
    // 处理数据...
    return nil
}
```

### 2. 并发优化
```go
// 使用 worker pool 控制并发
func (s *SyncService) downloadPackages(packages []Package) error {
    const maxWorkers = 3
    semaphore := make(chan struct{}, maxWorkers)
    
    var wg sync.WaitGroup
    for _, pkg := range packages {
        wg.Add(1)
        go func(p Package) {
            defer wg.Done()
            semaphore <- struct{}{} // 获取信号量
            defer func() { <-semaphore }() // 释放信号量
            
            s.downloadPackage(p)
        }(pkg)
    }
    
    wg.Wait()
    return nil
}
```

### 3. 前端优化
```javascript
// 使用 computed 缓存计算结果
computed: {
    filteredExperiments() {
        return this.experiments.filter(exp => 
            exp.name.includes(this.searchText)
        )
    }
}

// 使用 v-memo 优化列表渲染
<template v-for="item in largeList" :key="item.id" v-memo="[item.id, item.status]">
    <ExperimentCard :item="item" />
</template>

// 懒加载组件
const LazyComponent = defineAsyncComponent(() => import('./HeavyComponent.vue'))
```

## 常见问题

### 1. 编译错误
```bash
# CGO 相关错误
export CGO_ENABLED=1

# 缺少 C 编译器
# macOS: xcode-select --install
# Ubuntu: sudo apt-get install build-essential
# Windows: 安装 TDM-GCC 或 MinGW

# Go 版本不兼容
go mod edit -go=1.19
```

### 2. 运行时错误
```go
// 处理 panic
defer func() {
    if r := recover(); r != nil {
        logger.Error("App", "Panic", fmt.Sprintf("程序崩溃: %v", r))
        // 发送错误报告
    }
}()

// 检查文件权限
if _, err := os.Stat(filePath); os.IsPermission(err) {
    return fmt.Errorf("文件权限不足: %s", filePath)
}
```

### 3. 前端错误
```javascript
// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
    console.error('Vue error:', err, info)
    // 发送错误报告到服务器
}

// Promise 错误处理
window.addEventListener('unhandledrejection', event => {
    console.error('Unhandled promise rejection:', event.reason)
    event.preventDefault()
})
```

## 代码规范

### 1. Go 代码规范
```go
// 使用 gofmt 格式化代码
go fmt ./...

// 使用 golint 检查代码
golint ./...

// 使用 go vet 检查潜在问题
go vet ./...

// 命名规范
type UserService struct {} // 大写开头，公开
func (s *UserService) GetUser() {} // 大写开头，公开
func (s *UserService) validateUser() {} // 小写开头，私有
```

### 2. JavaScript 代码规范
```javascript
// 使用 ESLint
npm run lint

// 使用 Prettier 格式化
npm run format

// 命名规范
const userName = 'test' // camelCase
const API_URL = 'http://...' // UPPER_CASE for constants
```

### 3. 提交规范
```bash
# 使用 Conventional Commits
git commit -m "feat: 添加用户登录功能"
git commit -m "fix: 修复同步失败问题"
git commit -m "docs: 更新开发文档"
git commit -m "refactor: 重构认证服务"
```
