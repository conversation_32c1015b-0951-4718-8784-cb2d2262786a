package main

import (
	"embed"
	"log"

	"github.com/joho/godotenv"
	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/options/assetserver"

	"teacher-client/models"
	"teacher-client/services"
	"teacher-client/utils"
	"teacher-client/utils/migrations"
)

//go:embed all:frontend/dist
var assets embed.FS

// BuildVersion 程序版本号 - 自动从配置获取
var BuildVersion = migrations.GetCurrentVersion()

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("警告：未找到.env文件，使用默认设置")
	}

	// 确保必要的目录存在
	ensureDirectories()

	// 初始化日志系统
	if err := utils.InitLogger(); err != nil {
		log.Printf("初始化日志系统失败: %v", err)
	}

	utils.LogPrintf("教师端包管理系统启动，版本: %s", BuildVersion)

	// 初始化数据库
	if err := models.InitDB(); err != nil {
		utils.LogPrintf("初始化数据库失败: %v", err)
		log.Fatalf("初始化数据库失败: %v", err)
	}

	// 版本检查和数据库迁移
	logger, err := utils.GetLogger()
	if err != nil {
		utils.LogPrintf("获取日志器失败: %v", err)
	} else {
		versionManager := utils.NewVersionManager(BuildVersion, logger, models.DB)
		if err := versionManager.CheckAndMigrate(); err != nil {
			utils.LogPrintf("版本迁移失败: %v", err)
		}
	}

	// 初始化更新服务（自动检查更新）
	if logger != nil {
		services.InitUpdateService(BuildVersion, logger)
	}

	// 创建应用实例
	app := NewApp()

	// 创建应用窗口
	err = wails.Run(&options.App{
		Title:  "教师端包管理系统",
		Width:  1024,
		Height: 768,
		AssetServer: &assetserver.Options{
			Assets: assets,
		},
		BackgroundColour: &options.RGBA{R: 255, G: 255, B: 255, A: 1},
		OnStartup:        app.startup,
		OnShutdown:       app.shutdown,
		Bind: []interface{}{
			app,
		},
	})

	if err != nil {
		log.Fatal("Error starting application:", err)
	}
}

// ensureDirectories 确保必要的目录存在
func ensureDirectories() {
	dirs := []string{
		"./data",
		"./downloads",
		"./packages",
		"./extracted",
		"./logs",
		"./updates",
		"./backup",
		"./temp",
		"./config",
	}

	for _, dir := range dirs {
		if err := utils.EnsureDir(dir); err != nil {
			log.Printf("创建目录失败 %s: %v\n", dir, err)
		}
	}
}
