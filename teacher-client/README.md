# 教师端包管理系统架构文档

## 项目概述

教师端包管理系统是一个基于 Wails 框架开发的桌面应用程序，采用 Go 后端 + Vue.js 前端的混合架构。教师端直接连接中央服务器，绕过学校服务器层级，为教师提供实验包管理功能。

## 系统架构

### 整体架构
```
教师端 (Teacher Client)
    ↓ 直接连接
中央服务器 (Central Server)
```

### 技术栈
- **后端**: Go + Wails v2
- **前端**: Vue.js 3 + Element Plus
- **数据库**: SQLite
- **构建工具**: Wails CLI
- **包管理**: Go Modules + npm

## 目录结构

```
teacher-client/
├── app.go                    # Wails 应用主入口
├── main.go                   # 程序启动入口
├── wails.json               # Wails 配置文件
├── build/                   # 构建输出目录
├── frontend/                # Vue.js 前端代码
│   ├── src/
│   │   ├── views/          # 页面组件
│   │   ├── components/     # 通用组件
│   │   ├── router/         # 路由配置
│   │   └── main.js        # 前端入口
│   ├── package.json
│   └── vite.config.js
├── models/                  # 数据模型
│   ├── database.go         # 数据库初始化
│   ├── config.go           # 配置管理
│   ├── central_auth.go     # 中央端认证
│   └── central_sync.go     # 中央端同步
├── services/                # 业务服务
│   ├── auth_service.go     # 认证服务
│   ├── sync_service.go     # 同步服务
│   ├── extract_service.go  # 解压服务
│   └── update_service.go   # 自动更新服务
└── utils/                   # 工具类
    ├── logger.go           # 日志工具
    ├── hash.go             # 哈希计算
    └── version_manager.go  # 版本管理
```

## 核心功能模块

### 1. 认证模块 (AuthService)
- **功能**: 与中央服务器进行用户认证
- **特点**:
  - 使用中央服务器的学校端登录API
  - 支持长期token记忆
  - 自动获取中央服务器URL配置

### 2. 同步模块 (SyncService)
- **功能**: 从中央服务器同步实验包数据
- **特点**:
  - 直接连接中央服务器
  - 支持增量同步
  - 自动处理推送状态

### 3. 解压模块 (ExtractService)
- **功能**: 处理实验包的下载和解压
- **特点**:
  - 支持中文路径
  - 自动清理旧版本
  - 智能查找可执行文件

### 4. 自动更新模块 (UpdateService)
- **功能**: 检查和执行程序自动更新
- **特点**:
  - 连接中央服务器检查更新 (type=teacher)
  - 使用xxHash算法验证文件完整性
  - 支持静默自动更新

### 5. 配置管理 (Config)
- **功能**: 管理应用配置信息
- **特点**:
  - 支持中央服务器URL配置
  - 数据库存储配置
  - 环境变量优先级

## 数据流架构

### 登录流程
```
1. 用户输入凭据 → 2. 连接中央服务器认证 → 3. 获取token → 4. 保存认证状态
```

### 同步流程
```
1. 获取推送列表 → 2. 下载实验包 → 3. 验证哈希 → 4. 解压到本地 → 5. 更新数据库
```

### 更新流程
```
1. 检查中央服务器版本 → 2. 下载新版本 → 3. 验证文件 → 4. 启动更新器 → 5. 重启应用
```

## 关键设计决策

### 1. 直连中央服务器
- **原因**: 教师端需要访问全局数据，不依赖特定学校服务器
- **优势**: 简化架构，提高可靠性
- **实现**: 使用相同的API端点，但绕过学校服务器层级

### 2. 混合架构 (Wails)
- **原因**: 结合Web技术的灵活性和原生应用的性能
- **优势**: 快速开发，跨平台支持
- **实现**: Go处理业务逻辑，Vue.js处理用户界面

### 3. SQLite数据库
- **原因**: 轻量级，无需额外配置
- **优势**: 简化部署，提高性能
- **实现**: 本地存储配置和缓存数据

## API接口设计

### 中央服务器接口
```go
// 登录认证
POST /v1/packageSystem/packageSystemSchool/login

// 获取推送列表
POST /v1/packageSystem/packagePush/oneSchoolPushList

// 检查更新
GET /v1/packageSystem/packageClientUpdate/getLatestPushedVersion?type=teacher
```

### Wails前后端通信
```go
// 应用方法暴露给前端
func (a *App) Login(username, password, schoolName string) LoginResult
func (a *App) GetLibrary() []ExperimentItem
func (a *App) SyncPackages() error
func (a *App) SetCentralURL(url string) error
```

## 开发规范

### 1. 错误处理
- 统一使用Go的error接口
- 前端显示用户友好的错误信息
- 详细错误记录到日志文件

### 2. 日志记录
- 使用结构化日志格式
- 按模块分类记录
- 支持日志轮转和清理

### 3. 配置管理
- 优先级: 环境变量 > 数据库配置 > 默认值
- 敏感信息加密存储
- 支持运行时配置更新

## 部署和构建

### 开发环境
```bash
# 安装依赖
go mod tidy
cd frontend && npm install

# 开发模式运行
wails dev
```

### 生产构建
```bash
# Windows构建
wails build -platform windows/amd64

# macOS构建
wails build -platform darwin/amd64
```

### 跨平台编译
```bash
# 从macOS编译Windows版本
env GOOS=windows GOARCH=amd64 CGO_ENABLED=1 \
CC=x86_64-w64-mingw32-gcc CXX=x86_64-w64-mingw32-g++ \
wails build -ldflags '-extldflags "-static"' -skipbindings
```

## 版本管理

### 版本号格式
- 格式: YYYYMMDDNN (如: 25053004)
- 年月日 + 两位序号
- 支持数字比较和字符串显示

### 迁移机制
- 线性版本升级
- 每个版本独立的迁移脚本
- 支持SQL和Go方法混合迁移

## 安全考虑

### 1. 认证安全
- Token有效期管理
- 安全的密码传输
- 会话状态保护

### 2. 文件安全
- 下载文件哈希验证
- 解压路径验证
- 可执行文件权限控制

### 3. 网络安全
- HTTPS通信
- 请求超时控制
- 错误信息脱敏

## 性能优化

### 1. 数据库优化
- 合理的索引设计
- 批量操作优化
- 连接池管理

### 2. 网络优化
- 并发下载控制
- 断点续传支持
- 缓存策略

### 3. 内存优化
- 大文件流式处理
- 及时释放资源
- 垃圾回收优化

## 测试策略

### 1. 单元测试
- 核心业务逻辑测试
- 工具函数测试
- 错误处理测试

### 2. 集成测试
- API接口测试
- 数据库操作测试
- 文件操作测试

### 3. 端到端测试
- 完整业务流程测试
- 用户界面交互测试
- 跨平台兼容性测试

## 故障排查

### 1. 日志分析
- 查看应用日志文件
- 检查错误堆栈信息
- 分析性能指标

### 2. 网络问题
- 检查中央服务器连接
- 验证网络配置
- 测试API接口可用性

### 3. 数据库问题
- 检查数据库文件完整性
- 验证表结构
- 分析查询性能

## 开发最佳实践

### 1. 代码组织
```go
// 服务层接口定义
type AuthService interface {
    Login(username, password, schoolName string) (*LoginResult, error)
    Logout() error
    IsAuthenticated() bool
}

// 实现依赖注入
type App struct {
    authService AuthService
    syncService SyncService
    // ...
}
```

### 2. 错误处理模式
```go
// 统一错误类型
type AppError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}

// 错误包装
func (s *AuthService) Login(username, password string) error {
    if err := s.validateCredentials(username, password); err != nil {
        return fmt.Errorf("验证凭据失败: %w", err)
    }
    // ...
}
```

### 3. 配置管理模式
```go
// 配置优先级处理
func getCentralAPIURL() string {
    // 1. 环境变量优先
    if url := os.Getenv("CENTRAL_API_URL"); url != "" {
        return url
    }

    // 2. 数据库配置
    if url, err := models.GetConfig("central_url"); err == nil && url != "" {
        return url
    }

    // 3. 默认值
    return "http://localhost:8900"
}
```

### 4. 前端状态管理
```javascript
// 使用 Pinia 进行状态管理
export const useAuthStore = defineStore('auth', {
    state: () => ({
        isAuthenticated: false,
        userInfo: null,
        token: null
    }),

    actions: {
        async login(credentials) {
            try {
                const result = await window.go.main.App.Login(credentials)
                this.setAuthState(result)
                return result
            } catch (error) {
                throw new Error(`登录失败: ${error}`)
            }
        }
    }
})
```

## 关键技术细节

### 1. Wails 绑定机制
```go
// 在 app.go 中暴露方法给前端
//go:embed all:frontend/dist
var assets embed.FS

// App struct
type App struct {
    ctx context.Context
}

// NewApp creates a new App application struct
func NewApp() *App {
    return &App{}
}

// startup is called when the app starts up
func (a *App) startup(ctx context.Context) {
    a.ctx = ctx
}

// 暴露给前端的方法必须是公开的
func (a *App) GetLibrary() []ExperimentItem {
    // 实现逻辑
}
```

### 2. 数据库迁移系统
```go
// 版本迁移接口
type Migration interface {
    Version() string
    Up() error
    Down() error
}

// 具体迁移实现
type Migration25053001 struct {
    db *sql.DB
}

func (m *Migration25053001) Version() string {
    return "25053001"
}

func (m *Migration25053001) Up() error {
    // 执行升级SQL
    _, err := m.db.Exec(`
        CREATE TABLE IF NOT EXISTS experiments (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            version TEXT NOT NULL
        )
    `)
    return err
}
```

### 3. 文件哈希验证
```go
// 使用 xxHash 算法保持与服务端一致
func CalculateFileHash(filePath string) (string, error) {
    file, err := os.Open(filePath)
    if err != nil {
        return "", err
    }
    defer file.Close()

    hasher := xxhash.New()
    if _, err := io.Copy(hasher, file); err != nil {
        return "", err
    }

    return fmt.Sprintf("%x", hasher.Sum64()), nil
}
```

## 调试和开发工具

### 1. 开发模式调试
```bash
# 启用详细日志
export LOG_LEVEL=debug

# 开发模式运行
wails dev

# 查看实时日志
tail -f logs/teacher-client.log
```

### 2. 前端调试
```javascript
// 在开发模式下启用 Vue DevTools
if (import.meta.env.DEV) {
    window.__VUE_DEVTOOLS_GLOBAL_HOOK__ = true
}

// 调试 Go 方法调用
console.log('Calling Go method:', method, params)
const result = await window.go.main.App[method](...params)
console.log('Go method result:', result)
```

### 3. 性能监控
```go
// 添加性能监控中间件
func (s *SyncService) SyncWithMetrics() error {
    start := time.Now()
    defer func() {
        duration := time.Since(start)
        s.logger.Info("SyncService", "Performance",
            fmt.Sprintf("同步耗时: %v", duration))
    }()

    return s.doSync()
}
```

## 常见问题解决

### 1. 跨平台编译问题
```bash
# 安装 mingw-w64 (macOS)
brew install mingw-w64

# 设置 CGO 环境变量
export CGO_ENABLED=1
export CC=x86_64-w64-mingw32-gcc
export CXX=x86_64-w64-mingw32-g++
```

### 2. 中文路径问题
```go
// 处理 Windows 中文路径
func extractZipFile(src, dest string) error {
    // 使用 UTF-8 编码处理中文路径
    reader, err := zip.OpenReader(src)
    if err != nil {
        return err
    }
    defer reader.Close()

    for _, file := range reader.File {
        // 处理中文文件名编码
        fileName := file.Name
        if !utf8.ValidString(fileName) {
            // 尝试 GBK 解码
            if decoded, err := simplifiedchinese.GBK.NewDecoder().String(fileName); err == nil {
                fileName = decoded
            }
        }
        // 继续处理...
    }
}
```

### 3. 内存泄漏预防
```go
// 确保资源及时释放
func (s *ExtractService) ExtractPackage(packageID int64) error {
    // 使用 context 控制超时
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
    defer cancel()

    // 确保文件句柄关闭
    file, err := os.Open(packagePath)
    if err != nil {
        return err
    }
    defer file.Close()

    // 使用 sync.Pool 复用缓冲区
    buffer := bufferPool.Get().([]byte)
    defer bufferPool.Put(buffer)

    return s.doExtract(ctx, file, buffer)
}
```

## 未来规划

### 1. 功能扩展
- 支持更多实验包格式
- 增加批量操作功能
- 优化用户体验
- 添加实验包预览功能

### 2. 性能提升
- 优化同步算法
- 改进缓存策略
- 减少内存占用
- 支持并发下载

### 3. 安全增强
- 增加数据加密
- 完善权限控制
- 强化安全审计
- 实现数字签名验证
