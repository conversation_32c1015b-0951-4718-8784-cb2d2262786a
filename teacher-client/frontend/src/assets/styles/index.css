/* 全局样式 */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #909399;
}

/* 布局样式 */
.main-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.el-header {
  background-color: #304156;
  color: #fff;
  line-height: 60px;
  padding: 0 20px;
}

.el-aside {
  background-color: #304156;
  color: #fff;
}

.el-main {
  background-color: #f0f2f5;
  padding: 20px;
  overflow-y: auto;
}

/* 卡片样式 */
.el-card {
  margin-bottom: 20px;
  border-radius: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 表格样式 */
.el-table {
  margin-top: 15px;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 22px;
}

/* 按钮样式 */
.action-buttons {
  margin-top: 20px;
  text-align: right;
}

/* 进度条样式 */
.progress-container {
  margin: 15px 0;
}

/* 状态标签样式 */
.status-tag {
  text-align: center;
}

/* 图标样式 */
.icon-container {
  margin-right: 5px;
  vertical-align: middle;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .el-aside {
    width: 64px !important;
  }
  
  .el-main {
    padding: 10px;
  }
}
