<template>
  <div class="package-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <h2>实验包列表</h2>
          <div class="header-actions">
            <el-input
              v-model="searchQuery"
              placeholder="搜索实验包"
              class="search-input"
              clearable
              @clear="handleSearch"
            >
              <template #prefix>
                <el-icon><el-icon-search /></el-icon>
              </template>
            </el-input>
            <el-button type="primary" @click="refreshPackages" :loading="loading">
              <el-icon><el-icon-refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="全部" name="all"></el-tab-pane>
        <el-tab-pane label="已下载" name="downloaded"></el-tab-pane>
        <el-tab-pane label="可更新" name="updates"></el-tab-pane>
      </el-tabs>

      <el-table
        :data="filteredPackages"
        v-loading="loading"
        style="width: 100%"
        @row-click="handleRowClick"
      >
        <el-table-column prop="experimentName" label="实验名称" min-width="180" />
        <el-table-column prop="versionName" label="版本名称" min-width="150" />
        <el-table-column prop="version" label="版本号" width="100" />
        <el-table-column label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.syncStatus)">
              {{ getStatusText(scope.row.syncStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250">
          <template #default="scope">
            <el-button
              v-if="scope.row.syncStatus === 4"
              type="success"
              @click.stop="runPackage(scope.row.id)"
            >
              运行
            </el-button>
            <el-button
              v-else-if="scope.row.syncStatus === 0 || scope.row.syncStatus === 5"
              type="primary"
              @click.stop="downloadPackage(scope.row.id)"
            >
              {{ scope.row.syncStatus === 5 ? '重新下载' : '下载' }}
            </el-button>
            <el-button
              v-else-if="scope.row.syncStatus === 1"
              type="info"
              disabled
            >
              下载中...
            </el-button>
            <el-button
              v-else-if="scope.row.syncStatus === 3"
              type="info"
              disabled
            >
              解压中...
            </el-button>
            <el-button
              type="info"
              @click.stop="viewDetails(scope.row.id)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:currentPage="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalPackages"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 下载进度对话框 -->
    <el-dialog
      v-model="downloadDialogVisible"
      title="下载进度"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="download-progress">
        <div class="progress-info">
          <span>{{ downloadingPackage.experimentName }} - {{ downloadingPackage.versionName }}</span>
          <span>{{ formatFileSize(downloadProgress.downloaded) }} / {{ formatFileSize(downloadProgress.total) }}</span>
        </div>
        <el-progress
          :percentage="downloadProgress.percentage"
          :status="downloadStatus"
        />
        <div class="download-actions">
          <el-button
            v-if="downloadStatus === 'success'"
            type="primary"
            @click="closeDownloadDialog"
          >
            完成
          </el-button>
          <el-button
            v-else-if="downloadStatus === 'exception'"
            type="danger"
            @click="retryDownload"
          >
            重试
          </el-button>
          <el-button
            v-else
            type="warning"
            @click="cancelDownload"
          >
            取消
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'PackageList',
  data() {
    return {
      packages: [],
      loading: false,
      searchQuery: '',
      activeTab: 'all',
      currentPage: 1,
      pageSize: 10,
      totalPackages: 0,
      downloadDialogVisible: false,
      downloadingPackage: {},
      downloadProgress: {
        total: 0,
        downloaded: 0,
        percentage: 0
      },
      downloadStatus: 'primary' // primary, success, exception
    }
  },
  computed: {
    filteredPackages() {
      let result = [...this.packages]

      // 根据标签筛选
      if (this.activeTab === 'downloaded') {
        result = result.filter(pkg => pkg.syncStatus === 2 || pkg.syncStatus === 4)
      } else if (this.activeTab === 'updates') {
        result = result.filter(pkg => pkg.hasUpdate === true)
      }

      // 根据搜索词筛选
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        result = result.filter(pkg =>
          pkg.experimentName.toLowerCase().includes(query) ||
          pkg.versionName.toLowerCase().includes(query) ||
          pkg.version.toLowerCase().includes(query)
        )
      }

      return result
    }
  },
  mounted() {
    this.fetchPackages()
  },
  methods: {
    async fetchPackages() {
      this.loading = true
      try {
        // 从Go后端获取包列表
        // const result = await window.go.main.App.GetPackages(this.currentPage, this.pageSize)
        // this.packages = result.items
        // this.totalPackages = result.total

        // 模拟数据
        this.packages = [
          { id: 1, experimentName: '物理实验', versionName: '力学基础', version: '1.0.0', syncStatus: 2 },
          { id: 2, experimentName: '化学实验', versionName: '滴定分析', version: '2.1.0', syncStatus: 3 },
          { id: 3, experimentName: '生物实验', versionName: '细胞观察', version: '1.5.0', syncStatus: 0 },
          { id: 4, experimentName: '地理实验', versionName: '地形测量', version: '1.2.0', syncStatus: 1 }
        ]
        this.totalPackages = 4
      } catch (error) {
        console.error('获取包列表失败:', error)
        this.$message.error('获取包列表失败')
      } finally {
        this.loading = false
      }
    },
    refreshPackages() {
      this.fetchPackages()
    },
    handleSearch() {
      this.currentPage = 1
      this.fetchPackages()
    },
    handleTabClick() {
      this.currentPage = 1
      this.fetchPackages()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchPackages()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchPackages()
    },
    handleRowClick(row) {
      this.viewDetails(row.id)
    },
    getStatusType(status) {
      const types = {
        0: 'info',    // 未同步
        1: 'warning', // 下载中
        2: 'success', // 已下载
        3: 'primary', // 解压中
        4: 'success', // 已完成
        5: 'danger'   // 失败
      }
      return types[status] || 'info'
    },
    getStatusText(status) {
      const texts = {
        0: '未下载',
        1: '下载中',
        2: '已下载',
        3: '解压中',
        4: '可运行',
        5: '下载失败'
      }
      return texts[status] || '未知'
    },
    async downloadPackage(id) {
      try {
        const pkg = this.packages.find(p => p.id === id)
        if (!pkg) return

        this.downloadingPackage = pkg
        this.downloadDialogVisible = true
        this.downloadProgress = {
          total: 100000000, // 模拟100MB
          downloaded: 0,
          percentage: 0
        }
        this.downloadStatus = 'primary'

        // 模拟下载进度
        const interval = setInterval(() => {
          this.downloadProgress.downloaded += 5000000 // 每次增加5MB
          this.downloadProgress.percentage = Math.min(
            Math.floor(this.downloadProgress.downloaded / this.downloadProgress.total * 100),
            99
          )

          if (this.downloadProgress.percentage >= 99) {
            clearInterval(interval)
            setTimeout(() => {
              this.downloadProgress.percentage = 100
              this.downloadStatus = 'success'

              // 更新包状态
              const index = this.packages.findIndex(p => p.id === id)
              if (index !== -1) {
                this.packages[index].syncStatus = 2 // 已下载
              }
            }, 500)
          }
        }, 500)

        // 实际应该调用Go后端下载
        // await window.go.main.App.DownloadPackage(id)
      } catch (error) {
        console.error('下载包失败:', error)
        this.$message.error('下载包失败')
        this.downloadStatus = 'exception'
      }
    },
    updatePackage(id) {
      this.downloadPackage(id)
    },
    async runPackage(id) {
      try {
        // 调用Go后端运行包
        // await window.go.main.App.RunPackage(id)
        this.$message.success('应用启动成功')
      } catch (error) {
        console.error('启动应用失败:', error)
        this.$message.error('启动应用失败: ' + error.message)
      }
    },
    viewDetails(id) {
      this.$router.push(`/packages/${id}`)
    },
    closeDownloadDialog() {
      this.downloadDialogVisible = false
    },
    retryDownload() {
      this.downloadPackage(this.downloadingPackage.id)
    },
    cancelDownload() {
      // 调用Go后端取消下载
      // window.go.main.App.CancelDownload(this.downloadingPackage.id)
      this.downloadDialogVisible = false
      this.$message.info('下载已取消')
    },
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  }
}
</script>

<style scoped>
.header-actions {
  display: flex;
  align-items: center;
}

.search-input {
  width: 250px;
  margin-right: 15px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.download-progress {
  padding: 10px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.download-actions {
  margin-top: 20px;
  text-align: right;
}
</style>
