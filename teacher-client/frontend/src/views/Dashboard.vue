<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>包统计</h3>
            </div>
          </template>
          <div class="dashboard-panel">
            <el-row>
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-title">已下载</div>
                  <div class="stat-value">{{ stats.downloaded || 0 }}</div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-title">可用更新</div>
                  <div class="stat-value">{{ stats.updates || 0 }}</div>
                </div>
              </el-col>
            </el-row>
            <el-row style="margin-top: 20px;">
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-title">总包数</div>
                  <div class="stat-value">{{ stats.total || 0 }}</div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-title">下载中</div>
                  <div class="stat-value">{{ stats.downloading || 0 }}</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>存储信息</h3>
            </div>
          </template>
          <div class="dashboard-panel">
            <div class="storage-info">
              <div class="storage-title">已用空间</div>
              <el-progress 
                :percentage="storageUsage.percentage" 
                :color="storageUsage.color"
              />
              <div class="storage-details">
                {{ storageUsage.used }} / {{ storageUsage.total }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>服务器状态</h3>
            </div>
          </template>
          <div class="dashboard-panel">
            <div class="server-status">
              <div class="status-icon" :class="serverStatus.online ? 'online' : 'offline'">
                <el-icon v-if="serverStatus.online"><el-icon-circle-check /></el-icon>
                <el-icon v-else><el-icon-circle-close /></el-icon>
              </div>
              <div class="status-text">
                {{ serverStatus.online ? '已连接' : '未连接' }}
              </div>
              <div class="server-address">
                {{ serverStatus.address || '未配置服务器' }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>最近活动</h3>
              <el-button type="primary" size="small" @click="refreshActivities">刷新</el-button>
            </div>
          </template>
          <div class="activity-list">
            <el-table :data="activities" style="width: 100%">
              <el-table-column prop="time" label="时间" width="180" />
              <el-table-column prop="type" label="类型" width="120">
                <template #default="scope">
                  <el-tag :type="getActivityTypeColor(scope.row.type)">
                    {{ scope.row.type }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="packageName" label="包名称" />
              <el-table-column prop="message" label="详情" />
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Dashboard',
  data() {
    return {
      stats: {
        downloaded: 5,
        updates: 2,
        total: 10,
        downloading: 1
      },
      storageUsage: {
        used: '1.2 GB',
        total: '10 GB',
        percentage: 12,
        color: '#67C23A'
      },
      serverStatus: {
        online: true,
        address: '*************:18080'
      },
      activities: [
        { time: '2025-05-20 14:30', type: '下载', packageName: '物理实验 - 力学基础', message: '下载完成' },
        { time: '2025-05-20 13:15', type: '更新', packageName: '化学实验 - 滴定分析', message: '发现新版本' },
        { time: '2025-05-19 16:45', type: '运行', packageName: '生物实验 - 细胞观察', message: '应用启动' },
        { time: '2025-05-19 10:20', type: '同步', packageName: '所有包', message: '同步完成' }
      ]
    }
  },
  mounted() {
    this.loadDashboardData()
  },
  methods: {
    async loadDashboardData() {
      try {
        // 从Go后端加载数据
        // const data = await window.go.main.App.GetDashboardData()
        // this.stats = data.stats
        // this.storageUsage = data.storageUsage
        // this.serverStatus = data.serverStatus
        // this.activities = data.activities
      } catch (error) {
        console.error('加载仪表盘数据失败:', error)
        this.$message.error('加载仪表盘数据失败')
      }
    },
    refreshActivities() {
      this.loadDashboardData()
      this.$message.success('活动列表已刷新')
    },
    getActivityTypeColor(type) {
      const colors = {
        '下载': 'success',
        '更新': 'warning',
        '运行': 'primary',
        '同步': 'info',
        '错误': 'danger'
      }
      return colors[type] || 'info'
    }
  }
}
</script>

<style scoped>
.dashboard-panel {
  padding: 10px;
  height: 150px;
}

.stat-item {
  text-align: center;
  padding: 10px;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.storage-info {
  padding: 10px;
}

.storage-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 15px;
}

.storage-details {
  margin-top: 10px;
  text-align: right;
  font-size: 14px;
  color: #606266;
}

.server-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.status-icon {
  font-size: 40px;
  margin-bottom: 10px;
}

.status-icon.online {
  color: #67C23A;
}

.status-icon.offline {
  color: #F56C6C;
}

.status-text {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}

.server-address {
  font-size: 14px;
  color: #909399;
}

.activity-list {
  margin-top: 10px;
}
</style>
