<template>
  <div class="login-container">
    <div class="login-background"></div>
    <div class="login-content">
      <div class="login-box">
        <div class="login-header">
          <h2>智云鸿道思政虚拟仿真体验教学软件管理器</h2>
          <div class="subtitle">教师端 · V1.0.0</div>
        </div>
        <el-form
          ref="loginForm"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          label-position="top"
        >
          <el-form-item label="学校名称" prop="schoolName">
            <el-input
              v-model="loginForm.schoolName"
              placeholder="请输入学校名称"
              type="text"
              tabindex="1"
              autocomplete="off"
              prefix-icon="el-icon-school"
            />
          </el-form-item>
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              type="text"
              tabindex="2"
              autocomplete="off"
              prefix-icon="el-icon-user"
            />
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input
              v-model="loginForm.password"
              placeholder="请输入密码"
              type="password"
              tabindex="3"
              autocomplete="off"
              show-password
              prefix-icon="el-icon-lock"
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="loginForm.remember">记住我</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-button
              :loading="loading"
              type="primary"
              class="login-button"
              @click.prevent="handleLogin"
            >
              登录系统
            </el-button>
          </el-form-item>
        </el-form>
        <div class="login-footer">
          <p>技术支持：成都智云鸿道信息技术有限公司</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Login',
  data() {
    return {
      loginForm: {
        schoolName: '',
        username: '',
        password: '',
        remember: false
      },
      loginRules: {
        schoolName: [
          { required: true, message: '请输入学校名称', trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ]
      },
      loading: false,
      redirect: undefined
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  created() {
    // 从本地存储中恢复登录信息
    const savedSchoolName = localStorage.getItem('teacher_schoolName')
    const savedUsername = localStorage.getItem('teacher_username')
    if (savedSchoolName && savedUsername) {
      this.loginForm.schoolName = savedSchoolName
      this.loginForm.username = savedUsername
      this.loginForm.remember = true
    }
  },
  methods: {
    async handleLogin() {
      this.$refs.loginForm.validate(async (valid) => {
        if (valid) {
          this.loading = true
          try {
            // 调用后端登录API
            const result = await window.go.main.App.Login(
              this.loginForm.schoolName,
              this.loginForm.username,
              this.loginForm.password
            )

            if (result.success) {
              // 保存登录信息
              if (this.loginForm.remember) {
                localStorage.setItem('teacher_schoolName', this.loginForm.schoolName)
                localStorage.setItem('teacher_username', this.loginForm.username)
              } else {
                localStorage.removeItem('teacher_schoolName')
                localStorage.removeItem('teacher_username')
              }

              this.$message.success('登录成功')
              
              // 跳转到主页面
              this.$router.push({ path: this.redirect || '/' })
            } else {
              this.$message.error(result.message || '登录失败')
            }
          } catch (error) {
            console.error('登录失败:', error)
            this.$message.error('登录失败，请检查网络连接')
          } finally {
            this.loading = false
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped>
.login-container {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1c3f94, #2c5ebd);
  background-size: cover;
  background-position: center;
  filter: blur(0px);
  z-index: 0;
}

.login-background::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(28, 63, 148, 0.6);
}

.login-content {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.login-box {
  width: 450px;
  padding: 40px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: #1c3f94;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 10px 0;
  line-height: 1.3;
}

.subtitle {
  color: #666;
  font-size: 14px;
  font-weight: 400;
}

.login-form {
  margin-top: 20px;
}

.login-form .el-form-item {
  margin-bottom: 20px;
}

.login-form .el-input {
  height: 45px;
}

.login-form .el-input__inner {
  height: 45px;
  line-height: 45px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  font-size: 14px;
}

.login-form .el-input__inner:focus {
  border-color: #1c3f94;
}

.login-button {
  width: 100%;
  height: 45px;
  background-color: #1c3f94;
  border-color: #1c3f94;
  font-size: 16px;
  font-weight: 500;
}

.login-button:hover {
  background-color: #2c5ebd;
  border-color: #2c5ebd;
}

.login-footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.login-footer p {
  color: #999;
  font-size: 12px;
  margin: 0;
}
</style>
