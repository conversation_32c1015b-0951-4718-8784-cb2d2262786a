import router from '@/router'

/**
 * 处理授权过期
 * @param {string} redirectPath - 重定向路径
 */
export function handleAuthExpired(redirectPath = '/login') {
  console.log('检测到授权过期，清除本地状态并跳转到登录页面')
  
  // 调用后端处理授权过期
  if (window.go && window.go.main && window.go.main.App) {
    window.go.main.App.HandleAuthExpired()
      .then(result => {
        console.log('授权过期处理结果:', result)
      })
      .catch(error => {
        console.error('处理授权过期失败:', error)
      })
  }
  
  // 跳转到登录页面
  router.push({
    path: redirectPath,
    query: { 
      redirect: router.currentRoute.value.fullPath,
      reason: 'auth_expired'
    }
  })
}

/**
 * 检查错误是否为授权过期错误
 * @param {Error|string} error - 错误对象或错误消息
 * @returns {boolean} 是否为授权过期错误
 */
export function isAuthExpiredError(error) {
  if (!error) return false
  
  const errorMessage = typeof error === 'string' ? error : error.message || error.toString()
  
  // 检查错误消息中是否包含授权过期的关键词
  return errorMessage.includes('授权已过期') || 
         errorMessage.includes('授权过期') ||
         errorMessage.includes('auth_expired') ||
         errorMessage.includes('401')
}

/**
 * 包装API调用，自动处理授权过期
 * @param {Function} apiCall - API调用函数
 * @param {...any} args - API调用参数
 * @returns {Promise} API调用结果
 */
export async function wrapApiCall(apiCall, ...args) {
  try {
    const result = await apiCall(...args)
    return result
  } catch (error) {
    // 检查是否为授权过期错误
    if (isAuthExpiredError(error)) {
      handleAuthExpired()
      throw new Error('授权已过期，请重新登录')
    }
    throw error
  }
}

/**
 * 检查服务器状态响应是否为授权过期
 * @param {Object} statusResponse - 服务器状态响应
 * @returns {boolean} 是否为授权过期
 */
export function isServerStatusAuthExpired(statusResponse) {
  return statusResponse && 
         statusResponse.status === 'auth_expired' && 
         statusResponse.needLogin === true
}
