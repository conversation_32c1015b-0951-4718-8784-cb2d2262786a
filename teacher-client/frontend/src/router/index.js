import { createRouter, createWebHashHistory } from 'vue-router'

// 页面组件
const Login = () => import('../views/Login.vue')
const Layout = () => import('../views/Layout.vue')
const Library = () => import('../views/Library.vue')
const PackageDetail = () => import('../views/PackageDetail.vue')
const Settings = () => import('../views/Settings.vue')

const SyncLogs = () => import('../views/SyncLogs.vue')

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { title: '登录', requiresAuth: false }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/library',
    meta: { requiresAuth: true },
    children: [
      {
        path: 'library',
        name: 'Library',
        component: Library,
        meta: { title: '实验库', requiresAuth: true }
      },
      {
        path: 'package/:id',
        name: 'PackageDetail',
        component: PackageDetail,
        meta: { title: '实验详情', requiresAuth: true }
      },
      {
        path: 'sync-logs',
        name: 'SyncLogs',
        component: SyncLogs,
        meta: { title: '同步日志', requiresAuth: true }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: Settings,
        meta: { title: '设置', requiresAuth: true }
      }
    ]
  },
  {
    path: '/settings',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        component: Settings,
        meta: { title: '设置', requiresAuth: true }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 认证守卫
router.beforeEach(async (to, from, next) => {
  // 检查路由是否需要认证
  if (to.matched.some(record => record.meta.requiresAuth)) {
    try {
      // 检查是否已登录
      const isLoggedIn = await window.go.main.App.IsLoggedIn()

      if (!isLoggedIn) {
        // 未登录，跳转到登录页面
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }

      // 已登录，验证token是否有效
      const authResult = await window.go.main.App.ValidateAuth()
      if (!authResult.valid) {
        // token无效，跳转到登录页面
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }

      // 认证通过，继续访问
      next()
    } catch (error) {
      console.error('认证检查失败:', error)
      // 认证检查失败，跳转到登录页面
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
    }
  } else {
    // 不需要认证的页面，直接访问
    next()
  }
})

export default router
