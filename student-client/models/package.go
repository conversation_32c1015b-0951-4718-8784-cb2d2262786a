package models

import (
	"database/sql"
	"time"
)

// 包同步状态常量
const (
	SyncStatusNone        = 0 // 未同步
	SyncStatusDownloading = 1 // 下载中
	SyncStatusDownloaded  = 2 // 已下载
	SyncStatusExtracting  = 3 // 解压中
	SyncStatusCompleted   = 4 // 已完成
	SyncStatusFailed      = 5 // 失败
)

// Package 包模型
type Package struct {
	ID                    int64     `json:"id"`
	PackageVersionID      string    `json:"packageVersionId"`
	ExperimentID          string    `json:"experimentId"`
	ExperimentName        string    `json:"experimentName"`
	ExperimentVersionID   string    `json:"experimentVersionId"`
	ExperimentVersionName string    `json:"experimentVersionName"`
	Version               string    `json:"version"`
	VersionName           string    `json:"versionName"`
	VersionDesc           string    `json:"versionDesc"`
	FileHash              string    `json:"fileHash"`
	FilePath              string    `json:"filePath"`
	FileSize              int64     `json:"fileSize"`
	DownloadURL           string    `json:"downloadUrl"`
	LocalPath             string    `json:"localPath"`
	ExtractPath           string    `json:"extractPath"`
	ExecutablePath        string    `json:"executablePath"`
	SyncStatus            int       `json:"syncStatus"`
	DownloadCount         int       `json:"downloadCount"`
	SchoolID              string    `json:"schoolId"` // 学校ID
	LastRun               time.Time `json:"lastRun"`
	CreatedAt             time.Time `json:"createdAt"`
	UpdatedAt             time.Time `json:"updatedAt"`
}

// GetPackageByID 通过ID获取包
func GetPackageByID(id int64) (*Package, error) {
	pkg := &Package{}
	query := `SELECT id, package_version_id, experiment_id, experiment_name, experiment_version_id, experiment_version_name,
			  version, version_name, version_desc, file_hash, file_path, file_size, download_url, local_path, extract_path,
			  executable_path, sync_status, download_count, school_id, last_run, created_at, updated_at
			  FROM packages WHERE id = ?`

	err := DB.QueryRow(query, id).Scan(
		&pkg.ID,
		&pkg.PackageVersionID,
		&pkg.ExperimentID,
		&pkg.ExperimentName,
		&pkg.ExperimentVersionID,
		&pkg.ExperimentVersionName,
		&pkg.Version,
		&pkg.VersionName,
		&pkg.VersionDesc,
		&pkg.FileHash,
		&pkg.FilePath,
		&pkg.FileSize,
		&pkg.DownloadURL,
		&pkg.LocalPath,
		&pkg.ExtractPath,
		&pkg.ExecutablePath,
		&pkg.SyncStatus,
		&pkg.DownloadCount,
		&pkg.SchoolID,
		&pkg.LastRun,
		&pkg.CreatedAt,
		&pkg.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return pkg, nil
}

// GetPackageByVersionID 通过版本ID获取包
func GetPackageByVersionID(versionID string) (*Package, error) {
	pkg := &Package{}
	query := `SELECT id, package_version_id, experiment_id, experiment_name, experiment_version_id, experiment_version_name,
			  version, version_name, version_desc, file_hash, file_path, file_size, download_url, local_path, extract_path,
			  executable_path, sync_status, download_count, school_id, last_run, created_at, updated_at
			  FROM packages WHERE package_version_id = ?`

	err := DB.QueryRow(query, versionID).Scan(
		&pkg.ID,
		&pkg.PackageVersionID,
		&pkg.ExperimentID,
		&pkg.ExperimentName,
		&pkg.ExperimentVersionID,
		&pkg.ExperimentVersionName,
		&pkg.Version,
		&pkg.VersionName,
		&pkg.VersionDesc,
		&pkg.FileHash,
		&pkg.FilePath,
		&pkg.FileSize,
		&pkg.DownloadURL,
		&pkg.LocalPath,
		&pkg.ExtractPath,
		&pkg.ExecutablePath,
		&pkg.SyncStatus,
		&pkg.DownloadCount,
		&pkg.SchoolID,
		&pkg.LastRun,
		&pkg.CreatedAt,
		&pkg.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return pkg, nil
}

// GetAllPackages 获取所有包
func GetAllPackages() ([]*Package, error) {
	query := `SELECT id, package_version_id, experiment_id, experiment_name, experiment_version_id, experiment_version_name,
			  version, version_name, version_desc, file_hash, file_path, file_size, download_url, local_path, extract_path,
			  executable_path, sync_status, download_count, school_id, last_run, created_at, updated_at
			  FROM packages ORDER BY created_at DESC`

	rows, err := DB.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	packages := []*Package{}
	for rows.Next() {
		pkg := &Package{}
		err := rows.Scan(
			&pkg.ID,
			&pkg.PackageVersionID,
			&pkg.ExperimentID,
			&pkg.ExperimentName,
			&pkg.ExperimentVersionID,
			&pkg.ExperimentVersionName,
			&pkg.Version,
			&pkg.VersionName,
			&pkg.VersionDesc,
			&pkg.FileHash,
			&pkg.FilePath,
			&pkg.FileSize,
			&pkg.DownloadURL,
			&pkg.LocalPath,
			&pkg.ExtractPath,
			&pkg.ExecutablePath,
			&pkg.SyncStatus,
			&pkg.DownloadCount,
			&pkg.SchoolID,
			&pkg.LastRun,
			&pkg.CreatedAt,
			&pkg.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		packages = append(packages, pkg)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return packages, nil
}

// CreatePackage 创建包
func CreatePackage(pkg *Package) error {
	query := `INSERT INTO packages (
		package_version_id, experiment_id, experiment_name, experiment_version_id, experiment_version_name,
		version, version_name, version_desc, file_hash, file_path, file_size, download_url, local_path, extract_path,
		executable_path, sync_status, download_count, school_id, last_run, created_at, updated_at
	) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	now := time.Now()
	pkg.CreatedAt = now
	pkg.UpdatedAt = now

	result, err := DB.Exec(
		query,
		pkg.PackageVersionID,
		pkg.ExperimentID,
		pkg.ExperimentName,
		pkg.ExperimentVersionID,
		pkg.ExperimentVersionName,
		pkg.Version,
		pkg.VersionName,
		pkg.VersionDesc,
		pkg.FileHash,
		pkg.FilePath,
		pkg.FileSize,
		pkg.DownloadURL,
		pkg.LocalPath,
		pkg.ExtractPath,
		pkg.ExecutablePath,
		pkg.SyncStatus,
		pkg.DownloadCount,
		pkg.SchoolID,
		pkg.LastRun,
		pkg.CreatedAt,
		pkg.UpdatedAt,
	)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}

	pkg.ID = id
	return nil
}

// UpdatePackage 更新包
func UpdatePackage(pkg *Package) error {
	query := `UPDATE packages SET
		experiment_id = ?, experiment_name = ?, experiment_version_id = ?, experiment_version_name = ?,
		version = ?, version_name = ?, version_desc = ?, file_hash = ?, file_path = ?, file_size = ?,
		download_url = ?, local_path = ?, extract_path = ?, executable_path = ?, sync_status = ?,
		download_count = ?, school_id = ?, last_run = ?, updated_at = ?
		WHERE id = ?`

	pkg.UpdatedAt = time.Now()

	_, err := DB.Exec(
		query,
		pkg.ExperimentID,
		pkg.ExperimentName,
		pkg.ExperimentVersionID,
		pkg.ExperimentVersionName,
		pkg.Version,
		pkg.VersionName,
		pkg.VersionDesc,
		pkg.FileHash,
		pkg.FilePath,
		pkg.FileSize,
		pkg.DownloadURL,
		pkg.LocalPath,
		pkg.ExtractPath,
		pkg.ExecutablePath,
		pkg.SyncStatus,
		pkg.DownloadCount,
		pkg.SchoolID,
		pkg.LastRun,
		pkg.UpdatedAt,
		pkg.ID,
	)

	return err
}

// UpdatePackageStatus 更新包状态
func UpdatePackageStatus(id int64, status int) error {
	query := `UPDATE packages SET sync_status = ?, updated_at = ? WHERE id = ?`
	_, err := DB.Exec(query, status, time.Now(), id)
	return err
}

// UpdatePackageLocalPath 更新包本地路径
func UpdatePackageLocalPath(id int64, localPath string) error {
	query := `UPDATE packages SET local_path = ?, updated_at = ? WHERE id = ?`
	_, err := DB.Exec(query, localPath, time.Now(), id)
	return err
}

// UpdatePackageExtractPath 更新包解压路径
func UpdatePackageExtractPath(id int64, extractPath string) error {
	query := `UPDATE packages SET extract_path = ?, updated_at = ? WHERE id = ?`
	_, err := DB.Exec(query, extractPath, time.Now(), id)
	return err
}

// UpdatePackageExecutablePath 更新包可执行文件路径
func UpdatePackageExecutablePath(id int64, executablePath string) error {
	query := `UPDATE packages SET executable_path = ?, updated_at = ? WHERE id = ?`
	_, err := DB.Exec(query, executablePath, time.Now(), id)
	return err
}

// IncrementDownloadCount 增加下载次数
func IncrementDownloadCount(id int64) error {
	query := `UPDATE packages SET download_count = download_count + 1, updated_at = ? WHERE id = ?`
	_, err := DB.Exec(query, time.Now(), id)
	return err
}

// UpdateLastRun 更新最后运行时间
func UpdateLastRun(id int64) error {
	query := `UPDATE packages SET last_run = ?, updated_at = ? WHERE id = ?`
	now := time.Now()
	_, err := DB.Exec(query, now, now, id)
	return err
}

// GetDisplayPackagesByExperimentVersion 获取每个实验版本应该显示的包
// 优先显示已下载的包，如果没有已下载的包则显示最新版本
func GetDisplayPackagesByExperimentVersion() ([]*Package, error) {
	// 首先获取每个实验版本的已下载包（状态为已下载、解压中或已完成）
	downloadedQuery := `SELECT p1.id, p1.package_version_id, p1.experiment_id, p1.experiment_name,
						p1.experiment_version_id, p1.experiment_version_name,
						p1.version, p1.version_name, p1.version_desc, p1.file_hash, p1.file_path,
						p1.file_size, p1.download_url, p1.local_path, p1.extract_path,
						p1.executable_path, p1.sync_status, p1.download_count, p1.school_id,
						p1.last_run, p1.created_at, p1.updated_at
						FROM packages p1
						INNER JOIN (
							SELECT experiment_version_id, MAX(CAST(package_version_id AS INTEGER)) as max_downloaded_package_version_id
							FROM packages
							WHERE sync_status IN (2, 3, 4)  -- 已下载、解压中或已完成
							GROUP BY experiment_version_id
						) p2 ON p1.experiment_version_id = p2.experiment_version_id
						AND CAST(p1.package_version_id AS INTEGER) = p2.max_downloaded_package_version_id
						AND p1.sync_status IN (2, 3, 4)
						ORDER BY p1.created_at DESC`

	rows, err := DB.Query(downloadedQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	downloadedPackages := []*Package{}
	downloadedVersions := make(map[string]bool) // 记录已有下载包的实验版本ID

	for rows.Next() {
		pkg := &Package{}
		err := rows.Scan(
			&pkg.ID,
			&pkg.PackageVersionID,
			&pkg.ExperimentID,
			&pkg.ExperimentName,
			&pkg.ExperimentVersionID,
			&pkg.ExperimentVersionName,
			&pkg.Version,
			&pkg.VersionName,
			&pkg.VersionDesc,
			&pkg.FileHash,
			&pkg.FilePath,
			&pkg.FileSize,
			&pkg.DownloadURL,
			&pkg.LocalPath,
			&pkg.ExtractPath,
			&pkg.ExecutablePath,
			&pkg.SyncStatus,
			&pkg.DownloadCount,
			&pkg.SchoolID,
			&pkg.LastRun,
			&pkg.CreatedAt,
			&pkg.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		downloadedPackages = append(downloadedPackages, pkg)
		downloadedVersions[pkg.ExperimentVersionID] = true
	}

	// 然后获取没有已下载包的实验版本的最新包
	latestQuery := `SELECT p1.id, p1.package_version_id, p1.experiment_id, p1.experiment_name,
					p1.experiment_version_id, p1.experiment_version_name,
					p1.version, p1.version_name, p1.version_desc, p1.file_hash, p1.file_path,
					p1.file_size, p1.download_url, p1.local_path, p1.extract_path,
					p1.executable_path, p1.sync_status, p1.download_count, p1.school_id,
					p1.last_run, p1.created_at, p1.updated_at
					FROM packages p1
					INNER JOIN (
						SELECT experiment_version_id, MAX(CAST(package_version_id AS INTEGER)) as max_package_version_id
						FROM packages
						GROUP BY experiment_version_id
					) p2 ON p1.experiment_version_id = p2.experiment_version_id
					AND CAST(p1.package_version_id AS INTEGER) = p2.max_package_version_id
					ORDER BY p1.created_at DESC`

	rows2, err := DB.Query(latestQuery)
	if err != nil {
		return nil, err
	}
	defer rows2.Close()

	allPackages := downloadedPackages // 先添加已下载的包

	for rows2.Next() {
		pkg := &Package{}
		err := rows2.Scan(
			&pkg.ID,
			&pkg.PackageVersionID,
			&pkg.ExperimentID,
			&pkg.ExperimentName,
			&pkg.ExperimentVersionID,
			&pkg.ExperimentVersionName,
			&pkg.Version,
			&pkg.VersionName,
			&pkg.VersionDesc,
			&pkg.FileHash,
			&pkg.FilePath,
			&pkg.FileSize,
			&pkg.DownloadURL,
			&pkg.LocalPath,
			&pkg.ExtractPath,
			&pkg.ExecutablePath,
			&pkg.SyncStatus,
			&pkg.DownloadCount,
			&pkg.SchoolID,
			&pkg.LastRun,
			&pkg.CreatedAt,
			&pkg.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}

		// 只添加没有已下载包的实验版本
		if !downloadedVersions[pkg.ExperimentVersionID] {
			allPackages = append(allPackages, pkg)
		}
	}

	if err := rows2.Err(); err != nil {
		return nil, err
	}

	return allPackages, nil
}

// GetLatestPackagesByExperimentVersion 获取每个实验版本的最新包
// 按实验版本ID分组，每组只返回PackageVersionID最大的包
func GetLatestPackagesByExperimentVersion() ([]*Package, error) {
	query := `SELECT p1.id, p1.package_version_id, p1.experiment_id, p1.experiment_name,
			  p1.experiment_version_id, p1.experiment_version_name,
			  p1.version, p1.version_name, p1.version_desc, p1.file_hash, p1.file_path,
			  p1.file_size, p1.download_url, p1.local_path, p1.extract_path,
			  p1.executable_path, p1.sync_status, p1.download_count, p1.school_id,
			  p1.last_run, p1.created_at, p1.updated_at
			  FROM packages p1
			  INNER JOIN (
				  SELECT experiment_version_id, MAX(CAST(package_version_id AS INTEGER)) as max_package_version_id
				  FROM packages
				  GROUP BY experiment_version_id
			  ) p2 ON p1.experiment_version_id = p2.experiment_version_id
			  AND CAST(p1.package_version_id AS INTEGER) = p2.max_package_version_id
			  ORDER BY p1.created_at DESC`

	rows, err := DB.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	packages := []*Package{}
	for rows.Next() {
		pkg := &Package{}
		err := rows.Scan(
			&pkg.ID,
			&pkg.PackageVersionID,
			&pkg.ExperimentID,
			&pkg.ExperimentName,
			&pkg.ExperimentVersionID,
			&pkg.ExperimentVersionName,
			&pkg.Version,
			&pkg.VersionName,
			&pkg.VersionDesc,
			&pkg.FileHash,
			&pkg.FilePath,
			&pkg.FileSize,
			&pkg.DownloadURL,
			&pkg.LocalPath,
			&pkg.ExtractPath,
			&pkg.ExecutablePath,
			&pkg.SyncStatus,
			&pkg.DownloadCount,
			&pkg.SchoolID,
			&pkg.LastRun,
			&pkg.CreatedAt,
			&pkg.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		packages = append(packages, pkg)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return packages, nil
}

// CheckForUpdates 检查指定包是否有更新
func CheckForUpdates(experimentVersionID, currentPackageVersionID string) (*Package, error) {
	query := `SELECT id, package_version_id, experiment_id, experiment_name, experiment_version_id, experiment_version_name,
			  version, version_name, version_desc, file_hash, file_path, file_size, download_url, local_path, extract_path,
			  executable_path, sync_status, download_count, school_id, last_run, created_at, updated_at
			  FROM packages
			  WHERE experiment_version_id = ?
			  AND CAST(package_version_id AS INTEGER) > CAST(? AS INTEGER)
			  ORDER BY CAST(package_version_id AS INTEGER) DESC
			  LIMIT 1`

	pkg := &Package{}
	err := DB.QueryRow(query, experimentVersionID, currentPackageVersionID).Scan(
		&pkg.ID,
		&pkg.PackageVersionID,
		&pkg.ExperimentID,
		&pkg.ExperimentName,
		&pkg.ExperimentVersionID,
		&pkg.ExperimentVersionName,
		&pkg.Version,
		&pkg.VersionName,
		&pkg.VersionDesc,
		&pkg.FileHash,
		&pkg.FilePath,
		&pkg.FileSize,
		&pkg.DownloadURL,
		&pkg.LocalPath,
		&pkg.ExtractPath,
		&pkg.ExecutablePath,
		&pkg.SyncStatus,
		&pkg.DownloadCount,
		&pkg.SchoolID,
		&pkg.LastRun,
		&pkg.CreatedAt,
		&pkg.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // 没有更新
		}
		return nil, err
	}

	return pkg, nil
}

// GetOldPackagesByExperimentVersion 获取指定实验版本的旧包版本
// 返回除了最新PackageVersionID之外的所有包
func GetOldPackagesByExperimentVersion(experimentVersionID string) ([]*Package, error) {
	query := `SELECT id, package_version_id, experiment_id, experiment_name, experiment_version_id, experiment_version_name,
			  version, version_name, version_desc, file_hash, file_path, file_size, download_url, local_path, extract_path,
			  executable_path, sync_status, download_count, school_id, last_run, created_at, updated_at
			  FROM packages
			  WHERE experiment_version_id = ?
			  AND CAST(package_version_id AS INTEGER) < (
				  SELECT MAX(CAST(package_version_id AS INTEGER))
				  FROM packages
				  WHERE experiment_version_id = ?
			  )
			  ORDER BY CAST(package_version_id AS INTEGER) DESC`

	rows, err := DB.Query(query, experimentVersionID, experimentVersionID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	packages := []*Package{}
	for rows.Next() {
		pkg := &Package{}
		err := rows.Scan(
			&pkg.ID,
			&pkg.PackageVersionID,
			&pkg.ExperimentID,
			&pkg.ExperimentName,
			&pkg.ExperimentVersionID,
			&pkg.ExperimentVersionName,
			&pkg.Version,
			&pkg.VersionName,
			&pkg.VersionDesc,
			&pkg.FileHash,
			&pkg.FilePath,
			&pkg.FileSize,
			&pkg.DownloadURL,
			&pkg.LocalPath,
			&pkg.ExtractPath,
			&pkg.ExecutablePath,
			&pkg.SyncStatus,
			&pkg.DownloadCount,
			&pkg.SchoolID,
			&pkg.LastRun,
			&pkg.CreatedAt,
			&pkg.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		packages = append(packages, pkg)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return packages, nil
}

// ClearPackageFilePaths 清空包的文件路径，但保留记录
func ClearPackageFilePaths(id int64) error {
	query := `UPDATE packages SET
			  local_path = '',
			  extract_path = '',
			  executable_path = '',
			  sync_status = ?,
			  updated_at = ?
			  WHERE id = ?`
	_, err := DB.Exec(query, SyncStatusNone, time.Now(), id)
	return err
}

// DeletePackage 删除包记录
func DeletePackage(id int64) error {
	// 先删除相关的同步日志
	_, err := DB.Exec(`DELETE FROM sync_logs WHERE package_id = ?`, id)
	if err != nil {
		return err
	}

	// 删除包记录
	_, err = DB.Exec(`DELETE FROM packages WHERE id = ?`, id)
	return err
}

// ClearAllPackages 清除所有包信息
func ClearAllPackages() error {
	// 删除所有包记录
	_, err := DB.Exec(`DELETE FROM packages`)
	if err != nil {
		return err
	}

	// 删除所有同步日志
	_, err = DB.Exec(`DELETE FROM sync_logs`)
	if err != nil {
		return err
	}

	return nil
}
