package models

import (
	"database/sql"
	"time"
)

// Config 配置模型
type Config struct {
	ID        int64     `json:"id"`
	Key       string    `json:"key"`
	Value     string    `json:"value"`
	UpdatedAt time.Time `json:"updatedAt"`
}

// GetConfig 获取配置
func GetConfig(key string) (string, error) {
	var value string
	query := `SELECT value FROM configs WHERE key = ?`

	err := DB.QueryRow(query, key).Scan(&value)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", nil
		}
		return "", err
	}

	return value, nil
}

// SetConfig 设置配置
func SetConfig(key, value string) error {
	// 检查配置是否存在
	var count int
	err := DB.QueryRow(`SELECT COUNT(*) FROM configs WHERE key = ?`, key).Scan(&count)
	if err != nil {
		return err
	}

	if count > 0 {
		// 更新配置
		_, err = DB.Exec(`UPDATE configs SET value = ?, updated_at = ? WHERE key = ?`,
			value, time.Now(), key)
	} else {
		// 插入配置
		_, err = DB.Exec(`INSERT INTO configs (key, value, updated_at) VALUES (?, ?, ?)`,
			key, value, time.Now())
	}

	return err
}

// GetAllConfigs 获取所有配置
func GetAllConfigs() (map[string]string, error) {
	configs := make(map[string]string)

	rows, err := DB.Query(`SELECT key, value FROM configs`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var key, value string
		if err := rows.Scan(&key, &value); err != nil {
			return nil, err
		}
		configs[key] = value
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return configs, nil
}

// GetServerURL 获取服务器URL
func GetServerURL() (string, error) {
	return GetConfig("server_url")
}

// SetServerURL 设置服务器URL
func SetServerURL(url string) error {
	return SetConfig("server_url", url)
}

// GetServerPort 获取服务器端口
func GetServerPort() (string, error) {
	port, err := GetConfig("server_port")
	if err != nil {
		return "", err
	}

	if port == "" {
		// 默认端口
		port = "18080"
	}

	return port, nil
}

// SetServerPort 设置服务器端口
func SetServerPort(port string) error {
	return SetConfig("server_port", port)
}

// GetSchoolName 获取学校名称
func GetSchoolName() (string, error) {
	return GetConfig("school_name")
}

// SetSchoolName 设置学校名称
func SetSchoolName(name string) error {
	return SetConfig("school_name", name)
}

// GetSchoolID 获取学校ID
func GetSchoolID() (string, error) {
	return GetConfig("school_id")
}

// SetSchoolID 设置学校ID
func SetSchoolID(id string) error {
	return SetConfig("school_id", id)
}

// InsertDefaultConfigs 插入默认配置
func InsertDefaultConfigs() error {
	defaults := map[string]string{
		"server_url":  "127.0.0.1",
		"server_port": "18080",
		"school_name": "学院",
		"school_id":   "",
		"first_login": "true",
	}

	for key, value := range defaults {
		// 只有在配置不存在时才插入默认值
		var count int
		err := DB.QueryRow(`SELECT COUNT(*) FROM configs WHERE key = ?`, key).Scan(&count)
		if err != nil {
			return err
		}

		if count == 0 {
			if err := SetConfig(key, value); err != nil {
				return err
			}
		}
	}

	return nil
}
