package services

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"student-client/models"
	"student-client/utils"
)

// DownloadProgress 下载进度
type DownloadProgress struct {
	Total      int64   `json:"total"`
	Downloaded int64   `json:"downloaded"`
	Percentage float64 `json:"percentage"`
	Speed      float64 `json:"speed"` // 下载速度，单位：字节/秒
	Filename   string  `json:"filename"`
	Error      string  `json:"error,omitempty"`     // 错误信息，如果有的话
	Completed  bool    `json:"completed,omitempty"` // 是否完成
}

// DownloadService 下载服务
type DownloadService struct {
	Client      *SchoolClient
	DownloadDir string
}

// NewDownloadService 创建下载服务
func NewDownloadService() (*DownloadService, error) {
	client, err := NewSchoolClient()
	if err != nil {
		return nil, err
	}

	return &DownloadService{
		Client:      client,
		DownloadDir: "./downloads",
	}, nil
}

// DownloadPackage 下载包文件
func (s *DownloadService) DownloadPackage(pkg *models.Package, progress<PERSON>han chan<- DownloadProgress, progressHandler func(DownloadProgress)) error {
	// 构建保存路径
	filename := filepath.Base(pkg.DownloadURL)
	if filename == "" || filename == "." {
		filename = fmt.Sprintf("%s-%s.zip", pkg.ExperimentName, pkg.Version)
	}

	// 使用学校ID创建子目录
	schoolDir := filepath.Join(s.DownloadDir, pkg.SchoolID)
	if err := os.MkdirAll(schoolDir, 0755); err != nil {
		return fmt.Errorf("创建学校目录失败: %v", err)
	}

	savePath := filepath.Join(schoolDir, filename)

	// 修复Windows路径分隔符问题，将反斜杠转换为正斜杠
	downloadURL := strings.ReplaceAll(pkg.DownloadURL, "\\", "/")

	// 构建完整URL
	fullURL := s.Client.GetFullURL(downloadURL)

	// 创建请求
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		return err
	}

	// 添加请求头，改善下载兼容性
	req.Header.Set("User-Agent", "Student-Client/1.0 (Package Download)")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Accept-Encoding", "identity") // 禁用压缩，避免解压问题
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Cache-Control", "no-cache")

	// 发送请求
	resp, err := s.Client.HTTPClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载失败，状态码: %d", resp.StatusCode)
	}

	// 获取文件大小
	fileSize := resp.ContentLength

	// 创建临时文件
	tmpFile, err := os.CreateTemp(s.DownloadDir, "download-*.tmp")
	if err != nil {
		return err
	}
	tmpPath := tmpFile.Name()
	defer func() {
		tmpFile.Close()
		// 如果出错，删除临时文件
		if err != nil {
			os.Remove(tmpPath)
		}
	}()

	// 创建缓冲区
	buf := make([]byte, 64*1024) // 64KB 缓冲区，提高下载效率
	var downloaded int64 = 0
	startTime := time.Now()
	lastUpdateTime := startTime
	lastDownloaded := downloaded
	updateCount := 0 // 添加更新计数器

	utils.LogPrintf("开始下载文件，总大小: %d 字节", fileSize)

	// 读取响应体并写入文件
	for {
		n, err := resp.Body.Read(buf)
		if n > 0 {
			// 写入文件
			_, writeErr := tmpFile.Write(buf[:n])
			if writeErr != nil {
				utils.LogPrintf("写入文件失败: %v", writeErr)
				return writeErr
			}

			// 更新下载进度
			downloaded += int64(n)
			now := time.Now()
			elapsed := now.Sub(lastUpdateTime).Seconds()

			// 每1秒更新一次进度，或者每下载10MB更新一次
			if elapsed >= 1.0 || (downloaded-lastDownloaded) >= 10*1024*1024 {
				updateCount++

				// 计算下载速度
				speed := float64(downloaded-lastDownloaded) / elapsed

				// 计算百分比
				var percentage float64 = 0
				if fileSize > 0 {
					percentage = float64(downloaded) / float64(fileSize) * 100
				}

				// 创建进度对象
				progress := DownloadProgress{
					Total:      fileSize,
					Downloaded: downloaded,
					Percentage: percentage,
					Speed:      speed,
					Filename:   filename,
				}

				utils.LogPrintf("下载进度更新 #%d: %.2f%% (%d/%d), 速度: %.2f KB/s",
					updateCount, percentage, downloaded, fileSize, speed/1024)

				// 发送进度到通道
				if progressChan != nil {
					select {
					case progressChan <- progress:
						// 成功发送
					default:
						utils.LogPrintln("进度通道已满，跳过此次更新")
					}
				}

				// 使用进度处理函数
				if progressHandler != nil {
					progressHandler(progress)
				}

				// 更新时间和下载量
				lastUpdateTime = now
				lastDownloaded = downloaded
			}
		}

		if err != nil {
			if err == io.EOF {
				utils.LogPrintf("下载完成，总共下载: %d 字节，更新次数: %d", downloaded, updateCount)
				break
			}
			utils.LogPrintf("下载过程中出错: %v", err)
			return err
		}
	}

	// 关闭临时文件
	if err := tmpFile.Close(); err != nil {
		return err
	}

	// 验证文件哈希
	if pkg.FileHash != "" {
		utils.LogPrintln("开始验证文件哈希...")
		hash, err := utils.CalculateFileHash(tmpPath)
		if err != nil {
			utils.LogPrintf("计算文件哈希失败: %v", err)
			return err
		}

		utils.LogPrintf("文件哈希验证: 期望=%s, 实际=%s", pkg.FileHash, hash)
		if hash != pkg.FileHash {
			utils.LogPrintf("文件哈希验证失败: 期望=%s, 实际=%s", pkg.FileHash, hash)
			return fmt.Errorf("文件哈希验证失败，期望: %s，实际: %s", pkg.FileHash, hash)
		}
		utils.LogPrintln("文件哈希验证成功")
	}

	// 重命名临时文件
	if err := os.Rename(tmpPath, savePath); err != nil {
		return err
	}

	// 更新包的本地路径
	pkg.LocalPath = savePath
	if err := models.UpdatePackageLocalPath(pkg.ID, savePath); err != nil {
		return err
	}

	// 更新包状态为已下载
	if err := models.UpdatePackageStatus(pkg.ID, models.SyncStatusDownloaded); err != nil {
		return err
	}

	// 添加同步日志
	if err := models.AddSyncLog(pkg.ID, "download", models.LogStatusSuccess, "下载完成"); err != nil {
		return err
	}

	// 向学校端发送下载记录
	if err := s.recordDownloadToSchool(pkg); err != nil {
		// 记录错误但不影响下载流程
		utils.LogPrintf("向学校端发送下载记录失败: %v", err)
	}

	// 创建最终进度对象
	finalProgress := DownloadProgress{
		Total:      fileSize,
		Downloaded: downloaded,
		Percentage: 100,
		Speed:      0,
		Filename:   filename,
		Completed:  true,
	}

	// 发送最终进度到通道
	if progressChan != nil {
		select {
		case progressChan <- finalProgress:
			utils.LogPrintln("成功发送最终进度到通道")
		default:
			utils.LogPrintln("进度通道已满，直接使用处理函数发送最终进度")
		}
	}

	// 使用进度处理函数
	if progressHandler != nil {
		utils.LogPrintln("调用进度处理函数发送最终进度")
		progressHandler(finalProgress)

		// 再次发送一次，确保前端能收到
		time.Sleep(100 * time.Millisecond)
		utils.LogPrintln("再次调用进度处理函数发送最终进度")
		progressHandler(finalProgress)
	}

	// 等待一小段时间，确保前端能接收到最终进度
	time.Sleep(500 * time.Millisecond)

	return nil
}

// calculateFileHash 计算文件哈希（使用utils.CalculateFileHash）
func calculateFileHash(filePath string) (string, error) {
	return utils.CalculateFileHash(filePath)
}

// GetDownloadedPackages 获取已下载的包
func (s *DownloadService) GetDownloadedPackages() ([]*models.Package, error) {
	packages, err := models.GetAllPackages()
	if err != nil {
		return nil, err
	}

	downloadedPackages := make([]*models.Package, 0)
	for _, pkg := range packages {
		if pkg.SyncStatus >= models.SyncStatusDownloaded {
			downloadedPackages = append(downloadedPackages, pkg)
		}
	}

	return downloadedPackages, nil
}

// GetDownloadingPackages 获取下载中的包
func (s *DownloadService) GetDownloadingPackages() ([]*models.Package, error) {
	packages, err := models.GetAllPackages()
	if err != nil {
		return nil, err
	}

	downloadingPackages := make([]*models.Package, 0)
	for _, pkg := range packages {
		if pkg.SyncStatus == models.SyncStatusDownloading {
			downloadingPackages = append(downloadingPackages, pkg)
		}
	}

	return downloadingPackages, nil
}

// recordDownloadToSchool 向学校端发送下载记录
func (s *DownloadService) recordDownloadToSchool(pkg *models.Package) error {
	// 获取网络信息
	mac, ip, err := utils.GetNetworkInfo()
	if err != nil {
		return fmt.Errorf("获取网络信息失败: %v", err)
	}

	// 构建下载记录请求
	req := DownloadRecordRequest{
		PackageVersionID: pkg.PackageVersionID,
		StudentMAC:       mac,
		StudentIP:        ip,
		UserAgent:        "Student-Client/1.0",
		SchoolID:         pkg.SchoolID,
	}

	// 发送记录到学校端
	if err := s.Client.RecordDownload(req); err != nil {
		return fmt.Errorf("发送下载记录失败: %v", err)
	}

	utils.LogPrintf("成功向学校端发送下载记录: 包版本ID=%s, MAC=%s, IP=%s",
		pkg.PackageVersionID, mac, ip)

	return nil
}
