package services

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"

	"student-client/models"
	"student-client/utils"
)

// RunService 运行服务
type RunService struct {
}

// NewRunService 创建运行服务
func NewRunService() *RunService {
	return &RunService{}
}

// RunPackage 运行包
func (s *RunService) RunPackage(pkg *models.Package) error {
	utils.LogPrintf("开始运行包: ID=%d, 名称=%s, 版本=%s", pkg.ID, pkg.ExperimentName, pkg.Version)

	// 检查可执行文件是否存在
	if pkg.ExecutablePath == "" {
		utils.LogPrintf("可执行文件路径为空")
		return fmt.Errorf("可执行文件路径为空")
	}

	utils.LogPrintf("检查可执行文件: %s", pkg.ExecutablePath)
	fileInfo, err := os.Stat(pkg.ExecutablePath)
	if os.IsNotExist(err) {
		utils.LogPrintf("可执行文件不存在: %s", pkg.ExecutablePath)
		return fmt.Errorf("可执行文件不存在: %s", pkg.ExecutablePath)
	}

	utils.LogPrintf("可执行文件存在: %s, 大小: %d 字节, 修改时间: %s",
		pkg.ExecutablePath, fileInfo.Size(), fileInfo.ModTime().Format("2006-01-02 15:04:05"))

	// 获取工作目录
	workDir := filepath.Dir(pkg.ExecutablePath)

	// 获取绝对路径
	absPath, err := filepath.Abs(pkg.ExecutablePath)
	if err != nil {
		utils.LogPrintf("获取绝对路径失败: %v", err)
		return fmt.Errorf("获取绝对路径失败: %v", err)
	}

	// 记录路径信息
	utils.LogPrintf("执行文件绝对路径: %s", absPath)
	utils.LogPrintf("工作目录: %s", workDir)

	// 处理路径中的特殊字符
	var cmd *exec.Cmd

	// 根据操作系统选择不同的启动方式
	if runtime.GOOS == "windows" {
		// Windows系统使用cmd.exe启动
		utils.LogPrintf("使用Windows方式启动程序")

		// 处理路径中的引号
		exePath := absPath
		if strings.Contains(exePath, " ") {
			// 如果路径中包含空格，确保正确引用
			exePath = fmt.Sprintf(`"%s"`, exePath)
		}

		// 使用cmd.exe启动程序
		cmd = exec.Command("cmd.exe", "/C", "start", "", exePath)
	} else if runtime.GOOS == "darwin" {
		// macOS系统使用open命令启动
		utils.LogPrintf("使用macOS方式启动程序")

		// 检查文件是否是Windows可执行文件
		if strings.HasSuffix(strings.ToLower(absPath), ".exe") {
			utils.LogPrintf("检测到Windows可执行文件(.exe)，尝试使用Wine运行")

			// 尝试检查是否安装了Wine
			wineCmd := exec.Command("which", "wine")
			wineOutput, wineErr := wineCmd.Output()

			if wineErr == nil && len(wineOutput) > 0 {
				// 找到Wine，使用Wine运行
				winePath := strings.TrimSpace(string(wineOutput))
				utils.LogPrintf("找到Wine: %s", winePath)

				// 使用Wine运行Windows程序
				cmd = exec.Command(winePath, absPath)
				utils.LogPrintf("使用Wine运行Windows程序: %s %s", winePath, absPath)
			} else {
				// 没有找到Wine，提示错误
				utils.LogPrintf("在macOS上无法直接运行Windows可执行文件(.exe)，且未安装Wine")
				return fmt.Errorf("无法运行Windows程序(.exe)：请安装Wine或使用Windows系统运行此程序")
			}
		} else {
			// 使用open命令启动应用
			cmd = exec.Command("open", absPath)
		}
	} else {
		// 其他系统直接启动
		utils.LogPrintf("使用标准方式启动程序")
		cmd = exec.Command(absPath)
	}

	// 设置工作目录
	cmd.Dir = workDir

	// 设置标准输入输出
	cmd.Stdin = os.Stdin
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	// 启动命令
	utils.LogPrintf("开始启动命令")
	if err := cmd.Start(); err != nil {
		utils.LogPrintf("启动命令失败: %v", err)

		// 添加同步日志
		models.AddSyncLog(pkg.ID, "run", models.LogStatusFailed, fmt.Sprintf("启动失败: %v", err))

		return err
	}

	utils.LogPrintf("命令启动成功")

	// 更新最后运行时间
	if err := models.UpdateLastRun(pkg.ID); err != nil {
		utils.LogPrintf("更新最后运行时间失败: %v", err)
		return err
	}

	// 增加下载次数
	if err := models.IncrementDownloadCount(pkg.ID); err != nil {
		utils.LogPrintf("增加下载次数失败: %v", err)
		return err
	}

	// 添加同步日志
	if err := models.AddSyncLog(pkg.ID, "run", models.LogStatusSuccess, "启动成功"); err != nil {
		utils.LogPrintf("添加同步日志失败: %v", err)
		return err
	}

	utils.LogPrintf("包运行成功: ID=%d, 名称=%s", pkg.ID, pkg.ExperimentName)

	return nil
}

// CreateShortcut 创建桌面快捷方式
func (s *RunService) CreateShortcut(pkg *models.Package) error {
	// 检查可执行文件是否存在
	if pkg.ExecutablePath == "" {
		return fmt.Errorf("可执行文件路径为空")
	}

	if _, err := os.Stat(pkg.ExecutablePath); os.IsNotExist(err) {
		return fmt.Errorf("可执行文件不存在: %s", pkg.ExecutablePath)
	}

	// 获取桌面路径
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return err
	}
	desktopDir := filepath.Join(homeDir, "Desktop")

	// 检查桌面目录是否存在
	if _, err := os.Stat(desktopDir); os.IsNotExist(err) {
		// 尝试中文桌面路径
		desktopDir = filepath.Join(homeDir, "桌面")
		if _, err := os.Stat(desktopDir); os.IsNotExist(err) {
			return fmt.Errorf("找不到桌面目录")
		}
	}

	// 构建快捷方式名称
	shortcutName := fmt.Sprintf("%s-%s.bat", pkg.ExperimentName, pkg.Version)
	shortcutPath := filepath.Join(desktopDir, shortcutName)

	// 创建批处理文件
	content := fmt.Sprintf(`@echo off
cd /d "%s"
start "" "%s"
`, filepath.Dir(pkg.ExecutablePath), pkg.ExecutablePath)

	// 写入文件
	if err := os.WriteFile(shortcutPath, []byte(content), 0755); err != nil {
		return err
	}

	// 添加同步日志
	if err := models.AddSyncLog(pkg.ID, "shortcut", models.LogStatusSuccess, "创建桌面快捷方式成功"); err != nil {
		return err
	}

	return nil
}

// GetRecentlyRunPackages 获取最近运行的包
func (s *RunService) GetRecentlyRunPackages(limit int) ([]*models.Package, error) {
	packages, err := models.GetAllPackages()
	if err != nil {
		return nil, err
	}

	// 筛选出已运行过的包
	runPackages := make([]*models.Package, 0)
	for _, pkg := range packages {
		if !pkg.LastRun.IsZero() {
			runPackages = append(runPackages, pkg)
		}
	}

	// 按最后运行时间排序
	for i := 0; i < len(runPackages)-1; i++ {
		for j := i + 1; j < len(runPackages); j++ {
			if runPackages[i].LastRun.Before(runPackages[j].LastRun) {
				runPackages[i], runPackages[j] = runPackages[j], runPackages[i]
			}
		}
	}

	// 限制数量
	if len(runPackages) > limit {
		runPackages = runPackages[:limit]
	}

	return runPackages, nil
}

// GetMostRunPackages 获取运行次数最多的包
func (s *RunService) GetMostRunPackages(limit int) ([]*models.Package, error) {
	packages, err := models.GetAllPackages()
	if err != nil {
		return nil, err
	}

	// 筛选出已运行过的包
	runPackages := make([]*models.Package, 0)
	for _, pkg := range packages {
		if pkg.DownloadCount > 0 {
			runPackages = append(runPackages, pkg)
		}
	}

	// 按运行次数排序
	for i := 0; i < len(runPackages)-1; i++ {
		for j := i + 1; j < len(runPackages); j++ {
			if runPackages[i].DownloadCount < runPackages[j].DownloadCount {
				runPackages[i], runPackages[j] = runPackages[j], runPackages[i]
			}
		}
	}

	// 限制数量
	if len(runPackages) > limit {
		runPackages = runPackages[:limit]
	}

	return runPackages, nil
}
