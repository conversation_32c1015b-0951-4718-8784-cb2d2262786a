package services

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"student-client/models"
	"student-client/utils"
	"time"
)

// UpdateInfo 更新信息
type UpdateInfo struct {
	HasUpdate      bool               `json:"hasUpdate"`
	CurrentVersion string             `json:"currentVersion"`
	LatestVersion  string             `json:"latestVersion"`
	DownloadURL    string             `json:"downloadUrl"`
	Changelog      string             `json:"changelog"`
	ReleaseDate    string             `json:"releaseDate"`
	FileSize       int64              `json:"fileSize"`
	CheckSum       string             `json:"checkSum"`
	UpdateData     *CentralUpdateData `json:"updateData,omitempty"` // 完整的更新数据
}

// CentralUpdateResponse 中央端更新响应
type CentralUpdateResponse struct {
	Code string             `json:"code"`
	Msg  string             `json:"msg"`
	Data *CentralUpdateData `json:"data"`
}

// CentralUpdateData 中央端更新数据
type CentralUpdateData struct {
	PackageClientUpdateId string                 `json:"packageClientUpdateId"`
	CreateTime            int64                  `json:"createTime"`
	ExtraInfo             map[string]interface{} `json:"extraInfo"`
	VersionNumber         string                 `json:"versionNumber"` // 修改为string类型
	Type                  string                 `json:"type"`
	IsPushed              bool                   `json:"isPushed"`
	UpdateConfig          *UpdateConfig          `json:"updateConfig"`
}

// UpdateConfig 更新配置
type UpdateConfig struct {
	StudentClientDownloadUrl string `json:"studentClientDownloadUrl"`
	StudentClientFileHash    string `json:"studentClientFileHash"`
	StudentClientUpdateDes   string `json:"studentClientUpdateDes"`
}

// UpdateService 更新服务
type UpdateService struct {
	logger         *utils.Logger
	currentVersion string
	schoolAPIURL   string // 学校端API地址
	isChecking     bool
	lastCheckTime  time.Time
	autoUpdate     bool            // 是否启用自动更新
	ctx            context.Context // Wails上下文，用于发送事件
}

// UpdateServiceInstance 全局更新服务实例
var UpdateServiceInstance *UpdateService

// NewUpdateService 创建更新服务
func NewUpdateService(currentVersion string, logger *utils.Logger) *UpdateService {
	// 从学校端获取API地址
	schoolAPIURL := getSchoolAPIURL()

	// 检查是否启用自动更新
	autoUpdate := true // 默认启用自动更新
	if autoUpdateStr := os.Getenv("AUTO_UPDATE_ENABLED"); autoUpdateStr != "" {
		autoUpdate = autoUpdateStr == "true"
	}

	service := &UpdateService{
		logger:         logger,
		currentVersion: currentVersion,
		schoolAPIURL:   schoolAPIURL,
		isChecking:     false,
		autoUpdate:     autoUpdate,
	}

	// 注意：不在这里启动更新检查，而是在app.startup中触发
	// 这样可以确保有Wails上下文来发送事件

	return service
}

// InitUpdateService 初始化更新服务
func InitUpdateService(currentVersion string, logger *utils.Logger) {
	UpdateServiceInstance = NewUpdateService(currentVersion, logger)
}

// SetWailsContext 设置Wails上下文
func SetWailsContext(ctx context.Context) {
	if UpdateServiceInstance != nil {
		UpdateServiceInstance.ctx = ctx
	}
}

// getSchoolAPIURL 获取学校端API地址
func getSchoolAPIURL() string {
	// 从配置中获取学校端地址
	serverURL, err := models.GetServerURL()
	if err != nil || serverURL == "" {
		return ""
	}

	serverPort, err := models.GetServerPort()
	if err != nil || serverPort == "" {
		serverPort = "18080"
	}

	return fmt.Sprintf("http://%s:%s", serverURL, serverPort)
}

// CheckForUpdates 检查更新
func (us *UpdateService) CheckForUpdates() (*UpdateInfo, error) {
	if us.isChecking {
		return nil, fmt.Errorf("正在检查更新中，请稍后再试")
	}

	us.isChecking = true
	defer func() {
		us.isChecking = false
		us.lastCheckTime = time.Now()
	}()

	us.logger.Info("UpdateService", "CheckForUpdates", "开始检查程序更新")

	// 检查学校端API地址是否可用
	if us.schoolAPIURL == "" {
		return nil, fmt.Errorf("学校端API地址未配置")
	}

	// 构建请求URL - 对接学校端API（不需要传递参数，学校端根据自己的版本返回对应的学生端更新）
	checkURL := fmt.Sprintf("%s/api/student/update/check", us.schoolAPIURL)

	us.logger.Info("UpdateService", "CheckForUpdates",
		fmt.Sprintf("检查更新URL: %s", checkURL))

	// 发送HTTP请求
	resp, err := http.Get(checkURL)
	if err != nil {
		us.logger.Error("UpdateService", "CheckForUpdates",
			fmt.Sprintf("请求更新检查失败: %v", err))
		return nil, fmt.Errorf("请求更新检查失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析响应
	var updateResp CentralUpdateResponse
	if err := json.Unmarshal(body, &updateResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 构建更新信息
	updateInfo := &UpdateInfo{
		CurrentVersion: us.currentVersion,
		HasUpdate:      false,
	}

	// 检查是否有更新
	if updateResp.Code == "000000" && updateResp.Data != nil {
		updateInfo.LatestVersion = updateResp.Data.VersionNumber // 直接使用字符串
		updateInfo.UpdateData = updateResp.Data

		// 比较版本号，只有当版本不一致时才认为有更新
		if updateInfo.CurrentVersion != updateInfo.LatestVersion {
			updateInfo.HasUpdate = true

			// 设置下载信息
			if updateResp.Data.UpdateConfig != nil {
				updateInfo.DownloadURL = updateResp.Data.UpdateConfig.StudentClientDownloadUrl
				updateInfo.CheckSum = updateResp.Data.UpdateConfig.StudentClientFileHash
				updateInfo.Changelog = updateResp.Data.UpdateConfig.StudentClientUpdateDes
			}

			us.logger.Info("UpdateService", "CheckForUpdates",
				fmt.Sprintf("发现新版本: %s → %s", updateInfo.CurrentVersion, updateInfo.LatestVersion))
		} else {
			// 版本号相同，无需更新
			us.logger.Info("UpdateService", "CheckForUpdates",
				fmt.Sprintf("当前版本 %s 已是最新版本", updateInfo.CurrentVersion))
		}
	} else if updateResp.Code == "000001" {
		// 学校端返回000001表示当前已是最新版本
		us.logger.Info("UpdateService", "CheckForUpdates", "学校端返回：当前已是最新版本")
	} else {
		// 其他情况也认为是最新版本
		us.logger.Info("UpdateService", "CheckForUpdates",
			fmt.Sprintf("学校端返回代码: %s, 消息: %s", updateResp.Code, updateResp.Msg))
	}

	return updateInfo, nil
}

// parseVersionNumber 解析版本号为数字格式
func (us *UpdateService) parseVersionNumber(version string) (int64, error) {
	// 移除可能的前缀和后缀
	version = strings.TrimSpace(version)
	version = strings.TrimPrefix(version, "v")
	version = strings.TrimPrefix(version, "V")

	// 尝试直接转换为数字
	if num, err := strconv.ParseInt(version, 10, 64); err == nil {
		return num, nil
	}

	// 如果包含点号，尝试移除点号后转换
	if strings.Contains(version, ".") {
		version = strings.ReplaceAll(version, ".", "")
		if num, err := strconv.ParseInt(version, 10, 64); err == nil {
			return num, nil
		}
	}

	return 0, fmt.Errorf("无法解析版本号: %s", version)
}

// isWindows 检查是否为Windows系统
func (us *UpdateService) isWindows() bool {
	return runtime.GOOS == "windows"
}

// checkOnStartup 启动时检查更新
func (us *UpdateService) checkOnStartup() {
	// 等待一段时间后再检查，确保应用完全启动
	time.Sleep(5 * time.Second)

	us.logger.Info("UpdateService", "checkOnStartup", "启动时检查更新")

	// 检查更新
	updateInfo, err := us.CheckForUpdates()
	if err != nil {
		us.logger.Error("UpdateService", "checkOnStartup",
			fmt.Sprintf("启动时检查更新失败: %v", err))
		return
	}

	if !updateInfo.HasUpdate {
		us.logger.Info("UpdateService", "checkOnStartup", "当前已是最新版本")
		return
	}

	us.logger.Info("UpdateService", "checkOnStartup",
		fmt.Sprintf("发现新版本: %s → %s", updateInfo.CurrentVersion, updateInfo.LatestVersion))

	// 如果启用了自动更新，则自动下载并更新
	if us.autoUpdate {
		us.logger.Info("UpdateService", "checkOnStartup", "开始自动更新")

		// 下载更新文件
		newExePath, err := us.DownloadUpdateWithHash(updateInfo.DownloadURL, updateInfo.CheckSum, updateInfo.LatestVersion)
		if err != nil {
			us.logger.Error("UpdateService", "checkOnStartup",
				fmt.Sprintf("自动下载更新失败: %v", err))
			return
		}

		us.logger.Info("UpdateService", "checkOnStartup",
			fmt.Sprintf("更新文件下载完成: %s", newExePath))

		// 保存版本信息到数据库
		if err := us.saveVersionInfo(updateInfo.LatestVersion, newExePath, updateInfo.UpdateData); err != nil {
			us.logger.Warn("UpdateService", "checkOnStartup",
				fmt.Sprintf("保存版本信息失败: %v", err))
		}

		// 启动更新
		if err := us.StartUpdate(newExePath); err != nil {
			us.logger.Error("UpdateService", "checkOnStartup",
				fmt.Sprintf("启动更新失败: %v", err))
			return
		}

		us.logger.Info("UpdateService", "checkOnStartup", "自动更新已启动，程序即将重启")
	}
}

// DownloadUpdateWithHash 下载更新文件并验证哈希
func (us *UpdateService) DownloadUpdateWithHash(downloadURL, expectedHash, version string) (string, error) {
	us.logger.Info("UpdateService", "DownloadUpdate",
		fmt.Sprintf("开始下载更新文件: %s", downloadURL))

	// 确保更新目录存在
	updateDir := "./updates"
	if err := utils.EnsureDir(updateDir); err != nil {
		return "", fmt.Errorf("创建更新目录失败: %v", err)
	}

	// 构建保存路径
	var filename string
	if us.isWindows() {
		filename = fmt.Sprintf("student-client-v%s.exe", version)
	} else {
		filename = fmt.Sprintf("student-client-v%s", version)
	}
	savePath := filepath.Join(updateDir, filename)

	// 构建完整URL
	fullURL := downloadURL
	if !strings.HasPrefix(downloadURL, "http") {
		// 如果是相对路径，使用学校端API地址
		// 处理Windows路径分隔符
		downloadURL = strings.ReplaceAll(downloadURL, "\\", "/")
		if !strings.HasPrefix(downloadURL, "/") {
			downloadURL = "/" + downloadURL
		}
		fullURL = us.schoolAPIURL + downloadURL
	}

	// 创建请求
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		return "", err
	}

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Minute} // 30分钟超时
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("下载失败，状态码: %d", resp.StatusCode)
	}

	// 创建临时文件
	tmpFile, err := os.CreateTemp(updateDir, "download-*.tmp")
	if err != nil {
		return "", err
	}
	tmpPath := tmpFile.Name()
	defer func() {
		tmpFile.Close()
		// 如果出错，删除临时文件
		if err != nil {
			os.Remove(tmpPath)
		}
	}()

	// 复制数据到临时文件
	_, err = io.Copy(tmpFile, resp.Body)
	if err != nil {
		return "", err
	}

	// 关闭临时文件
	if err := tmpFile.Close(); err != nil {
		return "", err
	}

	// 验证文件哈希
	if expectedHash != "" {
		us.logger.Info("UpdateService", "DownloadUpdate", "开始验证文件哈希...")
		hash, err := us.calculateFileHash(tmpPath)
		if err != nil {
			us.logger.Error("UpdateService", "DownloadUpdate",
				fmt.Sprintf("计算文件哈希失败: %v", err))
			return "", err
		}

		us.logger.Info("UpdateService", "DownloadUpdate",
			fmt.Sprintf("文件哈希验证: 期望=%s, 实际=%s", expectedHash, hash))
		if hash != expectedHash {
			us.logger.Error("UpdateService", "DownloadUpdate",
				fmt.Sprintf("文件哈希验证失败: 期望=%s, 实际=%s", expectedHash, hash))
			return "", fmt.Errorf("文件哈希验证失败，期望: %s，实际: %s", expectedHash, hash)
		}
		us.logger.Info("UpdateService", "DownloadUpdate", "文件哈希验证成功")
	}

	// 重命名临时文件
	if err := os.Rename(tmpPath, savePath); err != nil {
		return "", err
	}

	// 设置可执行权限（非Windows系统）
	if !us.isWindows() {
		if err := os.Chmod(savePath, 0755); err != nil {
			us.logger.Warn("UpdateService", "DownloadUpdate",
				fmt.Sprintf("设置可执行权限失败: %v", err))
		}
	}

	us.logger.Info("UpdateService", "DownloadUpdate",
		fmt.Sprintf("更新文件下载完成: %s", savePath))

	return savePath, nil
}

// calculateFileHash 计算文件哈希值（使用xxHash算法，与学校端保持一致）
func (us *UpdateService) calculateFileHash(filePath string) (string, error) {
	return utils.CalculateFileHash(filePath)
}

// StartUpdate 启动更新
func (us *UpdateService) StartUpdate(newExePath string) error {
	us.logger.Info("UpdateService", "StartUpdate", "开始启动更新器")

	// 获取当前程序路径
	currentExe, err := os.Executable()
	if err != nil {
		return fmt.Errorf("获取当前程序路径失败: %v", err)
	}

	// 根据操作系统类型确定更新器路径
	var updaterPath string
	if us.isWindows() {
		updaterPath = "./updater.exe"
	} else {
		updaterPath = "./updater"
	}

	if _, err := os.Stat(updaterPath); err != nil {
		return fmt.Errorf("更新器不存在: %s", updaterPath)
	}

	// 启动更新器
	cmd := exec.Command(updaterPath,
		"--type=student",
		"--old="+currentExe,
		"--new="+newExePath,
		"--restart")

	us.logger.Info("UpdateService", "StartUpdate",
		fmt.Sprintf("启动更新器: %s", cmd.String()))

	if err := cmd.Start(); err != nil {
		return fmt.Errorf("启动更新器失败: %v", err)
	}

	us.logger.Info("UpdateService", "StartUpdate", "更新器已启动，程序即将退出")

	// 退出当前程序，让更新器接管
	os.Exit(0)
	return nil
}

// saveVersionInfo 保存版本信息到数据库
func (us *UpdateService) saveVersionInfo(version, filePath string, updateData *CentralUpdateData) error {
	// 构建版本信息
	versionInfo := map[string]interface{}{
		"version":                    version,
		"student_client_path":        filePath,
		"student_client_file_hash":   updateData.UpdateConfig.StudentClientFileHash,
		"student_update_description": updateData.UpdateConfig.StudentClientUpdateDes,
		"package_client_update_id":   updateData.PackageClientUpdateId,
		"installed_at":               time.Now(),
		"is_current":                 0, // 新下载的版本暂时不设为当前版本
	}

	// 这里应该调用models包的方法保存到数据库
	// 由于当前models包可能还没有相关方法，先记录日志
	us.logger.Info("UpdateService", "saveVersionInfo",
		fmt.Sprintf("保存版本信息: %v", versionInfo))

	return nil
}

// CheckAndAutoUpdate 检查并自动更新（可供外部调用）
func (us *UpdateService) CheckAndAutoUpdate() error {
	us.logger.Info("UpdateService", "CheckAndAutoUpdate", "开始检查并自动更新")

	// 检查更新
	updateInfo, err := us.CheckForUpdates()
	if err != nil {
		return fmt.Errorf("检查更新失败: %v", err)
	}

	if !updateInfo.HasUpdate {
		us.logger.Info("UpdateService", "CheckAndAutoUpdate", "当前已是最新版本")
		return nil
	}

	us.logger.Info("UpdateService", "CheckAndAutoUpdate",
		fmt.Sprintf("发现新版本: %s → %s", updateInfo.CurrentVersion, updateInfo.LatestVersion))

	// 如果启用了自动更新，则自动下载并更新
	if !us.autoUpdate {
		return fmt.Errorf("自动更新已禁用")
	}

	// 下载更新文件
	newExePath, err := us.DownloadUpdateWithHash(updateInfo.DownloadURL, updateInfo.CheckSum, updateInfo.LatestVersion)
	if err != nil {
		return fmt.Errorf("下载更新失败: %v", err)
	}

	us.logger.Info("UpdateService", "CheckAndAutoUpdate",
		fmt.Sprintf("更新文件下载完成: %s", newExePath))

	// 保存版本信息到数据库
	if err := us.saveVersionInfo(updateInfo.LatestVersion, newExePath, updateInfo.UpdateData); err != nil {
		us.logger.Warn("UpdateService", "CheckAndAutoUpdate",
			fmt.Sprintf("保存版本信息失败: %v", err))
	}

	// 启动更新
	if err := us.StartUpdate(newExePath); err != nil {
		return fmt.Errorf("启动更新失败: %v", err)
	}

	us.logger.Info("UpdateService", "CheckAndAutoUpdate", "自动更新已启动，程序即将重启")
	return nil
}

// GetCurrentVersion 获取当前版本
func (us *UpdateService) GetCurrentVersion() string {
	return us.currentVersion
}

// GetLastCheckTime 获取最后检查时间
func (us *UpdateService) GetLastCheckTime() time.Time {
	return us.lastCheckTime
}

// IsChecking 是否正在检查更新
func (us *UpdateService) IsChecking() bool {
	return us.isChecking
}

// IsAutoUpdateEnabled 是否启用自动更新
func (us *UpdateService) IsAutoUpdateEnabled() bool {
	return us.autoUpdate
}
