package services

import (
	"archive/zip"
	"bytes"
	"fmt"
	"io"
	"math"
	"os"
	"path/filepath"
	"strings"
	"time"

	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
	"student-client/models"
	"student-client/utils"
)

// ExtractProgress 解压进度
type ExtractProgress struct {
	Total       int     `json:"total"`
	Extracted   int     `json:"extracted"`
	Percentage  float64 `json:"percentage"`
	CurrentFile string  `json:"currentFile"`
	Error       string  `json:"error,omitempty"` // 错误信息，如果有的话
	Completed   bool    `json:"completed,omitempty"` // 是否完成
}

// ExtractService 解压服务
type ExtractService struct {
	ExtractDir string
	cancelChan chan bool // 取消通道
}

// NewExtractService 创建解压服务
func NewExtractService() *ExtractService {
	return &ExtractService{
		ExtractDir: "./extracted",
		cancelChan: make(chan bool, 1),
	}
}

// ExtractPackage 解压包文件
func (s *ExtractService) ExtractPackage(pkg *models.Package, progressChan chan<- ExtractProgress, progressHandler func(ExtractProgress)) error {
	// 检查本地文件是否存在
	if pkg.LocalPath == "" {
		return fmt.Errorf("包文件路径为空")
	}

	if _, err := os.Stat(pkg.LocalPath); os.IsNotExist(err) {
		return fmt.Errorf("包文件不存在: %s", pkg.LocalPath)
	}

	// 使用学校ID创建子目录
	schoolDir := filepath.Join(s.ExtractDir, pkg.SchoolID)
	if err := os.MkdirAll(schoolDir, 0755); err != nil {
		return fmt.Errorf("创建学校目录失败: %v", err)
	}

	// 创建实验目录
	experimentDir := filepath.Join(schoolDir, pkg.ExperimentID)
	if err := os.MkdirAll(experimentDir, 0755); err != nil {
		return fmt.Errorf("创建实验目录失败: %v", err)
	}

	// 创建解压目录，使用ExperimentVersionID命名
	utils.LogPrintf("创建解压目录: 学校ID=%s, 实验ID=%s, 实验版本ID=%s", pkg.SchoolID, pkg.ExperimentID, pkg.ExperimentVersionID)
	extractDir := filepath.Join(experimentDir, pkg.ExperimentVersionID)

	// 如果解压目录已存在，先清空它以防止新旧文件冲突
	if _, err := os.Stat(extractDir); err == nil {
		utils.LogPrintf("解压目录已存在，正在清空: %s", extractDir)

		// 使用同步方式删除整个目录
		if err := removeDirectorySync(extractDir); err != nil {
			utils.LogPrintf("清空解压目录失败: %v", err)
			return fmt.Errorf("清空解压目录失败: %v", err)
		}
		utils.LogPrintf("解压目录清空成功")
	}

	// 创建解压目录
	if err := os.MkdirAll(extractDir, 0755); err != nil {
		utils.LogPrintf("创建解压目录失败: %v", err)
		return fmt.Errorf("创建解压目录失败: %v", err)
	}
	utils.LogPrintf("解压目录创建成功: %s", extractDir)

	// 更新包状态为解压中
	if err := models.UpdatePackageStatus(pkg.ID, models.SyncStatusExtracting); err != nil {
		return err
	}

	// 添加同步日志
	if err := models.AddSyncLog(pkg.ID, "extract", models.LogStatusRunning, "开始解压"); err != nil {
		return err
	}

	// 打开zip文件
	reader, err := zip.OpenReader(pkg.LocalPath)
	if err != nil {
		// 添加同步日志
		models.AddSyncLog(pkg.ID, "extract", models.LogStatusFailed, fmt.Sprintf("打开zip文件失败: %v", err))
		return err
	}
	defer reader.Close()

	// 获取文件总数
	total := len(reader.File)
	utils.LogPrintf("开始解压，共 %d 个文件", total)

	// 解压文件
	for i, file := range reader.File {
		// 检查是否需要取消
		select {
		case <-s.cancelChan:
			utils.LogPrintf("解压被用户取消")
			return fmt.Errorf("解压被用户取消")
		default:
			// 继续解压
		}

		// 处理文件名编码问题
		var decodedFileName string
		if file.Flags == 0 {
			// 如果标志位是0，则是默认的本地编码（通常是GBK）
			utils.LogPrintf("检测到GBK编码的文件名: %s", file.Name)
			reader := bytes.NewReader([]byte(file.Name))
			decoder := transform.NewReader(reader, simplifiedchinese.GB18030.NewDecoder())
			content, err := io.ReadAll(decoder)
			if err != nil {
				utils.LogPrintf("解码文件名失败，使用原始文件名: %v", err)
				decodedFileName = file.Name
			} else {
				decodedFileName = string(content)
				utils.LogPrintf("解码后的文件名: %s", decodedFileName)
			}
		} else {
			// 如果标志位不是0，则使用UTF-8编码
			decodedFileName = file.Name
		}

		// 记录当前处理的文件
		utils.LogPrintf("正在处理第 %d 个文件: %s", i+1, decodedFileName)

		// 创建进度对象
		percentage := float64(i) / float64(total) * 100
		// 保留两位小数
		percentage = math.Floor(percentage*100) / 100

		progress := ExtractProgress{
			Total:       total,
			Extracted:   i,
			Percentage:  percentage,
			CurrentFile: decodedFileName,
		}

		// 每10个文件或者是第一个或最后一个文件时发送进度
		if i == 0 || i == total-1 || i%10 == 0 {
			utils.LogPrintf("解压进度: %.2f%% (%d/%d)", percentage, i, total)

			// 发送进度到通道
			if progressChan != nil {
				select {
				case progressChan <- progress:
					utils.LogPrintf("成功发送进度到通道")
				default:
					utils.LogPrintf("进度通道已满，跳过发送")
				}
			}

			// 使用进度处理函数
			if progressHandler != nil {
				utils.LogPrintf("调用进度处理函数")
				progressHandler(progress)
			}
		}

		// 构建解压路径，使用解码后的文件名
		path := filepath.Join(extractDir, decodedFileName)

		// 检查路径是否在解压目录内（防止zip slip漏洞）
		if !strings.HasPrefix(path, extractDir) {
			errMsg := fmt.Sprintf("非法的文件路径: %s", decodedFileName)
			utils.LogPrintf("错误: %s", errMsg)
			return fmt.Errorf(errMsg)
		}

		// 创建目录
		if file.FileInfo().IsDir() {
			utils.LogPrintf("创建目录: %s", path)
			if err := os.MkdirAll(path, file.Mode()); err != nil {
				utils.LogPrintf("创建目录失败: %v", err)
				return fmt.Errorf("创建目录失败 %s: %v", path, err)
			}
			continue
		}

		// 创建父目录
		utils.LogPrintf("创建父目录: %s", filepath.Dir(path))
		if err := os.MkdirAll(filepath.Dir(path), 0755); err != nil {
			utils.LogPrintf("创建父目录失败: %v", err)
			return fmt.Errorf("创建父目录失败 %s: %v", filepath.Dir(path), err)
		}

		// 创建文件
		utils.LogPrintf("创建文件: %s", path)
		outFile, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, file.Mode())
		if err != nil {
			utils.LogPrintf("创建文件失败: %v", err)
			return fmt.Errorf("创建文件失败 %s: %v", path, err)
		}

		// 打开zip中的文件
		utils.LogPrintf("打开zip中的文件: %s", decodedFileName)
		inFile, err := file.Open()
		if err != nil {
			outFile.Close()
			utils.LogPrintf("打开zip中的文件失败: %v", err)
			return fmt.Errorf("打开zip中的文件失败 %s: %v", decodedFileName, err)
		}

		// 复制内容
		utils.LogPrintf("复制文件内容: %s", decodedFileName)
		written, err := io.Copy(outFile, inFile)
		utils.LogPrintf("复制了 %d 字节", written)

		// 关闭文件
		outFileErr := outFile.Close()
		inFileErr := inFile.Close()

		if err != nil {
			utils.LogPrintf("复制文件内容失败: %v", err)
			return fmt.Errorf("复制文件内容失败 %s: %v", decodedFileName, err)
		}

		if outFileErr != nil {
			utils.LogPrintf("关闭输出文件失败: %v", outFileErr)
			return fmt.Errorf("关闭输出文件失败 %s: %v", path, outFileErr)
		}

		if inFileErr != nil {
			utils.LogPrintf("关闭输入文件失败: %v", inFileErr)
			return fmt.Errorf("关闭输入文件失败 %s: %v", decodedFileName, inFileErr)
		}

		// 每处理完一个文件，添加一个小延迟，避免系统资源占用过高
		if i > 0 && i%50 == 0 {
			utils.LogPrintf("处理了50个文件，暂停一下")
			time.Sleep(10 * time.Millisecond)
		}
	}

	// 查找可执行文件
	utils.LogPrintln("开始查找可执行文件...")
	executablePath, err := findExecutable(extractDir)
	if err != nil {
		utils.LogPrintf("查找可执行文件失败: %v", err)
		// 添加同步日志
		models.AddSyncLog(pkg.ID, "extract", models.LogStatusFailed, fmt.Sprintf("查找可执行文件失败: %v", err))
		return err
	}
	utils.LogPrintf("找到可执行文件: %s", executablePath)

	// 更新包的解压路径和可执行文件路径
	pkg.ExtractPath = extractDir
	pkg.ExecutablePath = executablePath
	if err := models.UpdatePackageExtractPath(pkg.ID, extractDir); err != nil {
		return err
	}
	if err := models.UpdatePackageExecutablePath(pkg.ID, executablePath); err != nil {
		return err
	}

	// 更新包状态为已完成
	if err := models.UpdatePackageStatus(pkg.ID, models.SyncStatusCompleted); err != nil {
		return err
	}

	// 添加同步日志
	if err := models.AddSyncLog(pkg.ID, "extract", models.LogStatusSuccess, "解压完成"); err != nil {
		return err
	}

	// 创建最终进度对象
	finalProgress := ExtractProgress{
		Total:       total,
		Extracted:   total,
		Percentage:  100.0,
		CurrentFile: "",
		Completed:   true,
	}

	utils.LogPrintln("解压完成，发送最终进度")

	// 发送最终进度到通道，使用非阻塞方式
	if progressChan != nil {
		select {
		case progressChan <- finalProgress:
			utils.LogPrintln("成功发送最终进度到通道")
		default:
			utils.LogPrintln("进度通道已满，直接使用处理函数发送最终进度")
		}
	}

	// 使用进度处理函数，确保最终进度一定会发送
	if progressHandler != nil {
		utils.LogPrintln("调用进度处理函数发送最终进度")
		progressHandler(finalProgress)

		// 再次发送一次，确保前端能收到
		time.Sleep(100 * time.Millisecond)
		utils.LogPrintln("再次调用进度处理函数发送最终进度")
		progressHandler(finalProgress)
	}

	// 等待一小段时间，确保前端能接收到最终进度
	time.Sleep(500 * time.Millisecond)

	return nil
}

// ExecutableInfo 可执行文件信息
type ExecutableInfo struct {
	Path  string
	Depth int    // 目录层级深度
	Size  int64  // 文件大小
}

// findExecutable 查找可执行文件
// 优先选择目录层级最浅（最上层）的可执行文件
func findExecutable(dir string) (string, error) {
	var executables []ExecutableInfo

	// 遍历目录查找可执行文件
	utils.LogPrintf("开始在目录 %s 中查找可执行文件", dir)
	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			utils.LogPrintf("遍历目录时出错: %v", err)
			return err
		}

		// 检查是否是可执行文件
		if !info.IsDir() {
			ext := strings.ToLower(filepath.Ext(path))
			if ext == ".exe" || ext == ".bat" || ext == ".cmd" {
				fileName := filepath.Base(path)
				// 排除 UnityCrashHandler64.exe 文件
				if strings.Contains(strings.ToLower(fileName), "unitycrashhandler") {
					utils.LogPrintf("跳过 Unity 崩溃处理程序: %s", fileName)
				} else {
					// 计算目录层级深度
					relPath, err := filepath.Rel(dir, path)
					if err != nil {
						utils.LogPrintf("计算相对路径失败: %v", err)
						return err
					}
					depth := len(strings.Split(filepath.ToSlash(relPath), "/")) - 1 // 减1因为文件本身不算层级

					utils.LogPrintf("找到可执行文件: %s (层级: %d, 大小: %d 字节)", fileName, depth, info.Size())
					executables = append(executables, ExecutableInfo{
						Path:  path,
						Depth: depth,
						Size:  info.Size(),
					})
				}
			}
		}

		return nil
	})

	if err != nil {
		return "", err
	}

	if len(executables) == 0 {
		utils.LogPrintln("未找到任何可执行文件")
		return "", fmt.Errorf("未找到可执行文件")
	}

	utils.LogPrintf("找到 %d 个可执行文件", len(executables))

	// 按目录层级排序，层级浅的优先
	// 如果层级相同，则按文件大小排序，大文件优先
	var selectedExecutable ExecutableInfo
	minDepth := int(^uint(0) >> 1) // 最大整数值
	maxSizeAtMinDepth := int64(0)

	for _, exec := range executables {
		utils.LogPrintf("检查可执行文件: %s, 层级: %d, 大小: %d 字节",
			filepath.Base(exec.Path), exec.Depth, exec.Size)

		// 优先选择层级最浅的文件
		if exec.Depth < minDepth {
			minDepth = exec.Depth
			maxSizeAtMinDepth = exec.Size
			selectedExecutable = exec
			utils.LogPrintf("选择更浅层级的文件: %s (层级: %d)",
				filepath.Base(exec.Path), exec.Depth)
		} else if exec.Depth == minDepth {
			// 如果层级相同，选择文件大小更大的
			if exec.Size > maxSizeAtMinDepth {
				maxSizeAtMinDepth = exec.Size
				selectedExecutable = exec
				utils.LogPrintf("在相同层级中选择更大的文件: %s (大小: %d 字节)",
					filepath.Base(exec.Path), exec.Size)
			}
		}
	}

	utils.LogPrintf("最终选择的可执行文件: %s (层级: %d, 大小: %d 字节)",
		filepath.Base(selectedExecutable.Path), selectedExecutable.Depth, selectedExecutable.Size)
	return selectedExecutable.Path, nil
}

// removeDirectorySync 同步删除目录
// 确保目录完全删除后才返回，避免并发问题
func removeDirectorySync(dir string) error {
	utils.LogPrintf("开始同步删除目录: %s", dir)

	// 首先尝试直接删除
	err := os.RemoveAll(dir)
	if err != nil {
		utils.LogPrintf("删除目录失败: %v", err)
		return err
	}

	// 等待并验证目录确实被删除
	maxRetries := 10
	for i := 0; i < maxRetries; i++ {
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			utils.LogPrintf("目录删除成功: %s", dir)
			return nil
		}

		// 如果目录仍然存在，等待一小段时间后重试
		utils.LogPrintf("目录仍然存在，等待删除完成... (重试 %d/%d)", i+1, maxRetries)
		time.Sleep(100 * time.Millisecond)

		// 再次尝试删除
		os.RemoveAll(dir)
	}

	// 如果经过多次重试后目录仍然存在，返回错误
	if _, err := os.Stat(dir); err == nil {
		return fmt.Errorf("目录删除超时，可能被其他进程占用: %s", dir)
	}

	utils.LogPrintf("目录删除完成: %s", dir)
	return nil
}

// CancelExtract 取消解压
func (s *ExtractService) CancelExtract() {
	select {
	case s.cancelChan <- true:
		utils.LogPrintf("发送取消解压信号")
	default:
		utils.LogPrintf("取消通道已满，解压可能已完成")
	}
}

// GetExtractedPackages 获取已解压的包
func (s *ExtractService) GetExtractedPackages() ([]*models.Package, error) {
	packages, err := models.GetAllPackages()
	if err != nil {
		return nil, err
	}

	extractedPackages := make([]*models.Package, 0)
	for _, pkg := range packages {
		if pkg.SyncStatus == models.SyncStatusCompleted && pkg.ExtractPath != "" {
			extractedPackages = append(extractedPackages, pkg)
		}
	}

	return extractedPackages, nil
}
