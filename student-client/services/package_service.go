package services

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"time"
)

// PackageService 包管理服务
type PackageService struct {
	ServerURL    string
	DownloadDir  string
	ExtractedDir string
}

// NewPackageService 创建包管理服务
func NewPackageService() *PackageService {
	return &PackageService{
		ServerURL:    "http://127.0.0.1:118080",
		DownloadDir:  "./downloads",
		ExtractedDir: "./extracted",
	}
}

// SetServerURL 设置服务器URL
func (s *PackageService) SetServerURL(url string) {
	s.ServerURL = url
}

// GetServerStatus 获取服务器状态
func (s *PackageService) GetServerStatus() map[string]interface{} {
	// 尝试连接服务器
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	resp, err := client.Get(s.ServerURL + "/api/system/status")
	if err != nil {
		log.Printf("连接服务器失败: %v", err)
		return map[string]interface{}{
			"online":  false,
			"message": fmt.Sprintf("连接服务器失败: %v", err),
			"address": s.ServerURL,
		}
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Printf("服务器返回错误状态码: %d", resp.StatusCode)
		return map[string]interface{}{
			"online":  false,
			"message": fmt.Sprintf("服务器返回错误状态码: %d", resp.StatusCode),
			"address": s.ServerURL,
		}
	}

	return map[string]interface{}{
		"online":  true,
		"message": "服务器连接正常",
		"address": s.ServerURL,
	}
}

// GetPackages 获取包列表
func (s *PackageService) GetPackages() ([]map[string]interface{}, error) {
	// 模拟数据，实际应该从服务器获取
	packages := []map[string]interface{}{
		{
			"id":                  1,
			"experimentName":      "物理实验",
			"versionName":         "力学基础",
			"version":             "1.0.0",
			"syncStatus":          2,
			"experimentVersionId": "exp001",
		},
		{
			"id":                  2,
			"experimentName":      "化学实验",
			"versionName":         "滴定分析",
			"version":             "2.1.0",
			"syncStatus":          3,
			"experimentVersionId": "exp002",
		},
		{
			"id":                  3,
			"experimentName":      "生物实验",
			"versionName":         "细胞观察",
			"version":             "1.5.0",
			"syncStatus":          0,
			"experimentVersionId": "exp003",
		},
	}

	return packages, nil
}

// RunPackage 运行包
func (s *PackageService) RunPackage(id int) error {
	// 模拟运行包
	log.Printf("运行包: %d", id)

	// 实际应该查询数据库获取可执行文件路径
	execPath := filepath.Join(s.ExtractedDir, fmt.Sprintf("package_%d", id), "app.exe")

	// 检查文件是否存在
	if _, err := os.Stat(execPath); os.IsNotExist(err) {
		return fmt.Errorf("可执行文件不存在: %s", execPath)
	}

	// 启动应用
	cmd := exec.Command(execPath)
	cmd.Dir = filepath.Dir(execPath)

	return cmd.Start()
}
