2025/05/24 00:00:49 logger.go:72: 日志系统初始化成功
2025/05/24 00:00:49 app.go:45: 应用启动
2025/05/24 00:00:49 db.go:47: Database initialized successfully
2025/05/24 00:00:50 app.go:143: 同步学校信息成功，学校ID: 588328868260089856，学校名称: 黑龙江农业职业技术学院
2025/05/24 00:00:50 logger.go:105: 开始同步包列表
2025/05/24 00:00:50 logger.go:105: 正在获取远程包列表...
2025/05/24 00:00:50 logger.go:105: 正在获取本地包列表...
2025/05/24 00:00:50 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592503365435396096
2025/05/24 00:00:50 logger.go:90: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/24 00:00:50 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592564199850921984
2025/05/24 00:00:50 logger.go:90: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/24 00:00:50 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592480007272534016
2025/05/24 00:00:50 logger.go:90: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/24 00:00:50 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592464199850921984
2025/05/24 00:00:50 logger.go:90: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/24 00:00:50 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592460029626421248
2025/05/24 00:00:50 logger.go:90: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/24 00:00:50 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592455327845191680
2025/05/24 00:00:50 logger.go:90: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/24 00:00:50 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592407926350548992
2025/05/24 00:00:50 logger.go:90: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/24 00:00:50 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592390778655870976
2025/05/24 00:00:50 logger.go:90: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/24 00:00:50 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592389792184930304
2025/05/24 00:00:50 logger.go:90: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/24 00:00:50 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592389406124412928
2025/05/24 00:00:50 logger.go:90: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/24 00:00:50 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388575442505728
2025/05/24 00:00:50 logger.go:90: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/24 00:00:50 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388420613967872
2025/05/24 00:00:50 logger.go:90: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/24 00:00:50 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388303035043840
2025/05/24 00:00:50 logger.go:90: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/24 00:00:50 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388093168848896
2025/05/24 00:00:50 logger.go:90: 发现新实验版本: 冠军精神虚拟仿真馆 (592362717994553344)
2025/05/24 00:00:50 logger.go:90: 同步包列表完成，共同步 14 个包
2025/05/24 00:01:07 logger.go:105: 开始验证文件哈希...
2025/05/24 00:01:07 logger.go:90: 文件哈希验证: 期望=069ef0ce77a248ee, 实际=069ef0ce77a248ee
2025/05/24 00:01:07 logger.go:105: 文件哈希验证成功
2025/05/24 00:01:07 logger.go:90: 成功向学校端发送下载记录: 包版本ID=592564199850921984, MAC=d8:43:ae:28:3a:60, IP=*************
2025/05/24 00:01:07 logger.go:105: 成功发送最终进度到通道
2025/05/24 00:01:07 logger.go:105: 调用进度处理函数发送最终进度
2025/05/24 00:01:07 logger.go:90: 开始解压包 ID: 2, 解压ID: extract-2-1748016067427052000
2025/05/24 00:01:07 logger.go:90: 创建解压目录: 学校ID=588328868260089856, 实验ID=592348408270946304, 实验版本ID=592362717994553344
2025/05/24 00:01:07 logger.go:90: 解压目录创建成功: extracted/588328868260089856/592348408270946304/592362717994553344
2025/05/24 00:01:07 logger.go:90: 开始解压，共 259 个文件
2025/05/24 00:01:07 logger.go:90: 正在处理第 1 个文件: JLJZY/
2025/05/24 00:01:07 logger.go:90: 解压进度: 0.00% (0/259)
2025/05/24 00:01:07 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:07 logger.go:90: 调用进度处理函数
2025/05/24 00:01:07 logger.go:90: 发送解压进度: 0.00% (0/259)
2025/05/24 00:01:07 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY
2025/05/24 00:01:07 logger.go:90: 正在处理第 2 个文件: JLJZY/JLJZY.exe
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY.exe
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY.exe
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY.exe
2025/05/24 00:01:07 logger.go:90: 复制了 650752 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 3 个文件: JLJZY/JLJZY_Data/
2025/05/24 00:01:07 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:07 logger.go:90: 正在处理第 4 个文件: JLJZY/JLJZY_Data/app.info
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/app.info
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/app.info
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/app.info
2025/05/24 00:01:07 logger.go:90: 复制了 28 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 5 个文件: JLJZY/JLJZY_Data/boot.config
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/boot.config
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/boot.config
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/boot.config
2025/05/24 00:01:07 logger.go:90: 复制了 62 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 6 个文件: JLJZY/JLJZY_Data/globalgamemanagers
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/globalgamemanagers
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/globalgamemanagers
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/globalgamemanagers
2025/05/24 00:01:07 logger.go:90: 复制了 247372 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 7 个文件: JLJZY/JLJZY_Data/globalgamemanagers.assets
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/globalgamemanagers.assets
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/globalgamemanagers.assets
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/globalgamemanagers.assets
2025/05/24 00:01:07 logger.go:90: 复制了 166104 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 8 个文件: JLJZY/JLJZY_Data/level0
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/level0
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/level0
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/level0
2025/05/24 00:01:07 logger.go:90: 复制了 81688 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 9 个文件: JLJZY/JLJZY_Data/level1
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/level1
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/level1
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/level1
2025/05/24 00:01:07 logger.go:90: 复制了 42548 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 10 个文件: JLJZY/JLJZY_Data/level2
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/level2
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/level2
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/level2
2025/05/24 00:01:07 logger.go:90: 复制了 7224 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 11 个文件: JLJZY/JLJZY_Data/level3
2025/05/24 00:01:07 logger.go:90: 解压进度: 3.86% (10/259)
2025/05/24 00:01:07 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:07 logger.go:90: 调用进度处理函数
2025/05/24 00:01:07 logger.go:90: 发送解压进度: 3.86% (10/259)
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/level3
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/level3
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/level3
2025/05/24 00:01:07 logger.go:90: 复制了 43340 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 12 个文件: JLJZY/JLJZY_Data/level4
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/level4
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/level4
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/level4
2025/05/24 00:01:07 logger.go:90: 复制了 44076 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 13 个文件: JLJZY/JLJZY_Data/level5
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/level5
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/level5
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/level5
2025/05/24 00:01:07 logger.go:90: 复制了 43828 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 14 个文件: JLJZY/JLJZY_Data/level6
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/level6
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/level6
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/level6
2025/05/24 00:01:07 logger.go:90: 复制了 3079328 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 15 个文件: JLJZY/JLJZY_Data/level7
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/level7
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/level7
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/level7
2025/05/24 00:01:07 logger.go:90: 复制了 2645136 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 16 个文件: JLJZY/JLJZY_Data/level7.resS
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/level7.resS
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/level7.resS
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/level7.resS
2025/05/24 00:01:07 logger.go:90: 复制了 2844640 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 17 个文件: JLJZY/JLJZY_Data/level8
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/level8
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/level8
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/level8
2025/05/24 00:01:07 logger.go:90: 复制了 1457344 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 18 个文件: JLJZY/JLJZY_Data/level8.resS
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/level8.resS
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/level8.resS
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/level8.resS
2025/05/24 00:01:07 logger.go:105: 再次调用进度处理函数发送最终进度
2025/05/24 00:01:07 logger.go:90: 复制了 3601944 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 19 个文件: JLJZY/JLJZY_Data/Managed/
2025/05/24 00:01:07 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 正在处理第 20 个文件: JLJZY/JLJZY_Data/Managed/Assembly-CSharp-firstpass.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/Assembly-CSharp-firstpass.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/Assembly-CSharp-firstpass.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/Assembly-CSharp-firstpass.dll
2025/05/24 00:01:07 logger.go:90: 复制了 109056 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 21 个文件: JLJZY/JLJZY_Data/Managed/Assembly-CSharp.dll
2025/05/24 00:01:07 logger.go:90: 解压进度: 7.72% (20/259)
2025/05/24 00:01:07 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:07 logger.go:90: 调用进度处理函数
2025/05/24 00:01:07 logger.go:90: 发送解压进度: 7.72% (20/259)
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/Assembly-CSharp.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/Assembly-CSharp.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/Assembly-CSharp.dll
2025/05/24 00:01:07 logger.go:90: 复制了 581632 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 22 个文件: JLJZY/JLJZY_Data/Managed/com.unity.multiplayer-hlapi.Runtime.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/com.unity.multiplayer-hlapi.Runtime.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/com.unity.multiplayer-hlapi.Runtime.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/com.unity.multiplayer-hlapi.Runtime.dll
2025/05/24 00:01:07 logger.go:90: 复制了 229888 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 23 个文件: JLJZY/JLJZY_Data/Managed/DemiLib.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/DemiLib.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/DemiLib.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/DemiLib.dll
2025/05/24 00:01:07 logger.go:90: 复制了 9728 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 24 个文件: JLJZY/JLJZY_Data/Managed/DOTween.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/DOTween.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/DOTween.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/DOTween.dll
2025/05/24 00:01:07 logger.go:90: 复制了 144384 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 25 个文件: JLJZY/JLJZY_Data/Managed/DOTweenPro.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/DOTweenPro.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/DOTweenPro.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/DOTweenPro.dll
2025/05/24 00:01:07 logger.go:90: 复制了 15872 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 26 个文件: JLJZY/JLJZY_Data/Managed/LitJson.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/LitJson.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/LitJson.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/LitJson.dll
2025/05/24 00:01:07 logger.go:90: 复制了 54272 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 27 个文件: JLJZY/JLJZY_Data/Managed/Mono.Security.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/Mono.Security.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/Mono.Security.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/Mono.Security.dll
2025/05/24 00:01:07 logger.go:90: 复制了 310272 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 28 个文件: JLJZY/JLJZY_Data/Managed/mscorlib.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/mscorlib.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/mscorlib.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/mscorlib.dll
2025/05/24 00:01:07 logger.go:90: 复制了 3905024 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 29 个文件: JLJZY/JLJZY_Data/Managed/netstandard.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/netstandard.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/netstandard.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/netstandard.dll
2025/05/24 00:01:07 logger.go:90: 复制了 84992 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 30 个文件: JLJZY/JLJZY_Data/Managed/Newtonsoft.Json.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/Newtonsoft.Json.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/Newtonsoft.Json.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/Newtonsoft.Json.dll
2025/05/24 00:01:07 logger.go:90: 复制了 254464 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 31 个文件: JLJZY/JLJZY_Data/Managed/System.ComponentModel.Composition.dll
2025/05/24 00:01:07 logger.go:90: 解压进度: 11.58% (30/259)
2025/05/24 00:01:07 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:07 logger.go:90: 调用进度处理函数
2025/05/24 00:01:07 logger.go:90: 发送解压进度: 11.58% (30/259)
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.ComponentModel.Composition.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.ComponentModel.Composition.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.ComponentModel.Composition.dll
2025/05/24 00:01:07 logger.go:90: 复制了 247808 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 32 个文件: JLJZY/JLJZY_Data/Managed/System.Configuration.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.Configuration.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.Configuration.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.Configuration.dll
2025/05/24 00:01:07 logger.go:90: 复制了 43008 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 33 个文件: JLJZY/JLJZY_Data/Managed/System.Core.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.Core.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.Core.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.Core.dll
2025/05/24 00:01:07 logger.go:90: 复制了 1057792 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 34 个文件: JLJZY/JLJZY_Data/Managed/System.Data.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.Data.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.Data.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.Data.dll
2025/05/24 00:01:07 logger.go:90: 复制了 1941504 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 35 个文件: JLJZY/JLJZY_Data/Managed/System.Diagnostics.StackTrace.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.Diagnostics.StackTrace.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.Diagnostics.StackTrace.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.Diagnostics.StackTrace.dll
2025/05/24 00:01:07 logger.go:90: 复制了 6656 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 36 个文件: JLJZY/JLJZY_Data/Managed/System.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.dll
2025/05/24 00:01:07 logger.go:90: 复制了 2141696 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 37 个文件: JLJZY/JLJZY_Data/Managed/System.Drawing.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.Drawing.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.Drawing.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.Drawing.dll
2025/05/24 00:01:07 logger.go:90: 复制了 184320 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 38 个文件: JLJZY/JLJZY_Data/Managed/System.EnterpriseServices.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.EnterpriseServices.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.EnterpriseServices.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.EnterpriseServices.dll
2025/05/24 00:01:07 logger.go:90: 复制了 33280 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 39 个文件: JLJZY/JLJZY_Data/Managed/System.Globalization.Extensions.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.Globalization.Extensions.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.Globalization.Extensions.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.Globalization.Extensions.dll
2025/05/24 00:01:07 logger.go:90: 复制了 6144 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 40 个文件: JLJZY/JLJZY_Data/Managed/System.IO.Compression.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.IO.Compression.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.IO.Compression.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.IO.Compression.dll
2025/05/24 00:01:07 logger.go:90: 复制了 98816 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 41 个文件: JLJZY/JLJZY_Data/Managed/System.IO.Compression.FileSystem.dll
2025/05/24 00:01:07 logger.go:90: 解压进度: 15.44% (40/259)
2025/05/24 00:01:07 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:07 logger.go:90: 调用进度处理函数
2025/05/24 00:01:07 logger.go:90: 发送解压进度: 15.44% (40/259)
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.IO.Compression.FileSystem.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.IO.Compression.FileSystem.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.IO.Compression.FileSystem.dll
2025/05/24 00:01:07 logger.go:90: 复制了 23040 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 42 个文件: JLJZY/JLJZY_Data/Managed/System.Net.Http.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.Net.Http.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.Net.Http.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.Net.Http.dll
2025/05/24 00:01:07 logger.go:90: 复制了 114688 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 43 个文件: JLJZY/JLJZY_Data/Managed/System.Numerics.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.Numerics.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.Numerics.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.Numerics.dll
2025/05/24 00:01:07 logger.go:90: 复制了 114176 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 44 个文件: JLJZY/JLJZY_Data/Managed/System.Runtime.Serialization.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.Runtime.Serialization.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.Runtime.Serialization.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.Runtime.Serialization.dll
2025/05/24 00:01:07 logger.go:90: 复制了 840704 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 45 个文件: JLJZY/JLJZY_Data/Managed/System.Runtime.Serialization.Xml.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.Runtime.Serialization.Xml.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.Runtime.Serialization.Xml.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.Runtime.Serialization.Xml.dll
2025/05/24 00:01:07 logger.go:90: 复制了 7168 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 46 个文件: JLJZY/JLJZY_Data/Managed/System.ServiceModel.Internals.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.ServiceModel.Internals.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.ServiceModel.Internals.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.ServiceModel.Internals.dll
2025/05/24 00:01:07 logger.go:90: 复制了 218112 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 47 个文件: JLJZY/JLJZY_Data/Managed/System.Transactions.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.Transactions.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.Transactions.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.Transactions.dll
2025/05/24 00:01:07 logger.go:90: 复制了 33280 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 48 个文件: JLJZY/JLJZY_Data/Managed/System.Xaml.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.Xaml.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.Xaml.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.Xaml.dll
2025/05/24 00:01:07 logger.go:90: 复制了 44032 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 49 个文件: JLJZY/JLJZY_Data/Managed/System.Xml.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.Xml.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.Xml.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.Xml.dll
2025/05/24 00:01:07 logger.go:90: 复制了 2414592 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 50 个文件: JLJZY/JLJZY_Data/Managed/System.Xml.Linq.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.Xml.Linq.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.Xml.Linq.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.Xml.Linq.dll
2025/05/24 00:01:07 logger.go:90: 复制了 119296 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 51 个文件: JLJZY/JLJZY_Data/Managed/System.Xml.XPath.XDocument.dll
2025/05/24 00:01:07 logger.go:90: 解压进度: 19.30% (50/259)
2025/05/24 00:01:07 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:07 logger.go:90: 调用进度处理函数
2025/05/24 00:01:07 logger.go:90: 发送解压进度: 19.30% (50/259)
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/System.Xml.XPath.XDocument.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/System.Xml.XPath.XDocument.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/System.Xml.XPath.XDocument.dll
2025/05/24 00:01:07 logger.go:90: 复制了 5120 字节
2025/05/24 00:01:07 logger.go:90: 处理了50个文件，暂停一下
2025/05/24 00:01:07 logger.go:90: 正在处理第 52 个文件: JLJZY/JLJZY_Data/Managed/Unity.Analytics.DataPrivacy.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/Unity.Analytics.DataPrivacy.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/Unity.Analytics.DataPrivacy.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/Unity.Analytics.DataPrivacy.dll
2025/05/24 00:01:07 logger.go:90: 复制了 7680 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 53 个文件: JLJZY/JLJZY_Data/Managed/Unity.Postprocessing.Runtime.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/Unity.Postprocessing.Runtime.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/Unity.Postprocessing.Runtime.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/Unity.Postprocessing.Runtime.dll
2025/05/24 00:01:07 logger.go:90: 复制了 148480 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 54 个文件: JLJZY/JLJZY_Data/Managed/Unity.ProGrids.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/Unity.ProGrids.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/Unity.ProGrids.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/Unity.ProGrids.dll
2025/05/24 00:01:07 logger.go:90: 复制了 4096 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 55 个文件: JLJZY/JLJZY_Data/Managed/Unity.TextMeshPro.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/Unity.TextMeshPro.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/Unity.TextMeshPro.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/Unity.TextMeshPro.dll
2025/05/24 00:01:07 logger.go:90: 复制了 331776 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 56 个文件: JLJZY/JLJZY_Data/Managed/Unity.Timeline.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/Unity.Timeline.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/Unity.Timeline.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/Unity.Timeline.dll
2025/05/24 00:01:07 logger.go:90: 复制了 108544 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 57 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.AccessibilityModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.AccessibilityModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.AccessibilityModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.AccessibilityModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 12288 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 58 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.Advertisements.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.Advertisements.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.Advertisements.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.Advertisements.dll
2025/05/24 00:01:07 logger.go:90: 复制了 31232 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 59 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.AIModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.AIModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.AIModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.AIModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 44544 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 60 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.AndroidJNIModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.AndroidJNIModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.AndroidJNIModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.AndroidJNIModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 57344 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 61 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.AnimationModule.dll
2025/05/24 00:01:07 logger.go:90: 解压进度: 23.16% (60/259)
2025/05/24 00:01:07 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:07 logger.go:90: 调用进度处理函数
2025/05/24 00:01:07 logger.go:90: 发送解压进度: 23.16% (60/259)
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.AnimationModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.AnimationModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.AnimationModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 145920 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 62 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.ARModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.ARModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.ARModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.ARModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 12800 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 63 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.AssetBundleModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.AssetBundleModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.AssetBundleModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.AssetBundleModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 21504 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 64 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.AudioModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.AudioModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.AudioModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.AudioModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 57344 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 65 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.ClothModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.ClothModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.ClothModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.ClothModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 15872 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 66 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.ClusterInputModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.ClusterInputModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.ClusterInputModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.ClusterInputModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 10240 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 67 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.ClusterRendererModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.ClusterRendererModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.ClusterRendererModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.ClusterRendererModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 9216 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 68 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.CoreModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.CoreModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.CoreModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.CoreModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 1005568 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 69 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.CrashReportingModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.CrashReportingModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.CrashReportingModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.CrashReportingModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 9728 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 70 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.DirectorModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.DirectorModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.DirectorModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.DirectorModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 13312 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 71 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.dll
2025/05/24 00:01:07 logger.go:90: 解压进度: 27.02% (70/259)
2025/05/24 00:01:07 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:07 logger.go:90: 调用进度处理函数
2025/05/24 00:01:07 logger.go:90: 发送解压进度: 27.02% (70/259)
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.dll
2025/05/24 00:01:07 logger.go:90: 复制了 84992 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 72 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.DSPGraphModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.DSPGraphModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.DSPGraphModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.DSPGraphModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 17920 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 73 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.GameCenterModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.GameCenterModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.GameCenterModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.GameCenterModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 27136 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 74 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.GridModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.GridModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.GridModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.GridModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 13824 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 75 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.HotReloadModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.HotReloadModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.HotReloadModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.HotReloadModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 8704 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 76 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.ImageConversionModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.ImageConversionModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.ImageConversionModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.ImageConversionModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 12800 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 77 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.IMGUIModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.IMGUIModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.IMGUIModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.IMGUIModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 156160 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 78 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.InputLegacyModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.InputLegacyModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.InputLegacyModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.InputLegacyModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 25600 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 79 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.InputModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.InputModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.InputModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.InputModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 12288 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 80 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.JSONSerializeModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.JSONSerializeModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.JSONSerializeModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.JSONSerializeModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 10752 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 81 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.LocalizationModule.dll
2025/05/24 00:01:07 logger.go:90: 解压进度: 30.88% (80/259)
2025/05/24 00:01:07 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:07 logger.go:90: 调用进度处理函数
2025/05/24 00:01:07 logger.go:90: 发送解压进度: 30.88% (80/259)
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.LocalizationModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.LocalizationModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.LocalizationModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 9728 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 82 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.Monetization.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.Monetization.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.Monetization.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.Monetization.dll
2025/05/24 00:01:07 logger.go:90: 复制了 28672 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 83 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.ParticleSystemModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.ParticleSystemModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.ParticleSystemModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.ParticleSystemModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 135168 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 84 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.PerformanceReportingModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.PerformanceReportingModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.PerformanceReportingModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.PerformanceReportingModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 9216 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 85 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.Physics2DModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.Physics2DModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.Physics2DModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.Physics2DModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 105984 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 86 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.PhysicsModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.PhysicsModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.PhysicsModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.PhysicsModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 87040 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 87 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.ProfilerModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.ProfilerModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.ProfilerModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.ProfilerModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 8704 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 88 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.Purchasing.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.Purchasing.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.Purchasing.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.Purchasing.dll
2025/05/24 00:01:07 logger.go:90: 复制了 27648 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 89 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.ScreenCaptureModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.ScreenCaptureModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.ScreenCaptureModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.ScreenCaptureModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 9728 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 90 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.SharedInternalsModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.SharedInternalsModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.SharedInternalsModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.SharedInternalsModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 19456 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 91 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.SpatialTracking.dll
2025/05/24 00:01:07 logger.go:90: 解压进度: 34.74% (90/259)
2025/05/24 00:01:07 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:07 logger.go:90: 调用进度处理函数
2025/05/24 00:01:07 logger.go:90: 发送解压进度: 34.74% (90/259)
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.SpatialTracking.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.SpatialTracking.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.SpatialTracking.dll
2025/05/24 00:01:07 logger.go:90: 复制了 10240 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 92 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.SpriteMaskModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.SpriteMaskModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.SpriteMaskModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.SpriteMaskModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 10240 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 93 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.SpriteShapeModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.SpriteShapeModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.SpriteShapeModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.SpriteShapeModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 13824 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 94 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.StreamingModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.StreamingModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.StreamingModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.StreamingModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 9728 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 95 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.SubstanceModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.SubstanceModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.SubstanceModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.SubstanceModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 13312 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 96 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.SubsystemsModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.SubsystemsModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.SubsystemsModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.SubsystemsModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 16896 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 97 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.TerrainModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.TerrainModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.TerrainModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.TerrainModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 80384 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 98 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.TerrainPhysicsModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.TerrainPhysicsModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.TerrainPhysicsModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.TerrainPhysicsModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 9728 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 99 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.TextCoreModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.TextCoreModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.TextCoreModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.TextCoreModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 183808 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 100 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.TextRenderingModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.TextRenderingModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.TextRenderingModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.TextRenderingModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 27648 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 101 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.TilemapModule.dll
2025/05/24 00:01:07 logger.go:90: 解压进度: 38.61% (100/259)
2025/05/24 00:01:07 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:07 logger.go:90: 调用进度处理函数
2025/05/24 00:01:07 logger.go:90: 发送解压进度: 38.61% (100/259)
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.TilemapModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.TilemapModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.TilemapModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 25088 字节
2025/05/24 00:01:07 logger.go:90: 处理了50个文件，暂停一下
2025/05/24 00:01:07 logger.go:90: 正在处理第 102 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.TLSModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.TLSModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.TLSModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.TLSModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 8704 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 103 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UI.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.UI.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UI.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.UI.dll
2025/05/24 00:01:07 logger.go:90: 复制了 225792 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 104 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UIElementsModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.UIElementsModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UIElementsModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.UIElementsModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 725504 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 105 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UIModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.UIModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UIModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.UIModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 23040 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 106 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UmbraModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.UmbraModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UmbraModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.UmbraModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 8704 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 107 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UNETModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.UNETModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UNETModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.UNETModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 77312 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 108 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityAnalyticsModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.UnityAnalyticsModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityAnalyticsModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityAnalyticsModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 32768 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 109 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityConnectModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.UnityConnectModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityConnectModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityConnectModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 10752 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 110 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityTestProtocolModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.UnityTestProtocolModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityTestProtocolModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityTestProtocolModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 8704 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 111 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll
2025/05/24 00:01:07 logger.go:90: 解压进度: 42.47% (110/259)
2025/05/24 00:01:07 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:07 logger.go:90: 调用进度处理函数
2025/05/24 00:01:07 logger.go:90: 发送解压进度: 42.47% (110/259)
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 11776 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 112 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestAudioModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestAudioModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestAudioModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestAudioModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 11264 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 113 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 43008 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 114 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestTextureModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestTextureModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestTextureModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestTextureModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 10752 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 115 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestWWWModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestWWWModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestWWWModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.UnityWebRequestWWWModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 20480 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 116 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.VehiclesModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.VehiclesModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.VehiclesModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.VehiclesModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 12800 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 117 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.VFXModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.VFXModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.VFXModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.VFXModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 38912 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 118 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.VideoModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.VideoModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.VideoModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.VideoModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 28672 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 119 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.VRModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.VRModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.VRModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.VRModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 34816 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 120 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.WindModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.WindModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.WindModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.WindModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 9728 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 121 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.XR.LegacyInputHelpers.dll
2025/05/24 00:01:07 logger.go:90: 解压进度: 46.33% (120/259)
2025/05/24 00:01:07 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:07 logger.go:90: 调用进度处理函数
2025/05/24 00:01:07 logger.go:90: 发送解压进度: 46.33% (120/259)
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.XR.LegacyInputHelpers.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.XR.LegacyInputHelpers.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.XR.LegacyInputHelpers.dll
2025/05/24 00:01:07 logger.go:90: 复制了 20992 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 122 个文件: JLJZY/JLJZY_Data/Managed/UnityEngine.XRModule.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/UnityEngine.XRModule.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/UnityEngine.XRModule.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/UnityEngine.XRModule.dll
2025/05/24 00:01:07 logger.go:90: 复制了 54272 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 123 个文件: JLJZY/JLJZY_Data/Managed/WindowsBase.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/WindowsBase.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/WindowsBase.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/WindowsBase.dll
2025/05/24 00:01:07 logger.go:90: 复制了 161280 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 124 个文件: JLJZY/JLJZY_Data/Managed/ZFBrowser.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Managed/ZFBrowser.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Managed/ZFBrowser.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Managed/ZFBrowser.dll
2025/05/24 00:01:07 logger.go:90: 复制了 344576 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 125 个文件: JLJZY/JLJZY_Data/Plugins/
2025/05/24 00:01:07 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins
2025/05/24 00:01:07 logger.go:90: 正在处理第 126 个文件: JLJZY/JLJZY_Data/Plugins/cef.pak
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/cef.pak
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/cef.pak
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/cef.pak
2025/05/24 00:01:07 logger.go:90: 复制了 3676692 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 127 个文件: JLJZY/JLJZY_Data/Plugins/cef_100_percent.pak
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/cef_100_percent.pak
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/cef_100_percent.pak
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/cef_100_percent.pak
2025/05/24 00:01:07 logger.go:90: 复制了 742152 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 128 个文件: JLJZY/JLJZY_Data/Plugins/cef_200_percent.pak
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/cef_200_percent.pak
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/cef_200_percent.pak
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/cef_200_percent.pak
2025/05/24 00:01:07 logger.go:90: 复制了 872802 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 129 个文件: JLJZY/JLJZY_Data/Plugins/cef_extensions.pak
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/cef_extensions.pak
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/cef_extensions.pak
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/cef_extensions.pak
2025/05/24 00:01:07 logger.go:90: 复制了 1833343 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 130 个文件: JLJZY/JLJZY_Data/Plugins/chrome_elf.dll
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/chrome_elf.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/chrome_elf.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/chrome_elf.dll
2025/05/24 00:01:07 logger.go:90: 复制了 814592 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 131 个文件: JLJZY/JLJZY_Data/Plugins/d3dcompiler_47.dll
2025/05/24 00:01:07 logger.go:90: 解压进度: 50.19% (130/259)
2025/05/24 00:01:07 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:07 logger.go:90: 调用进度处理函数
2025/05/24 00:01:07 logger.go:90: 发送解压进度: 50.19% (130/259)
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/d3dcompiler_47.dll
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/d3dcompiler_47.dll
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/d3dcompiler_47.dll
2025/05/24 00:01:07 logger.go:90: 复制了 4346120 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 132 个文件: JLJZY/JLJZY_Data/Plugins/devtools_resources.pak
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/devtools_resources.pak
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/devtools_resources.pak
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/devtools_resources.pak
2025/05/24 00:01:07 logger.go:90: 复制了 6014631 字节
2025/05/24 00:01:07 logger.go:90: 正在处理第 133 个文件: JLJZY/JLJZY_Data/Plugins/icudtl.dat
2025/05/24 00:01:07 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins
2025/05/24 00:01:07 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/icudtl.dat
2025/05/24 00:01:07 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/icudtl.dat
2025/05/24 00:01:07 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/icudtl.dat
2025/05/24 00:01:08 logger.go:90: 复制了 10326064 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 134 个文件: JLJZY/JLJZY_Data/Plugins/libEGL.dll
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/libEGL.dll
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/libEGL.dll
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/libEGL.dll
2025/05/24 00:01:08 logger.go:90: 复制了 131072 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 135 个文件: JLJZY/JLJZY_Data/Plugins/libGLESv2.dll
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/libGLESv2.dll
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/libGLESv2.dll
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/libGLESv2.dll
2025/05/24 00:01:08 logger.go:90: 复制了 5516288 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 136 个文件: JLJZY/JLJZY_Data/Plugins/locales/
2025/05/24 00:01:08 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 正在处理第 137 个文件: JLJZY/JLJZY_Data/Plugins/locales/am.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/am.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/am.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/am.pak
2025/05/24 00:01:08 logger.go:90: 复制了 314448 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 138 个文件: JLJZY/JLJZY_Data/Plugins/locales/ar.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/ar.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/ar.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/ar.pak
2025/05/24 00:01:08 logger.go:90: 复制了 315053 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 139 个文件: JLJZY/JLJZY_Data/Plugins/locales/bg.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/bg.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/bg.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/bg.pak
2025/05/24 00:01:08 logger.go:90: 复制了 362060 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 140 个文件: JLJZY/JLJZY_Data/Plugins/locales/bn.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/bn.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/bn.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/bn.pak
2025/05/24 00:01:08 logger.go:90: 复制了 467103 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 141 个文件: JLJZY/JLJZY_Data/Plugins/locales/ca.pak
2025/05/24 00:01:08 logger.go:90: 解压进度: 54.05% (140/259)
2025/05/24 00:01:08 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:08 logger.go:90: 调用进度处理函数
2025/05/24 00:01:08 logger.go:90: 发送解压进度: 54.05% (140/259)
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/ca.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/ca.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/ca.pak
2025/05/24 00:01:08 logger.go:90: 复制了 224643 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 142 个文件: JLJZY/JLJZY_Data/Plugins/locales/cs.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/cs.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/cs.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/cs.pak
2025/05/24 00:01:08 logger.go:90: 复制了 228150 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 143 个文件: JLJZY/JLJZY_Data/Plugins/locales/da.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/da.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/da.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/da.pak
2025/05/24 00:01:08 logger.go:90: 复制了 204754 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 144 个文件: JLJZY/JLJZY_Data/Plugins/locales/de.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/de.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/de.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/de.pak
2025/05/24 00:01:08 logger.go:90: 复制了 223214 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 145 个文件: JLJZY/JLJZY_Data/Plugins/locales/el.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/el.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/el.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/el.pak
2025/05/24 00:01:08 logger.go:90: 复制了 396704 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 146 个文件: JLJZY/JLJZY_Data/Plugins/locales/en-GB.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/en-GB.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/en-GB.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/en-GB.pak
2025/05/24 00:01:08 logger.go:90: 复制了 183659 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 147 个文件: JLJZY/JLJZY_Data/Plugins/locales/en-US.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/en-US.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/en-US.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/en-US.pak
2025/05/24 00:01:08 logger.go:90: 复制了 185090 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 148 个文件: JLJZY/JLJZY_Data/Plugins/locales/es-419.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/es-419.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/es-419.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/es-419.pak
2025/05/24 00:01:08 logger.go:90: 复制了 220764 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 149 个文件: JLJZY/JLJZY_Data/Plugins/locales/es.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/es.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/es.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/es.pak
2025/05/24 00:01:08 logger.go:90: 复制了 224140 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 150 个文件: JLJZY/JLJZY_Data/Plugins/locales/et.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/et.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/et.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/et.pak
2025/05/24 00:01:08 logger.go:90: 复制了 199921 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 151 个文件: JLJZY/JLJZY_Data/Plugins/locales/fa.pak
2025/05/24 00:01:08 logger.go:90: 解压进度: 57.91% (150/259)
2025/05/24 00:01:08 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:08 logger.go:90: 调用进度处理函数
2025/05/24 00:01:08 logger.go:90: 发送解压进度: 57.91% (150/259)
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/fa.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/fa.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/fa.pak
2025/05/24 00:01:08 logger.go:90: 复制了 317594 字节
2025/05/24 00:01:08 logger.go:90: 处理了50个文件，暂停一下
2025/05/24 00:01:08 logger.go:90: 正在处理第 152 个文件: JLJZY/JLJZY_Data/Plugins/locales/fi.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/fi.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/fi.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/fi.pak
2025/05/24 00:01:08 logger.go:90: 复制了 206125 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 153 个文件: JLJZY/JLJZY_Data/Plugins/locales/fil.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/fil.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/fil.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/fil.pak
2025/05/24 00:01:08 logger.go:90: 复制了 226979 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 154 个文件: JLJZY/JLJZY_Data/Plugins/locales/fr.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/fr.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/fr.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/fr.pak
2025/05/24 00:01:08 logger.go:90: 复制了 239622 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 155 个文件: JLJZY/JLJZY_Data/Plugins/locales/gu.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/gu.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/gu.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/gu.pak
2025/05/24 00:01:08 logger.go:90: 复制了 444053 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 156 个文件: JLJZY/JLJZY_Data/Plugins/locales/he.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/he.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/he.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/he.pak
2025/05/24 00:01:08 logger.go:90: 复制了 268510 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 157 个文件: JLJZY/JLJZY_Data/Plugins/locales/hi.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/hi.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/hi.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/hi.pak
2025/05/24 00:01:08 logger.go:90: 复制了 456292 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 158 个文件: JLJZY/JLJZY_Data/Plugins/locales/hr.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/hr.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/hr.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/hr.pak
2025/05/24 00:01:08 logger.go:90: 复制了 216276 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 159 个文件: JLJZY/JLJZY_Data/Plugins/locales/hu.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/hu.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/hu.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/hu.pak
2025/05/24 00:01:08 logger.go:90: 复制了 235681 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 160 个文件: JLJZY/JLJZY_Data/Plugins/locales/id.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/id.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/id.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/id.pak
2025/05/24 00:01:08 logger.go:90: 复制了 198224 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 161 个文件: JLJZY/JLJZY_Data/Plugins/locales/it.pak
2025/05/24 00:01:08 logger.go:90: 解压进度: 61.77% (160/259)
2025/05/24 00:01:08 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:08 logger.go:90: 调用进度处理函数
2025/05/24 00:01:08 logger.go:90: 发送解压进度: 61.77% (160/259)
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/it.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/it.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/it.pak
2025/05/24 00:01:08 logger.go:90: 复制了 216353 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 162 个文件: JLJZY/JLJZY_Data/Plugins/locales/ja.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/ja.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/ja.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/ja.pak
2025/05/24 00:01:08 logger.go:90: 复制了 268987 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 163 个文件: JLJZY/JLJZY_Data/Plugins/locales/kn.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/kn.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/kn.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/kn.pak
2025/05/24 00:01:08 logger.go:90: 复制了 517629 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 164 个文件: JLJZY/JLJZY_Data/Plugins/locales/ko.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/ko.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/ko.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/ko.pak
2025/05/24 00:01:08 logger.go:90: 复制了 225809 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 165 个文件: JLJZY/JLJZY_Data/Plugins/locales/lt.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/lt.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/lt.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/lt.pak
2025/05/24 00:01:08 logger.go:90: 复制了 232209 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 166 个文件: JLJZY/JLJZY_Data/Plugins/locales/lv.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/lv.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/lv.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/lv.pak
2025/05/24 00:01:08 logger.go:90: 复制了 231033 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 167 个文件: JLJZY/JLJZY_Data/Plugins/locales/ml.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/ml.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/ml.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/ml.pak
2025/05/24 00:01:08 logger.go:90: 复制了 556723 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 168 个文件: JLJZY/JLJZY_Data/Plugins/locales/mr.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/mr.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/mr.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/mr.pak
2025/05/24 00:01:08 logger.go:90: 复制了 450943 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 169 个文件: JLJZY/JLJZY_Data/Plugins/locales/ms.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/ms.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/ms.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/ms.pak
2025/05/24 00:01:08 logger.go:90: 复制了 204927 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 170 个文件: JLJZY/JLJZY_Data/Plugins/locales/nb.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/nb.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/nb.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/nb.pak
2025/05/24 00:01:08 logger.go:90: 复制了 201815 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 171 个文件: JLJZY/JLJZY_Data/Plugins/locales/nl.pak
2025/05/24 00:01:08 logger.go:90: 解压进度: 65.63% (170/259)
2025/05/24 00:01:08 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:08 logger.go:90: 调用进度处理函数
2025/05/24 00:01:08 logger.go:90: 发送解压进度: 65.63% (170/259)
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/nl.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/nl.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/nl.pak
2025/05/24 00:01:08 logger.go:90: 复制了 210980 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 172 个文件: JLJZY/JLJZY_Data/Plugins/locales/pl.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/pl.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/pl.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/pl.pak
2025/05/24 00:01:08 logger.go:90: 复制了 224402 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 173 个文件: JLJZY/JLJZY_Data/Plugins/locales/pt-BR.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/pt-BR.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/pt-BR.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/pt-BR.pak
2025/05/24 00:01:08 logger.go:90: 复制了 217734 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 174 个文件: JLJZY/JLJZY_Data/Plugins/locales/pt-PT.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/pt-PT.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/pt-PT.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/pt-PT.pak
2025/05/24 00:01:08 logger.go:90: 复制了 219947 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 175 个文件: JLJZY/JLJZY_Data/Plugins/locales/ro.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/ro.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/ro.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/ro.pak
2025/05/24 00:01:08 logger.go:90: 复制了 225389 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 176 个文件: JLJZY/JLJZY_Data/Plugins/locales/ru.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/ru.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/ru.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/ru.pak
2025/05/24 00:01:08 logger.go:90: 复制了 350023 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 177 个文件: JLJZY/JLJZY_Data/Plugins/locales/sk.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/sk.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/sk.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/sk.pak
2025/05/24 00:01:08 logger.go:90: 复制了 231395 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 178 个文件: JLJZY/JLJZY_Data/Plugins/locales/sl.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/sl.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/sl.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/sl.pak
2025/05/24 00:01:08 logger.go:90: 复制了 217553 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 179 个文件: JLJZY/JLJZY_Data/Plugins/locales/sr.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/sr.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/sr.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/sr.pak
2025/05/24 00:01:08 logger.go:90: 复制了 338848 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 180 个文件: JLJZY/JLJZY_Data/Plugins/locales/sv.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/sv.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/sv.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/sv.pak
2025/05/24 00:01:08 logger.go:90: 复制了 203076 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 181 个文件: JLJZY/JLJZY_Data/Plugins/locales/sw.pak
2025/05/24 00:01:08 logger.go:90: 解压进度: 69.49% (180/259)
2025/05/24 00:01:08 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:08 logger.go:90: 调用进度处理函数
2025/05/24 00:01:08 logger.go:90: 发送解压进度: 69.49% (180/259)
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/sw.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/sw.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/sw.pak
2025/05/24 00:01:08 logger.go:90: 复制了 207909 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 182 个文件: JLJZY/JLJZY_Data/Plugins/locales/ta.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/ta.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/ta.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/ta.pak
2025/05/24 00:01:08 logger.go:90: 复制了 525312 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 183 个文件: JLJZY/JLJZY_Data/Plugins/locales/te.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/te.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/te.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/te.pak
2025/05/24 00:01:08 logger.go:90: 复制了 498318 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 184 个文件: JLJZY/JLJZY_Data/Plugins/locales/th.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/th.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/th.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/th.pak
2025/05/24 00:01:08 logger.go:90: 复制了 423628 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 185 个文件: JLJZY/JLJZY_Data/Plugins/locales/tr.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/tr.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/tr.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/tr.pak
2025/05/24 00:01:08 logger.go:90: 复制了 217133 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 186 个文件: JLJZY/JLJZY_Data/Plugins/locales/uk.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/uk.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/uk.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/uk.pak
2025/05/24 00:01:08 logger.go:90: 复制了 354530 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 187 个文件: JLJZY/JLJZY_Data/Plugins/locales/vi.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/vi.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/vi.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/vi.pak
2025/05/24 00:01:08 logger.go:90: 复制了 251195 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 188 个文件: JLJZY/JLJZY_Data/Plugins/locales/zh-CN.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/zh-CN.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/zh-CN.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/zh-CN.pak
2025/05/24 00:01:08 logger.go:90: 复制了 187627 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 189 个文件: JLJZY/JLJZY_Data/Plugins/locales/zh-TW.pak
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/locales/zh-TW.pak
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/locales/zh-TW.pak
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/locales/zh-TW.pak
2025/05/24 00:01:08 logger.go:90: 复制了 187941 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 190 个文件: JLJZY/JLJZY_Data/Plugins/natives_blob.bin
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/natives_blob.bin
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/natives_blob.bin
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/natives_blob.bin
2025/05/24 00:01:08 logger.go:90: 复制了 83217 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 191 个文件: JLJZY/JLJZY_Data/Plugins/snapshot_blob.bin
2025/05/24 00:01:08 logger.go:90: 解压进度: 73.35% (190/259)
2025/05/24 00:01:08 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:08 logger.go:90: 调用进度处理函数
2025/05/24 00:01:08 logger.go:90: 发送解压进度: 73.35% (190/259)
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/snapshot_blob.bin
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/snapshot_blob.bin
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/snapshot_blob.bin
2025/05/24 00:01:08 logger.go:90: 复制了 292784 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 192 个文件: JLJZY/JLJZY_Data/Plugins/ThirdPartyNotices.txt
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/ThirdPartyNotices.txt
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/ThirdPartyNotices.txt
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/ThirdPartyNotices.txt
2025/05/24 00:01:08 logger.go:90: 复制了 886860 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 193 个文件: JLJZY/JLJZY_Data/Plugins/v8_context_snapshot.bin
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/v8_context_snapshot.bin
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/v8_context_snapshot.bin
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/v8_context_snapshot.bin
2025/05/24 00:01:08 logger.go:90: 复制了 703424 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 194 个文件: JLJZY/JLJZY_Data/Plugins/x86_64/
2025/05/24 00:01:08 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/x86_64
2025/05/24 00:01:08 logger.go:90: 正在处理第 195 个文件: JLJZY/JLJZY_Data/Plugins/ZFGameBrowser.exe
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/ZFGameBrowser.exe
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/ZFGameBrowser.exe
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/ZFGameBrowser.exe
2025/05/24 00:01:08 logger.go:90: 复制了 1061376 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 196 个文件: JLJZY/JLJZY_Data/Plugins/ZFProxyWeb.dll
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/ZFProxyWeb.dll
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/ZFProxyWeb.dll
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/ZFProxyWeb.dll
2025/05/24 00:01:08 logger.go:90: 复制了 562688 字节
2025/05/24 00:01:08 logger.go:90: 正在处理第 197 个文件: JLJZY/JLJZY_Data/Plugins/zf_cef.dll
2025/05/24 00:01:08 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins
2025/05/24 00:01:08 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Plugins/zf_cef.dll
2025/05/24 00:01:08 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Plugins/zf_cef.dll
2025/05/24 00:01:08 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Plugins/zf_cef.dll
2025/05/24 00:01:09 logger.go:90: 复制了 110340608 字节
2025/05/24 00:01:09 logger.go:90: 正在处理第 198 个文件: JLJZY/JLJZY_Data/Resources/
2025/05/24 00:01:09 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Resources
2025/05/24 00:01:09 logger.go:90: 正在处理第 199 个文件: JLJZY/JLJZY_Data/Resources/browser_assets
2025/05/24 00:01:09 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Resources
2025/05/24 00:01:09 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Resources/browser_assets
2025/05/24 00:01:09 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Resources/browser_assets
2025/05/24 00:01:09 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Resources/browser_assets
2025/05/24 00:01:09 logger.go:90: 复制了 14 字节
2025/05/24 00:01:09 logger.go:90: 正在处理第 200 个文件: JLJZY/JLJZY_Data/Resources/unity default resources
2025/05/24 00:01:09 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Resources
2025/05/24 00:01:09 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Resources/unity default resources
2025/05/24 00:01:09 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Resources/unity default resources
2025/05/24 00:01:09 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Resources/unity default resources
2025/05/24 00:01:09 logger.go:90: 复制了 3837020 字节
2025/05/24 00:01:09 logger.go:90: 正在处理第 201 个文件: JLJZY/JLJZY_Data/Resources/unity_builtin_extra
2025/05/24 00:01:09 logger.go:90: 解压进度: 77.22% (200/259)
2025/05/24 00:01:09 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:09 logger.go:90: 调用进度处理函数
2025/05/24 00:01:09 logger.go:90: 发送解压进度: 77.22% (200/259)
2025/05/24 00:01:09 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Resources
2025/05/24 00:01:09 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/Resources/unity_builtin_extra
2025/05/24 00:01:09 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/Resources/unity_builtin_extra
2025/05/24 00:01:09 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/Resources/unity_builtin_extra
2025/05/24 00:01:09 logger.go:90: 复制了 623672 字节
2025/05/24 00:01:09 logger.go:90: 处理了50个文件，暂停一下
2025/05/24 00:01:09 logger.go:90: 正在处理第 202 个文件: JLJZY/JLJZY_Data/resources.assets
2025/05/24 00:01:09 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:09 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/resources.assets
2025/05/24 00:01:09 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/resources.assets
2025/05/24 00:01:09 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/resources.assets
2025/05/24 00:01:09 logger.go:90: 复制了 15700656 字节
2025/05/24 00:01:09 logger.go:90: 正在处理第 203 个文件: JLJZY/JLJZY_Data/resources.assets.resS
2025/05/24 00:01:09 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:09 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/resources.assets.resS
2025/05/24 00:01:09 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/resources.assets.resS
2025/05/24 00:01:09 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/resources.assets.resS
2025/05/24 00:01:10 logger.go:90: 复制了 183308468 字节
2025/05/24 00:01:10 logger.go:90: 正在处理第 204 个文件: JLJZY/JLJZY_Data/resources.resource
2025/05/24 00:01:10 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:10 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/resources.resource
2025/05/24 00:01:10 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/resources.resource
2025/05/24 00:01:10 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/resources.resource
2025/05/24 00:01:12 logger.go:90: 复制了 199107825 字节
2025/05/24 00:01:12 logger.go:90: 正在处理第 205 个文件: JLJZY/JLJZY_Data/sharedassets0.assets
2025/05/24 00:01:12 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:12 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/sharedassets0.assets
2025/05/24 00:01:12 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/sharedassets0.assets
2025/05/24 00:01:12 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/sharedassets0.assets
2025/05/24 00:01:13 logger.go:90: 复制了 15202738 字节
2025/05/24 00:01:13 logger.go:90: 正在处理第 206 个文件: JLJZY/JLJZY_Data/sharedassets0.assets.resS
2025/05/24 00:01:13 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:13 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/sharedassets0.assets.resS
2025/05/24 00:01:13 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/sharedassets0.assets.resS
2025/05/24 00:01:13 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/sharedassets0.assets.resS
2025/05/24 00:01:13 logger.go:90: 复制了 29061368 字节
2025/05/24 00:01:13 logger.go:90: 正在处理第 207 个文件: JLJZY/JLJZY_Data/sharedassets0.resource
2025/05/24 00:01:13 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:13 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/sharedassets0.resource
2025/05/24 00:01:13 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/sharedassets0.resource
2025/05/24 00:01:13 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/sharedassets0.resource
2025/05/24 00:01:13 logger.go:90: 复制了 6557488 字节
2025/05/24 00:01:13 logger.go:90: 正在处理第 208 个文件: JLJZY/JLJZY_Data/sharedassets1.assets
2025/05/24 00:01:13 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:13 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/sharedassets1.assets
2025/05/24 00:01:13 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/sharedassets1.assets
2025/05/24 00:01:13 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/sharedassets1.assets
2025/05/24 00:01:13 logger.go:90: 复制了 40552 字节
2025/05/24 00:01:13 logger.go:90: 正在处理第 209 个文件: JLJZY/JLJZY_Data/sharedassets1.assets.resS
2025/05/24 00:01:13 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:13 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/sharedassets1.assets.resS
2025/05/24 00:01:13 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/sharedassets1.assets.resS
2025/05/24 00:01:13 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/sharedassets1.assets.resS
2025/05/24 00:01:13 logger.go:90: 复制了 12412072 字节
2025/05/24 00:01:13 logger.go:90: 正在处理第 210 个文件: JLJZY/JLJZY_Data/sharedassets2.assets
2025/05/24 00:01:13 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:13 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/sharedassets2.assets
2025/05/24 00:01:13 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/sharedassets2.assets
2025/05/24 00:01:13 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/sharedassets2.assets
2025/05/24 00:01:13 logger.go:90: 复制了 7560 字节
2025/05/24 00:01:13 logger.go:90: 正在处理第 211 个文件: JLJZY/JLJZY_Data/sharedassets2.assets.resS
2025/05/24 00:01:13 logger.go:90: 解压进度: 81.08% (210/259)
2025/05/24 00:01:13 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:13 logger.go:90: 调用进度处理函数
2025/05/24 00:01:13 logger.go:90: 发送解压进度: 81.08% (210/259)
2025/05/24 00:01:13 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:13 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/sharedassets2.assets.resS
2025/05/24 00:01:13 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/sharedassets2.assets.resS
2025/05/24 00:01:13 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/sharedassets2.assets.resS
2025/05/24 00:01:13 logger.go:90: 复制了 61920 字节
2025/05/24 00:01:13 logger.go:90: 正在处理第 212 个文件: JLJZY/JLJZY_Data/sharedassets3.assets
2025/05/24 00:01:13 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:13 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/sharedassets3.assets
2025/05/24 00:01:13 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/sharedassets3.assets
2025/05/24 00:01:13 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/sharedassets3.assets
2025/05/24 00:01:13 logger.go:90: 复制了 6029 字节
2025/05/24 00:01:13 logger.go:90: 正在处理第 213 个文件: JLJZY/JLJZY_Data/sharedassets4.assets
2025/05/24 00:01:13 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:13 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/sharedassets4.assets
2025/05/24 00:01:13 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/sharedassets4.assets
2025/05/24 00:01:13 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/sharedassets4.assets
2025/05/24 00:01:13 logger.go:90: 复制了 28780 字节
2025/05/24 00:01:13 logger.go:90: 正在处理第 214 个文件: JLJZY/JLJZY_Data/sharedassets4.assets.resS
2025/05/24 00:01:13 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:13 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/sharedassets4.assets.resS
2025/05/24 00:01:13 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/sharedassets4.assets.resS
2025/05/24 00:01:13 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/sharedassets4.assets.resS
2025/05/24 00:01:13 logger.go:90: 复制了 25165904 字节
2025/05/24 00:01:13 logger.go:90: 正在处理第 215 个文件: JLJZY/JLJZY_Data/sharedassets5.assets
2025/05/24 00:01:13 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:13 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/sharedassets5.assets
2025/05/24 00:01:13 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/sharedassets5.assets
2025/05/24 00:01:13 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/sharedassets5.assets
2025/05/24 00:01:13 logger.go:90: 复制了 7280 字节
2025/05/24 00:01:13 logger.go:90: 正在处理第 216 个文件: JLJZY/JLJZY_Data/sharedassets5.assets.resS
2025/05/24 00:01:13 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:13 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/sharedassets5.assets.resS
2025/05/24 00:01:13 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/sharedassets5.assets.resS
2025/05/24 00:01:13 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/sharedassets5.assets.resS
2025/05/24 00:01:13 logger.go:90: 复制了 11184824 字节
2025/05/24 00:01:13 logger.go:90: 正在处理第 217 个文件: JLJZY/JLJZY_Data/sharedassets6.assets
2025/05/24 00:01:13 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:13 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/sharedassets6.assets
2025/05/24 00:01:13 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/sharedassets6.assets
2025/05/24 00:01:13 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/sharedassets6.assets
2025/05/24 00:01:13 logger.go:90: 复制了 20503064 字节
2025/05/24 00:01:13 logger.go:90: 正在处理第 218 个文件: JLJZY/JLJZY_Data/sharedassets6.assets.resS
2025/05/24 00:01:13 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:13 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/sharedassets6.assets.resS
2025/05/24 00:01:13 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/sharedassets6.assets.resS
2025/05/24 00:01:13 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/sharedassets6.assets.resS
2025/05/24 00:01:15 logger.go:90: 复制了 271341060 字节
2025/05/24 00:01:15 logger.go:90: 正在处理第 219 个文件: JLJZY/JLJZY_Data/sharedassets6.resource
2025/05/24 00:01:15 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:15 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/sharedassets6.resource
2025/05/24 00:01:15 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/sharedassets6.resource
2025/05/24 00:01:15 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/sharedassets6.resource
2025/05/24 00:01:15 logger.go:90: 复制了 9024 字节
2025/05/24 00:01:15 logger.go:90: 正在处理第 220 个文件: JLJZY/JLJZY_Data/sharedassets7.assets
2025/05/24 00:01:15 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:15 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/sharedassets7.assets
2025/05/24 00:01:15 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/sharedassets7.assets
2025/05/24 00:01:15 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/sharedassets7.assets
2025/05/24 00:01:16 logger.go:90: 复制了 1512792 字节
2025/05/24 00:01:16 logger.go:90: 正在处理第 221 个文件: JLJZY/JLJZY_Data/sharedassets7.assets.resS
2025/05/24 00:01:16 logger.go:90: 解压进度: 84.94% (220/259)
2025/05/24 00:01:16 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:16 logger.go:90: 调用进度处理函数
2025/05/24 00:01:16 logger.go:90: 发送解压进度: 84.94% (220/259)
2025/05/24 00:01:16 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:16 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/sharedassets7.assets.resS
2025/05/24 00:01:16 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/sharedassets7.assets.resS
2025/05/24 00:01:16 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/sharedassets7.assets.resS
2025/05/24 00:01:17 logger.go:90: 复制了 169196404 字节
2025/05/24 00:01:17 logger.go:90: 正在处理第 222 个文件: JLJZY/JLJZY_Data/sharedassets8.assets
2025/05/24 00:01:17 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:17 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/sharedassets8.assets
2025/05/24 00:01:17 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/sharedassets8.assets
2025/05/24 00:01:17 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/sharedassets8.assets
2025/05/24 00:01:17 logger.go:90: 复制了 9236280 字节
2025/05/24 00:01:17 logger.go:90: 正在处理第 223 个文件: JLJZY/JLJZY_Data/sharedassets8.assets.resS
2025/05/24 00:01:17 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data
2025/05/24 00:01:17 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/sharedassets8.assets.resS
2025/05/24 00:01:17 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/sharedassets8.assets.resS
2025/05/24 00:01:17 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/sharedassets8.assets.resS
2025/05/24 00:01:19 logger.go:90: 复制了 251190160 字节
2025/05/24 00:01:19 logger.go:90: 正在处理第 224 个文件: JLJZY/JLJZY_Data/StreamingAssets/
2025/05/24 00:01:19 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/StreamingAssets
2025/05/24 00:01:19 logger.go:90: 正在处理第 225 个文件: JLJZY/JLJZY_Data/StreamingAssets/IPAdress/
2025/05/24 00:01:19 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/StreamingAssets/IPAdress
2025/05/24 00:01:19 logger.go:90: 正在处理第 226 个文件: JLJZY/JLJZY_Data/StreamingAssets/IPAdress/IPAdress.Json
2025/05/24 00:01:19 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/StreamingAssets/IPAdress
2025/05/24 00:01:19 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY_Data/StreamingAssets/IPAdress/IPAdress.Json
2025/05/24 00:01:19 logger.go:90: 打开zip中的文件: JLJZY/JLJZY_Data/StreamingAssets/IPAdress/IPAdress.Json
2025/05/24 00:01:19 logger.go:90: 复制文件内容: JLJZY/JLJZY_Data/StreamingAssets/IPAdress/IPAdress.Json
2025/05/24 00:01:19 logger.go:90: 复制了 1412 字节
2025/05/24 00:01:19 logger.go:90: 正在处理第 227 个文件: JLJZY/MonoBleedingEdge/
2025/05/24 00:01:19 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge
2025/05/24 00:01:19 logger.go:90: 正在处理第 228 个文件: JLJZY/MonoBleedingEdge/EmbedRuntime/
2025/05/24 00:01:19 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/EmbedRuntime
2025/05/24 00:01:19 logger.go:90: 正在处理第 229 个文件: JLJZY/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll
2025/05/24 00:01:19 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/EmbedRuntime
2025/05/24 00:01:19 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll
2025/05/24 00:01:19 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll
2025/05/24 00:01:19 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll
2025/05/24 00:01:20 logger.go:90: 复制了 4967552 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 230 个文件: JLJZY/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/EmbedRuntime
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll
2025/05/24 00:01:20 logger.go:90: 复制了 780288 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 231 个文件: JLJZY/MonoBleedingEdge/etc/
2025/05/24 00:01:20 logger.go:90: 解压进度: 88.80% (230/259)
2025/05/24 00:01:20 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:20 logger.go:90: 调用进度处理函数
2025/05/24 00:01:20 logger.go:90: 发送解压进度: 88.80% (230/259)
2025/05/24 00:01:20 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc
2025/05/24 00:01:20 logger.go:90: 正在处理第 232 个文件: JLJZY/MonoBleedingEdge/etc/mono/
2025/05/24 00:01:20 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono
2025/05/24 00:01:20 logger.go:90: 正在处理第 233 个文件: JLJZY/MonoBleedingEdge/etc/mono/2.0/
2025/05/24 00:01:20 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/2.0
2025/05/24 00:01:20 logger.go:90: 正在处理第 234 个文件: JLJZY/MonoBleedingEdge/etc/mono/2.0/Browsers/
2025/05/24 00:01:20 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/2.0/Browsers
2025/05/24 00:01:20 logger.go:90: 正在处理第 235 个文件: JLJZY/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/2.0/Browsers
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser
2025/05/24 00:01:20 logger.go:90: 复制了 1605 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 236 个文件: JLJZY/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/2.0
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx
2025/05/24 00:01:20 logger.go:90: 复制了 60575 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 237 个文件: JLJZY/MonoBleedingEdge/etc/mono/2.0/machine.config
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/2.0
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/2.0/machine.config
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/etc/mono/2.0/machine.config
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/etc/mono/2.0/machine.config
2025/05/24 00:01:20 logger.go:90: 复制了 29116 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 238 个文件: JLJZY/MonoBleedingEdge/etc/mono/2.0/settings.map
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/2.0
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/2.0/settings.map
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/etc/mono/2.0/settings.map
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/etc/mono/2.0/settings.map
2025/05/24 00:01:20 logger.go:90: 复制了 2622 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 239 个文件: JLJZY/MonoBleedingEdge/etc/mono/2.0/web.config
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/2.0
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/2.0/web.config
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/etc/mono/2.0/web.config
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/etc/mono/2.0/web.config
2025/05/24 00:01:20 logger.go:90: 复制了 11686 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 240 个文件: JLJZY/MonoBleedingEdge/etc/mono/4.0/
2025/05/24 00:01:20 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.0
2025/05/24 00:01:20 logger.go:90: 正在处理第 241 个文件: JLJZY/MonoBleedingEdge/etc/mono/4.0/Browsers/
2025/05/24 00:01:20 logger.go:90: 解压进度: 92.66% (240/259)
2025/05/24 00:01:20 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:20 logger.go:90: 调用进度处理函数
2025/05/24 00:01:20 logger.go:90: 发送解压进度: 92.66% (240/259)
2025/05/24 00:01:20 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.0/Browsers
2025/05/24 00:01:20 logger.go:90: 正在处理第 242 个文件: JLJZY/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.0/Browsers
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser
2025/05/24 00:01:20 logger.go:90: 复制了 1605 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 243 个文件: JLJZY/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.0
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx
2025/05/24 00:01:20 logger.go:90: 复制了 60575 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 244 个文件: JLJZY/MonoBleedingEdge/etc/mono/4.0/machine.config
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.0
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.0/machine.config
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/etc/mono/4.0/machine.config
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/etc/mono/4.0/machine.config
2025/05/24 00:01:20 logger.go:90: 复制了 33648 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 245 个文件: JLJZY/MonoBleedingEdge/etc/mono/4.0/settings.map
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.0
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.0/settings.map
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/etc/mono/4.0/settings.map
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/etc/mono/4.0/settings.map
2025/05/24 00:01:20 logger.go:90: 复制了 2622 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 246 个文件: JLJZY/MonoBleedingEdge/etc/mono/4.0/web.config
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.0
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.0/web.config
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/etc/mono/4.0/web.config
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/etc/mono/4.0/web.config
2025/05/24 00:01:20 logger.go:90: 复制了 18848 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 247 个文件: JLJZY/MonoBleedingEdge/etc/mono/4.5/
2025/05/24 00:01:20 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.5
2025/05/24 00:01:20 logger.go:90: 正在处理第 248 个文件: JLJZY/MonoBleedingEdge/etc/mono/4.5/Browsers/
2025/05/24 00:01:20 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.5/Browsers
2025/05/24 00:01:20 logger.go:90: 正在处理第 249 个文件: JLJZY/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.5/Browsers
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser
2025/05/24 00:01:20 logger.go:90: 复制了 1605 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 250 个文件: JLJZY/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.5
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx
2025/05/24 00:01:20 logger.go:90: 复制了 60575 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 251 个文件: JLJZY/MonoBleedingEdge/etc/mono/4.5/machine.config
2025/05/24 00:01:20 logger.go:90: 解压进度: 96.52% (250/259)
2025/05/24 00:01:20 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:20 logger.go:90: 调用进度处理函数
2025/05/24 00:01:20 logger.go:90: 发送解压进度: 96.52% (250/259)
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.5
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.5/machine.config
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/etc/mono/4.5/machine.config
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/etc/mono/4.5/machine.config
2025/05/24 00:01:20 logger.go:90: 复制了 34106 字节
2025/05/24 00:01:20 logger.go:90: 处理了50个文件，暂停一下
2025/05/24 00:01:20 logger.go:90: 正在处理第 252 个文件: JLJZY/MonoBleedingEdge/etc/mono/4.5/settings.map
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.5
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.5/settings.map
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/etc/mono/4.5/settings.map
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/etc/mono/4.5/settings.map
2025/05/24 00:01:20 logger.go:90: 复制了 2622 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 253 个文件: JLJZY/MonoBleedingEdge/etc/mono/4.5/web.config
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.5
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/4.5/web.config
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/etc/mono/4.5/web.config
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/etc/mono/4.5/web.config
2025/05/24 00:01:20 logger.go:90: 复制了 18857 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 254 个文件: JLJZY/MonoBleedingEdge/etc/mono/browscap.ini
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/browscap.ini
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/etc/mono/browscap.ini
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/etc/mono/browscap.ini
2025/05/24 00:01:20 logger.go:90: 复制了 311984 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 255 个文件: JLJZY/MonoBleedingEdge/etc/mono/config
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/config
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/etc/mono/config
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/etc/mono/config
2025/05/24 00:01:20 logger.go:90: 复制了 3276 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 256 个文件: JLJZY/MonoBleedingEdge/etc/mono/mconfig/
2025/05/24 00:01:20 logger.go:90: 创建目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/mconfig
2025/05/24 00:01:20 logger.go:90: 正在处理第 257 个文件: JLJZY/MonoBleedingEdge/etc/mono/mconfig/config.xml
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/mconfig
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/MonoBleedingEdge/etc/mono/mconfig/config.xml
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/MonoBleedingEdge/etc/mono/mconfig/config.xml
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/MonoBleedingEdge/etc/mono/mconfig/config.xml
2025/05/24 00:01:20 logger.go:90: 复制了 25817 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 258 个文件: JLJZY/UnityCrashHandler64.exe
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/UnityCrashHandler64.exe
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/UnityCrashHandler64.exe
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/UnityCrashHandler64.exe
2025/05/24 00:01:20 logger.go:90: 复制了 1094784 字节
2025/05/24 00:01:20 logger.go:90: 正在处理第 259 个文件: JLJZY/UnityPlayer.dll
2025/05/24 00:01:20 logger.go:90: 解压进度: 99.61% (258/259)
2025/05/24 00:01:20 logger.go:90: 成功发送进度到通道
2025/05/24 00:01:20 logger.go:90: 调用进度处理函数
2025/05/24 00:01:20 logger.go:90: 发送解压进度: 99.61% (258/259)
2025/05/24 00:01:20 logger.go:90: 创建父目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY
2025/05/24 00:01:20 logger.go:90: 创建文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/UnityPlayer.dll
2025/05/24 00:01:20 logger.go:90: 打开zip中的文件: JLJZY/UnityPlayer.dll
2025/05/24 00:01:20 logger.go:90: 复制文件内容: JLJZY/UnityPlayer.dll
2025/05/24 00:01:20 logger.go:90: 复制了 25961088 字节
2025/05/24 00:01:20 logger.go:105: 开始查找可执行文件...
2025/05/24 00:01:20 logger.go:90: 开始在目录 extracted/588328868260089856/592348408270946304/592362717994553344 中查找可执行文件
2025/05/24 00:01:20 logger.go:90: 找到可执行文件: JLJZY.exe (层级: 1, 大小: 650752 字节)
2025/05/24 00:01:20 logger.go:90: 找到可执行文件: ZFGameBrowser.exe (层级: 3, 大小: 1061376 字节)
2025/05/24 00:01:20 logger.go:90: 跳过 Unity 崩溃处理程序: UnityCrashHandler64.exe
2025/05/24 00:01:20 logger.go:90: 找到 2 个可执行文件
2025/05/24 00:01:20 logger.go:90: 检查可执行文件: JLJZY.exe, 层级: 1, 大小: 650752 字节
2025/05/24 00:01:20 logger.go:90: 选择更浅层级的文件: JLJZY.exe (层级: 1)
2025/05/24 00:01:20 logger.go:90: 检查可执行文件: ZFGameBrowser.exe, 层级: 3, 大小: 1061376 字节
2025/05/24 00:01:20 logger.go:90: 最终选择的可执行文件: JLJZY.exe (层级: 1, 大小: 650752 字节)
2025/05/24 00:01:20 logger.go:90: 找到可执行文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY.exe
2025/05/24 00:01:20 logger.go:105: 解压完成，发送最终进度
2025/05/24 00:01:20 logger.go:105: 成功发送最终进度到通道
2025/05/24 00:01:20 logger.go:105: 调用进度处理函数发送最终进度
2025/05/24 00:01:20 logger.go:90: 发送解压进度: 100.00% (259/259)
2025/05/24 00:01:20 logger.go:105: 再次调用进度处理函数发送最终进度
2025/05/24 00:01:20 logger.go:90: 发送解压进度: 100.00% (259/259)
2025/05/24 00:01:21 app.go:143: 同步学校信息成功，学校ID: 588328868260089856，学校名称: 黑龙江农业职业技术学院
2025/05/24 00:01:21 logger.go:105: 开始同步包列表
2025/05/24 00:01:21 logger.go:105: 正在获取远程包列表...
2025/05/24 00:01:21 logger.go:105: 正在获取本地包列表...
2025/05/24 00:01:21 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592564199850921984
2025/05/24 00:01:21 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592503365435396096
2025/05/24 00:01:21 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592480007272534016
2025/05/24 00:01:21 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592464199850921984
2025/05/24 00:01:21 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592460029626421248
2025/05/24 00:01:21 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592455327845191680
2025/05/24 00:01:21 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592407926350548992
2025/05/24 00:01:21 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592390778655870976
2025/05/24 00:01:21 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592389792184930304
2025/05/24 00:01:21 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592389406124412928
2025/05/24 00:01:21 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388575442505728
2025/05/24 00:01:21 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388420613967872
2025/05/24 00:01:21 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388303035043840
2025/05/24 00:01:21 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388093168848896
2025/05/24 00:01:21 logger.go:90: 同步包列表完成，共同步 14 个包
2025/05/24 00:01:21 logger.go:90: 解压成功完成
2025/05/24 00:01:25 logger.go:90: 开始运行包: ID=2, 名称=冠军精神虚拟仿真馆, 版本=13
2025/05/24 00:01:25 logger.go:90: 检查可执行文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY.exe
2025/05/24 00:01:25 logger.go:90: 可执行文件存在: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY.exe, 大小: 650752 字节, 修改时间: 2025-05-24 00:01:07
2025/05/24 00:01:25 logger.go:90: 执行文件绝对路径: /Users/<USER>/code/go/packageSystem_school/student-client/extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY.exe
2025/05/24 00:01:25 logger.go:90: 工作目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY
2025/05/24 00:01:25 logger.go:90: 使用macOS方式启动程序
2025/05/24 00:01:25 logger.go:90: 检测到Windows可执行文件(.exe)，尝试使用Wine运行
2025/05/24 00:01:25 logger.go:90: 在macOS上无法直接运行Windows可执行文件(.exe)，且未安装Wine
2025/05/24 00:03:00 app.go:55: 应用关闭
2025/05/24 00:03:00 logger.go:80: 关闭日志系统
2025/05/24 00:03:17 logger.go:72: 日志系统初始化成功
2025/05/24 00:03:17 app.go:45: 应用启动
2025/05/24 00:03:17 db.go:47: Database initialized successfully
2025/05/24 00:03:17 app.go:143: 同步学校信息成功，学校ID: 588328868260089856，学校名称: 黑龙江农业职业技术学院
2025/05/24 00:03:17 logger.go:105: 开始同步包列表
2025/05/24 00:03:17 logger.go:105: 正在获取远程包列表...
2025/05/24 00:03:17 logger.go:105: 正在获取本地包列表...
2025/05/24 00:03:17 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592564199850921984
2025/05/24 00:03:17 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592503365435396096
2025/05/24 00:03:17 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592480007272534016
2025/05/24 00:03:17 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592464199850921984
2025/05/24 00:03:17 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592460029626421248
2025/05/24 00:03:17 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592455327845191680
2025/05/24 00:03:17 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592407926350548992
2025/05/24 00:03:17 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592390778655870976
2025/05/24 00:03:17 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592389792184930304
2025/05/24 00:03:17 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592389406124412928
2025/05/24 00:03:17 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388575442505728
2025/05/24 00:03:17 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388420613967872
2025/05/24 00:03:17 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388303035043840
2025/05/24 00:03:17 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388093168848896
2025/05/24 00:03:17 logger.go:90: 同步包列表完成，共同步 14 个包
2025/05/24 00:05:22 app.go:55: 应用关闭
2025/05/24 00:05:22 logger.go:80: 关闭日志系统
2025/05/24 00:05:43 logger.go:72: 日志系统初始化成功
2025/05/24 00:05:43 app.go:45: 应用启动
2025/05/24 00:05:43 db.go:47: Database initialized successfully
2025/05/24 00:05:44 app.go:143: 同步学校信息成功，学校ID: 588328868260089856，学校名称: 黑龙江农业职业技术学院
2025/05/24 00:05:44 logger.go:105: 开始同步包列表
2025/05/24 00:05:44 logger.go:105: 正在获取远程包列表...
2025/05/24 00:05:44 logger.go:105: 正在获取本地包列表...
2025/05/24 00:05:44 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592564199850921984
2025/05/24 00:05:44 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592503365435396096
2025/05/24 00:05:44 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592480007272534016
2025/05/24 00:05:44 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592464199850921984
2025/05/24 00:05:44 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592460029626421248
2025/05/24 00:05:44 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592455327845191680
2025/05/24 00:05:44 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592407926350548992
2025/05/24 00:05:44 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592390778655870976
2025/05/24 00:05:44 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592389792184930304
2025/05/24 00:05:44 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592389406124412928
2025/05/24 00:05:44 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388575442505728
2025/05/24 00:05:44 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388420613967872
2025/05/24 00:05:44 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388303035043840
2025/05/24 00:05:44 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388093168848896
2025/05/24 00:05:44 logger.go:90: 同步包列表完成，共同步 14 个包
2025/05/24 00:06:07 app.go:55: 应用关闭
2025/05/24 00:06:07 logger.go:80: 关闭日志系统
2025/05/24 00:06:27 logger.go:72: 日志系统初始化成功
2025/05/24 00:06:27 app.go:45: 应用启动
2025/05/24 00:06:27 db.go:47: Database initialized successfully
2025/05/24 00:06:28 app.go:143: 同步学校信息成功，学校ID: 588328868260089856，学校名称: 黑龙江农业职业技术学院
2025/05/24 00:06:28 logger.go:105: 开始同步包列表
2025/05/24 00:06:28 logger.go:105: 正在获取远程包列表...
2025/05/24 00:06:28 logger.go:105: 正在获取本地包列表...
2025/05/24 00:06:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592564199850921984
2025/05/24 00:06:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592503365435396096
2025/05/24 00:06:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592480007272534016
2025/05/24 00:06:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592464199850921984
2025/05/24 00:06:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592460029626421248
2025/05/24 00:06:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592455327845191680
2025/05/24 00:06:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592407926350548992
2025/05/24 00:06:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592390778655870976
2025/05/24 00:06:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592389792184930304
2025/05/24 00:06:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592389406124412928
2025/05/24 00:06:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388575442505728
2025/05/24 00:06:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388420613967872
2025/05/24 00:06:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388303035043840
2025/05/24 00:06:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388093168848896
2025/05/24 00:06:28 logger.go:90: 同步包列表完成，共同步 14 个包
2025/05/24 00:07:27 logger.go:72: 日志系统初始化成功
2025/05/24 00:07:27 app.go:45: 应用启动
2025/05/24 00:07:27 db.go:47: Database initialized successfully
2025/05/24 00:07:27 app.go:143: 同步学校信息成功，学校ID: 588328868260089856，学校名称: 黑龙江农业职业技术学院
2025/05/24 00:07:27 logger.go:105: 开始同步包列表
2025/05/24 00:07:27 logger.go:105: 正在获取远程包列表...
2025/05/24 00:07:27 logger.go:105: 正在获取本地包列表...
2025/05/24 00:07:27 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592564199850921984
2025/05/24 00:07:27 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592503365435396096
2025/05/24 00:07:27 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592480007272534016
2025/05/24 00:07:27 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592464199850921984
2025/05/24 00:07:27 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592460029626421248
2025/05/24 00:07:27 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592455327845191680
2025/05/24 00:07:27 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592407926350548992
2025/05/24 00:07:27 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592390778655870976
2025/05/24 00:07:27 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592389792184930304
2025/05/24 00:07:27 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592389406124412928
2025/05/24 00:07:27 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388575442505728
2025/05/24 00:07:27 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388420613967872
2025/05/24 00:07:27 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388303035043840
2025/05/24 00:07:27 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388093168848896
2025/05/24 00:07:27 logger.go:90: 同步包列表完成，共同步 14 个包
2025/05/24 00:08:12 logger.go:90: 发现新包: 产业扶贫之弹吉他虚拟仿真体验教学软件, 版本: 1
2025/05/24 00:08:12 app.go:1019: 获取学校ID: 588328868260089856
2025/05/24 00:08:25 logger.go:105: 开始验证文件哈希...
2025/05/24 00:08:25 logger.go:90: 文件哈希验证: 期望=a45d57b6596908db, 实际=a45d57b6596908db
2025/05/24 00:08:25 logger.go:105: 文件哈希验证成功
2025/05/24 00:08:25 logger.go:90: 成功向学校端发送下载记录: 包版本ID=592506153078886400, MAC=d8:43:ae:28:3a:60, IP=*************
2025/05/24 00:08:25 logger.go:105: 成功发送最终进度到通道
2025/05/24 00:08:25 logger.go:105: 调用进度处理函数发送最终进度
2025/05/24 00:08:25 logger.go:90: 开始解压包 ID: 15, 解压ID: extract-15-1748016505155022000
2025/05/24 00:08:25 logger.go:90: 创建解压目录: 学校ID=588328868260089856, 实验ID=584047769623330816, 实验版本ID=589448092135198720
2025/05/24 00:08:25 logger.go:90: 解压目录创建成功: extracted/588328868260089856/584047769623330816/589448092135198720
2025/05/24 00:08:25 logger.go:90: 开始解压，共 68 个文件
2025/05/24 00:08:25 logger.go:90: 正在处理第 1 个文件: StreamingAssets/
2025/05/24 00:08:25 logger.go:90: 解压进度: 0.00% (0/68)
2025/05/24 00:08:25 logger.go:90: 成功发送进度到通道
2025/05/24 00:08:25 logger.go:90: 调用进度处理函数
2025/05/24 00:08:25 logger.go:90: 发送解压进度: 0.00% (0/68)
2025/05/24 00:08:25 logger.go:90: 创建目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets
2025/05/24 00:08:25 logger.go:90: 正在处理第 2 个文件: __MACOSX/._StreamingAssets
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/._StreamingAssets
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/._StreamingAssets
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/._StreamingAssets
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 3 个文件: StreamingAssets/Video/
2025/05/24 00:08:25 logger.go:90: 创建目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Video
2025/05/24 00:08:25 logger.go:90: 正在处理第 4 个文件: __MACOSX/StreamingAssets/._Video
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/._Video
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/._Video
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/._Video
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 5 个文件: StreamingAssets/.DS_Store
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/.DS_Store
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/.DS_Store
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/.DS_Store
2025/05/24 00:08:25 logger.go:90: 复制了 6148 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 6 个文件: __MACOSX/StreamingAssets/._.DS_Store
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/._.DS_Store
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/._.DS_Store
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/._.DS_Store
2025/05/24 00:08:25 logger.go:90: 复制了 120 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 7 个文件: StreamingAssets/Step/
2025/05/24 00:08:25 logger.go:90: 创建目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Step
2025/05/24 00:08:25 logger.go:90: 正在处理第 8 个文件: __MACOSX/StreamingAssets/._Step
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/._Step
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/._Step
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/._Step
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 9 个文件: StreamingAssets/.vs/
2025/05/24 00:08:25 logger.go:90: 创建目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/.vs
2025/05/24 00:08:25 logger.go:90: 正在处理第 10 个文件: __MACOSX/StreamingAssets/._.vs
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/._.vs
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/._.vs
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/._.vs
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 11 个文件: StreamingAssets/IPAdress/
2025/05/24 00:08:25 logger.go:90: 解压进度: 14.70% (10/68)
2025/05/24 00:08:25 logger.go:90: 成功发送进度到通道
2025/05/24 00:08:25 logger.go:90: 调用进度处理函数
2025/05/24 00:08:25 logger.go:90: 发送解压进度: 14.70% (10/68)
2025/05/24 00:08:25 logger.go:90: 创建目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/IPAdress
2025/05/24 00:08:25 logger.go:90: 正在处理第 12 个文件: __MACOSX/StreamingAssets/._IPAdress
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/._IPAdress
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/._IPAdress
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/._IPAdress
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 13 个文件: StreamingAssets/Question/
2025/05/24 00:08:25 logger.go:90: 创建目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Question
2025/05/24 00:08:25 logger.go:90: 正在处理第 14 个文件: __MACOSX/StreamingAssets/._Question
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/._Question
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/._Question
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/._Question
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 15 个文件: StreamingAssets/GameSceneConfig/
2025/05/24 00:08:25 logger.go:90: 创建目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/GameSceneConfig
2025/05/24 00:08:25 logger.go:90: 正在处理第 16 个文件: __MACOSX/StreamingAssets/._GameSceneConfig
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/._GameSceneConfig
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/._GameSceneConfig
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/._GameSceneConfig
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 17 个文件: StreamingAssets/ProcessUI/
2025/05/24 00:08:25 logger.go:90: 创建目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/ProcessUI
2025/05/24 00:08:25 logger.go:90: 正在处理第 18 个文件: __MACOSX/StreamingAssets/._ProcessUI
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/._ProcessUI
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/._ProcessUI
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/._ProcessUI
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 19 个文件: StreamingAssets/Video/两路精神mp4.mp4
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Video
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Video/两路精神mp4.mp4
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/Video/两路精神mp4.mp4
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/Video/两路精神mp4.mp4
2025/05/24 00:08:25 logger.go:105: 再次调用进度处理函数发送最终进度
2025/05/24 00:08:25 logger.go:90: 复制了 26599131 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 20 个文件: __MACOSX/StreamingAssets/Video/._两路精神mp4.mp4
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Video
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Video/._两路精神mp4.mp4
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/Video/._两路精神mp4.mp4
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/Video/._两路精神mp4.mp4
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 21 个文件: StreamingAssets/Step/CommandSceneStep7.json
2025/05/24 00:08:25 logger.go:90: 解压进度: 29.41% (20/68)
2025/05/24 00:08:25 logger.go:90: 成功发送进度到通道
2025/05/24 00:08:25 logger.go:90: 调用进度处理函数
2025/05/24 00:08:25 logger.go:90: 发送解压进度: 29.41% (20/68)
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Step
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Step/CommandSceneStep7.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/Step/CommandSceneStep7.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/Step/CommandSceneStep7.json
2025/05/24 00:08:25 logger.go:90: 复制了 5013 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 22 个文件: __MACOSX/StreamingAssets/Step/._CommandSceneStep7.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Step
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Step/._CommandSceneStep7.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/Step/._CommandSceneStep7.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/Step/._CommandSceneStep7.json
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 23 个文件: StreamingAssets/Step/CommandSceneStep6.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Step
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Step/CommandSceneStep6.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/Step/CommandSceneStep6.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/Step/CommandSceneStep6.json
2025/05/24 00:08:25 logger.go:90: 复制了 1767 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 24 个文件: __MACOSX/StreamingAssets/Step/._CommandSceneStep6.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Step
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Step/._CommandSceneStep6.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/Step/._CommandSceneStep6.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/Step/._CommandSceneStep6.json
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 25 个文件: StreamingAssets/Step/TaskStep.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Step
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Step/TaskStep.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/Step/TaskStep.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/Step/TaskStep.json
2025/05/24 00:08:25 logger.go:90: 复制了 365 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 26 个文件: __MACOSX/StreamingAssets/Step/._TaskStep.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Step
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Step/._TaskStep.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/Step/._TaskStep.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/Step/._TaskStep.json
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 27 个文件: StreamingAssets/Step/CommandSceneStep3.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Step
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Step/CommandSceneStep3.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/Step/CommandSceneStep3.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/Step/CommandSceneStep3.json
2025/05/24 00:08:25 logger.go:90: 复制了 7426 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 28 个文件: __MACOSX/StreamingAssets/Step/._CommandSceneStep3.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Step
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Step/._CommandSceneStep3.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/Step/._CommandSceneStep3.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/Step/._CommandSceneStep3.json
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 29 个文件: StreamingAssets/Step/CommandSceneStep2.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Step
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Step/CommandSceneStep2.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/Step/CommandSceneStep2.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/Step/CommandSceneStep2.json
2025/05/24 00:08:25 logger.go:90: 复制了 4123 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 30 个文件: __MACOSX/StreamingAssets/Step/._CommandSceneStep2.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Step
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Step/._CommandSceneStep2.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/Step/._CommandSceneStep2.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/Step/._CommandSceneStep2.json
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 31 个文件: StreamingAssets/Step/CommandSceneStep5.json
2025/05/24 00:08:25 logger.go:90: 解压进度: 44.11% (30/68)
2025/05/24 00:08:25 logger.go:90: 成功发送进度到通道
2025/05/24 00:08:25 logger.go:90: 调用进度处理函数
2025/05/24 00:08:25 logger.go:90: 发送解压进度: 44.11% (30/68)
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Step
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Step/CommandSceneStep5.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/Step/CommandSceneStep5.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/Step/CommandSceneStep5.json
2025/05/24 00:08:25 logger.go:90: 复制了 10368 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 32 个文件: __MACOSX/StreamingAssets/Step/._CommandSceneStep5.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Step
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Step/._CommandSceneStep5.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/Step/._CommandSceneStep5.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/Step/._CommandSceneStep5.json
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 33 个文件: StreamingAssets/Step/CommandSceneStep4.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Step
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Step/CommandSceneStep4.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/Step/CommandSceneStep4.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/Step/CommandSceneStep4.json
2025/05/24 00:08:25 logger.go:90: 复制了 3460 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 34 个文件: __MACOSX/StreamingAssets/Step/._CommandSceneStep4.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Step
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Step/._CommandSceneStep4.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/Step/._CommandSceneStep4.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/Step/._CommandSceneStep4.json
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 35 个文件: StreamingAssets/Step/CommandSceneStep.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Step
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Step/CommandSceneStep.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/Step/CommandSceneStep.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/Step/CommandSceneStep.json
2025/05/24 00:08:25 logger.go:90: 复制了 9016 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 36 个文件: __MACOSX/StreamingAssets/Step/._CommandSceneStep.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Step
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Step/._CommandSceneStep.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/Step/._CommandSceneStep.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/Step/._CommandSceneStep.json
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 37 个文件: StreamingAssets/.vs/slnx.sqlite
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/.vs
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/.vs/slnx.sqlite
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/.vs/slnx.sqlite
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/.vs/slnx.sqlite
2025/05/24 00:08:25 logger.go:90: 复制了 90112 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 38 个文件: __MACOSX/StreamingAssets/.vs/._slnx.sqlite
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/.vs
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/.vs/._slnx.sqlite
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/.vs/._slnx.sqlite
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/.vs/._slnx.sqlite
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 39 个文件: StreamingAssets/.vs/ProjectSettings.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/.vs
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/.vs/ProjectSettings.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/.vs/ProjectSettings.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/.vs/ProjectSettings.json
2025/05/24 00:08:25 logger.go:90: 复制了 37 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 40 个文件: __MACOSX/StreamingAssets/.vs/._ProjectSettings.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/.vs
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/.vs/._ProjectSettings.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/.vs/._ProjectSettings.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/.vs/._ProjectSettings.json
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 41 个文件: StreamingAssets/.vs/VSWorkspaceState.json
2025/05/24 00:08:25 logger.go:90: 解压进度: 58.82% (40/68)
2025/05/24 00:08:25 logger.go:90: 成功发送进度到通道
2025/05/24 00:08:25 logger.go:90: 调用进度处理函数
2025/05/24 00:08:25 logger.go:90: 发送解压进度: 58.82% (40/68)
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/.vs
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/.vs/VSWorkspaceState.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/.vs/VSWorkspaceState.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/.vs/VSWorkspaceState.json
2025/05/24 00:08:25 logger.go:90: 复制了 221 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 42 个文件: __MACOSX/StreamingAssets/.vs/._VSWorkspaceState.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/.vs
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/.vs/._VSWorkspaceState.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/.vs/._VSWorkspaceState.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/.vs/._VSWorkspaceState.json
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 43 个文件: StreamingAssets/.vs/StreamingAssets/
2025/05/24 00:08:25 logger.go:90: 创建目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/.vs/StreamingAssets
2025/05/24 00:08:25 logger.go:90: 正在处理第 44 个文件: __MACOSX/StreamingAssets/.vs/._StreamingAssets
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/.vs
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/.vs/._StreamingAssets
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/.vs/._StreamingAssets
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/.vs/._StreamingAssets
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 45 个文件: StreamingAssets/IPAdress/IPAdress.Json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/IPAdress
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/IPAdress/IPAdress.Json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/IPAdress/IPAdress.Json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/IPAdress/IPAdress.Json
2025/05/24 00:08:25 logger.go:90: 复制了 270 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 46 个文件: __MACOSX/StreamingAssets/IPAdress/._IPAdress.Json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/IPAdress
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/IPAdress/._IPAdress.Json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/IPAdress/._IPAdress.Json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/IPAdress/._IPAdress.Json
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 47 个文件: StreamingAssets/Question/Question.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Question
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/Question/Question.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/Question/Question.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/Question/Question.json
2025/05/24 00:08:25 logger.go:90: 复制了 2546 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 48 个文件: __MACOSX/StreamingAssets/Question/._Question.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Question
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/Question/._Question.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/Question/._Question.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/Question/._Question.json
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 49 个文件: StreamingAssets/GameSceneConfig/LoadingConfig.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/GameSceneConfig
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/GameSceneConfig/LoadingConfig.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/GameSceneConfig/LoadingConfig.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/GameSceneConfig/LoadingConfig.json
2025/05/24 00:08:25 logger.go:90: 复制了 1071 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 50 个文件: __MACOSX/StreamingAssets/GameSceneConfig/._LoadingConfig.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/GameSceneConfig
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/GameSceneConfig/._LoadingConfig.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/GameSceneConfig/._LoadingConfig.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/GameSceneConfig/._LoadingConfig.json
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 51 个文件: StreamingAssets/GameSceneConfig/LunBoConfig.json
2025/05/24 00:08:25 logger.go:90: 解压进度: 73.52% (50/68)
2025/05/24 00:08:25 logger.go:90: 成功发送进度到通道
2025/05/24 00:08:25 logger.go:90: 调用进度处理函数
2025/05/24 00:08:25 logger.go:90: 发送解压进度: 73.52% (50/68)
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/GameSceneConfig
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/GameSceneConfig/LunBoConfig.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/GameSceneConfig/LunBoConfig.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/GameSceneConfig/LunBoConfig.json
2025/05/24 00:08:25 logger.go:90: 复制了 1167 字节
2025/05/24 00:08:25 logger.go:90: 处理了50个文件，暂停一下
2025/05/24 00:08:25 logger.go:90: 正在处理第 52 个文件: __MACOSX/StreamingAssets/GameSceneConfig/._LunBoConfig.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/GameSceneConfig
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/GameSceneConfig/._LunBoConfig.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/GameSceneConfig/._LunBoConfig.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/GameSceneConfig/._LunBoConfig.json
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 53 个文件: StreamingAssets/GameSceneConfig/JianYuConfig.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/GameSceneConfig
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/GameSceneConfig/JianYuConfig.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/GameSceneConfig/JianYuConfig.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/GameSceneConfig/JianYuConfig.json
2025/05/24 00:08:25 logger.go:90: 复制了 1329 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 54 个文件: __MACOSX/StreamingAssets/GameSceneConfig/._JianYuConfig.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/GameSceneConfig
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/GameSceneConfig/._JianYuConfig.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/GameSceneConfig/._JianYuConfig.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/GameSceneConfig/._JianYuConfig.json
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 55 个文件: StreamingAssets/GameSceneConfig/LoadConfig.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/GameSceneConfig
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/GameSceneConfig/LoadConfig.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/GameSceneConfig/LoadConfig.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/GameSceneConfig/LoadConfig.json
2025/05/24 00:08:25 logger.go:90: 复制了 1040 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 56 个文件: __MACOSX/StreamingAssets/GameSceneConfig/._LoadConfig.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/GameSceneConfig
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/GameSceneConfig/._LoadConfig.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/GameSceneConfig/._LoadConfig.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/GameSceneConfig/._LoadConfig.json
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 57 个文件: StreamingAssets/GameSceneConfig/TouFangQingDan.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/GameSceneConfig
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/GameSceneConfig/TouFangQingDan.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/GameSceneConfig/TouFangQingDan.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/GameSceneConfig/TouFangQingDan.json
2025/05/24 00:08:25 logger.go:90: 复制了 291 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 58 个文件: __MACOSX/StreamingAssets/GameSceneConfig/._TouFangQingDan.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/GameSceneConfig
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/GameSceneConfig/._TouFangQingDan.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/GameSceneConfig/._TouFangQingDan.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/GameSceneConfig/._TouFangQingDan.json
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 59 个文件: StreamingAssets/GameSceneConfig/ChooseGoodConfig.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/GameSceneConfig
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/GameSceneConfig/ChooseGoodConfig.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/GameSceneConfig/ChooseGoodConfig.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/GameSceneConfig/ChooseGoodConfig.json
2025/05/24 00:08:25 logger.go:90: 复制了 2155 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 60 个文件: __MACOSX/StreamingAssets/GameSceneConfig/._ChooseGoodConfig.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/GameSceneConfig
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/GameSceneConfig/._ChooseGoodConfig.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/GameSceneConfig/._ChooseGoodConfig.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/GameSceneConfig/._ChooseGoodConfig.json
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 61 个文件: StreamingAssets/ProcessUI/ProcessUI.json
2025/05/24 00:08:25 logger.go:90: 解压进度: 88.23% (60/68)
2025/05/24 00:08:25 logger.go:90: 成功发送进度到通道
2025/05/24 00:08:25 logger.go:90: 调用进度处理函数
2025/05/24 00:08:25 logger.go:90: 发送解压进度: 88.23% (60/68)
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/ProcessUI
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/ProcessUI/ProcessUI.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/ProcessUI/ProcessUI.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/ProcessUI/ProcessUI.json
2025/05/24 00:08:25 logger.go:90: 复制了 18901 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 62 个文件: __MACOSX/StreamingAssets/ProcessUI/._ProcessUI.json
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/ProcessUI
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/ProcessUI/._ProcessUI.json
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/ProcessUI/._ProcessUI.json
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/ProcessUI/._ProcessUI.json
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 63 个文件: StreamingAssets/.vs/StreamingAssets/v16/
2025/05/24 00:08:25 logger.go:90: 创建目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/.vs/StreamingAssets/v16
2025/05/24 00:08:25 logger.go:90: 正在处理第 64 个文件: __MACOSX/StreamingAssets/.vs/StreamingAssets/._v16
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/.vs/StreamingAssets
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/.vs/StreamingAssets/._v16
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/.vs/StreamingAssets/._v16
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/.vs/StreamingAssets/._v16
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 65 个文件: StreamingAssets/.vs/StreamingAssets/v16/.suo
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/.vs/StreamingAssets/v16
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/StreamingAssets/.vs/StreamingAssets/v16/.suo
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: StreamingAssets/.vs/StreamingAssets/v16/.suo
2025/05/24 00:08:25 logger.go:90: 复制文件内容: StreamingAssets/.vs/StreamingAssets/v16/.suo
2025/05/24 00:08:25 logger.go:90: 复制了 120320 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 66 个文件: __MACOSX/StreamingAssets/.vs/StreamingAssets/v16/._.suo
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/.vs/StreamingAssets/v16
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/StreamingAssets/.vs/StreamingAssets/v16/._.suo
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/StreamingAssets/.vs/StreamingAssets/v16/._.suo
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/StreamingAssets/.vs/StreamingAssets/v16/._.suo
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 67 个文件: “两路”精神-社会主义革命和建设时期.exe
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/“两路”精神-社会主义革命和建设时期.exe
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: “两路”精神-社会主义革命和建设时期.exe
2025/05/24 00:08:25 logger.go:90: 复制文件内容: “两路”精神-社会主义革命和建设时期.exe
2025/05/24 00:08:25 logger.go:90: 复制了 650752 字节
2025/05/24 00:08:25 logger.go:90: 正在处理第 68 个文件: __MACOSX/._“两路”精神-社会主义革命和建设时期.exe
2025/05/24 00:08:25 logger.go:90: 解压进度: 98.52% (67/68)
2025/05/24 00:08:25 logger.go:90: 成功发送进度到通道
2025/05/24 00:08:25 logger.go:90: 调用进度处理函数
2025/05/24 00:08:25 logger.go:90: 发送解压进度: 98.52% (67/68)
2025/05/24 00:08:25 logger.go:90: 创建父目录: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX
2025/05/24 00:08:25 logger.go:90: 创建文件: extracted/588328868260089856/584047769623330816/589448092135198720/__MACOSX/._“两路”精神-社会主义革命和建设时期.exe
2025/05/24 00:08:25 logger.go:90: 打开zip中的文件: __MACOSX/._“两路”精神-社会主义革命和建设时期.exe
2025/05/24 00:08:25 logger.go:90: 复制文件内容: __MACOSX/._“两路”精神-社会主义革命和建设时期.exe
2025/05/24 00:08:25 logger.go:90: 复制了 178 字节
2025/05/24 00:08:25 logger.go:105: 开始查找可执行文件...
2025/05/24 00:08:25 logger.go:90: 开始在目录 extracted/588328868260089856/584047769623330816/589448092135198720 中查找可执行文件
2025/05/24 00:08:25 logger.go:90: 找到可执行文件: ._“两路”精神-社会主义革命和建设时期.exe (层级: 1, 大小: 178 字节)
2025/05/24 00:08:25 logger.go:90: 找到可执行文件: “两路”精神-社会主义革命和建设时期.exe (层级: 0, 大小: 650752 字节)
2025/05/24 00:08:25 logger.go:90: 找到 2 个可执行文件
2025/05/24 00:08:25 logger.go:90: 检查可执行文件: ._“两路”精神-社会主义革命和建设时期.exe, 层级: 1, 大小: 178 字节
2025/05/24 00:08:25 logger.go:90: 选择更浅层级的文件: ._“两路”精神-社会主义革命和建设时期.exe (层级: 1)
2025/05/24 00:08:25 logger.go:90: 检查可执行文件: “两路”精神-社会主义革命和建设时期.exe, 层级: 0, 大小: 650752 字节
2025/05/24 00:08:25 logger.go:90: 选择更浅层级的文件: “两路”精神-社会主义革命和建设时期.exe (层级: 0)
2025/05/24 00:08:25 logger.go:90: 最终选择的可执行文件: “两路”精神-社会主义革命和建设时期.exe (层级: 0, 大小: 650752 字节)
2025/05/24 00:08:25 logger.go:90: 找到可执行文件: extracted/588328868260089856/584047769623330816/589448092135198720/“两路”精神-社会主义革命和建设时期.exe
2025/05/24 00:08:25 logger.go:105: 解压完成，发送最终进度
2025/05/24 00:08:25 logger.go:105: 成功发送最终进度到通道
2025/05/24 00:08:25 logger.go:105: 调用进度处理函数发送最终进度
2025/05/24 00:08:25 logger.go:90: 发送解压进度: 100.00% (68/68)
2025/05/24 00:08:25 logger.go:105: 再次调用进度处理函数发送最终进度
2025/05/24 00:08:25 logger.go:90: 发送解压进度: 100.00% (68/68)
2025/05/24 00:08:25 logger.go:90: 解压成功完成
2025/05/24 00:08:26 app.go:143: 同步学校信息成功，学校ID: 588328868260089856，学校名称: 黑龙江农业职业技术学院
2025/05/24 00:08:26 logger.go:105: 开始同步包列表
2025/05/24 00:08:26 logger.go:105: 正在获取远程包列表...
2025/05/24 00:08:26 logger.go:105: 正在获取本地包列表...
2025/05/24 00:08:26 logger.go:90: 处理远程包: 产业扶贫之弹吉他虚拟仿真体验教学软件, 实验版本ID: 589448092135198720, 包版本ID: 592506153078886400
2025/05/24 00:08:26 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592564199850921984
2025/05/24 00:08:26 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592503365435396096
2025/05/24 00:08:26 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592480007272534016
2025/05/24 00:08:26 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592464199850921984
2025/05/24 00:08:26 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592460029626421248
2025/05/24 00:08:26 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592455327845191680
2025/05/24 00:08:26 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592407926350548992
2025/05/24 00:08:26 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592390778655870976
2025/05/24 00:08:26 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592389792184930304
2025/05/24 00:08:26 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592389406124412928
2025/05/24 00:08:26 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388575442505728
2025/05/24 00:08:26 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388420613967872
2025/05/24 00:08:26 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388303035043840
2025/05/24 00:08:26 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388093168848896
2025/05/24 00:08:26 logger.go:90: 同步包列表完成，共同步 15 个包
2025/05/24 00:09:05 app.go:55: 应用关闭
2025/05/24 00:09:05 logger.go:80: 关闭日志系统
2025/05/24 00:09:27 logger.go:72: 日志系统初始化成功
2025/05/24 00:09:27 app.go:45: 应用启动
2025/05/24 00:09:27 db.go:47: Database initialized successfully
2025/05/24 00:09:28 app.go:143: 同步学校信息成功，学校ID: 588328868260089856，学校名称: 黑龙江农业职业技术学院
2025/05/24 00:09:28 logger.go:105: 开始同步包列表
2025/05/24 00:09:28 logger.go:105: 正在获取远程包列表...
2025/05/24 00:09:28 logger.go:105: 正在获取本地包列表...
2025/05/24 00:09:28 logger.go:90: 处理远程包: 产业扶贫之弹吉他虚拟仿真体验教学软件, 实验版本ID: 589448092135198720, 包版本ID: 592506153078886400
2025/05/24 00:09:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592564199850921984
2025/05/24 00:09:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592503365435396096
2025/05/24 00:09:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592480007272534016
2025/05/24 00:09:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592464199850921984
2025/05/24 00:09:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592460029626421248
2025/05/24 00:09:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592455327845191680
2025/05/24 00:09:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592407926350548992
2025/05/24 00:09:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592390778655870976
2025/05/24 00:09:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592389792184930304
2025/05/24 00:09:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592389406124412928
2025/05/24 00:09:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388575442505728
2025/05/24 00:09:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388420613967872
2025/05/24 00:09:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388303035043840
2025/05/24 00:09:28 logger.go:90: 处理远程包: 冠军精神虚拟仿真馆, 实验版本ID: 592362717994553344, 包版本ID: 592388093168848896
2025/05/24 00:09:28 logger.go:90: 同步包列表完成，共同步 15 个包
2025/05/24 00:14:02 logger.go:90: 开始运行包: ID=2, 名称=冠军精神虚拟仿真馆, 版本=13
2025/05/24 00:14:02 logger.go:90: 检查可执行文件: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY.exe
2025/05/24 00:14:02 logger.go:90: 可执行文件存在: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY.exe, 大小: 650752 字节, 修改时间: 2025-05-24 00:01:07
2025/05/24 00:14:02 logger.go:90: 执行文件绝对路径: /Users/<USER>/code/go/packageSystem_school/student-client/extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY/JLJZY.exe
2025/05/24 00:14:02 logger.go:90: 工作目录: extracted/588328868260089856/592348408270946304/592362717994553344/JLJZY
2025/05/24 00:14:02 logger.go:90: 使用macOS方式启动程序
2025/05/24 00:14:02 logger.go:90: 检测到Windows可执行文件(.exe)，尝试使用Wine运行
2025/05/24 00:14:02 logger.go:90: 在macOS上无法直接运行Windows可执行文件(.exe)，且未安装Wine
2025/05/24 00:14:04 app.go:55: 应用关闭
2025/05/24 00:14:04 logger.go:80: 关闭日志系统
