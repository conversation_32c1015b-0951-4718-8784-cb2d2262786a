import { createRouter, createWebHashHistory } from 'vue-router'

// 页面组件
const Layout = () => import('../views/Layout.vue')
const Library = () => import('../views/Library.vue')
const PackageDetail = () => import('../views/PackageDetail.vue')
const Settings = () => import('../views/Settings.vue')

const SyncLogs = () => import('../views/SyncLogs.vue')

const routes = [
  {
    path: '/',
    component: Layout,
    redirect: '/library',
    children: [
      {
        path: 'library',
        name: 'Library',
        component: Library,
        meta: { title: '实验库' }
      },
      {
        path: 'package/:id',
        name: 'PackageDetail',
        component: PackageDetail,
        meta: { title: '实验详情' }
      },
      {
        path: 'sync-logs',
        name: 'SyncLogs',
        component: SyncLogs,
        meta: { title: '同步日志' }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: Settings,
        meta: { title: '设置' }
      }
    ]
  },
  {
    path: '/settings',
    component: Layout,
    children: [
      {
        path: '',
        component: Settings,
        meta: { title: '设置' }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router
