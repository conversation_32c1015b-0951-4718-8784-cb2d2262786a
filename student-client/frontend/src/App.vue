<template>
  <div class="app-container">
    <router-view />

    <!-- 服务器配置对话框 -->
    <el-dialog
      v-model="showServerConfigDialog"
      title="服务器配置"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="config-dialog-content">
        <el-alert
          title="首次使用提示"
          type="info"
          description="请配置学校端服务器地址以开始使用"
          :closable="false"
          style="margin-bottom: 20px"
        />

        <el-form :model="serverForm" :rules="serverRules" ref="serverFormRef" label-width="120px">
          <el-form-item label="服务器地址" prop="serverUrl">
            <el-input v-model="serverForm.serverUrl" placeholder="请输入学校端服务器地址，例如：*************">
              <template #prepend>http://</template>
            </el-input>
          </el-form-item>

          <el-form-item label="端口" prop="port">
            <el-input-number v-model="serverForm.port" :min="1" :max="65535" />
          </el-form-item>
        </el-form>

        <div class="connection-status" v-if="connectionStatus.message">
          <el-alert
            :title="connectionStatus.message"
            :type="connectionStatus.status === 'online' ? 'success' : 'error'"
            :closable="false"
          />
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="testConnection" :loading="testing">测试连接</el-button>
          <el-button type="primary" @click="saveAndContinue" :loading="saving" :disabled="!connectionSuccess">
            保存并继续
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'App',
  data() {
    return {
      showServerConfigDialog: false,
      serverForm: {
        serverUrl: '',
        port: 18080
      },
      serverRules: {
        serverUrl: [
          { required: true, message: '请输入服务器地址', trigger: 'blur' }
        ],
        port: [
          { required: true, message: '请输入端口号', trigger: 'blur' },
          { type: 'number', min: 1, max: 65535, message: '端口号必须在1-65535之间', trigger: 'blur' }
        ]
      },
      connectionStatus: {
        status: '',
        message: ''
      },
      testing: false,
      saving: false,
      connectionSuccess: false
    }
  },
  async mounted() {
    // 检查是否需要显示服务器配置对话框
    await this.checkServerConfig()
  },
  methods: {
    async checkServerConfig() {
      try {
        // 获取当前服务器配置
        const config = await window.go.main.App.GetServerConfig()

        // 如果没有配置服务器地址，显示配置对话框
        if (!config.serverURL || config.serverURL === '') {
          this.showServerConfigDialog = true
          return
        }

        // 如果有配置，测试连接
        const status = await window.go.main.App.GetServerStatus('')
        if (status.status === 'offline' || status.status === 'error') {
          // 如果允许跳过连接（非首次登录且有已下载的包）
          if (status.allowSkip) {
            console.log('非首次登录，有已下载包，允许跳过服务器连接')
            return // 不显示服务器配置对话框，允许直接使用
          }
          
          // 连接失败且不允许跳过，显示配置对话框
          this.serverForm.serverUrl = config.serverURL
          this.serverForm.port = parseInt(config.serverPort) || 18080
          this.showServerConfigDialog = true
        }
      } catch (error) {
        console.error('检查服务器配置失败:', error)
        // 出错时也显示配置对话框
        this.showServerConfigDialog = true
      }
    },
    async testConnection() {
      try {
        await this.$refs.serverFormRef.validate()

        this.testing = true
        this.connectionStatus = { status: '', message: '' }

        // 先保存服务器配置
        await window.go.main.App.SetServerConfig(
          this.serverForm.serverUrl,
          this.serverForm.port.toString()
        )

        // 测试连接
        const status = await window.go.main.App.GetServerStatus('')

        if (status.status === 'online') {
          this.connectionStatus = {
            status: 'online',
            message: '连接成功！'
          }
          this.connectionSuccess = true
          
          // 连接成功自动保存并关闭弹窗
          setTimeout(() => {
            this.saveAndContinue()
          }, 1000)
        } else {
          this.connectionStatus = {
            status: 'offline',
            message: '服务器连接失败'
          }
          this.connectionSuccess = false
          
          // 显示连接失败提示，不显示后端原始错误
          this.$message.error('服务器连接失败')
        }
      } catch (error) {
        console.error('测试连接失败:', error)
        this.connectionStatus = {
          status: 'offline',
          message: '服务器连接失败'
        }
        this.connectionSuccess = false
        
        // 显示连接失败提示，不显示原始错误
        this.$message.error('服务器连接失败')
      } finally {
        this.testing = false
      }
    },
    async saveAndContinue() {
      if (!this.connectionSuccess) {
        this.$message.warning('请先测试连接成功后再继续')
        return
      }

      try {
        this.saving = true

        // 保存配置
        await window.go.main.App.SetServerConfig(
          this.serverForm.serverUrl,
          this.serverForm.port.toString()
        )

        // 关闭对话框
        this.showServerConfigDialog = false
        this.$message.success('配置保存成功')

        // 触发Layout组件状态刷新
        this.$nextTick(() => {
          // 发送全局事件通知Layout组件刷新
          window.dispatchEvent(new CustomEvent('server-status-changed'))
        })

        // 刷新页面以重新加载数据
        window.location.reload()
      } catch (error) {
        console.error('保存配置失败:', error)
        this.$message.error('保存配置失败: ' + error.toString())
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style>
#app {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  height: 100vh;
}

body {
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow: hidden;
}

.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.config-dialog-content {
  padding: 10px 0;
}

.connection-status {
  margin-top: 15px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
