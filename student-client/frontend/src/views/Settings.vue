<template>
  <div class="settings-container">
    <div class="settings-header">
      <h2 class="settings-title">系统设置</h2>
      <div class="header-actions">
        <el-button type="primary" @click="goBack">
          <el-icon><el-icon-back /></el-icon>
          返回
        </el-button>
      </div>
    </div>
    
    <el-card class="settings-card">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="服务器设置" name="server">
          <el-form ref="serverFormRef" :model="serverForm" :rules="serverRules" label-width="120px">
            <el-form-item label="服务器地址" prop="serverUrl">
              <el-input v-model="serverForm.serverUrl" placeholder="请输入学校端服务器地址，例如：192.168.1.100">
                <template #prepend>http://</template>
              </el-input>
            </el-form-item>
            
            <el-form-item label="端口" prop="port">
              <el-input-number v-model="serverForm.port" :min="1" :max="65535" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="testConnection" :loading="testing">测试连接</el-button>
              <el-button @click="saveSettings" :disabled="!connectionSuccess" :loading="saving">保存设置</el-button>
            </el-form-item>
          </el-form>
          
          <div class="connection-status" v-if="connectionStatus.message">
            <el-alert
              :title="connectionStatus.message"
              :type="connectionStatus.status === 'online' ? 'success' : 'error'"
              :closable="false"
            />
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="关于" name="about">
          <div class="about-info">
            <h3>学生端包管理系统</h3>
            <p>版本: 1.0.0</p>
            <p>技术支持：成都智云鸿道信息技术有限公司</p>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'Settings',
  data() {
    return {
      activeTab: 'server',
      serverForm: {
        serverUrl: '',
        port: 18080
      },
      serverRules: {
        serverUrl: [
          { required: true, message: '请输入服务器地址', trigger: 'blur' }
        ],
        port: [
          { required: true, message: '请输入端口号', trigger: 'blur' },
          { type: 'number', min: 1, max: 65535, message: '端口号必须在1-65535之间', trigger: 'blur' }
        ]
      },
      connectionStatus: {
        status: '',
        message: ''
      },
      testing: false,
      saving: false,
      connectionSuccess: false
    }
  },
  created() {
    console.log('设置页面已创建')
    // 加载当前服务器设置
    this.loadServerConfig()
  },
  methods: {
    async loadServerConfig() {
      try {
        console.log('加载服务器配置')
        // 从Go后端获取服务器配置
        const config = await window.go.main.App.GetServerConfig()
        console.log('获取到服务器配置:', config)
        
        // 更新表单数据
        this.serverForm.serverUrl = config.serverURL || ''
        this.serverForm.port = parseInt(config.serverPort) || 18080
      } catch (error) {
        console.error('加载服务器配置失败:', error)
        this.$message.error('加载服务器配置失败')
      }
    },
    async testConnection() {
      try {
        console.log('测试连接')
        await this.$refs.serverFormRef.validate()

        this.testing = true
        this.connectionStatus = { status: '', message: '' }

        // 先保存服务器配置
        await window.go.main.App.SetServerConfig(
          this.serverForm.serverUrl,
          this.serverForm.port.toString()
        )

        // 测试连接
        const status = await window.go.main.App.GetServerStatus('')
        console.log('连接状态:', status)

        if (status.status === 'online') {
          this.connectionStatus = {
            status: 'online',
            message: '连接成功！'
          }
          this.connectionSuccess = true
          this.$message.success('连接成功')
          
          // 通知父组件Layout立即更新服务器状态
          this.$emit('server-status-changed')
          
          // 通过事件总线通知Layout组件刷新状态
          this.$nextTick(() => {
            // 如果有父组件Layout，直接调用其方法
            const layout = this.findLayoutComponent()
            if (layout && layout.getServerStatus) {
              layout.getServerStatus()
            }
          })
        } else {
          this.connectionStatus = {
            status: 'offline',
            message: '服务器连接失败'
          }
          this.connectionSuccess = false
          this.$message.error('服务器连接失败')
        }
      } catch (error) {
        console.error('测试连接失败:', error)
        this.connectionStatus = {
          status: 'offline',
          message: '服务器连接失败'
        }
        this.connectionSuccess = false
        this.$message.error('服务器连接失败')
      } finally {
        this.testing = false
      }
    },
    // 查找Layout组件
    findLayoutComponent() {
      let parent = this.$parent
      while (parent) {
        if (parent.$options.name === 'Layout') {
          return parent
        }
        parent = parent.$parent
      }
      return null
    },
    async saveSettings() {
      if (!this.connectionSuccess) {
        this.$message.warning('请先测试连接成功后再保存')
        return
      }

      try {
        this.saving = true

        // 保存配置
        await window.go.main.App.SetServerConfig(
          this.serverForm.serverUrl,
          this.serverForm.port.toString()
        )

        this.$message.success('配置保存成功')
      } catch (error) {
        console.error('保存配置失败:', error)
        this.$message.error('保存配置失败: ' + error.toString())
      } finally {
        this.saving = false
      }
    },
    goBack() {
      this.$router.push('/library')
    }
  }
}
</script>

<style scoped>
.settings-container {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.settings-title {
  font-size: 22px;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.3px;
}

.settings-card {
  margin: 0 auto;
  max-width: 800px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.connection-status {
  margin-top: 20px;
}

.about-info {
  padding: 20px;
  text-align: center;
}

.about-info h3 {
  font-size: 18px;
  margin-bottom: 10px;
  color: #409EFF;
}

.about-info p {
  margin: 5px 0;
  color: #606266;
}
</style>
