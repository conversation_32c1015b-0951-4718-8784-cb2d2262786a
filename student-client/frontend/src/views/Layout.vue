<template>
  <div class="launcher-container">
    <div class="launcher-header">
      <div class="logo">
        <!-- <img src="../assets/logo.png" alt="学生端包管理系统" class="logo-image" /> -->
        <div class="title-container">
          <h1>思政虚拟仿真体验教学软件启动器</h1>
          <div class="school-name">{{ schoolName || '未连接学校' }}</div>
        </div>
      </div>
      <div class="header-nav">
        <div class="nav-item">
          <router-link to="/library" class="nav-link" :class="{ active: activeMenu === '/library' }">
            <el-icon><el-icon-collection /></el-icon>
            <span>软件库</span>
          </router-link>
        </div>
      </div>
      <div class="header-actions">
        <el-tooltip content="设置" placement="bottom">
          <el-button circle @click="goToSettings">
            <el-icon><el-icon-setting /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <div class="launcher-content">
      <router-view />
    </div>

    <div class="launcher-footer">
      <div class="server-status">
        <span class="status-dot" :class="{ 'online': serverStatus.online }"></span>
        <span class="status-text">{{ serverStatus.online ? '已连接到学校服务器' : '未连接到学校服务器' }}</span>
      </div>
      <div class="update-status" v-if="updateStatus.message">
        <span class="update-dot" :class="updateStatus.type"></span>
        <span class="update-text">{{ updateStatus.message }}</span>
      </div>
      <div>技术支持：成都智云鸿道信息技术有限公司</div>
      <div class="footer-info">
        <span>学员端 v{{ currentVersion }}</span>
      </div>

    </div>
  </div>
</template>

<script>
export default {
  name: 'Layout',
  data() {
    return {
      serverStatus: {
        online: false,  // 默认为false，防止误导用户
        address: '*************:18080'
      },
      schoolName: '',
      statusCheckTimer: null,  // 状态检查定时器
      currentVersion: '1.0.0',
      updateStatus: {
        message: '',
        type: '', // 'checking', 'countdown', 'downloading', 'updating', 'error'
        countdown: 0
      }
    }
  },
  computed: {
    activeMenu() {
      return this.$route.path
    }
  },
  mounted() {
    this.getServerStatus()
    this.loadSchoolName()
    this.getCurrentVersion()
    this.setupUpdateEventListeners()

    // 设置定时器，每60秒检查一次服务器状态
    this.statusCheckTimer = setInterval(() => {
      this.getServerStatus()
    }, 60000)  // 60秒

    // 监听全局服务器状态改变事件
    window.addEventListener('server-status-changed', this.handleServerStatusChanged)
  },
  beforeUnmount() {
    // 清理定时器
    if (this.statusCheckTimer) {
      clearInterval(this.statusCheckTimer)
      this.statusCheckTimer = null
    }

    // 移除全局事件监听
    window.removeEventListener('server-status-changed', this.handleServerStatusChanged)
  },
  methods: {
    goToSettings() {
      console.log('跳转到设置页面')
      // 明确使用绝对路径，确保正确跳转
      this.$router.push('/settings')
    },
    async getServerStatus() {
      try {
        // 从Go后端获取服务器状态
        const status = await window.go.main.App.GetServerStatus('')
        const isOnline = status.status === 'online'

        // 只在状态真正改变时更新，避免不必要的响应式更新
        if (this.serverStatus.online !== isOnline) {
          this.serverStatus.online = isOnline
          console.log('服务器状态更新:', isOnline ? '在线' : '离线')

          // 如果连接成功，重新加载学校名称
          if (isOnline) {
            this.loadSchoolName()
          }
        }
      } catch (error) {
        console.error('获取服务器状态失败:', error)
        if (this.serverStatus.online !== false) {
          this.serverStatus.online = false
        }
      }
    },

    async loadSchoolName() {
      try {
        // 从Go后端获取服务器配置，包含学校名称
        const config = await window.go.main.App.GetServerConfig()
        this.schoolName = config.schoolName || ''

        // 如果没有学校名称，尝试同步
        if (!this.schoolName) {
          // 触发同步方法获取学校信息
          this.syncSchoolInfo()
        }
      } catch (error) {
        console.error('获取学校名称失败:', error)
        this.schoolName = ''
      }
    },
    async syncSchoolInfo() {
      try {
        // 调用同步方法获取学校信息
        const syncID = await window.go.main.App.SyncNow()

        // 监听同步进度，完成后重新加载学校名称
        const handleProgress = (progress) => {
          if (progress.percentage >= 100) {
            // 同步完成，重新加载学校名称
            setTimeout(() => {
              this.loadSchoolName()
            }, 1000)
            // 清理事件监听
            window.runtime.EventsOff(syncID)
          }

          if (progress.error) {
            // 清理事件监听
            window.runtime.EventsOff(syncID)
          }
        }

        // 开始监听同步进度事件
        window.runtime.EventsOn(syncID, handleProgress)
      } catch (error) {
        console.error('同步学校信息失败:', error)
      }
    },
    handleServerStatusChanged() {
      console.log('收到服务器状态改变通知，立即刷新状态')
      // 立即检查服务器状态
      this.getServerStatus()
      // 重新加载学校名称
      this.loadSchoolName()
    },

    // 获取当前版本
    async getCurrentVersion() {
      try {
        const version = await window.go.main.App.GetCurrentVersion()
        this.currentVersion = version
      } catch (error) {
        console.error('获取当前版本失败:', error)
      }
    },

    // 设置更新事件监听
    setupUpdateEventListeners() {
      // 监听更新检查开始
      window.runtime.EventsOn('update-check-start', (data) => {
        this.updateStatus = {
          message: data.message,
          type: 'checking',
          countdown: 0
        }
        console.log('更新检查开始:', data)
      })

      // 监听更新检查完成
      window.runtime.EventsOn('update-check-complete', (data) => {
        if (data.hasUpdate) {
          this.updateStatus = {
            message: data.message,
            type: 'available',
            countdown: 0
          }
        } else {
          this.updateStatus = {
            message: data.message,
            type: 'latest',
            countdown: 0
          }
          // 3秒后隐藏消息
          setTimeout(() => {
            this.updateStatus.message = ''
          }, 3000)
        }
        console.log('更新检查完成:', data)
      })

      // 监听更新倒计时
      window.runtime.EventsOn('update-countdown', (data) => {
        this.updateStatus = {
          message: data.message,
          type: 'countdown',
          countdown: data.countdown
        }
        console.log('更新倒计时:', data)
      })

      // 监听下载开始
      window.runtime.EventsOn('update-download-start', (data) => {
        this.updateStatus = {
          message: data.message,
          type: 'downloading',
          countdown: 0
        }
        console.log('下载开始:', data)
      })

      // 监听下载完成
      window.runtime.EventsOn('update-download-complete', (data) => {
        this.updateStatus = {
          message: data.message,
          type: 'downloaded',
          countdown: 0
        }
        console.log('下载完成:', data)
      })

      // 监听下载错误
      window.runtime.EventsOn('update-download-error', (data) => {
        this.updateStatus = {
          message: data.message,
          type: 'error',
          countdown: 0
        }
        console.error('下载错误:', data)
        // 5秒后隐藏错误消息
        setTimeout(() => {
          this.updateStatus.message = ''
        }, 5000)
      })

      // 监听更新开始
      window.runtime.EventsOn('update-start', (data) => {
        this.updateStatus = {
          message: data.message,
          type: 'updating',
          countdown: 0
        }
        console.log('更新开始:', data)
      })

      // 监听更新错误
      window.runtime.EventsOn('update-error', (data) => {
        this.updateStatus = {
          message: data.message,
          type: 'error',
          countdown: 0
        }
        console.error('更新错误:', data)
        // 5秒后隐藏错误消息
        setTimeout(() => {
          this.updateStatus.message = ''
        }, 5000)
      })
    }
  }
}
</script>

<style scoped>
.launcher-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.launcher-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;
  background-color: #1a1a1a;
  color: #fff;
  position: relative;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-image {
  width: 32px;
  height: 32px;
  margin-right: 10px;
}

.title-container {
  display: flex;
  align-items: baseline;
}

.logo h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.school-name {
  margin-left: 10px;
  font-size: 14px;
  color: #e6a23c;
  font-weight: 500;
}

.header-nav {
  position: absolute;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
}

.nav-item {
  height: 60px;
  display: flex;
  align-items: center;
  pointer-events: auto;
}

.nav-link {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 20px;
  color: #ffffff;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s;
}

.nav-link:hover {
  color: #409EFF;
}

.nav-link.active {
  color: #409EFF;
  border-bottom: 2px solid #409EFF;
}

.nav-link .el-icon {
  margin-right: 5px;
}

.header-actions {
  display: flex;
  align-items: center;
}

.launcher-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  background-color: #f5f7fa;
}

.launcher-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 40px;
  background-color: #f5f7fa;
  border-top: 1px solid #e4e7ed;
  color: #606266;
  font-size: 12px;
}

.server-status {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #f56c6c;
  margin-right: 5px;
}

.status-dot.online {
  background-color: #67c23a;
}

.status-text {
  font-size: 12px;
}

.update-status {
  display: flex;
  align-items: center;
  margin: 0 10px;
}

.update-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

.update-dot.checking {
  background-color: #409EFF;
  animation: pulse 1.5s infinite;
}

.update-dot.countdown {
  background-color: #E6A23C;
  animation: pulse 1s infinite;
}

.update-dot.downloading {
  background-color: #E6A23C;
  animation: pulse 1.5s infinite;
}

.update-dot.error {
  background-color: #F56C6C;
}

.update-dot.latest {
  background-color: #67C23A;
}

.update-dot.available {
  background-color: #E6A23C;
}

.update-dot.downloaded {
  background-color: #67C23A;
}

.update-dot.updating {
  background-color: #409EFF;
  animation: pulse 1.5s infinite;
}

.update-text {
  font-size: 12px;
  margin-right: 10px;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.footer-info {
  text-align: center;
}

.footer-actions {
  text-align: right;
}

/* 覆盖Element Plus菜单样式 */
:deep(.el-menu--horizontal) {
  border-bottom: none;
}

:deep(.el-menu--horizontal > .el-menu-item) {
  height: 60px;
  line-height: 60px;
}
</style>
