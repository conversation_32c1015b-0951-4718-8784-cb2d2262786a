<template>
  <div class="server-config">
    <el-card>
      <template #header>
        <div class="card-header">
          <h2>服务器配置</h2>
        </div>
      </template>
      
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-form-item label="服务器地址" prop="serverUrl">
          <el-input v-model="form.serverUrl" placeholder="请输入学校端服务器地址，例如：http://192.168.1.100:18080">
            <template #prepend>http://</template>
          </el-input>
        </el-form-item>
        
        <el-form-item label="端口" prop="port">
          <el-input-number v-model="form.port" :min="1" :max="65535" />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="testConnection" :loading="testing">测试连接</el-button>
          <el-button type="success" @click="saveConfig" :loading="saving">保存配置</el-button>
        </el-form-item>
      </el-form>
      
      <div v-if="connectionStatus" class="connection-status">
        <el-alert
          :title="connectionStatus.message"
          :type="connectionStatus.status === 'online' ? 'success' : 'error'"
          :closable="false"
          show-icon
        />
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'ServerConfig',
  data() {
    return {
      form: {
        serverUrl: '',
        port: 18080
      },
      rules: {
        serverUrl: [
          { required: true, message: '请输入服务器地址', trigger: 'blur' },
          { pattern: /^[a-zA-Z0-9\.\-]+$/, message: '服务器地址格式不正确', trigger: 'blur' }
        ],
        port: [
          { required: true, message: '请输入端口号', trigger: 'blur' },
          { type: 'number', min: 1, max: 65535, message: '端口号范围为1-65535', trigger: 'blur' }
        ]
      },
      connectionStatus: null,
      testing: false,
      saving: false
    }
  },
  mounted() {
    this.loadConfig()
  },
  methods: {
    async loadConfig() {
      try {
        // 从Go后端加载配置
        // const config = await window.go.main.App.GetServerConfig()
        // this.form.serverUrl = config.serverUrl || ''
        // this.form.port = config.port || 18080
      } catch (error) {
        console.error('加载配置失败:', error)
        this.$message.error('加载配置失败')
      }
    },
    async testConnection() {
      try {
        this.testing = true
        const serverUrl = `${this.form.serverUrl}:${this.form.port}`
        
        // 调用Go后端测试连接
        // const status = await window.go.main.App.GetServerStatus(serverUrl)
        // 模拟测试结果
        const status = { status: 'online', message: '服务器连接正常' }
        
        this.connectionStatus = status
        
        if (status.status === 'online') {
          this.$message.success('连接成功')
        } else {
          this.$message.error('连接失败: ' + status.message)
        }
      } catch (error) {
        console.error('测试连接失败:', error)
        this.$message.error('测试连接失败')
        this.connectionStatus = { status: 'offline', message: '连接失败: ' + error.message }
      } finally {
        this.testing = false
      }
    },
    async saveConfig() {
      try {
        await this.$refs.formRef.validate()
        
        this.saving = true
        const config = {
          serverUrl: this.form.serverUrl,
          port: this.form.port
        }
        
        // 调用Go后端保存配置
        // await window.go.main.App.SaveServerConfig(config)
        
        this.$message.success('配置保存成功')
      } catch (error) {
        console.error('保存配置失败:', error)
        this.$message.error('保存配置失败')
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style scoped>
.server-config {
  max-width: 800px;
  margin: 0 auto;
}

.connection-status {
  margin-top: 20px;
}
</style>
