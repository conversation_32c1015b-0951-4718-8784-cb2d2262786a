<template>
  <div class="sync-logs-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h2>同步日志</h2>
          <div class="header-actions">
            <el-button type="primary" @click="refreshLogs" :loading="loading">
              <el-icon><el-icon-refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <div class="filter-bar">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="handleDateChange"
        />

        <el-select v-model="logType" placeholder="日志类型" clearable @change="handleFilterChange">
          <el-option label="全部类型" value="" />
          <el-option label="同步" value="sync" />
          <el-option label="下载" value="download" />
          <el-option label="更新" value="update" />
          <el-option label="错误" value="error" />
        </el-select>

        <el-select v-model="logStatus" placeholder="状态" clearable @change="handleFilterChange">
          <el-option label="全部状态" value="" />
          <el-option label="成功" value="success" />
          <el-option label="失败" value="failed" />
        </el-select>
      </div>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>

      <div v-else-if="filteredLogs.length === 0" class="empty-container">
        <el-empty description="没有找到同步日志" />
      </div>

      <div v-else class="logs-table">
        <el-table :data="filteredLogs" style="width: 100%">
          <el-table-column prop="time" label="时间" width="180" sortable />
          <el-table-column label="类型" width="100">
            <template #default="scope">
              <el-tag :type="getLogTypeColor(scope.row.type)">
                {{ getLogTypeText(scope.row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="experimentName" label="实验名称" min-width="150" />
          <el-table-column prop="versionName" label="版本" width="120" />
          <el-table-column label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusColor(scope.row.status)">
                {{ scope.row.statusText || scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="message" label="详情" min-width="200" />
          <el-table-column label="操作" width="120" v-if="showActions">
            <template #default="scope">
              <el-button
                v-if="scope.row.status === 'failed'"
                type="warning"
                size="small"
                @click="retryAction(scope.row)"
              >
                重试
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="viewPackage(scope.row.packageId)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            v-model:currentPage="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalLogs"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>


  </div>
</template>

<script>
export default {
  name: 'SyncLogs',
  data() {
    return {
      logs: [],
      loading: false,
      dateRange: [],
      logType: '',
      logStatus: '',
      currentPage: 1,
      pageSize: 20,
      totalLogs: 0,
      showActions: true
    }
  },
  computed: {
    filteredLogs() {
      let result = [...this.logs]

      // 根据日期筛选
      if (this.dateRange && this.dateRange.length === 2) {
        const startDate = new Date(this.dateRange[0])
        const endDate = new Date(this.dateRange[1])
        endDate.setHours(23, 59, 59, 999) // 设置为当天结束时间

        result = result.filter(log => {
          const logDate = new Date(log.time)
          return logDate >= startDate && logDate <= endDate
        })
      }

      // 根据类型筛选
      if (this.logType) {
        result = result.filter(log => log.type === this.logType)
      }

      // 根据状态筛选
      if (this.logStatus) {
        result = result.filter(log => log.status === this.logStatus)
      }

      return result
    }
  },
  mounted() {
    this.fetchLogs()
  },
  methods: {
    async fetchLogs() {
      this.loading = true
      try {
        // 从Go后端获取日志
        const result = await window.go.main.App.GetSyncLogs(
          this.currentPage,
          this.pageSize,
          this.logType,
          this.logStatus === 'success' ? 0 : this.logStatus === 'failed' ? 2 : -1
        )

        this.logs = result.logs
        this.totalLogs = result.total
      } catch (error) {
        console.error('获取同步日志失败:', error)
        this.$message.error('获取同步日志失败: ' + error.toString())
        this.logs = []
        this.totalLogs = 0
      } finally {
        this.loading = false
      }
    },
    refreshLogs() {
      this.fetchLogs()
    },
    handleDateChange() {
      this.currentPage = 1
      this.fetchLogs()
    },
    handleFilterChange() {
      this.currentPage = 1
      this.fetchLogs()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchLogs()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchLogs()
    },
    getLogTypeColor(type) {
      const colors = {
        'sync': 'info',
        'download': 'success',
        'update': 'warning',
        'error': 'danger'
      }
      return colors[type] || 'info'
    },
    getLogTypeText(type) {
      const texts = {
        'sync': '同步',
        'download': '下载',
        'update': '更新',
        'error': '错误',
        'extract': '解压',
        'run': '运行',
        'create': '创建',
        'shortcut': '快捷方式'
      }
      return texts[type] || type
    },
    getStatusColor(status) {
      const colors = {
        'success': 'success',
        'running': 'warning',
        'failed': 'danger',
        'unknown': 'info'
      }
      return colors[status] || 'info'
    },
    async retryAction(log) {
      try {
        if (log.type === 'download' || log.type === 'update') {
          // 调用Go后端重试下载
          await window.go.main.App.DownloadPackage(log.packageId)
          this.$message.success('已重新开始下载')
          // 刷新日志
          this.fetchLogs()
        } else if (log.type === 'sync') {
          // 跳转到软件库页面进行同步
          this.$router.push('/library')
          this.$message.info('请在软件库页面点击刷新按钮进行同步')
        } else if (log.type === 'extract') {
          // 调用Go后端重试解压
          await window.go.main.App.ExtractPackage(log.packageId)
          this.$message.success('已重新开始解压')
          // 刷新日志
          this.fetchLogs()
        }
      } catch (error) {
        console.error('重试操作失败:', error)
        this.$message.error('重试操作失败: ' + error.toString())
      }
    },
    viewPackage(packageId) {
      if (packageId) {
        this.$router.push(`/package/${packageId}`)
      }
    },

  }
}
</script>

<style scoped>
.sync-logs-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-bar {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.loading-container, .empty-container {
  padding: 40px;
  text-align: center;
}


</style>
