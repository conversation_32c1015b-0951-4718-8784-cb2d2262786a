<template>
  <div class="package-detail">
    <el-card>
      <template #header>
        <div class="card-header">
          <h2>包详情</h2>
          <div class="header-actions">
            <el-button type="primary" @click="goBack">
              <el-icon><el-icon-arrow-left /></el-icon>
              返回
            </el-button>
          </div>
        </div>
      </template>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>

      <div v-else-if="packageDetail" class="package-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="实验名称">{{ packageDetail.experimentName || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="实验版本">{{ packageDetail.experimentVersionName || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="包版本名称">{{ packageDetail.versionName }}</el-descriptions-item>
          <el-descriptions-item label="版本号">{{ packageDetail.version }}</el-descriptions-item>
          <el-descriptions-item label="实验ID">{{ packageDetail.experimentId }}</el-descriptions-item>
          <el-descriptions-item label="实验版本ID">{{ packageDetail.experimentVersionId }}</el-descriptions-item>
          <el-descriptions-item label="文件大小">{{ formatFileSize(packageDetail.fileSize) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(packageDetail.syncStatus)">
              {{ getStatusText(packageDetail.syncStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="最后运行时间">{{ formatDate(packageDetail.lastRun) }}</el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">{{ packageDetail.versionDesc || '无描述' }}</el-descriptions-item>
        </el-descriptions>

        <div class="action-buttons">
          <el-button
            v-if="packageDetail.syncStatus === 4"
            type="success"
            @click="runPackage"
          >
            运行
          </el-button>
          <el-button
            v-else-if="packageDetail.syncStatus === 0 || packageDetail.syncStatus === 5"
            type="primary"
            @click="downloadPackage"
          >
            {{ packageDetail.syncStatus === 5 ? '重新下载' : '下载' }}
          </el-button>
          <el-button
            v-else-if="packageDetail.syncStatus === 1 || packageDetail.syncStatus === 3"
            type="info"
            disabled
          >
            {{ packageDetail.syncStatus === 1 ? '下载中...' : '解压中...' }}
          </el-button>
          <el-button
            v-if="packageDetail.syncStatus === 4"
            type="info"
            @click="createShortcut"
          >
            创建快捷方式
          </el-button>
        </div>
      </div>

      <div v-else class="error-container">
        <el-empty description="未找到包详情" />
      </div>

      <el-divider />

      <h3>同步日志</h3>
      <el-table :data="syncLogs" style="width: 100%">
        <el-table-column prop="createdAt" label="时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="action" label="操作" width="120">
          <template #default="scope">
            <el-tag :type="getActionType(scope.row.action)">
              {{ getActionText(scope.row.action) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getLogStatusColor(scope.row.status)">
              {{ scope.row.statusText || getLogStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="详情" />
      </el-table>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'PackageDetail',
  data() {
    return {
      loading: true,
      packageDetail: null,
      syncLogs: []
    }
  },
  mounted() {
    this.fetchPackageDetail()
  },
  methods: {
    async fetchPackageDetail() {
      this.loading = true
      try {
        const id = parseInt(this.$route.params.id)

        // 调用Go后端获取包详情
        const result = await window.go.main.App.GetPackageDetail(id)
        this.packageDetail = result.packageDetail
        this.syncLogs = result.syncLogs

        console.log('包详情:', this.packageDetail)
        console.log('同步日志:', this.syncLogs)
      } catch (error) {
        console.error('获取包详情失败:', error)
        this.$message.error('获取包详情失败: ' + error.toString())
      } finally {
        this.loading = false
      }
    },
    goBack() {
      this.$router.push('/library')
    },
    async runPackage() {
      try {
        // 调用Go后端运行包
        await window.go.main.App.RunPackage(this.packageDetail.id)
        this.$message.success('应用启动成功')
      } catch (error) {
        console.error('启动应用失败:', error)
        this.$message.error('启动应用失败: ' + error.toString())
      }
    },
    async downloadPackage() {
      try {
        // 调用Go后端下载包
        const downloadID = await window.go.main.App.DownloadPackage(this.packageDetail.id)
        this.$message.info('开始下载，下载ID: ' + downloadID)

        // 可以在这里监听下载进度
        // window.runtime.EventsOn(downloadID, (progress) => {
        //   console.log('下载进度:', progress)
        // })
      } catch (error) {
        console.error('下载失败:', error)
        this.$message.error('下载失败: ' + error.toString())
      }
    },
    async updatePackage() {
      try {
        // 调用Go后端下载包（更新就是重新下载）
        const downloadID = await window.go.main.App.DownloadPackage(this.packageDetail.id)
        this.$message.info('开始更新，下载ID: ' + downloadID)
      } catch (error) {
        console.error('更新失败:', error)
        this.$message.error('更新失败: ' + error.toString())
      }
    },
    async createShortcut() {
      try {
        // 调用Go后端创建快捷方式
        await window.go.main.App.CreateShortcut(this.packageDetail.id)
        this.$message.success('快捷方式创建成功')
      } catch (error) {
        console.error('创建快捷方式失败:', error)
        this.$message.error('创建快捷方式失败: ' + error.toString())
      }
    },
    formatFileSize(bytes) {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    formatDate(dateStr) {
      if (!dateStr) return '从未运行'

      const date = new Date(dateStr)

      // 检查是否是无效日期或零值时间
      if (isNaN(date.getTime()) || date.getFullYear() < 1900) {
        return '从未运行'
      }

      // 格式化为 YYYY-MM-DD HH:MM:SS
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    getStatusType(status) {
      const types = {
        0: 'info',    // 未同步
        1: 'warning', // 下载中
        2: 'success', // 已下载
        3: 'primary', // 解压中
        4: 'success', // 已完成
        5: 'danger'   // 失败
      }
      return types[status] || 'info'
    },
    getStatusText(status) {
      const texts = {
        0: '未下载',
        1: '下载中',
        2: '已下载',
        3: '解压中',
        4: '可运行',
        5: '下载失败'
      }
      return texts[status] || '未知'
    },
    getActionType(action) {
      const types = {
        'download': 'success',
        'sync': 'primary',
        'update': 'warning',
        'run': 'info',
        'error': 'danger'
      }
      return types[action] || 'info'
    },
    getActionText(action) {
      const texts = {
        'download': '下载',
        'sync': '同步',
        'update': '更新',
        'run': '运行',
        'error': '错误',
        'extract': '解压',
        'create': '创建',
        'shortcut': '快捷方式'
      }
      return texts[action] || action
    },
    getLogStatusColor(status) {
      const colors = {
        0: 'success',  // 成功
        1: 'warning',  // 进行中
        2: 'danger'    // 失败
      }
      return colors[status] || 'info'
    },
    getLogStatusText(status) {
      const texts = {
        0: '成功',
        1: '进行中',
        2: '失败'
      }
      return texts[status] || '未知'
    }
  }
}
</script>

<style scoped>
.package-detail {
  max-width: 1000px;
  margin: 0 auto;
}

.header-actions {
  display: flex;
  align-items: center;
}

.package-info {
  margin-bottom: 20px;
}

.action-buttons {
  margin-top: 20px;
  text-align: right;
}

.loading-container {
  padding: 20px;
}

.error-container {
  padding: 40px 0;
  text-align: center;
}
</style>
