# 学生端包管理系统

这是学校端包管理系统的配套学生客户端，用于从学校端同步和管理包版本，并提供一键运行功能。

## 技术栈

- **后端**：Go语言
- **前端/UI**：Wails框架 + Vue 3 + Element Plus
- **数据存储**：SQLite + 本地文件系统
- **打包**：Go编译 + Wails打包工具

## 项目结构

```
/student-client/
├── frontend/                # Vue 3前端
│   ├── public/              # 静态资源
│   ├── src/                 # 源代码
│   │   ├── assets/          # 资源文件
│   │   ├── components/      # 组件
│   │   ├── views/           # 视图
│   │   ├── router/          # 路由
│   │   ├── store/           # 状态管理
│   │   ├── utils/           # 工具函数
│   │   ├── api/             # API请求
│   │   ├── App.vue          # 根组件
│   │   └── main.js          # 入口文件
│   ├── package.json         # 依赖配置
│   └── vite.config.js       # Vite配置
├── app.go                   # 应用主入口
├── main.go                  # 主程序
├── models/                  # 数据模型
│   ├── db.go                # 数据库初始化
│   ├── package.go           # 包模型
│   ├── config.go            # 配置模型
│   └── sync_log.go          # 同步日志模型
├── services/                # 服务
│   ├── sync_service.go      # 同步服务
│   ├── package_service.go   # 包管理服务
│   └── extract_service.go   # 解压服务
├── utils/                   # 工具函数
│   ├── http.go              # HTTP工具
│   ├── file.go              # 文件工具
│   └── logger.go            # 日志工具
├── wails.json               # Wails配置
├── go.mod                   # Go模块文件
├── go.sum                   # Go依赖校验文件
├── build/                   # 构建输出目录
└── downloads/               # 下载的包文件存储目录
```

## 功能规划

### 1. 基础功能

- **配置管理**：
  - 设置学校端服务器地址和端口
  - 保存和加载配置

- **包列表展示**：
  - 从学校端获取包列表
  - 显示包名称、版本、描述等信息
  - 支持筛选和搜索

- **包同步**：
  - 检查新版本
  - 下载包文件
  - 显示下载进度
  - 验证文件完整性

- **包管理**：
  - 解压包文件
  - 管理本地已安装的包
  - 支持版本回退

- **一键运行**：
  - 直接启动已安装的包
  - 创建桌面快捷方式

### 2. 高级功能

- **自动更新**：
  - 定期检查新版本
  - 后台自动下载和安装

- **断点续传**：
  - 支持大文件下载中断后继续
  - 避免重复下载

- **智能解压**：
  - 自动识别压缩格式
  - 保留配置文件
  - 增量更新

- **日志记录**：
  - 记录同步和运行日志
  - 支持日志查看和导出

## 数据模型设计

### 1. 配置模型 (Config)

```go
type Config struct {
    ID        int64
    Key       string
    Value     string
    UpdatedAt time.Time
}
```

### 2. 包模型 (Package)

```go
type Package struct {
    ID                  int64
    PackageVersionID    string
    ExperimentID        string
    ExperimentName      string
    ExperimentVersionID string
    Version             string
    VersionName         string
    VersionDesc         string
    FileHash            string
    FilePath            string
    FileSize            int64
    DownloadURL         string
    LocalPath           string
    ExtractPath         string
    ExecutablePath      string
    SyncStatus          int
    DownloadCount       int
    LastRun             time.Time
    CreatedAt           time.Time
    UpdatedAt           time.Time
}
```

### 3. 同步日志模型 (SyncLog)

```go
type SyncLog struct {
    ID        int64
    PackageID int64
    Action    string
    Status    int
    Message   string
    CreatedAt time.Time
}
```

## 开发计划

### 阶段一：基础架构搭建

1. 初始化Wails项目
2. 设置Vue 3 + Element Plus前端
3. 实现数据库初始化和模型定义
4. 创建基本UI布局

### 阶段二：核心功能实现

1. 实现与学校端的通信
2. 开发包列表获取和展示功能
3. 实现包下载和进度显示
4. 开发文件解压和管理功能

### 阶段三：一键运行功能

1. 实现可执行文件识别
2. 开发一键运行功能
3. 添加桌面快捷方式创建

### 阶段四：高级功能和优化

1. 实现自动更新功能
2. 添加断点续传支持
3. 优化UI和用户体验
4. 完善日志和错误处理

### 阶段五：测试和打包

1. 单元测试和集成测试
2. 性能优化
3. 使用Wails打包成可执行文件
4. 创建安装程序

## 技术要点

1. **与学校端通信**：
   - 使用HTTP客户端与学校端API交互
   - 处理认证和会话管理

2. **文件下载**：
   - 支持大文件下载
   - 显示下载进度
   - 实现断点续传

3. **文件解压**：
   - 支持多种压缩格式
   - 处理文件权限
   - 实现增量更新

4. **数据存储**：
   - 使用SQLite存储包信息和配置
   - 管理本地文件系统

5. **UI交互**：
   - 响应式设计
   - 进度显示
   - 状态通知

## 安装和使用

### 开发环境

1. 安装Wails CLI：
   ```bash
   go install github.com/wailsapp/wails/v2/cmd/wails@latest
   ```

2. 安装前端依赖：
   ```bash
   cd frontend
   npm install
   ```

3. 运行开发服务器：
   ```bash
   wails dev
   ```

### 构建

```bash
wails build
```

## 许可证

MIT
