# 学生端包管理系统 - 开发规划与技术实现

## 1. 系统架构

### 1.1 整体架构

学生端包管理系统采用前后端一体化的桌面应用架构，使用Wails框架将Go后端与Vue前端结合起来。

```
+----------------------------------+
|            学生端客户端           |
+----------------------------------+
|  +------------+  +------------+  |
|  |   Vue 3    |  |    Go     |  |
|  | 前端界面    |<->|  后端逻辑  |  |
|  +------------+  +------------+  |
|           |            |         |
|  +------------+  +------------+  |
|  |  SQLite    |  | 文件系统   |  |
|  |  数据存储   |  | 包管理    |  |
|  +------------+  +------------+  |
+----------------------------------+
           |
           v
+----------------------------------+
|          学校端服务器            |
+----------------------------------+
```

### 1.2 技术栈详解

- **Wails**：桌面应用框架，将Go与Web技术结合
- **Go**：后端逻辑，处理文件下载、解压和管理
- **Vue 3**：前端界面，提供用户交互
- **Element Plus**：UI组件库，提供美观的界面元素
- **SQLite**：嵌入式数据库，存储包信息和配置
- **Vite**：前端构建工具，提供快速的开发体验

## 2. 核心功能实现

### 2.1 与学校端通信

#### 2.1.1 API封装

```go
// SchoolServerClient 学校服务器客户端
type SchoolServerClient struct {
    BaseURL    string
    HTTPClient *http.Client
}

// GetPackageList 获取包列表
func (c *SchoolServerClient) GetPackageList() ([]Package, error) {
    // 实现与学校端API通信，获取包列表
}

// DownloadPackage 下载包文件
func (c *SchoolServerClient) DownloadPackage(packageID string, savePath string) error {
    // 实现文件下载，支持断点续传
}
```

#### 2.1.2 前端API调用

```javascript
// 在Vue组件中调用Go后端函数
import { GetPackageList, DownloadPackage } from '../../wailsjs/go/services/PackageService'

export default {
  data() {
    return {
      packages: []
    }
  },
  methods: {
    async fetchPackages() {
      try {
        this.packages = await GetPackageList()
      } catch (error) {
        console.error('获取包列表失败:', error)
      }
    }
  }
}
```

### 2.2 包下载与管理

#### 2.2.1 下载服务

```go
// DownloadService 下载服务
type DownloadService struct {
    Client      *SchoolServerClient
    DownloadDir string
}

// DownloadWithProgress 带进度的下载
func (s *DownloadService) DownloadWithProgress(pkg *Package) (chan DownloadProgress, error) {
    // 实现带进度的下载，返回进度通道
}

// DownloadProgress 下载进度
type DownloadProgress struct {
    Total     int64
    Downloaded int64
    Percentage float64
}
```

#### 2.2.2 解压服务

```go
// ExtractService 解压服务
type ExtractService struct {
    ExtractDir string
}

// Extract 解压文件
func (s *ExtractService) Extract(filePath string, destDir string) error {
    // 根据文件类型选择解压方法
    switch filepath.Ext(filePath) {
    case ".zip":
        return s.extractZip(filePath, destDir)
    case ".tar", ".tgz", ".tar.gz":
        return s.extractTarGz(filePath, destDir)
    // 其他格式...
    default:
        return fmt.Errorf("不支持的文件格式: %s", filepath.Ext(filePath))
    }
}
```

### 2.3 一键运行功能

#### 2.3.1 可执行文件识别

```go
// FindExecutable 查找可执行文件
func FindExecutable(dir string) (string, error) {
    // 在解压目录中查找可执行文件
    var exePath string

    err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
        if err != nil {
            return err
        }

        // 检查是否是可执行文件
        if !info.IsDir() && (filepath.Ext(path) == ".exe" || isExecutable(path)) {
            exePath = path
            return filepath.SkipDir // 找到后停止遍历
        }

        return nil
    })

    if exePath == "" {
        return "", errors.New("未找到可执行文件")
    }

    return exePath, err
}
```

#### 2.3.2 应用启动

```go
// RunPackage 运行包
func (s *PackageService) RunPackage(packageID int64) error {
    // 获取包信息
    pkg, err := s.GetPackageByID(packageID)
    if err != nil {
        return err
    }

    // 检查可执行文件路径
    if pkg.ExecutablePath == "" {
        return errors.New("未找到可执行文件")
    }

    // 启动应用
    cmd := exec.Command(pkg.ExecutablePath)
    cmd.Dir = filepath.Dir(pkg.ExecutablePath)

    return cmd.Start()
}
```

### 2.4 数据库设计

#### 2.4.1 数据库初始化

```go
// InitDB 初始化数据库
func InitDB() error {
    var err error
    DB, err = sql.Open("sqlite3", "./student-client.db")
    if err != nil {
        return err
    }

    // 创建表
    err = createTables()
    if err != nil {
        return err
    }

    return nil
}

// createTables 创建表
func createTables() error {
    // 创建配置表
    _, err := DB.Exec(`
    CREATE TABLE IF NOT EXISTS configs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT NOT NULL UNIQUE,
        value TEXT NOT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`)
    if err != nil {
        return err
    }

    // 创建包表
    _, err = DB.Exec(`
    CREATE TABLE IF NOT EXISTS packages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        package_version_id TEXT NOT NULL UNIQUE,
        experiment_id TEXT NOT NULL,
        experiment_name TEXT NOT NULL,
        experiment_version_id TEXT NOT NULL,
        version TEXT NOT NULL,
        version_name TEXT NOT NULL,
        version_desc TEXT,
        file_hash TEXT NOT NULL,
        file_path TEXT,
        file_size INTEGER DEFAULT 0,
        download_url TEXT NOT NULL,
        local_path TEXT,
        extract_path TEXT,
        executable_path TEXT,
        sync_status INTEGER DEFAULT 0,
        download_count INTEGER DEFAULT 0,
        last_run TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`)
    if err != nil {
        return err
    }

    // 创建同步日志表
    _, err = DB.Exec(`
    CREATE TABLE IF NOT EXISTS sync_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        package_id INTEGER NOT NULL,
        action TEXT NOT NULL,
        status INTEGER NOT NULL,
        message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (package_id) REFERENCES packages (id)
    )`)

    return err
}
```

## 3. 用户界面设计

### 3.1 主界面布局

```vue
<template>
  <el-container class="main-container">
    <el-header>
      <app-header />
    </el-header>

    <el-container>
      <el-aside width="200px">
        <app-sidebar />
      </el-aside>

      <el-main>
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>
```

### 3.2 包列表页面

```vue
<template>
  <div class="package-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <h2>实验包列表</h2>
          <el-button type="primary" @click="refreshPackages">刷新</el-button>
        </div>
      </template>

      <el-table :data="packages" v-loading="loading">
        <el-table-column prop="experimentName" label="实验名称" />
        <el-table-column prop="versionName" label="版本名称" />
        <el-table-column prop="version" label="版本号" />
        <el-table-column label="状态">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.syncStatus)">
              {{ getStatusText(scope.row.syncStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250">
          <template #default="scope">
            <el-button
              v-if="scope.row.syncStatus === 2"
              type="success"
              @click="runPackage(scope.row.id)"
            >
              运行
            </el-button>
            <el-button
              v-else
              type="primary"
              @click="downloadPackage(scope.row.id)"
              :loading="scope.row.syncStatus === 1"
            >
              下载
            </el-button>
            <el-button type="info" @click="viewDetails(scope.row.id)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>
```

## 4. 打包与部署

### 4.1 Wails打包配置

```json
{
  "name": "学生端包管理系统",
  "outputfilename": "student-client",
  "frontend:install": "npm install",
  "frontend:build": "npm run build",
  "frontend:dev:watcher": "npm run dev",
  "frontend:dev:serverUrl": "auto",
  "author": {
    "name": "Your Name",
    "email": "<EMAIL>"
  }
}
```

### 4.2 构建脚本

```bash
#!/bin/bash
# 构建Windows版本
wails build -platform windows/amd64

# 可选：使用NSIS创建安装程序
# makensis installer.nsi
```

## 5. 测试计划

### 5.1 单元测试

为核心功能编写单元测试，特别是：
- 文件下载和校验
- 解压功能
- 数据库操作

### 5.2 集成测试

测试完整流程：
- 从学校端获取包列表
- 下载包文件
- 解压和安装
- 运行应用

### 5.3 用户体验测试

- 界面友好性
- 错误处理和提示
- 性能和响应速度

## 6. 后续优化方向

1. **增量更新**：只下载和更新变化的文件
2. **自动更新**：客户端自身的更新功能
3. **离线模式**：支持在无网络环境下使用已下载的包
4. **多语言支持**：国际化
5. **主题切换**：明暗主题支持

## 开发日志

### 2024-12-19
- 完成学生端基础架构搭建
- 实现与学校端的基本通信
- 完成包列表同步功能
- 实现下载和解压功能
- 添加进度显示和错误处理
- **新增功能：下载统计和日志记录**
  - 在学校端创建下载日志表(download_logs)
  - 实现下载次数统计功能
  - 学生端下载完成后自动向学校端发送下载记录
  - 记录学生端MAC地址、IP地址、下载时间等信息
  - 添加网络工具函数获取本机网络信息
  - 学校端提供下载日志查询API
