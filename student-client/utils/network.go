package utils

import (
	"fmt"
	"net"
	"strings"
)

// GetMACAddress 获取本机MAC地址
func GetMACAddress() (string, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return "", err
	}

	for _, iface := range interfaces {
		// 跳过回环接口和虚拟接口
		if iface.Flags&net.FlagLoopback != 0 || iface.Flags&net.FlagUp == 0 {
			continue
		}

		// 跳过虚拟网络接口（如Docker、VMware等）
		name := strings.ToLower(iface.Name)
		if strings.Contains(name, "docker") ||
			strings.Contains(name, "veth") ||
			strings.Contains(name, "br-") ||
			strings.Contains(name, "vmnet") ||
			strings.Contains(name, "vbox") ||
			strings.Contains(name, "virbr") ||
			strings.Contains(name, "lo") {
			continue
		}

		// 获取MAC地址
		if len(iface.HardwareAddr) >= 6 {
			return iface.HardwareAddr.String(), nil
		}
	}

	return "", fmt.Errorf("未找到有效的MAC地址")
}

// GetLocalIP 获取本机IP地址
func GetLocalIP() (string, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return "", err
	}

	for _, iface := range interfaces {
		// 跳过回环接口和未启用的接口
		if iface.Flags&net.FlagLoopback != 0 || iface.Flags&net.FlagUp == 0 {
			continue
		}

		// 跳过虚拟网络接口
		name := strings.ToLower(iface.Name)
		if strings.Contains(name, "docker") ||
			strings.Contains(name, "veth") ||
			strings.Contains(name, "br-") ||
			strings.Contains(name, "vmnet") ||
			strings.Contains(name, "vbox") ||
			strings.Contains(name, "virbr") {
			continue
		}

		addrs, err := iface.Addrs()
		if err != nil {
			continue
		}

		for _, addr := range addrs {
			var ip net.IP
			switch v := addr.(type) {
			case *net.IPNet:
				ip = v.IP
			case *net.IPAddr:
				ip = v.IP
			}

			// 跳过回环地址和IPv6地址
			if ip == nil || ip.IsLoopback() || ip.To4() == nil {
				continue
			}

			// 优先返回私有网络地址
			if isPrivateIP(ip) {
				return ip.String(), nil
			}
		}
	}

	return "", fmt.Errorf("未找到有效的IP地址")
}

// isPrivateIP 判断是否为私有IP地址
func isPrivateIP(ip net.IP) bool {
	if ip.IsLoopback() || ip.IsLinkLocalMulticast() || ip.IsLinkLocalUnicast() {
		return false
	}

	// 检查私有网络地址范围
	privateBlocks := []*net.IPNet{
		{IP: net.IPv4(10, 0, 0, 0), Mask: net.CIDRMask(8, 32)},     // 10.0.0.0/8
		{IP: net.IPv4(172, 16, 0, 0), Mask: net.CIDRMask(12, 32)},  // **********/12
		{IP: net.IPv4(192, 168, 0, 0), Mask: net.CIDRMask(16, 32)}, // ***********/16
	}

	for _, block := range privateBlocks {
		if block.Contains(ip) {
			return true
		}
	}

	return false
}

// GetNetworkInfo 获取网络信息（MAC地址和IP地址）
func GetNetworkInfo() (mac string, ip string, err error) {
	mac, err = GetMACAddress()
	if err != nil {
		return "", "", fmt.Errorf("获取MAC地址失败: %v", err)
	}

	ip, err = GetLocalIP()
	if err != nil {
		return "", "", fmt.Errorf("获取IP地址失败: %v", err)
	}

	return mac, ip, nil
}
