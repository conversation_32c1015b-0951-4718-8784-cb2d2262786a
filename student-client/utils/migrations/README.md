# 学生端数据库迁移系统

## 概述

学生端迁移系统提供了一套完整的版本管理和数据库迁移机制，确保程序在版本升级时能够正确处理数据库结构变更、配置更新等操作。

## 核心特性

1. **集中化版本管理** - 所有版本信息集中在 `version_config.go` 中管理
2. **线性迁移** - 支持从任意老版本升级到最新版本
3. **自动版本同步** - 程序版本自动从配置获取
4. **迁移日志** - 完整记录迁移过程和结果
5. **错误回滚** - 迁移失败时自动回滚

## 目录结构

```
utils/migrations/
├── README.md              # 本文档
├── version_config.go      # 版本配置（唯一需要修改的文件）
├── migration.go           # 迁移接口定义
├── common.go             # 通用迁移方法
└── migration_XXX_to_YYY.go # 具体的迁移实现
```

## 新增版本步骤

### 1. 创建迁移脚本

为新版本创建迁移脚本文件，例如 `migration_25052901_to_25053001.go`：

```go
package migrations

import (
	"fmt"
)

// Migration_25052901_to_25053001 从版本25052901升级到25053001的迁移
type Migration_25052901_to_25053001 struct{}

func (m *Migration_25052901_to_25053001) Version() string {
	return "25053001"
}

func (m *Migration_25052901_to_25053001) Description() string {
	return "升级到版本25053001：添加新功能"
}

func (m *Migration_25052901_to_25053001) Execute(ctx *MigrationContext) error {
	// 1. 更新学生端程序文件
	if err := UpdateStudentClientFromVersion(ctx, "25053001"); err != nil {
		return err
	}

	// 2. 数据库结构变更
	sqls := []string{
		`ALTER TABLE packages ADD COLUMN new_field TEXT DEFAULT ''`,
		`CREATE INDEX IF NOT EXISTS idx_packages_new_field ON packages(new_field)`,
	}
	for _, sql := range sqls {
		if _, err := ctx.DB.Exec(sql); err != nil {
			return fmt.Errorf("执行SQL失败: %s, 错误: %v", sql, err)
		}
	}

	// 3. 配置文件更新
	if err := EnsureDir("./config/v25053001"); err != nil {
		return err
	}

	return nil
}

func (m *Migration_25052901_to_25053001) Rollback(ctx *MigrationContext) error {
	// 回滚操作（可选实现）
	ctx.Logger.Warn("Migration", "Rollback", "版本25053001回滚操作")
	return nil
}
```

### 2. 更新版本配置

**这是唯一需要修改的配置文件！**

编辑 `version_config.go` 文件中的 `GetVersionConfig()` 函数：

```go
func GetVersionConfig() *VersionConfig {
	// ===== 新增版本时，只需要修改这里 =====

	// 1. 定义版本升级路径（按时间顺序，最后一个就是当前版本）
	versionPath := []string{
		"25052901", // 初始版本
		"25053001", // 新增版本在这里添加
		// 新版本在这里继续添加...
	}

	// 2. 注册迁移实例（每个版本对应一个迁移）
	migrationMap := map[string]Migration{
		"25053001": &Migration_25052901_to_25053001{}, // 新增迁移在这里添加
		// 新迁移在这里继续添加...
	}

	// ===== 修改结束 =====

	// 当前版本自动从versionPath的最后一个元素获取
	currentVersion := versionPath[len(versionPath)-1]
}
```

### 3. 测试验证

在测试环境验证迁移脚本：

```bash
# 运行程序，观察迁移日志
./student-client

# 检查数据库版本
sqlite3 ./data/student-client.db "SELECT * FROM app_versions ORDER BY installed_at DESC;"

# 检查迁移日志
sqlite3 ./data/student-client.db "SELECT * FROM migration_logs ORDER BY executed_at DESC;"
```

## 迁移系统工作流程

1. **程序启动** → 检查数据库版本 → 执行必要的迁移
2. **版本检查** → 比较程序版本和数据库版本
3. **线性迁移** → 按版本路径顺序执行所有中间迁移
4. **迁移执行** → 逐个执行迁移脚本
5. **版本更新** → 更新数据库中的当前版本记录
6. **日志记录** → 记录迁移过程和结果

## 常用迁移操作

### 数据库结构变更

```go
// 添加新表
_, err := ctx.DB.Exec(`
	CREATE TABLE IF NOT EXISTS new_table (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		name TEXT NOT NULL,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP
	)
`)

// 添加新字段
_, err := ctx.DB.Exec(`
	ALTER TABLE packages ADD COLUMN new_field TEXT DEFAULT ''
`)

// 创建索引
_, err := ctx.DB.Exec(`
	CREATE INDEX IF NOT EXISTS idx_packages_new_field ON packages(new_field)
`)
```

### 配置文件更新

```go
// 创建配置目录
if err := EnsureDir("./config/v25053001"); err != nil {
	return err
}

// 复制配置文件
// 实现具体的配置文件操作
```

### 数据迁移

```go
// 数据转换
rows, err := ctx.DB.Query("SELECT id, old_field FROM packages")
if err != nil {
	return err
}
defer rows.Close()

for rows.Next() {
	var id int64
	var oldValue string
	if err := rows.Scan(&id, &oldValue); err != nil {
		return err
	}
	
	// 转换数据
	newValue := transformData(oldValue)
	
	// 更新数据
	_, err := ctx.DB.Exec("UPDATE packages SET new_field = ? WHERE id = ?", newValue, id)
	if err != nil {
		return err
	}
}
```

## 注意事项

1. **版本号格式**：使用8位数字格式，如 `25052901`（年月日版本）
2. **迁移顺序**：严格按照版本路径顺序执行迁移
3. **错误处理**：迁移失败时会自动回滚，确保数据一致性
4. **备份机制**：迁移前会自动创建数据库备份
5. **日志记录**：所有迁移操作都会记录详细日志

## 故障排查

### 查看迁移日志

```bash
# 查看应用日志
tail -f ./logs/student-client-*.log | grep Migration

# 查看数据库迁移记录
sqlite3 ./data/student-client.db "SELECT * FROM migration_logs ORDER BY executed_at DESC LIMIT 10;"
```

### 手动修复版本

```bash
# 查看当前版本
sqlite3 ./data/student-client.db "SELECT * FROM app_versions WHERE is_current = 1;"

# 手动设置版本（谨慎操作）
sqlite3 ./data/student-client.db "UPDATE app_versions SET is_current = 0; INSERT OR REPLACE INTO app_versions (version, is_current, installed_at) VALUES ('25053001', 1, datetime('now'));"
```

## 最佳实践

1. **小步迁移**：每个版本的迁移保持简单，避免复杂操作
2. **测试优先**：在测试环境充分验证迁移脚本
3. **备份重要**：重要升级前手动备份数据
4. **日志详细**：在迁移脚本中添加详细的日志记录
5. **回滚准备**：为重要迁移准备回滚方案

就这么简单！系统会自动处理版本同步、迁移注册等所有细节。
