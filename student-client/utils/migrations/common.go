package migrations

import (
	"fmt"
	"os"
)

// UpdateStudentClientFromVersion 通用的学生端更新方法
// 从appVersions表中获取指定版本的学生端文件路径，替换当前可执行文件
func UpdateStudentClientFromVersion(ctx *MigrationContext, version string) error {
	ctx.Logger.Info("Migration", "UpdateStudentClient", fmt.Sprintf("开始更新学生端程序，版本: %s", version))

	// 从数据库获取指定版本的学生端文件路径
	query := `SELECT student_client_path FROM app_versions WHERE version = ? AND student_client_path != ''`
	var studentClientPath string
	err := ctx.DB.QueryRow(query, version).Scan(&studentClientPath)
	if err != nil {
		return fmt.Errorf("获取学生端文件路径失败: %v", err)
	}

	if studentClientPath == "" {
		ctx.Logger.Info("Migration", "UpdateStudentClient", "没有学生端文件需要更新")
		return nil
	}

	// 检查文件是否存在
	if _, err := os.Stat(studentClientPath); err != nil {
		return fmt.Errorf("学生端文件不存在: %s", studentClientPath)
	}

	ctx.Logger.Info("Migration", "UpdateStudentClient", fmt.Sprintf("学生端文件路径: %s", studentClientPath))

	// 注意：在实际的迁移中，学生端程序的更新应该由外部更新器处理
	// 这里只是记录日志，实际的文件替换会在程序重启后由更新器完成
	ctx.Logger.Info("Migration", "UpdateStudentClient", "学生端程序更新将在重启后完成")

	return nil
}

// EnsureDir 确保目录存在的通用函数
func EnsureDir(dir string) error {
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败 %s: %v", dir, err)
	}
	return nil
}
