package utils

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"
)

const (
	// 最大日志文件大小（10MB）
	maxLogSize = 10 * 1024 * 1024
	// 最大日志文件数量
	maxLogFiles = 10
)

var (
	// 日志文件
	logFile *os.File
	// 日志目录
	logDir = "./logs"
	// 当前日志文件路径
	currentLogPath string
	// 当前日志文件大小
	currentLogSize int64
	// 全局日志器实例
	globalLogger *Logger
)

// Logger 日志器结构体
type Logger struct {
	prefix string
}

// NewLogger 创建新的日志器
func NewLogger(prefix string) *Logger {
	return &Logger{
		prefix: prefix,
	}
}

// GetLogger 获取全局日志器实例
func GetLogger() (*Logger, error) {
	if globalLogger == nil {
		globalLogger = NewLogger("StudentClient")
	}
	return globalLogger, nil
}

// Info 记录信息日志
func (l *Logger) Info(module, action, message string) {
	LogPrintf("[INFO] [%s] [%s] [%s] %s", l.prefix, module, action, message)
}

// Error 记录错误日志
func (l *Logger) Error(module, action, message string) {
	LogPrintf("[ERROR] [%s] [%s] [%s] %s", l.prefix, module, action, message)
}

// Warn 记录警告日志
func (l *Logger) Warn(module, action, message string) {
	LogPrintf("[WARN] [%s] [%s] [%s] %s", l.prefix, module, action, message)
}

// InitLogger 初始化日志系统
func InitLogger() error {
	// 确保日志目录存在
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	// 清理旧日志文件
	if err := cleanupOldLogs(); err != nil {
		fmt.Printf("清理旧日志文件失败: %v\n", err)
	}

	// 创建日志文件名（使用当前日期）
	logFileName := fmt.Sprintf("student-client-%s.log", time.Now().Format("2006-01-02"))
	currentLogPath = filepath.Join(logDir, logFileName)

	// 打开日志文件（如果不存在则创建）
	file, err := os.OpenFile(currentLogPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %v", err)
	}

	// 获取当前日志文件大小
	fileInfo, err := file.Stat()
	if err != nil {
		return fmt.Errorf("获取日志文件信息失败: %v", err)
	}
	currentLogSize = fileInfo.Size()

	// 创建多输出写入器，同时输出到控制台和文件
	multiWriter := io.MultiWriter(os.Stdout, file)

	// 设置日志输出到多输出写入器
	log.SetOutput(multiWriter)
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	// 保存文件句柄以便后续关闭
	logFile = file

	// 记录启动日志
	log.Println("日志系统初始化成功")

	return nil
}

// CloseLogger 关闭日志系统
func CloseLogger() {
	if logFile != nil {
		log.Println("关闭日志系统")
		logFile.Close()
		logFile = nil
	}
}

// LogPrintf 打印格式化日志，并检查是否需要轮转日志文件
func LogPrintf(format string, v ...interface{}) {
	// 打印日志
	message := fmt.Sprintf(format, v...)
	log.Print(message)

	// 更新日志文件大小
	currentLogSize += int64(len(message) + 1) // +1 for newline

	// 检查是否需要轮转日志文件
	if currentLogSize > maxLogSize {
		rotateLogFile()
	}
}

// LogPrintln 打印一行日志，并检查是否需要轮转日志文件
func LogPrintln(v ...interface{}) {
	// 打印日志
	message := fmt.Sprintln(v...)
	log.Print(message)

	// 更新日志文件大小
	currentLogSize += int64(len(message))

	// 检查是否需要轮转日志文件
	if currentLogSize > maxLogSize {
		rotateLogFile()
	}
}

// rotateLogFile 轮转日志文件
func rotateLogFile() {
	// 关闭当前日志文件
	if logFile != nil {
		log.Println("轮转日志文件")
		logFile.Close()
		logFile = nil
	}

	// 创建新的日志文件名（使用当前时间戳）
	timestamp := time.Now().Format("2006-01-02-150405")
	logFileName := fmt.Sprintf("student-client-%s.log", timestamp)
	currentLogPath = filepath.Join(logDir, logFileName)

	// 打开新的日志文件
	file, err := os.OpenFile(currentLogPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		fmt.Printf("打开新日志文件失败: %v\n", err)
		return
	}

	// 创建多输出写入器，同时输出到控制台和文件
	multiWriter := io.MultiWriter(os.Stdout, file)

	// 设置日志输出到多输出写入器
	log.SetOutput(multiWriter)

	// 保存文件句柄以便后续关闭
	logFile = file

	// 重置日志文件大小
	currentLogSize = 0

	// 清理旧日志文件
	if err := cleanupOldLogs(); err != nil {
		fmt.Printf("清理旧日志文件失败: %v\n", err)
	}

	// 记录日志轮转信息
	log.Println("日志文件已轮转")
}

// cleanupOldLogs 清理旧日志文件，只保留最近的maxLogFiles个文件
func cleanupOldLogs() error {
	// 读取日志目录中的所有文件
	files, err := os.ReadDir(logDir)
	if err != nil {
		return err
	}

	// 筛选出日志文件
	var logFiles []string
	for _, file := range files {
		if !file.IsDir() && strings.HasPrefix(file.Name(), "student-client-") && strings.HasSuffix(file.Name(), ".log") {
			logFiles = append(logFiles, file.Name())
		}
	}

	// 如果日志文件数量小于等于最大数量，不需要清理
	if len(logFiles) <= maxLogFiles {
		return nil
	}

	// 按文件名排序（文件名包含日期，所以按字母排序即可）
	sort.Strings(logFiles)

	// 删除最旧的文件，直到文件数量等于最大数量
	for i := 0; i < len(logFiles)-maxLogFiles; i++ {
		filePath := filepath.Join(logDir, logFiles[i])
		if err := os.Remove(filePath); err != nil {
			return err
		}
	}

	return nil
}
