package utils

import (
	"bufio"
	"fmt"
	"io"
	"os"
	"path/filepath"

	"github.com/cespare/xxhash"
)

// EnsureDirectories 确保必要的目录存在
func EnsureDirectories(dirs []string) error {
	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建目录失败 %s: %v", dir, err)
		}
	}
	return nil
}

// EnsureDir 确保单个目录存在
func EnsureDir(dir string) error {
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败 %s: %v", dir, err)
	}
	return nil
}

// CalculateFileHash 计算文件哈希值（使用xxHash算法）
func CalculateFileHash(filePath string) (string, error) {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 创建xxHash64摘要器
	h := xxhash.New()

	// 分块读取文件并更新哈希
	buffer := make([]byte, 4*1024*1024) // 4MB 缓冲区，与学校端保持一致
	reader := bufio.NewReader(file)

	for {
		n, err := reader.Read(buffer)
		if err != nil && err != io.EOF {
			return "", err
		}

		if n == 0 {
			break
		}

		h.Write(buffer[:n])
	}

	// 获取哈希值并转换为16位十六进制字符串
	hashValue := h.Sum64()
	hashHex := fmt.Sprintf("%016x", hashValue)

	return hashHex, nil
}

// FormatFileSize 格式化文件大小
func FormatFileSize(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// GetAvailableDiskSpace 获取可用磁盘空间
func GetAvailableDiskSpace(path string) (uint64, error) {
	// 确保目录存在
	if err := os.MkdirAll(path, 0755); err != nil {
		return 0, err
	}

	// 创建一个临时文件来测试磁盘空间
	tempFile := filepath.Join(path, ".diskspace_test")
	f, err := os.Create(tempFile)
	if err != nil {
		return 0, err
	}
	defer func() {
		f.Close()
		os.Remove(tempFile)
	}()

	// 尝试写入一些数据
	data := make([]byte, 1024)
	var totalWritten uint64 = 0
	for {
		_, err := f.Write(data)
		if err != nil {
			break
		}
		totalWritten += 1024
		if totalWritten > 1024*1024*10 { // 最多测试10MB
			break
		}
	}

	// 这只是一个简单的估计，实际上应该使用系统调用获取磁盘信息
	// 但这需要特定于操作系统的代码
	return totalWritten, nil
}
