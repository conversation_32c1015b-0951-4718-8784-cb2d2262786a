# 文档目录

本目录包含学生客户端项目的相关技术文档和设计方案。

## 文档列表

### 1. [编译.md](./编译.md)
- **用途**：编译构建相关说明
- **适用人员**：开发人员、运维人员

### 2. [自更新设计方案.md](./自更新设计方案.md)
- **用途**：完整的自更新系统设计方案
- **内容**：架构设计、版本管理、数据库迁移、安全机制等
- **适用人员**：架构师、产品经理、开发团队

### 3. [自更新技术实现指南.md](./自更新技术实现指南.md)
- **用途**：自更新功能的具体技术实现
- **内容**：Go代码示例、Wails集成、测试策略等
- **适用人员**：开发人员、测试人员

### 4. [三级程序自更新架构.md](./三级程序自更新架构.md) ⭐️ **重点推荐**
- **用途**：三级系统（中央端-学校端-学生端）程序自更新架构
- **内容**：专注于程序版本升级，包含推送机制、自替换更新、安全验证等
- **适用人员**：系统架构师、项目经理、技术负责人
- **备注**：针对本项目实际需求的程序自更新专用方案，不涉及实验包更新

## 如何使用这些文档

### 项目规划阶段
1. 阅读 `自更新设计方案.md` 了解整体架构
2. 根据项目需求调整方案细节
3. 制定开发计划和里程碑

### 开发实施阶段
1. 参考 `自更新技术实现指南.md` 进行编码
2. 使用文档中的代码示例作为起点
3. 根据具体业务需求进行调整

### 测试验证阶段
1. 参考测试策略部分设计测试用例
2. 验证各个功能模块的正确性
3. 进行端到端测试

### 部署运维阶段
1. 参考部署章节进行服务器配置
2. 设置监控和告警机制
3. 建立运维流程和应急响应

## 文档维护

### 更新原则
- 技术方案变更时及时更新文档
- 保持文档与实际实现的一致性
- 记录重要的设计决策和变更原因

### 版本控制
- 文档使用Git进行版本控制
- 重大变更需要通过代码审查
- 维护文档变更日志

## 相关资源

### 外部依赖
- [Wails框架文档](https://wails.io/zh-Hans/docs/introduction)
- [Go语言官方文档](https://golang.org/doc/)
- [SQLite文档](https://www.sqlite.org/docs.html)

### 工具推荐
- **开发环境**：VS Code + Go插件
- **数据库工具**：DB Browser for SQLite
- **API测试**：Postman
- **文档编辑**：Typora 或 VS Code

## 贡献指南

如果您需要更新或补充文档内容，请：

1. 创建新的分支
2. 修改相应文档
3. 提交Pull Request
4. 等待团队审查

## 联系方式

如果对文档有疑问或建议，请：
- 创建Issue进行讨论
- 联系项目维护者
- 在团队会议中提出

---

*最后更新：2024年1月* 