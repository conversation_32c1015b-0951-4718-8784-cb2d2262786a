# 学生端自动更新系统

## 概述

学生端自动更新系统是从学校端自更新机制完整复刻而来的版本管理和自动更新解决方案。该系统提供了完整的版本管理、数据库迁移、自动更新检查和程序更新功能。

## 系统架构

### 核心组件

1. **版本管理系统** (`utils/migrations/`)
   - `version_config.go` - 集中化版本配置
   - `migration.go` - 迁移接口定义
   - `common.go` - 通用迁移方法
   - `version_manager.go` - 版本管理器

2. **更新服务** (`services/update_service.go`)
   - 自动检查更新
   - 下载更新文件
   - 启动外部更新器
   - 与学校端API对接

3. **数据库表结构**
   - `app_versions` - 应用版本记录
   - `migration_logs` - 迁移日志记录

4. **主程序集成** (`main.go`)
   - 启动时版本检查
   - 自动迁移执行
   - 更新服务初始化

## 版本管理机制

### 版本配置 (`utils/migrations/version_config.go`)

```go
func GetVersionConfig() *VersionConfig {
    // 版本升级路径（按时间顺序，最后一个是当前版本）
    versionPath := []string{
        "25052901", // 初始版本
        // 新版本在这里继续添加...
    }

    // 迁移映射（每个版本对应一个迁移）
    migrationMap := map[string]Migration{
        // "25053001": &Migration_25052901_to_25053001{},
        // 新迁移在这里继续添加...
    }

    // 当前版本自动从versionPath的最后一个元素获取
    currentVersion := versionPath[len(versionPath)-1]
}
```

### 版本管理器 (`utils/version_manager.go`)

- **线性迁移**: 支持从任意老版本升级到最新版本
- **自动备份**: 迁移前自动创建数据库备份
- **错误回滚**: 迁移失败时自动回滚
- **日志记录**: 完整记录迁移过程和结果

## 自动更新系统

### 更新服务特性

1. **启动时检查**: 程序启动后5秒自动检查更新
2. **学校端对接**: 通过学校端API获取更新信息
3. **哈希验证**: 下载文件后验证SHA256哈希
4. **外部更新器**: 使用独立更新器进行文件替换
5. **自动重启**: 更新完成后自动重启程序

### 更新流程

```
启动程序 → 版本检查 → 发现更新 → 下载文件 → 验证哈希 → 启动更新器 → 程序重启
```

### API对接

学生端通过以下API与学校端通信：

```
GET /api/student/update/check?currentVersionNumber={version}
```

响应格式：
```json
{
    "code": "000000",
    "msg": "success",
    "data": {
        "packageClientUpdateId": "xxx",
        "versionNumber": 25053001,
        "updateConfig": {
            "studentClientDownloadUrl": "/api/files/student-client-v25053001.exe",
            "studentClientFileHash": "sha256hash",
            "studentClientUpdateDes": "更新说明"
        }
    }
}
```

## 数据库表结构

### app_versions 表

```sql
CREATE TABLE app_versions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    version TEXT NOT NULL UNIQUE,
    installed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_current BOOLEAN DEFAULT FALSE,
    student_client_path TEXT DEFAULT '',
    student_client_file_hash TEXT DEFAULT '',
    student_update_description TEXT DEFAULT '',
    package_client_update_id TEXT DEFAULT ''
);
```

### migration_logs 表

```sql
CREATE TABLE migration_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    from_version TEXT NOT NULL,
    to_version TEXT NOT NULL,
    description TEXT NOT NULL,
    executed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TEXT NOT NULL,
    error_message TEXT
);
```

## 前端API接口

学生端为前端提供以下更新相关API：

1. **CheckForUpdates()** - 检查程序更新
2. **DownloadUpdate(url, hash, version)** - 下载更新文件
3. **StartUpdate(exePath)** - 启动程序更新
4. **CheckAndAutoUpdate()** - 检查并自动更新
5. **GetUpdateStatus()** - 获取更新状态
6. **GetCurrentVersion()** - 获取当前版本

## 新增版本步骤

### 1. 创建迁移脚本

创建 `utils/migrations/migration_XXX_to_YYY.go`:

```go
type Migration_25052901_to_25053001 struct{}

func (m *Migration_25052901_to_25053001) Version() string {
    return "25053001"
}

func (m *Migration_25052901_to_25053001) Description() string {
    return "升级到版本25053001：添加新功能"
}

func (m *Migration_25052901_to_25053001) Execute(ctx *MigrationContext) error {
    // 1. 更新学生端程序文件
    if err := UpdateStudentClientFromVersion(ctx, "25053001"); err != nil {
        return err
    }

    // 2. 数据库结构变更
    sqls := []string{
        `ALTER TABLE packages ADD COLUMN new_field TEXT DEFAULT ''`,
    }
    for _, sql := range sqls {
        if _, err := ctx.DB.Exec(sql); err != nil {
            return fmt.Errorf("执行SQL失败: %s, 错误: %v", sql, err)
        }
    }

    return nil
}

func (m *Migration_25052901_to_25053001) Rollback(ctx *MigrationContext) error {
    // 回滚操作（可选实现）
    return nil
}
```

### 2. 更新版本配置

修改 `utils/migrations/version_config.go`:

```go
// 1. 添加新版本到版本路径
versionPath := []string{
    "25052901", // 初始版本
    "25053001", // 新增版本
}

// 2. 注册新迁移
migrationMap := map[string]Migration{
    "25053001": &Migration_25052901_to_25053001{},
}
```

## 配置说明

### 环境变量

- `AUTO_UPDATE_ENABLED`: 是否启用自动更新（默认true）

### 目录结构

```
student-client/
├── data/           # 数据库文件
├── downloads/      # 下载文件
├── packages/       # 实验包文件
├── extracted/      # 解压文件
├── logs/          # 日志文件
├── updates/       # 更新文件
├── backup/        # 备份文件
├── temp/          # 临时文件
└── config/        # 配置文件
```

## 外部更新器

学生端使用外部更新器 `updater.exe` 进行文件替换：

```bash
updater.exe --type=student --old=current.exe --new=new.exe --restart
```

更新器参数：
- `--type=student`: 指定更新类型
- `--old`: 当前程序路径
- `--new`: 新程序路径
- `--restart`: 更新完成后重启程序

## 日志系统

更新过程的所有操作都会记录到日志文件：

```
[INFO] [StudentClient] [UpdateService] [CheckForUpdates] 开始检查程序更新
[INFO] [StudentClient] [UpdateService] [DownloadUpdate] 开始下载更新文件
[INFO] [StudentClient] [VersionManager] [Migration] 开始线性迁移: 25052901 → 25053001
```

## 故障排查

### 查看更新日志

```bash
# 查看应用日志
tail -f ./logs/student-client-*.log | grep UpdateService

# 查看迁移日志
tail -f ./logs/student-client-*.log | grep Migration
```

### 查看数据库状态

```bash
# 查看当前版本
sqlite3 ./data/student-client.db "SELECT * FROM app_versions WHERE is_current = 1;"

# 查看迁移记录
sqlite3 ./data/student-client.db "SELECT * FROM migration_logs ORDER BY executed_at DESC LIMIT 10;"
```

## 安全特性

1. **哈希验证**: 所有下载文件都进行SHA256哈希验证
2. **备份机制**: 迁移前自动创建数据库备份
3. **回滚支持**: 迁移失败时自动回滚
4. **权限检查**: 确保更新器具有适当权限

## 性能优化

1. **异步更新**: 更新检查和下载在后台进行
2. **增量迁移**: 只执行必要的迁移步骤
3. **缓存机制**: 避免重复的版本检查
4. **超时控制**: 设置合理的网络超时时间

## 总结

学生端自动更新系统提供了完整的版本管理和自动更新解决方案，具有以下特点：

- ✅ **集中化配置**: 版本信息集中管理，易于维护
- ✅ **自动化更新**: 启动时自动检查和更新
- ✅ **安全可靠**: 哈希验证、备份机制、错误回滚
- ✅ **完整日志**: 详细记录所有操作过程
- ✅ **前端集成**: 提供完整的前端API接口
- ✅ **学校端对接**: 与学校端API无缝集成

该系统确保学生端程序能够自动保持最新版本，同时提供了完整的版本管理和数据库迁移能力。
