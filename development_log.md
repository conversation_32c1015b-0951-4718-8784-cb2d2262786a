# 学生端包管理系统开发日志

## 2024-12-19 - 实验版本去重和旧版本清理功能

### 需求分析
用户反馈当前学生端将每个包版本都当作独立项目显示，但实际上：
- 一个实验有一个 `experimentId`
- 同一实验可能有多个版本（如验收版、试用版），每个版本有一个 `experimentVersionId`
- 每个实验版本可能有多个包，每个包有一个 `packageVersionId`（字符串格式的long整数）
- 相同 `experimentVersionId` 的包只应显示最新的（`packageVersionId` 数值最大的）
- 解压路径应从 `学校id/实验id/` 改为 `学校id/实验id/实验版本id/`

### 实现方案

#### 1. 数据库层面修改 (`models/package.go`)
- 添加 `GetLatestPackagesByExperimentVersion()` 方法：按实验版本ID分组，每组只返回PackageVersionID最大的包
- 添加 `GetOldPackagesByExperimentVersion()` 方法：获取指定实验版本的旧包版本
- 添加 `DeletePackage()` 方法：删除包记录和相关同步日志

#### 2. 业务逻辑层面修改 (`app.go`)
- 修改 `GetPackages()` 方法：使用新的去重逻辑，只返回每个实验版本的最新包
- 修改同步逻辑：在创建新包时调用清理旧版本的方法
- 添加 `cleanupOldPackageVersions()` 方法：清理指定实验版本的旧包版本，包括：
  - 删除本地下载文件
  - 删除解压目录
  - 删除数据库记录

#### 3. 解压路径修改 (`services/extract_service.go`)
- 修改解压目录结构：从 `学校id/实验id/` 改为 `学校id/实验id/实验版本id/`
- 确保每个实验版本有独立的解压目录

### 技术细节

#### SQL查询优化
使用子查询和JOIN来实现实验版本去重：
```sql
SELECT p1.* FROM packages p1
INNER JOIN (
    SELECT experiment_version_id, MAX(CAST(package_version_id AS INTEGER)) as max_package_version_id
    FROM packages
    GROUP BY experiment_version_id
) p2 ON p1.experiment_version_id = p2.experiment_version_id
AND CAST(p1.package_version_id AS INTEGER) = p2.max_package_version_id
```

#### 文件清理策略
- 在同步新包时自动清理同一实验版本的旧包
- 清理包括：本地下载文件、解压目录、数据库记录
- 使用日志记录清理过程

#### 目录结构变更
- 旧结构：`extracted/{schoolId}/{experimentId}/`
- 新结构：`extracted/{schoolId}/{experimentId}/{experimentVersionId}/`

### 影响范围
- 前端显示：实验库页面将只显示每个实验版本的最新包
- 后端逻辑：包列表获取、同步逻辑、解压逻辑
- 文件系统：解压目录结构变更
- 数据库：新增查询方法，包清理逻辑

### 测试要点
1. 验证实验版本去重是否正确工作
2. 验证旧版本清理是否完整（文件+数据库）
3. 验证新的解压路径是否正确
4. 验证前端显示是否符合预期
5. 验证同步逻辑是否正常

### 后续优化
- 可考虑添加配置选项控制是否自动清理旧版本
- 可考虑添加手动清理旧版本的功能
- 可考虑添加清理统计信息

## 2024-12-19 - 修复更新逻辑问题

### 问题描述
用户反馈：如果上一个版本的包已经下载好了，那么如果有新包应该提示去更新，而不是直接显示最新的包让重新下载，这样更新逻辑就没用了。没更新前，运行这些功能也要是正常的，用户可以自主选择更新。

### 解决方案

#### 1. 修改显示逻辑 (`models/package.go`)
- 添加 `GetDisplayPackagesByExperimentVersion()` 方法：
  - 优先显示已下载的包（状态为已下载或已完成）
  - 对于没有已下载包的实验版本，显示最新版本
- 添加 `CheckForUpdates()` 方法：检查指定包是否有更新版本

#### 2. 修改后端API (`app.go`)
- 修改 `GetPackages()` 方法：使用新的显示逻辑
- 添加 `CheckPackageUpdates()` 方法：检查包是否有更新并返回更新信息

#### 3. 修改前端逻辑 (`frontend/src/views/Library.vue`)
- 在已下载包上添加更新按钮（仅当有更新时显示）
- 修改 `fetchPackages()` 方法：获取包列表后检查更新
- 添加 `checkPackageUpdates()` 方法：批量检查包更新
- 修改 `updatePackage()` 方法：使用更新包的信息显示更新日志
- 修改下载完成逻辑：更新操作完成后刷新包列表

### 实现效果
1. **已下载包**：继续显示已下载的版本，用户可以正常运行
2. **更新提示**：如果有新版本，显示"更新"按钮
3. **用户选择**：用户可以选择是否更新，不强制更新
4. **更新流程**：点击更新按钮显示新版本的更新日志，确认后下载新版本
5. **版本切换**：更新完成后自动切换到新版本显示

### 技术细节
- 使用两个SQL查询分别获取已下载包和最新包
- 前端使用 `$set` 方法动态添加更新信息到包对象
- 更新操作时保存原包ID，用于状态更新和包列表刷新
- 下载完成后根据操作类型（下载/更新）执行不同的后续逻辑

## 2024-12-19 - 修正全量包更新逻辑

### 问题确认
用户确认这是一个Wails Go程序，包都是全量包，不需要渐进式版本更新。
不管实验版本以前已经下载的什么包版本，如果检测更新逻辑，都只需要升级到获取到的最新包版本就可以了。

### 修正实现
- 修正了 `app.go` 中的 `GetPackages()` 方法，移除了错误的引用
- 确认使用 `GetDisplayPackagesByExperimentVersion()` 方法：
  - 优先显示已下载的包（用户可以继续正常运行）
  - 对于没有已下载包的实验版本，显示最新版本
- 确认使用 `CheckForUpdates()` 方法：
  - 检查是否有比当前版本更新的包版本
  - 直接升级到最新版本（全量包替换）

### 当前逻辑确认
1. **已下载包**：继续显示已下载的版本，用户可以正常运行
2. **更新检测**：检查是否有更新版本，有则显示"更新"按钮
3. **全量更新**：点击更新直接下载最新版本，完全替换旧版本
4. **自动清理**：更新时自动清理旧版本文件和数据库记录
5. **目录结构**：使用 `学校id/实验id/实验版本id/` 的目录结构

### 符合需求
✅ 已下载包的正常运行功能
✅ 全量包更新（不是渐进式）
✅ 直接升级到最新版本
✅ 用户自主选择更新时机

## 2024-12-19 - 修复数据库记录清理问题

### 问题发现
用户测试发现：给已下载的实验版本推送新包后，只显示最新包（下载按钮），没有显示以前下载的包（运行按钮）。
原因：`cleanupOldPackageVersions()` 方法删除了已下载包的数据库记录，导致 `GetDisplayPackagesByExperimentVersion()` 找不到已下载的包。

### 问题分析
1. **数据库记录被删除**：旧版本的数据库记录被完全删除
2. **显示逻辑失效**：无法找到已下载的包，只能显示最新包
3. **用户体验问题**：已下载的包无法运行，只能重新下载

### 解决方案
修改清理逻辑，**保留已下载包的数据库记录**：

#### 1. 修改清理策略 (`app.go`)
- **已下载包**（状态为已下载或已完成）：
  - ✅ 保留数据库记录
  - ✅ 清理文件（下载文件、解压目录）
  - ✅ 清空文件路径字段
  - ✅ 重置状态为"未同步"
- **未下载包**（状态为未同步）：
  - ✅ 完全删除数据库记录和文件

#### 2. 添加新方法 (`models/package.go`)
- 添加 `ClearPackageFilePaths()` 方法：
  - 清空 `local_path`、`extract_path`、`executable_path` 字段
  - 重置 `sync_status` 为 `SyncStatusNone`
  - 保留包的基本信息和下载历史

### 预期效果
1. **已下载包继续显示**：用户可以看到之前下载的包，但状态变为"未同步"
2. **显示更新按钮**：检测到有新版本时显示"更新"按钮
3. **保持运行能力**：虽然文件被清理，但记录保留，用户知道曾经下载过
4. **更新流程正常**：点击更新下载新版本，替换旧版本

### 技术细节
- 根据包的 `sync_status` 决定清理策略
- 已下载包：保留记录 + 清理文件 + 重置状态
- 未下载包：完全删除记录和文件
- 确保 `GetDisplayPackagesByExperimentVersion()` 能找到已下载包的记录

## 2024-12-19 - 重构同步逻辑和日志系统

### 问题根源
用户测试发现数据库里还是只有最新的一个记录，说明后端同步逻辑有问题：
1. **同步逻辑错误**：基于 `packageVersionID` 判断是否存在，导致同一实验版本的旧包被新包覆盖
2. **日志外键问题**：`sync_logs` 表的外键是 `package_id`，应该改为实验版本ID来追踪实验版本的历史

### 解决方案

#### 1. 重构同步逻辑 (`app.go`)
**旧逻辑**：基于 `packageVersionID` 作为唯一标识
**新逻辑**：基于实验版本ID管理，支持同一实验版本的多个包版本

- **按实验版本分组**：将本地包按 `experimentVersionID` 分组
- **智能同步策略**：
  - 实验版本已存在 + 包版本已存在 → 更新包信息
  - 实验版本已存在 + 包版本不存在 → 添加新包版本
  - 实验版本不存在 → 创建新实验版本
- **保留已下载包**：只清理未下载的旧包版本，保留已下载包的记录

#### 2. 重构日志系统 (`models/sync_log.go`, `models/db.go`)
**数据库表结构升级**：
- 添加 `experiment_version_id` 字段到 `sync_logs` 表
- 支持数据库升级，自动添加新字段
- 兼容旧版本数据

**新增日志方法**：
- `AddSyncLogByExperimentVersion()`：基于实验版本ID添加日志
- `GetSyncLogsByExperimentVersionID()`：获取实验版本的历史日志
- 更新所有查询方法支持新字段

#### 3. 修改清理策略 (`app.go`)
**智能清理**：
- **已下载包**：保留数据库记录，清理文件，重置状态
- **未下载包**：完全删除记录和文件
- **基于状态判断**：根据 `sync_status` 决定清理策略

### 技术实现

#### 同步逻辑重构
```go
// 按实验版本ID分组本地包
localExperimentVersionMap := make(map[string][]*models.Package)
for _, pkg := range localPackages {
    localExperimentVersionMap[pkg.ExperimentVersionID] = append(localExperimentVersionMap[pkg.ExperimentVersionID], pkg)
}

// 智能同步策略
for _, remotePkg := range remotePackages {
    localPkgs, experimentExists := localExperimentVersionMap[remotePkg.ExperimentVersionID]
    if experimentExists {
        // 检查是否有相同的包版本ID
        // 处理更新或新增包版本
    } else {
        // 全新的实验版本
    }
}
```

#### 数据库升级
```go
// 自动添加新字段
err = addColumnIfNotExists("sync_logs", "experiment_version_id", "TEXT DEFAULT ''")
```

#### 日志系统升级
```go
// 基于实验版本ID的日志
func AddSyncLogByExperimentVersion(experimentVersionID string, action string, status int, message string) error
func GetSyncLogsByExperimentVersionID(experimentVersionID string) ([]*SyncLog, error)
```

### 预期效果
1. **保留已下载包**：数据库中保留已下载包的记录
2. **支持多版本**：同一实验版本可以有多个包版本记录
3. **智能显示**：优先显示已下载包，提供更新选项
4. **完整日志**：基于实验版本ID追踪完整的操作历史
5. **数据一致性**：确保数据库记录和文件状态的一致性

### 关键改进
- ✅ 以实验版本ID为核心的数据管理
- ✅ 保留已下载包的数据库记录
- ✅ 智能的包版本管理和清理策略
- ✅ 基于实验版本的日志追踪
- ✅ 数据库结构平滑升级

## 2024-12-19 - 修复已下载包被误清理的问题

### 问题发现
用户测试发现：虽然数据库中保留了下载过的和未下载过的两条记录，但是下载过的记录的 `sync_status`、`executable_path` 等字段记录都没有了，说明这条记录被删除后重新创建的新记录。

### 问题分析
**根本原因**：`cleanupOldPackageVersions()` 方法中的清理逻辑有误

1. **错误的清理范围**：`GetOldPackagesByExperimentVersion()` 返回"除了最新PackageVersionID之外的所有包"
2. **误清理已下载包**：如果用户下载了版本2，学校推送版本3，那么版本2被认为是"旧版本"而被清理
3. **数据丢失**：已下载包的状态、路径等重要信息被清空或删除

### 问题场景
```
用户下载了实验版本A的包版本2 (sync_status=2, executable_path=/path/to/exe)
学校推送实验版本A的包版本3
同步时：版本2被认为是"旧版本"，状态和路径被清空
结果：用户无法运行已下载的版本2
```

### 解决方案
**修改清理策略**：只清理未下载的旧包版本，完全保留已下载的包

#### 修改前的逻辑
```go
// 错误：清理所有旧版本，包括已下载的
for _, oldPkg := range oldPackages {
    if oldPkg.SyncStatus == Downloaded {
        // 清空文件路径，但保留记录 ❌ 错误！
        ClearPackageFilePaths(oldPkg.ID)
    } else {
        // 删除未下载的包
        DeletePackage(oldPkg.ID)
    }
}
```

#### 修改后的逻辑
```go
// 正确：只清理未下载的包，完全保留已下载的包
for _, oldPkg := range oldPackages {
    if oldPkg.SyncStatus == Downloaded || oldPkg.SyncStatus == Completed {
        // 已下载的包：完全保留，不做任何清理 ✅ 正确！
        utils.LogPrintf("保留已下载包: ID=%d", oldPkg.ID)
        continue
    }

    // 只清理未下载的包
    DeletePackage(oldPkg.ID)
}
```

### 修复效果
1. **保留已下载包状态**：`sync_status`、`executable_path` 等字段完整保留
2. **保持运行能力**：用户可以继续运行已下载的包
3. **正确显示更新**：前端显示已下载包（运行按钮）+ 更新按钮
4. **清理未下载包**：只删除未下载的旧版本，节省存储空间

### 测试场景
1. 下载实验包版本2并解压运行
2. 学校推送实验包版本3
3. 学生端同步后应该显示：
   - 版本2：已下载状态，显示"运行"按钮
   - 检测到版本3可用，显示"更新"按钮
   - 版本2的所有状态和路径信息完整保留

### 关键改进
- ✅ **完全保留已下载包**：不清理任何已下载包的信息
- ✅ **精确清理策略**：只清理未下载的旧版本
- ✅ **状态完整性**：保持已下载包的完整状态信息
- ✅ **用户体验**：确保已下载包可以正常运行
