/* 全局样式 */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  color: #333;
  background-color: #f5f7fa;
}

/* 清除浮动 */
.clearfix:after {
  content: '';
  display: table;
  clear: both;
}

/* 文本溢出省略号 */
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 常用边距 */
.mt-10 { margin-top: 10px; }
.mt-20 { margin-top: 20px; }
.mb-10 { margin-bottom: 10px; }
.mb-20 { margin-bottom: 20px; }
.ml-10 { margin-left: 10px; }
.mr-10 { margin-right: 10px; }
.p-10 { padding: 10px; }
.p-20 { padding: 20px; }

/* 卡片样式 */
.el-card {
  margin-bottom: 20px;
}

/* 表格样式 */
.el-table .cell {
  word-break: break-word;
}

/* 表单样式 */
.el-form-item__label {
  font-weight: bold;
}

/* 按钮样式 */
.el-button + .el-button {
  margin-left: 10px;
}

/* 状态标签样式 */
.el-tag + .el-tag {
  margin-left: 5px;
}

/* 分页样式 */
.el-pagination {
  margin-top: 20px;
  text-align: right;
}

/* 加载样式 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 弹窗样式 */
.el-dialog__header {
  border-bottom: 1px solid #eee;
}

.el-dialog__footer {
  border-top: 1px solid #eee;
  padding-top: 15px;
}

/* 主题色 */
:root {
  --primary-color: #409EFF;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
}
