<template>
  <div class="search-container">
    <el-form :inline="true" :model="searchForm" class="search-form">
      <slot name="before"></slot>
      
      <el-form-item v-for="(item, index) in searchItems" :key="index" :label="item.label">
        <!-- 输入框 -->
        <el-input 
          v-if="item.type === 'input'" 
          v-model="searchForm[item.prop]" 
          :placeholder="item.placeholder || `请输入${item.label}`"
          clearable
          @keyup.enter="handleSearch"
        />
        
        <!-- 选择器 -->
        <el-select 
          v-else-if="item.type === 'select'" 
          v-model="searchForm[item.prop]" 
          :placeholder="item.placeholder || `请选择${item.label}`"
          clearable
        >
          <el-option 
            v-for="option in item.options" 
            :key="option.value" 
            :label="option.label" 
            :value="option.value" 
          />
        </el-select>
        
        <!-- 日期选择器 -->
        <el-date-picker 
          v-else-if="item.type === 'date'" 
          v-model="searchForm[item.prop]" 
          :type="item.dateType || 'date'" 
          :placeholder="item.placeholder || `请选择${item.label}`"
          clearable
        />
        
        <!-- 日期范围选择器 -->
        <el-date-picker 
          v-else-if="item.type === 'daterange'" 
          v-model="searchForm[item.prop]" 
          type="daterange" 
          range-separator="至" 
          start-placeholder="开始日期" 
          end-placeholder="结束日期"
          clearable
        />
      </el-form-item>
      
      <slot name="after"></slot>
      
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'SearchBar',
  props: {
    searchItems: {
      type: Array,
      required: true
    },
    initialValues: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      searchForm: { ...this.initialValues }
    }
  },
  watch: {
    initialValues: {
      handler(val) {
        this.searchForm = { ...val }
      },
      deep: true
    }
  },
  methods: {
    handleSearch() {
      this.$emit('search', this.searchForm)
    },
    handleReset() {
      // 重置表单
      this.searchItems.forEach(item => {
        this.searchForm[item.prop] = undefined
      })
      this.$emit('reset')
      this.$emit('search', this.searchForm)
    }
  }
}
</script>

<style scoped>
.search-container {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}
</style>
