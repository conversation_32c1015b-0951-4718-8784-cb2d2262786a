<template>
  <el-tag :type="statusType" :effect="effect">{{ statusText }}</el-tag>
</template>

<script>
export default {
  name: 'SyncStatusBadge',
  props: {
    status: {
      type: Number,
      required: true
    },
    effect: {
      type: String,
      default: 'light'
    }
  },
  computed: {
    statusType() {
      const statusMap = {
        0: 'info',    // 未同步
        1: 'warning', // 同步中
        2: 'success', // 已同步
        3: 'danger'   // 同步失败
      }
      return statusMap[this.status] || 'info'
    },
    statusText() {
      const statusMap = {
        0: '未同步',
        1: '同步中',
        2: '已同步',
        3: '失败'
      }
      return statusMap[this.status] || '未知'
    }
  }
}
</script>
