<template>
  <el-card class="package-card" shadow="hover">
    <div class="package-header">
      <h3 class="package-title">{{ packageData.versionName }}</h3>
      <sync-status-badge :status="packageData.syncStatus" />
    </div>
    <div class="package-info">
      <p><strong>实验ID:</strong> {{ packageData.experimentId }}</p>
      <p><strong>版本:</strong> {{ packageData.version }}</p>
      <p><strong>文件大小:</strong> {{ formatFileSize(packageData.fileSize) }}</p>
      <p><strong>同步时间:</strong> {{ packageData.updatedAt }}</p>
    </div>
    <div class="package-actions">
      <el-button type="primary" size="small" @click="viewDetail">查看详情</el-button>
      <el-button 
        type="success" 
        size="small" 
        :disabled="packageData.syncStatus === 1" 
        :loading="syncing"
        @click="syncPackage"
      >
        {{ packageData.syncStatus === 2 ? '重新同步' : '同步' }}
      </el-button>
    </div>
  </el-card>
</template>

<script>
import SyncStatusBadge from './SyncStatusBadge.vue'

export default {
  name: 'PackageCard',
  components: {
    SyncStatusBadge
  },
  props: {
    packageData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      syncing: false
    }
  },
  methods: {
    formatFileSize(size) {
      if (!size) return '0 B'
      
      const units = ['B', 'KB', 'MB', 'GB', 'TB']
      let i = 0
      while (size >= 1024 && i < units.length - 1) {
        size /= 1024
        i++
      }
      
      return `${size.toFixed(2)} ${units[i]}`
    },
    viewDetail() {
      this.$emit('view-detail', this.packageData.id)
    },
    syncPackage() {
      this.syncing = true
      this.$emit('sync-package', this.packageData.id)
        .finally(() => {
          this.syncing = false
        })
    }
  }
}
</script>

<style scoped>
.package-card {
  margin-bottom: 20px;
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.package-title {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
}

.package-info {
  margin-bottom: 15px;
}

.package-info p {
  margin: 5px 0;
  font-size: 14px;
}

.package-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
