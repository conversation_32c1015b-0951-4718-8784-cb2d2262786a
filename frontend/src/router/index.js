import { createRouter, createWebHistory } from 'vue-router'
import store from '../store'

// 页面组件
const Login = () => import('../views/Login.vue')
const Layout = () => import('../views/Layout.vue')
const Dashboard = () => import('../views/Dashboard.vue')
const PackageList = () => import('../views/PackageList.vue')
const PackageDetail = () => import('../views/PackageDetail.vue')
const SyncLogs = () => import('../views/SyncLogs.vue')
const SystemLogs = () => import('../views/SystemLogs.vue')
const Settings = () => import('../views/Settings.vue')
const Profile = () => import('../views/Profile.vue')

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: Dashboard,
        meta: { title: '仪表盘' }
      },
      {
        path: 'packages',
        name: 'PackageList',
        component: PackageList,
        meta: { title: '包列表' }
      },
      {
        path: 'packages/:id',
        name: 'PackageDetail',
        component: PackageDetail,
        meta: { title: '包详情' }
      },
      {
        path: 'logs/sync',
        name: 'SyncLogs',
        component: SyncLogs,
        meta: { title: '同步日志' }
      },
      {
        path: 'logs/system',
        name: 'SystemLogs',
        component: SystemLogs,
        meta: { title: '系统日志' }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: Settings,
        meta: { title: '系统设置' }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: Profile,
        meta: { title: '用户信息' }
      }
    ]
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(process.env.NODE_ENV === 'production' ? '/web/' : '/'),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 检查是否需要认证
  if (to.matched.some(record => record.meta.requiresAuth)) {
    // 检查用户是否已登录
    if (!store.getters['user/isLoggedIn']) {
      // 未登录则重定向到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
    } else {
      // 已登录则继续
      next()
    }
  } else {
    // 不需要认证的页面
    next()
  }
})

export default router
