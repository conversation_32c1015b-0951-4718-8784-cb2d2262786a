<template>
  <div class="settings-container">
    <el-card v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>系统设置</span>
          <div>
            <el-button type="primary" @click="saveSettings">保存设置</el-button>
            <el-button @click="resetSettings">重置</el-button>
          </div>
        </div>
      </template>

      <el-tabs v-model="activeTab">
        <!-- 同步设置 -->
        <el-tab-pane label="同步设置" name="sync">
          <el-form :model="syncSettings" label-width="180px">
            <el-form-item label="同步间隔（分钟）">
              <el-input-number v-model="syncSettings.sync_interval" :min="1" :max="1440" />
              <div class="form-item-help">设置自动同步的时间间隔，单位为分钟</div>
            </el-form-item>

            <el-form-item label="同步重试次数">
              <el-input-number v-model="syncSettings.sync_retry_count" :min="0" :max="10" />
              <div class="form-item-help">设置同步失败后的重试次数</div>
            </el-form-item>

            <el-form-item label="同步重试延迟（分钟）">
              <el-input-number v-model="syncSettings.sync_retry_delay" :min="1" :max="60" />
              <div class="form-item-help">设置同步失败后重试的延迟时间，单位为分钟</div>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 存储设置 -->
        <el-tab-pane label="存储设置" name="storage">
          <el-form :model="storageSettings" label-width="180px">
            <el-form-item label="最大存储大小（GB）">
              <el-input-number
                v-model="storageSettings.max_storage_size_gb"
                :min="1"
                :max="1000"
                :precision="1"
                :step="0.5"
              />
              <div class="form-item-help">设置系统可使用的最大存储空间，单位为GB</div>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 日志设置 -->
        <el-tab-pane label="日志设置" name="log">
          <el-form :model="logSettings" label-width="180px">
            <el-form-item label="日志级别">
              <el-select v-model="logSettings.log_level">
                <el-option label="DEBUG" value="DEBUG" />
                <el-option label="INFO" value="INFO" />
                <el-option label="WARN" value="WARN" />
                <el-option label="ERROR" value="ERROR" />
              </el-select>
              <div class="form-item-help">设置系统日志记录的最低级别</div>
            </el-form-item>

            <el-form-item label="最大日志大小（MB）">
              <el-input-number
                v-model="logSettings.max_log_size_mb"
                :min="1"
                :max="1000"
              />
              <div class="form-item-help">设置单个日志文件的最大大小，单位为MB</div>
            </el-form-item>

            <el-form-item label="最大日志备份数">
              <el-input-number v-model="logSettings.max_log_backups" :min="1" :max="100" />
              <div class="form-item-help">设置保留的日志文件备份数量</div>
            </el-form-item>

            <el-form-item label="最大日志保存天数">
              <el-input-number v-model="logSettings.max_log_age" :min="1" :max="365" />
              <div class="form-item-help">设置日志文件的最大保存天数</div>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Settings',
  data() {
    return {
      loading: false,
      activeTab: 'sync',
      syncSettings: {
        sync_interval: 30,
        sync_retry_count: 3,
        sync_retry_delay: 5
      },
      storageSettings: {
        max_storage_size_gb: 10
      },
      logSettings: {
        log_level: 'info',
        max_log_size_mb: 100,
        max_log_backups: 5,
        max_log_age: 30
      },
      originalSettings: null
    }
  },
  computed: {
    ...mapGetters({
      configs: 'config/configs'
    })
  },
  created() {
    this.getSettings()
  },
  methods: {
    getSettings() {
      this.loading = true
      this.$store.dispatch('config/getConfig')
        .then(() => {
          this.initSettings()
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    initSettings() {
      // 同步设置
      this.syncSettings.sync_interval = this.getConfigValueAsNumber('sync_interval', 30)
      this.syncSettings.sync_retry_count = this.getConfigValueAsNumber('sync_retry_count', 3)
      this.syncSettings.sync_retry_delay = this.getConfigValueAsNumber('sync_retry_delay', 5)

      // 存储设置
      const maxStorageBytes = this.getConfigValueAsNumber('max_storage_size', 10737418240)
      this.storageSettings.max_storage_size_gb = Math.round((maxStorageBytes / 1073741824) * 10) / 10

      // 日志设置
      this.logSettings.log_level = this.getConfigValue('log_level', 'info')
      const maxLogBytes = this.getConfigValueAsNumber('max_log_size', 104857600)
      this.logSettings.max_log_size_mb = Math.round(maxLogBytes / 1048576)
      this.logSettings.max_log_backups = this.getConfigValueAsNumber('max_log_backups', 5)
      this.logSettings.max_log_age = this.getConfigValueAsNumber('max_log_age', 30)

      // 保存原始设置用于重置
      this.originalSettings = {
        syncSettings: { ...this.syncSettings },
        storageSettings: { ...this.storageSettings },
        logSettings: { ...this.logSettings }
      }
    },
    saveSettings() {
      this.loading = true

      // 准备更新的配置
      const updatedConfigs = [
        { key: 'sync_interval', value: this.syncSettings.sync_interval.toString() },
        { key: 'sync_retry_count', value: this.syncSettings.sync_retry_count.toString() },
        { key: 'sync_retry_delay', value: this.syncSettings.sync_retry_delay.toString() },
        { key: 'max_storage_size', value: Math.floor(this.storageSettings.max_storage_size_gb * 1073741824).toString() },
        { key: 'log_level', value: this.logSettings.log_level },
        { key: 'max_log_size', value: Math.floor(this.logSettings.max_log_size_mb * 1048576).toString() },
        { key: 'max_log_backups', value: this.logSettings.max_log_backups.toString() },
        { key: 'max_log_age', value: this.logSettings.max_log_age.toString() }
      ]

      this.$store.dispatch('config/updateConfig', { configs: updatedConfigs })
        .then(() => {
          this.$message.success('设置已保存')
          this.getSettings()
        })
        .catch(error => {
          this.$message.error(`保存设置失败: ${error.message || '未知错误'}`)
          this.loading = false
        })
    },
    resetSettings() {
      if (this.originalSettings) {
        this.syncSettings = { ...this.originalSettings.syncSettings }
        this.storageSettings = { ...this.originalSettings.storageSettings }
        this.logSettings = { ...this.originalSettings.logSettings }
      } else {
        this.getSettings()
      }
    },
    getConfigValue(key, defaultValue) {
      const config = this.configs.find(item => item.key === key)
      return config ? config.value : defaultValue
    },
    getConfigValueAsNumber(key, defaultValue) {
      const value = this.getConfigValue(key, defaultValue.toString())
      return parseInt(value, 10)
    }
  }
}
</script>

<style scoped>
.settings-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-item-help {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
