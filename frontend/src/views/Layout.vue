<template>
  <div class="app-container">
    <el-container>
      <el-aside width="220px">
        <div class="logo">
          <div class="logo-icon">
            <el-icon><el-icon-office-building /></el-icon>
          </div>
          <div class="logo-text">
            <h3>智云鸿道</h3>
            <span class="logo-subtitle">思政虚拟仿真软件管理器</span>
            <span class="logo-school">学校管理端</span>
          </div>
        </div>
        <el-menu
          :default-active="activeMenu"
          class="el-menu-vertical"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          router
        >
          <el-menu-item index="/">
            <el-icon><el-icon-odometer /></el-icon>
            <span>仪表盘</span>
          </el-menu-item>
          <el-menu-item index="/packages">
            <el-icon><el-icon-box /></el-icon>
            <span>包列表</span>
          </el-menu-item>
          <el-menu-item index="/logs/sync">
            <el-icon><el-icon-document /></el-icon>
            <span>同步日志</span>
          </el-menu-item>
          <el-menu-item index="/logs/system">
            <el-icon><el-icon-warning /></el-icon>
            <span>系统日志</span>
          </el-menu-item>
          <!-- <el-menu-item index="/settings">
            <el-icon><el-icon-setting /></el-icon>
            <span>系统设置</span>
          </el-menu-item> -->
          <el-menu-item index="/profile">
            <el-icon><el-icon-user /></el-icon>
            <span>用户信息</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      <el-container>
        <el-header>
          <div class="header-left">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item v-if="$route.meta.title">{{ $route.meta.title }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="header-right">
            <el-dropdown trigger="click" @command="handleCommand">
              <span class="user-dropdown">
                {{ currentUser.username }}
                <el-icon class="el-icon--right"><el-icon-arrow-down /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        <el-main>
          <router-view />
        </el-main>
        <el-footer>
          <div class="footer-content">
            <span>智云鸿道思政虚拟仿真软件管理器-学校端 {{ currentVersion ? `V${currentVersion}` : 'V1.0.0' }}</span>
          </div>
        </el-footer>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Layout',
  data() {
    return {
      currentVersion: ''
    }
  },
  computed: {
    ...mapGetters({
      currentUser: 'user/currentUser'
    }),
    activeMenu() {
      return this.$route.path
    }
  },
  mounted() {
    this.getSystemVersion()
  },
  methods: {
    handleCommand(command) {
      if (command === 'logout') {
        this.$store.dispatch('user/logout').then(() => {
          this.$router.push('/login')
        })
      }
    },
    async getSystemVersion() {
      try {
        const response = await this.$store.dispatch('system/getSystemStatus')
        console.log('系统状态响应:', response) // 添加调试日志
        if (response.data && response.data.system && response.data.system.version) {
          this.currentVersion = response.data.system.version
          console.log('获取到系统版本:', this.currentVersion) // 添加调试日志
        } else {
          console.log('响应数据结构不正确:', response) // 添加调试日志
        }
      } catch (error) {
        console.error('获取系统版本失败:', error)
        // 如果获取失败，保持默认值
      }
    }
  }
}
</script>

<style scoped>
.app-container {
  height: 100vh;
}

.el-container {
  height: 100%;
}

.el-aside {
  background-color: #304156;
  color: #bfcbd9;
}

.logo {
  height: 90px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-icon {
  margin-right: 8px;
  font-size: 22px;
  color: #fff;
}

.logo-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 1.1;
}

.logo h3 {
  font-size: 14px;
  margin: 0;
  padding: 0;
  font-weight: 600;
  line-height: 1.2;
  color: #fff;
  margin-bottom: 3px;
}

.logo-subtitle {
  font-size: 13px;
  opacity: 0.9;
  margin-top: 2px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.9);
}

.logo-school {
  font-size: 16px;
  opacity: 0.8;
  margin-top: 1px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 5px;
}

.el-menu-vertical {
  border-right: none;
}

.el-header {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e6e6e6;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-dropdown {
  cursor: pointer;
  color: #409EFF;
}

.el-main {
  background-color: #f5f7fa;
  padding: 20px;
  overflow-y: auto;
}

.el-footer {
  background-color: #fff;
  color: #333;
  text-align: center;
  line-height: 60px;
  border-top: 1px solid #e6e6e6;
}

.footer-content {
  font-size: 12px;
  color: #999;
}
</style>
