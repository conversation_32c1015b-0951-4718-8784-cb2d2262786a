<template>
  <div class="dashboard-container">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-card-header">
            <div class="stat-card-title">包总数</div>
            <el-icon class="stat-card-icon"><el-icon-box /></el-icon>
          </div>
          <div class="stat-card-value">{{ stats.totalPackages }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-card-header">
            <div class="stat-card-title">已同步包</div>
            <el-icon class="stat-card-icon"><el-icon-check /></el-icon>
          </div>
          <div class="stat-card-value">{{ stats.syncedPackages }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-card-header">
            <div class="stat-card-title">同步中包</div>
            <el-icon class="stat-card-icon"><el-icon-loading /></el-icon>
          </div>
          <div class="stat-card-value">{{ stats.syncingPackages }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-card-header">
            <div class="stat-card-title">同步失败包</div>
            <el-icon class="stat-card-icon"><el-icon-close /></el-icon>
          </div>
          <div class="stat-card-value">{{ stats.failedPackages }}</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 同步操作区域 -->
    <el-row :gutter="20" class="mt-20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>同步操作</span>
              <div>
                <el-button
                  type="primary"
                  :loading="syncLoading"
                  :disabled="syncStatus.isRunning"
                  @click="handleManualSync"
                >
                  <el-icon><el-icon-refresh /></el-icon>
                  {{ syncLoading ? '同步中...' : '立即同步' }}
                </el-button>
                <el-button
                  type="info"
                  @click="refreshSyncStatus"
                >
                  <el-icon><el-icon-refresh /></el-icon>
                  刷新状态
                </el-button>
              </div>
            </div>
          </template>
          <div class="sync-status">
            <el-descriptions :column="3" border>
              <el-descriptions-item label="同步状态">
                <el-tag :type="syncStatus.isRunning ? 'warning' : 'success'">
                  {{ syncStatus.isRunning ? '同步中' : '空闲' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="最后同步时间">
                {{ formatDateTime(syncStatus.lastRunTime) || '暂无' }}
              </el-descriptions-item>
              <el-descriptions-item label="同步间隔">
                30分钟
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近同步的包</span>
              <el-button class="button" text @click="goToPackages">查看全部</el-button>
            </div>
          </template>
          <el-table :data="recentPackages" style="width: 100%">
            <el-table-column prop="experimentName" label="实验名称" width="120" show-overflow-tooltip />
            <el-table-column prop="experimentVersionName" label="实验版本" width="120" show-overflow-tooltip />
            <el-table-column prop="versionName" label="包名称" show-overflow-tooltip />
            <el-table-column prop="fileSize" label="大小" width="80" />
            <el-table-column prop="syncTime" label="同步时间" width="150">
              <template #default="scope">
                {{ formatDateTime(scope.row.syncTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="syncStatus" label="状态" width="80">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.syncStatus)">
                  {{ getStatusText(scope.row.syncStatus) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近同步日志</span>
              <el-button class="button" text @click="goToSyncLogs">查看全部</el-button>
            </div>
          </template>
          <el-table :data="recentLogs" style="width: 100%">
            <el-table-column prop="action" label="操作" width="100" />
            <el-table-column prop="message" label="消息" show-overflow-tooltip />
            <el-table-column prop="createdAt" label="时间" width="150">
              <template #default="scope">
                {{ formatDateTime(scope.row.createdAt) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Dashboard',
  data() {
    return {
      stats: {
        totalPackages: 0,
        syncedPackages: 0,
        syncingPackages: 0,
        failedPackages: 0
      },
      recentPackages: [],
      recentLogs: [],
      syncLoading: false,
      syncStatus: {
        isRunning: false,
        lastRunTime: null
      }
    }
  },
  created() {
    this.fetchData()
    // 启动定时刷新
    this.startAutoRefresh()
  },
  beforeUnmount() {
    // 组件销毁前清除定时器
    this.stopAutoRefresh()
  },
  methods: {
    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '';

      // 处理ISO 8601格式的时间字符串
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) return dateTime; // 如果转换失败，返回原始值

      // 格式化为 YYYY-MM-DD HH:MM:SS
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size || size === 0) return '0 B';

      const units = ['B', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(size) / Math.log(1024));
      return (size / Math.pow(1024, i)).toFixed(2) + ' ' + units[i];
    },

    fetchData() {
      // 获取包统计数据
      this.$store.dispatch('packages/getPackages', { page: 1, limit: 1 })
        .then(response => {
          const { data } = response
          if (data && data.stats) {
            this.stats = data.stats
          }
        })
        .catch(error => {
          console.error('获取包统计数据失败:', error)
          // 使用默认数据
          this.stats = {
            totalPackages: 0,
            syncedPackages: 0,
            syncingPackages: 0,
            failedPackages: 0
          }
        })

      // 获取同步状态
      this.refreshSyncStatus()

      // 获取最近同步的包
      this.$store.dispatch('packages/getPackages', { page: 1, limit: 4, sort: 'updatedAt,desc' })
        .then(response => {
          const { data } = response
          if (data && data.items) {
            // 使用items字段，与后端返回的字段名一致
            this.recentPackages = data.items.map(pkg => ({
              id: pkg.id,
              experimentName: pkg.experimentName || '未知实验',
              experimentVersionName: pkg.experimentVersionName || '未知版本',
              versionName: pkg.versionName,
              syncTime: pkg.updatedAt,
              syncStatus: pkg.syncStatus,
              fileSize: this.formatFileSize(pkg.fileSize)
            }))
          }
        })
        .catch(error => {
          console.error('获取最近同步的包失败:', error)
          // 使用默认数据
          this.recentPackages = []
        })

      // 获取最近的日志
      this.$store.dispatch('logs/getRecentLogs')
        .then(response => {
          const { data } = response
          if (data && data.logs) {
            this.recentLogs = data.logs
          } else if (data && data.syncLogs) {
            // 如果没有logs字段，但有syncLogs和systemLogs字段，则合并这两个字段
            const syncLogs = (data.syncLogs || []).map(log => ({
              id: log.id,
              action: log.action,
              message: log.message,
              createdAt: log.createdAt,
              type: 'sync'
            }))

            const systemLogs = (data.systemLogs || []).map(log => ({
              id: log.id,
              action: log.module + '.' + log.action,
              message: log.message,
              createdAt: log.createdAt,
              type: 'system',
              level: log.level
            }))

            // 合并日志并按时间排序
            this.recentLogs = [...syncLogs, ...systemLogs]
              .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
              .slice(0, 5) // 只取前5条
          }
        })
        .catch(error => {
          console.error('获取最近日志失败:', error)
          // 使用默认数据
          this.recentLogs = []
        })
    },
    getStatusType(status) {
      const statusMap = {
        0: 'info',    // 未同步
        1: 'warning', // 同步中
        2: 'success', // 已同步
        3: 'danger'   // 同步失败
      }
      return statusMap[status] || 'info'
    },
    getStatusText(status) {
      const statusMap = {
        0: '未同步',
        1: '同步中',
        2: '已同步',
        3: '失败'
      }
      return statusMap[status] || '未知'
    },
    goToPackages() {
      this.$router.push('/packages')
    },
    goToSyncLogs() {
      this.$router.push('/logs/sync')
    },
    startAutoRefresh() {
      // 每30秒刷新一次数据
      this.refreshTimer = setInterval(() => {
        this.fetchData()
      }, 30000)
    },
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },
    // 手动触发同步
    async handleManualSync() {
      this.syncLoading = true
      try {
        await this.$store.dispatch('packages/syncAll')
        this.$message.success('同步任务已启动')
        // 立即刷新同步状态
        setTimeout(() => {
          this.refreshSyncStatus()
        }, 1000)
      } catch (error) {
        console.error('启动同步失败:', error)
        this.$message.error(`启动同步失败: ${error.message || '未知错误'}`)
      } finally {
        this.syncLoading = false
      }
    },
    // 刷新同步状态
    async refreshSyncStatus() {
      try {
        const response = await this.$store.dispatch('packages/getSyncStatus')
        if (response && response.data) {
          this.syncStatus = response.data
        }
      } catch (error) {
        console.error('获取同步状态失败:', error)
        // 使用默认状态
        this.syncStatus = {
          isRunning: false,
          lastRunTime: null
        }
      }
    }
  }
}

</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.stat-card {
  height: 120px;
}

.stat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.stat-card-title {
  font-size: 16px;
  color: #606266;
}

.stat-card-icon {
  font-size: 24px;
  color: #409EFF;
}

.stat-card-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sync-status {
  margin-top: 16px;
}
</style>
