.<template>
  <div class="package-list-container">
    <!-- 搜索栏 -->
    <search-bar
      :search-items="searchItems"
      :initial-values="searchForm"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 包版本列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>包列表</span>
          <div>
            <el-button
              type="success"
              :loading="syncAllLoading"
              :disabled="syncStatus.isRunning"
              @click="handleSyncAll"
            >
              <el-icon><el-icon-refresh /></el-icon>
              {{ syncAllLoading ? '同步中...' : '立即同步' }}
            </el-button>
            <el-button type="primary" @click="refreshList">刷新列表</el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="packageList"
        style="width: 100%"
        border
      >
        <el-table-column prop="experimentName" label="实验名称" width="120" show-overflow-tooltip />
        <el-table-column prop="experimentVersionName" label="实验版本" width="120" show-overflow-tooltip />
        <el-table-column prop="versionName" label="包版本名称" width="150" show-overflow-tooltip />
        <el-table-column prop="version" label="版本号" width="80" />
        <el-table-column prop="versionDesc" label="版本介绍" min-width="180" show-overflow-tooltip />
        <el-table-column prop="downloadCount" label="下载次数" width="90" />
        <el-table-column label="同步状态" width="100">
          <template #default="scope">
            <sync-status-badge :status="scope.row.syncStatus" />
          </template>
        </el-table-column>
        <el-table-column prop="fileSize" label="文件大小" width="120">
          <template #default="scope">
            {{ formatFileSize(scope.row.fileSize) }}
          </template>
        </el-table-column>
        <el-table-column label="更新时间" width="150">
          <template #default="scope">
            {{ formatDateTime(scope.row.updatedAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="viewDetail(scope.row.id)"
            >
              详情
            </el-button>
            <el-button
              v-if="scope.row.syncStatus !== 2"
              type="success"
              size="small"
              :disabled="scope.row.syncStatus === 1"
              :loading="syncingId === scope.row.id"
              @click="syncPackage(scope.row.id)"
            >
              同步
            </el-button>
            <el-button
              v-if="scope.row.filePath"
              type="warning"
              size="small"
              @click="downloadPackage(scope.row)"
            >
              下载
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:currentPage="listQuery.page"
          v-model:page-size="listQuery.limit"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import SearchBar from '@/components/common/SearchBar.vue'
import SyncStatusBadge from '@/components/business/SyncStatusBadge.vue'

export default {
  name: 'PackageList',
  components: {
    SearchBar,
    SyncStatusBadge
  },
  data() {
    return {
      loading: false,
      syncingId: null,
      syncAllLoading: false,
      syncStatus: {
        isRunning: false,
        lastRunTime: null
      },
      // 下载配置
      downloadConfig: {
        enableRename: true // 是否启用文件重命名
      },
      searchForm: {
        keyword: '',
        experimentId: '',
        experimentName: '',
        experimentVersionName: '',
        syncStatus: ''
      },
      searchItems: [
        { type: 'input', label: '关键词', prop: 'keyword', placeholder: '版本名称/版本号' },
        { type: 'input', label: '实验名称', prop: 'experimentName' },
        { type: 'input', label: '实验版本', prop: 'experimentVersionName' },
        { type: 'input', label: '实验ID', prop: 'experimentId' },
        {
          type: 'select',
          label: '同步状态',
          prop: 'syncStatus',
          options: [
            { label: '未同步', value: '0' },
            { label: '同步中', value: '1' },
            { label: '已同步', value: '2' },
            { label: '同步失败', value: '3' }
          ]
        }
      ],
      listQuery: {
        page: 1,
        limit: 10
      },
      total: 0
    }
  },
  computed: {
    ...mapGetters({
      packageList: 'packages/packageList'
    })
  },
  created() {
    this.getList()
    this.getSyncStatus()
  },
  methods: {
    getList() {
      this.loading = true

      // 合并查询参数
      const params = {
        ...this.listQuery,
        ...this.searchForm
      }

      // 将limit转换为pageSize以匹配后端API
      if (params.limit) {
        params.pageSize = params.limit
        delete params.limit
      }

      this.$store.dispatch('packages/getPackages', params)
        .then(response => {
          const { data } = response
          this.total = data.total || 0
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleSearch(form) {
      this.searchForm = { ...form }
      this.listQuery.page = 1
      this.getList()
    },
    handleReset() {
      this.searchForm = {
        keyword: '',
        experimentId: '',
        experimentName: '',
        experimentVersionName: '',
        syncStatus: ''
      }
      // 重置后自动搜索
      this.listQuery.page = 1
      this.getList()
    },
    refreshList() {
      this.getList()
    },
    viewDetail(id) {
      this.$router.push(`/packages/${id}`)
    },
    syncPackage(id) {
      this.syncingId = id
      this.$store.dispatch('packages/syncPackage', id)
        .then(() => {
          this.$message.success('同步请求已发送')
          // 刷新列表
          setTimeout(() => {
            this.getList()
          }, 1000)
        })
        .catch(error => {
          this.$message.error(`同步失败: ${error.message || '未知错误'}`)
        })
        .finally(() => {
          this.syncingId = null
        })
    },
    formatFileSize(size) {
      if (!size) return '0 B'

      const units = ['B', 'KB', 'MB', 'GB', 'TB']
      let i = 0
      while (size >= 1024 && i < units.length - 1) {
        size /= 1024
        i++
      }

      return `${size.toFixed(2)} ${units[i]}`
    },

    // 格式化日期时间为 YYYY-MM-DD HH:MM:SS
    formatDateTime(dateTime) {
      if (!dateTime) return '';

      // 处理ISO 8601格式的时间字符串
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) return dateTime; // 如果转换失败，返回原始值

      // 格式化为 YYYY-MM-DD HH:MM:SS
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    handleSizeChange(newSize) {
      this.listQuery.limit = newSize
      this.getList()
    },
    handleCurrentChange(newPage) {
      this.listQuery.page = newPage
      this.getList()
    },
    // 手动触发全局同步
    async handleSyncAll() {
      this.syncAllLoading = true
      try {
        await this.$store.dispatch('packages/syncAll')
        this.$message.success('同步任务已启动')
        // 立即刷新同步状态和列表
        setTimeout(() => {
          this.getSyncStatus()
          this.getList()
        }, 1000)
      } catch (error) {
        console.error('启动同步失败:', error)
        this.$message.error(`启动同步失败: ${error.message || '未知错误'}`)
      } finally {
        this.syncAllLoading = false
      }
    },
    // 获取同步状态
    async getSyncStatus() {
      try {
        const response = await this.$store.dispatch('packages/getSyncStatus')
        if (response && response.data) {
          this.syncStatus = response.data
        }
      } catch (error) {
        console.error('获取同步状态失败:', error)
        // 使用默认状态
        this.syncStatus = {
          isRunning: false,
          lastRunTime: null
        }
      }
    },
    // 下载包文件
    downloadPackage(packageRow) {
      if (!packageRow.filePath) {
        this.$message.error('文件路径为空，无法下载')
        return
      }

      // 构建下载URL
      // filePath 格式类似：/packages/schoolId/filename.zip
      // 后端已配置静态文件服务：r.Static("/packages", "./packages")
      // 需要加上后端服务器地址，但不包含 /api 部分
      const baseUrl = window.location.origin // 获取当前页面的协议+域名+端口
      const downloadUrl = `${baseUrl}${packageRow.filePath}`

      // 生成文件名
      let filename
      if (this.downloadConfig.enableRename) {
        // 使用重命名规则：实验名称_实验版本名称_包版本名称.zip
        const experimentName = packageRow.experimentName || '未知实验'
        const experimentVersionName = packageRow.experimentVersionName || '未知版本'
        const versionName = packageRow.versionName || '未知包版本'

        // 清理文件名中的特殊字符
        const cleanName = (name) => name.replace(/[<>:"/\\|?*]/g, '_')

        filename = `${cleanName(experimentName)}_${cleanName(experimentVersionName)}_${cleanName(versionName)}.zip`
      } else {
        // 使用原始文件名
        filename = packageRow.filePath.split('/').pop() || 'package.zip'
      }

      // 创建下载链接
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename
      link.style.display = 'none'

      // 添加到DOM并触发下载
      document.body.appendChild(link)
      link.click()

      // 清理
      document.body.removeChild(link)

      this.$message.success(`开始下载：${filename}`)
    }
  }
}
</script>

<style scoped>
.package-list-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
