<template>
  <div class="package-detail-container" v-loading="loading">
    <el-card>
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <span class="title">包版本详情</span>
            <sync-status-badge v-if="packageDetail" :status="packageDetail.syncStatus" />
          </div>
          <div class="header-right">
            <el-button type="primary" @click="goBack">返回</el-button>
            <el-button
              v-if="packageDetail && packageDetail.syncStatus !== 2"
              type="success"
              :disabled="packageDetail && packageDetail.syncStatus === 1"
              :loading="syncing"
              @click="syncPackage"
            >
              同步
            </el-button>
          </div>
        </div>
      </template>

      <div v-if="packageDetail" class="package-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="实验名称">{{ packageDetail.experimentName || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="实验版本">{{ packageDetail.experimentVersionName || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="包版本名称">{{ packageDetail.versionName }}</el-descriptions-item>
          <el-descriptions-item label="版本号">{{ packageDetail.version }}</el-descriptions-item>
          <el-descriptions-item label="实验ID">{{ packageDetail.experimentId }}</el-descriptions-item>
          <el-descriptions-item label="实验版本ID">{{ packageDetail.experimentVersionId }}</el-descriptions-item>
          <el-descriptions-item label="文件大小">{{ formatFileSize(packageDetail.fileSize) }}</el-descriptions-item>
          <el-descriptions-item label="下载次数">{{ packageDetail.downloadCount }}</el-descriptions-item>
          <el-descriptions-item label="文件哈希">{{ packageDetail.fileHash }}</el-descriptions-item>
          <el-descriptions-item label="本地文件路径">{{ packageDetail.filePath }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(packageDetail.createdAt) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime(packageDetail.updatedAt) }}</el-descriptions-item>
          <el-descriptions-item label="版本描述" :span="2">
            {{ packageDetail.versionDesc || '无描述' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div v-else class="no-data">
        <el-empty description="未找到包版本信息" />
      </div>

      <div class="sync-history" v-if="packageDetail">
        <h3>同步历史记录</h3>
        <el-table :data="syncLogs" style="width: 100%" border>
          <el-table-column prop="action" label="操作" width="100" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : scope.row.status === 0 ? 'warning' : 'danger'">
                {{ scope.row.status === 1 ? '成功' : scope.row.status === 0 ? '进行中' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="message" label="消息" show-overflow-tooltip />
          <el-table-column label="时间" width="150">
            <template #default="scope">
              {{ formatDateTime(scope.row.createdAt) }}
            </template>
          </el-table-column>
        </el-table>

        <div v-if="syncLogs.length === 0" class="no-data">
          <el-empty description="暂无同步记录" />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import SyncStatusBadge from '@/components/business/SyncStatusBadge.vue'

export default {
  name: 'PackageDetail',
  components: {
    SyncStatusBadge
  },
  data() {
    return {
      loading: false,
      syncing: false,
      syncLogs: []
    }
  },
  computed: {
    ...mapGetters({
      packageDetail: 'packages/packageDetail'
    }),
    packageId() {
      return this.$route.params.id
    }
  },
  created() {
    this.getPackageDetail()
    this.getSyncLogs()
  },
  methods: {
    getPackageDetail() {
      this.loading = true
      this.$store.dispatch('packages/getPackage', this.packageId)
        .then(response => {
          // 如果包详情中包含同步日志，直接使用
          const { data } = response
          if (data && data.syncLogs) {
            this.syncLogs = data.syncLogs
          }
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    getSyncLogs() {
      this.$store.dispatch('logs/getSyncLogs', {
        packageVersionId: this.packageId,
        page: 1,
        limit: 10
      })
        .then(response => {
          const { data } = response
          // 适配后端返回的数据结构
          if (data && data.items) {
            this.syncLogs = data.items
          } else if (data && data.syncLogs) {
            this.syncLogs = data.syncLogs
          } else {
            this.syncLogs = data.logs || []
          }
        })
        .catch(error => {
          console.error('获取同步日志失败:', error)
          this.syncLogs = []
        })
    },
    syncPackage() {
      this.syncing = true
      this.$store.dispatch('packages/syncPackage', this.packageId)
        .then(() => {
          this.$message.success('同步请求已发送')
          // 刷新详情
          setTimeout(() => {
            this.getPackageDetail()
            this.getSyncLogs()
          }, 1000)
        })
        .catch(error => {
          this.$message.error(`同步失败: ${error.message || '未知错误'}`)
        })
        .finally(() => {
          this.syncing = false
        })
    },
    goBack() {
      this.$router.push('/packages')
    },
    formatFileSize(size) {
      if (!size) return '0 B'

      const units = ['B', 'KB', 'MB', 'GB', 'TB']
      let i = 0
      while (size >= 1024 && i < units.length - 1) {
        size /= 1024
        i++
      }

      return `${size.toFixed(2)} ${units[i]}`
    },

    // 格式化日期时间为 YYYY-MM-DD HH:MM:SS
    formatDateTime(dateTime) {
      if (!dateTime) return '';

      // 处理ISO 8601格式的时间字符串
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) return dateTime; // 如果转换失败，返回原始值

      // 格式化为 YYYY-MM-DD HH:MM:SS
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
  }
}
</script>

<style scoped>
.package-detail-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.package-info {
  margin-bottom: 30px;
}

.sync-history {
  margin-top: 30px;
}

.sync-history h3 {
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bold;
}

.no-data {
  padding: 20px 0;
}
</style>
