<template>
  <div class="sync-logs-container">
    <!-- 搜索栏 -->
    <search-bar
      :search-items="searchItems"
      :initial-values="searchForm"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 日志列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>同步日志</span>
          <el-button type="primary" @click="refreshList">刷新</el-button>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="syncLogs"
        style="width: 100%"
        border
      >
        <el-table-column prop="packageVersionId" label="包版本ID" width="120" />
        <el-table-column prop="action" label="操作" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : scope.row.status === 0 ? 'warning' : 'danger'">
              {{ scope.row.status === 1 ? '成功' : scope.row.status === 0 ? '进行中' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="消息" min-width="300" show-overflow-tooltip />
        <el-table-column label="时间" width="150">
          <template #default="scope">
            {{ formatDateTime(scope.row.createdAt) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:currentPage="listQuery.page"
          v-model:page-size="listQuery.limit"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import SearchBar from '@/components/common/SearchBar.vue'

export default {
  name: 'SyncLogs',
  components: {
    SearchBar
  },
  data() {
    return {
      loading: false,
      searchForm: {
        packageVersionId: '',
        action: '',
        status: '',
        dateRange: []
      },
      searchItems: [
        { type: 'input', label: '包版本ID', prop: 'packageVersionId' },
        {
          type: 'select',
          label: '操作',
          prop: 'action',
          options: [
            { label: '检查', value: 'check' },
            { label: '创建', value: 'create' },
            { label: '接受', value: 'accept' },
            { label: '下载', value: 'download' },
            { label: '验证', value: 'verify' },
            { label: '完成', value: 'complete' },
            { label: '状态更新', value: 'status' },
            { label: '手动同步', value: 'manual_sync' }
          ]
        },
        {
          type: 'select',
          label: '状态',
          prop: 'status',
          options: [
            { label: '成功', value: '1' },
            { label: '失败', value: '0' }
          ]
        },
        { type: 'daterange', label: '时间范围', prop: 'dateRange' }
      ],
      listQuery: {
        page: 1,
        limit: 10
      },
      total: 0
    }
  },
  computed: {
    ...mapGetters({
      syncLogs: 'logs/syncLogs',
      syncTotal: 'logs/syncTotal'
    })
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true

      // 处理日期范围
      let params = { ...this.listQuery }

      // 将limit转换为pageSize以匹配后端API
      if (params.limit) {
        params.pageSize = params.limit
        delete params.limit
      }

      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
        params.startDate = this.formatDate(this.searchForm.dateRange[0])
        params.endDate = this.formatDate(this.searchForm.dateRange[1])
      }

      // 添加其他搜索条件
      if (this.searchForm.packageVersionId) {
        params.packageVersionId = this.searchForm.packageVersionId
      }

      if (this.searchForm.action) {
        params.action = this.searchForm.action
      }

      if (this.searchForm.status) {
        params.status = this.searchForm.status
      }

      this.$store.dispatch('logs/getSyncLogs', params)
        .then(() => {
          this.total = this.syncTotal
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleSearch(form) {
      this.searchForm = { ...form }
      this.listQuery.page = 1
      this.getList()
    },
    handleReset() {
      this.searchForm = {
        packageVersionId: '',
        action: '',
        status: '',
        dateRange: []
      }
    },
    refreshList() {
      this.getList()
    },
    formatDate(date) {
      if (!date) return ''

      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')

      return `${year}-${month}-${day}`
    },

    // 格式化日期时间为 YYYY-MM-DD HH:MM:SS
    formatDateTime(dateTime) {
      if (!dateTime) return '';

      // 处理ISO 8601格式的时间字符串
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) return dateTime; // 如果转换失败，返回原始值

      // 格式化为 YYYY-MM-DD HH:MM:SS
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    handleSizeChange(newSize) {
      this.listQuery.limit = newSize
      this.getList()
    },
    handleCurrentChange(newPage) {
      this.listQuery.page = newPage
      this.getList()
    }
  }
}
</script>

<style scoped>
.sync-logs-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
