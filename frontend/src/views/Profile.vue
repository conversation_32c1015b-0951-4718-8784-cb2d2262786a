<template>
  <div class="profile-container">
    <el-card v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>用户信息</span>
          <el-button type="primary" size="small" @click="getUserInfo" :loading="loading">刷新</el-button>
        </div>
      </template>

      <el-descriptions :column="1" border>
        <el-descriptions-item label="用户名">{{ currentUser.username }}</el-descriptions-item>
<!--        <el-descriptions-item label="学校ID">-->
<!--          <el-tag type="info">{{ currentUser.schoolId || '未知' }}</el-tag>-->
<!--        </el-descriptions-item>-->
        <el-descriptions-item label="学校名称">{{ currentUser.schoolName }}</el-descriptions-item>
        <el-descriptions-item label="最后登录时间">{{ lastLoginTime }}</el-descriptions-item>
      </el-descriptions>
    </el-card>


  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { formatDateTime } from '@/utils/format'

export default {
  name: 'Profile',
  data() {
    return {
      loading: false
    }
  },
  computed: {
    ...mapGetters({
      currentUser: 'user/currentUser'
    }),
    lastLoginTime() {
      return this.currentUser.lastLogin
        ? formatDateTime(this.currentUser.lastLogin)
        : '未知'
    }
  },
  created() {
    // 在组件创建时获取最新的用户信息，确保显示最新的登录时间
    this.getUserInfo()
  },
  methods: {
    // 获取用户信息
    getUserInfo() {
      this.loading = true
      this.$store.dispatch('user/getUserInfo')
        .then(() => {
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style scoped>
.profile-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mt-20 {
  margin-top: 20px;
}

.password-info {
  padding: 20px 0;
}
</style>
