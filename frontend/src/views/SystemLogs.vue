<template>
  <div class="system-logs-container">
    <!-- 搜索栏 -->
    <search-bar
      :search-items="searchItems"
      :initial-values="searchForm"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 日志列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>系统日志</span>
          <el-button type="primary" @click="refreshList">刷新</el-button>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="systemLogs"
        style="width: 100%"
        border
      >
        <el-table-column prop="level" label="级别" width="100">
          <template #default="scope">
            <el-tag :type="getLevelType(scope.row.level)">
              {{ scope.row.level }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="module" label="模块" width="120" />
        <el-table-column prop="action" label="操作" width="120" />
        <el-table-column prop="message" label="消息" min-width="300" show-overflow-tooltip />
        <el-table-column label="时间" width="150">
          <template #default="scope">
            {{ formatDateTime(scope.row.createdAt) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:currentPage="listQuery.page"
          v-model:page-size="listQuery.limit"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import SearchBar from '@/components/common/SearchBar.vue'

export default {
  name: 'SystemLogs',
  components: {
    SearchBar
  },
  data() {
    return {
      loading: false,
      searchForm: {
        level: '',
        module: '',
        action: '',
        keyword: '',
        dateRange: []
      },
      searchItems: [
        {
          type: 'select',
          label: '级别',
          prop: 'level',
          options: [
            { label: 'INFO', value: 'INFO' },
            { label: 'WARN', value: 'WARN' },
            { label: 'ERROR', value: 'ERROR' },
            { label: 'DEBUG', value: 'DEBUG' }
          ]
        },
        {
          type: 'select',
          label: '模块',
          prop: 'module',
          options: [
            { label: '全部', value: '' },
            { label: 'SyncService', value: 'SyncService' },
            { label: 'Main', value: 'Main' },
            { label: 'Auth', value: 'Auth' },
            { label: 'API', value: 'API' }
          ]
        },
        { type: 'input', label: '操作', prop: 'action' },
        { type: 'input', label: '关键词', prop: 'keyword', placeholder: '消息内容' },
        { type: 'daterange', label: '时间范围', prop: 'dateRange' }
      ],
      listQuery: {
        page: 1,
        limit: 10
      },
      total: 0
    }
  },
  computed: {
    ...mapGetters({
      systemLogs: 'logs/systemLogs',
      systemTotal: 'logs/systemTotal'
    })
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true

      // 处理日期范围
      let params = { ...this.listQuery }

      // 将limit转换为pageSize以匹配后端API
      if (params.limit) {
        params.pageSize = params.limit
        delete params.limit
      }

      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
        params.startDate = this.formatDate(this.searchForm.dateRange[0])
        params.endDate = this.formatDate(this.searchForm.dateRange[1])
      }

      // 添加其他搜索条件
      if (this.searchForm.level) {
        params.level = this.searchForm.level
      }

      if (this.searchForm.module) {
        params.module = this.searchForm.module
      }

      if (this.searchForm.action) {
        params.action = this.searchForm.action
      }

      if (this.searchForm.keyword) {
        params.keyword = this.searchForm.keyword
      }

      this.$store.dispatch('logs/getSystemLogs', params)
        .then(() => {
          this.total = this.systemTotal
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleSearch(form) {
      this.searchForm = { ...form }
      this.listQuery.page = 1
      this.getList()
    },
    handleReset() {
      this.searchForm = {
        level: '',
        module: '',
        action: '',
        keyword: '',
        dateRange: []
      }
      // 重置后自动搜索
      this.listQuery.page = 1
      this.getList()
    },
    refreshList() {
      this.getList()
    },
    getLevelType(level) {
      if (!level) return 'info'

      const levelMap = {
        'INFO': 'info',
        'WARN': 'warning',
        'ERROR': 'danger',
        'DEBUG': 'success'
      }
      return levelMap[level.toUpperCase()] || 'info'
    },
    formatDate(date) {
      if (!date) return ''

      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')

      return `${year}-${month}-${day}`
    },

    // 格式化日期时间为 YYYY-MM-DD HH:MM:SS
    formatDateTime(dateTime) {
      if (!dateTime) return '';

      // 处理ISO 8601格式的时间字符串
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) return dateTime; // 如果转换失败，返回原始值

      // 格式化为 YYYY-MM-DD HH:MM:SS
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    handleSizeChange(newSize) {
      this.listQuery.limit = newSize
      this.getList()
    },
    handleCurrentChange(newPage) {
      this.listQuery.page = newPage
      this.getList()
    }
  }
}
</script>

<style scoped>
.system-logs-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
