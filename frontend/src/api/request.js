import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '../router'

// 创建axios实例
const service = axios.create({
  baseURL: '/api',
  timeout: 30000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data

    // 如果返回的状态码不是200，说明接口请求有误
    if (response.status !== 200) {
      ElMessage({
        message: res.message || 'Error',
        type: 'error',
        duration: 5 * 1000
      })

      // 401: 未登录或token过期
      if (response.status === 401) {
        // 重新登录
        router.push('/login')
      }

      return Promise.reject(new Error(res.message || 'Error'))
    } else {
      return res
    }
  },
  error => {
    console.error('Response error:', error)

    // 获取错误信息
    const errorMessage = error.response && error.response.data && error.response.data.message
      ? error.response.data.message
      : error.message || '请求失败'

    // 根据状态码处理不同错误
    if (error.response) {
      switch (error.response.status) {
        case 400:
          ElMessage({
            message: `请求参数错误: ${errorMessage}`,
            type: 'error',
            duration: 5 * 1000
          })
          break

        case 401:
          // 清除token
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          // 重定向到登录页
          ElMessage({
            message: `登录失败，请重新登录。${errorMessage}`,
            type: 'warning',
            duration: 5 * 1000
          })
          router.push('/login')
          break

        case 403:
          ElMessage({
            message: '没有权限执行此操作',
            type: 'error',
            duration: 5 * 1000
          })
          break

        case 404:
          ElMessage({
            message: '请求的资源不存在',
            type: 'error',
            duration: 5 * 1000
          })
          break

        case 500:
          ElMessage({
            message: `服务器错误: ${errorMessage}`,
            type: 'error',
            duration: 5 * 1000
          })
          break

        default:
          ElMessage({
            message: errorMessage,
            type: 'error',
            duration: 5 * 1000
          })
      }
    } else {
      // 网络错误
      ElMessage({
        message: '网络连接失败，请检查网络设置',
        type: 'error',
        duration: 5 * 1000
      })
    }

    return Promise.reject(error)
  }
)

export default service
