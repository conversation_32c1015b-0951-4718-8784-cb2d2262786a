import request from './request'

export default {
  // 获取同步日志
  getSyncLogs(params) {
    return request({
      url: '/logs/sync',
      method: 'get',
      params
    })
  },
  
  // 获取系统日志
  getSystemLogs(params) {
    return request({
      url: '/logs/system',
      method: 'get',
      params
    })
  },
  
  // 获取最近的日志
  getRecentLogs() {
    return request({
      url: '/logs/recent',
      method: 'get'
    })
  }
}
