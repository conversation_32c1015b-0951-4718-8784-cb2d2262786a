import request from './request'

export default {
  // 获取包版本列表
  getPackages(params) {
    return request({
      url: '/packages',
      method: 'get',
      params
    })
  },

  // 获取包版本详情
  getPackage(id) {
    return request({
      url: `/packages/${id}`,
      method: 'get'
    })
  },

  // 手动同步包版本
  syncPackage(id) {
    return request({
      url: `/packages/${id}/sync`,
      method: 'post'
    })
  },

  // 获取同步状态
  getSyncStatus() {
    return request({
      url: '/packages/sync/status',
      method: 'get'
    })
  },

  // 手动触发全局同步
  syncAll() {
    return request({
      url: '/packages/sync/all',
      method: 'post'
    })
  }
}
