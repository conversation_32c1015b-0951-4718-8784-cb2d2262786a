import api from '../../api/user'

const state = {
  token: localStorage.getItem('token') || '',
  user: JSON.parse(localStorage.getItem('user') || '{}')
}

const getters = {
  isLoggedIn: state => !!state.token,
  currentUser: state => state.user
}

const actions = {
  // 登录
  login({ commit }, credentials) {
    return new Promise((resolve, reject) => {
      api.login(credentials)
        .then(response => {
          // 检查响应中是否包含token和user
          if (response.data && response.data.token) {
            const { token, user } = response.data
            localStorage.setItem('token', token)
            localStorage.setItem('user', JSON.stringify(user || {}))
            commit('SET_TOKEN', token)
            commit('SET_USER', user || {})
            resolve(response)
          } else {
            // 如果响应格式不符合预期，返回错误
            reject(new Error('Invalid response format'))
          }
        })
        .catch(error => {
          reject(error)
        })
    })
  },

  // 获取用户信息
  getUserInfo({ commit }) {
    return new Promise((resolve, reject) => {
      api.getUserInfo()
        .then(response => {
          const { user } = response.data
          commit('SET_USER', user)
          resolve(response)
        })
        .catch(error => {
          reject(error)
        })
    })
  },

  // 登出
  logout({ commit }) {
    return new Promise((resolve) => {
      commit('RESET_STATE')
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      resolve()
    })
  },

  // 修改密码
  changePassword({ commit }, data) {
    return new Promise((resolve, reject) => {
      api.changePassword(data)
        .then(response => {
          resolve(response)
        })
        .catch(error => {
          reject(error)
        })
    })
  }
}

const mutations = {
  SET_TOKEN(state, token) {
    state.token = token
  },
  SET_USER(state, user) {
    state.user = user
  },
  RESET_STATE(state) {
    state.token = ''
    state.user = {}
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
