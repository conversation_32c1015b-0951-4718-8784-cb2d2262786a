import api from '../../api/config'

const state = {
  configs: [],
  loading: false
}

const getters = {
  configs: state => state.configs,
  isLoading: state => state.loading,
  // 获取特定配置项的值
  getConfigValue: state => key => {
    const config = state.configs.find(item => item.key === key)
    return config ? config.value : null
  }
}

const actions = {
  // 获取配置
  getConfig({ commit }) {
    commit('SET_LOADING', true)
    return new Promise((resolve, reject) => {
      api.getConfig()
        .then(response => {
          const { data } = response
          commit('SET_CONFIGS', data || [])
          commit('SET_LOADING', false)
          resolve(response)
        })
        .catch(error => {
          commit('SET_LOADING', false)
          reject(error)
        })
    })
  },

  // 更新配置
  updateConfig({ commit }, data) {
    commit('SET_LOADING', true)
    return new Promise((resolve, reject) => {
      // 检查是否是批量更新
      if (data.configs && Array.isArray(data.configs)) {
        // 创建一个Promise数组，每个Promise处理一个配置项的更新
        const updatePromises = data.configs.map(config => {
          return api.updateConfig({
            key: config.key,
            value: config.value
          })
        })

        // 使用Promise.all等待所有更新完成
        Promise.all(updatePromises)
          .then(responses => {
            commit('SET_LOADING', false)
            resolve(responses[0]) // 返回第一个响应作为结果
          })
          .catch(error => {
            commit('SET_LOADING', false)
            reject(error)
          })
      } else {
        // 单个配置更新
        api.updateConfig(data)
          .then(response => {
            commit('SET_LOADING', false)
            resolve(response)
          })
          .catch(error => {
            commit('SET_LOADING', false)
            reject(error)
          })
      }
    })
  }
}

const mutations = {
  SET_CONFIGS(state, configs) {
    state.configs = configs
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
