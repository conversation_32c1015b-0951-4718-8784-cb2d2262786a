import api from '../../api/packages'

const state = {
  packages: [],
  currentPackage: null,
  total: 0,
  loading: false,
  syncStatus: {}
}

const getters = {
  packageList: state => state.packages,
  packageDetail: state => state.currentPackage,
  packageTotal: state => state.total,
  isLoading: state => state.loading,
  syncStatus: state => state.syncStatus
}

const actions = {
  // 获取包版本列表
  getPackages({ commit }, params) {
    commit('SET_LOADING', true)
    return new Promise((resolve, reject) => {
      api.getPackages(params)
        .then(response => {
          const { data } = response
          // 优先使用packages字段，如果没有则使用items字段
          const packages = data.packages || data.items || []
          commit('SET_PACKAGES', packages)
          commit('SET_TOTAL', data.total || 0)
          commit('SET_LOADING', false)
          resolve(response)
        })
        .catch(error => {
          commit('SET_LOADING', false)
          reject(error)
        })
    })
  },

  // 获取包版本详情
  getPackage({ commit }, id) {
    commit('SET_LOADING', true)
    return new Promise((resolve, reject) => {
      api.getPackage(id)
        .then(response => {
          const { data } = response
          // 检查数据结构，适配后端返回的数据格式
          if (data && data.packageVersion) {
            commit('SET_CURRENT_PACKAGE', data.packageVersion)

            // 如果有同步日志，也保存到store中
            if (data.syncLogs) {
              commit('SET_SYNC_LOGS', data.syncLogs)
            }
          } else {
            commit('SET_CURRENT_PACKAGE', data)
          }
          commit('SET_LOADING', false)
          resolve(response)
        })
        .catch(error => {
          commit('SET_LOADING', false)
          reject(error)
        })
    })
  },

  // 手动同步包版本
  syncPackage({ commit }, id) {
    return new Promise((resolve, reject) => {
      api.syncPackage(id)
        .then(response => {
          resolve(response)
        })
        .catch(error => {
          reject(error)
        })
    })
  },

  // 获取同步状态
  getSyncStatus({ commit }) {
    return new Promise((resolve, reject) => {
      api.getSyncStatus()
        .then(response => {
          const { data } = response
          commit('SET_SYNC_STATUS', data)
          resolve(response)
        })
        .catch(error => {
          reject(error)
        })
    })
  },

  // 手动触发全局同步
  syncAll({ commit }) {
    return new Promise((resolve, reject) => {
      api.syncAll()
        .then(response => {
          resolve(response)
        })
        .catch(error => {
          reject(error)
        })
    })
  }
}

const mutations = {
  SET_PACKAGES(state, packages) {
    state.packages = packages
  },
  SET_CURRENT_PACKAGE(state, packageData) {
    state.currentPackage = packageData
  },
  SET_SYNC_LOGS(state, logs) {
    // 这个mutation会被logs模块使用
    // 由于我们没有在state中定义syncLogs，所以这里不需要做任何事情
    // 但是为了配合上面的代码，我们需要定义这个mutation
  },
  SET_TOTAL(state, total) {
    state.total = total
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  SET_SYNC_STATUS(state, status) {
    state.syncStatus = status
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
