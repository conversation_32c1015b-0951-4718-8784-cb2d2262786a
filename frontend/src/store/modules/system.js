import api from '../../api/system'

const state = {
  status: {},
  loading: false
}

const getters = {
  systemStatus: state => state.status,
  isLoading: state => state.loading
}

const actions = {
  // 获取系统状态
  getSystemStatus({ commit }) {
    commit('SET_LOADING', true)
    return new Promise((resolve, reject) => {
      api.getSystemStatus()
        .then(response => {
          const { data } = response
          commit('SET_SYSTEM_STATUS', data)
          commit('SET_LOADING', false)
          resolve(response)
        })
        .catch(error => {
          commit('SET_LOADING', false)
          reject(error)
        })
    })
  }
}

const mutations = {
  SET_SYSTEM_STATUS(state, status) {
    state.status = status
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
