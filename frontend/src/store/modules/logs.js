import api from '../../api/logs'

const state = {
  syncLogs: [],
  systemLogs: [],
  recentLogs: [],
  total: {
    sync: 0,
    system: 0
  },
  loading: false
}

const getters = {
  syncLogs: state => state.syncLogs,
  systemLogs: state => state.systemLogs,
  recentLogs: state => state.recentLogs,
  syncTotal: state => state.total.sync,
  systemTotal: state => state.total.system,
  isLoading: state => state.loading
}

const actions = {
  // 获取同步日志
  getSyncLogs({ commit }, params) {
    commit('SET_LOADING', true)
    return new Promise((resolve, reject) => {
      api.getSyncLogs(params)
        .then(response => {
          const { data } = response
          // 适配后端返回的数据结构
          if (data.items) {
            commit('SET_SYNC_LOGS', data.items)
            commit('SET_SYNC_TOTAL', data.total || 0)
          } else if (data.logs) {
            commit('SET_SYNC_LOGS', data.logs)
            commit('SET_SYNC_TOTAL', data.total || 0)
          } else {
            commit('SET_SYNC_LOGS', [])
            commit('SET_SYNC_TOTAL', 0)
          }
          commit('SET_LOADING', false)
          resolve(response)
        })
        .catch(error => {
          commit('SET_LOADING', false)
          reject(error)
        })
    })
  },

  // 获取系统日志
  getSystemLogs({ commit }, params) {
    commit('SET_LOADING', true)
    return new Promise((resolve, reject) => {
      api.getSystemLogs(params)
        .then(response => {
          const { data } = response
          // 适配后端返回的数据结构
          if (data.items) {
            commit('SET_SYSTEM_LOGS', data.items)
            commit('SET_SYSTEM_TOTAL', data.total || 0)
          } else if (data.logs) {
            commit('SET_SYSTEM_LOGS', data.logs)
            commit('SET_SYSTEM_TOTAL', data.total || 0)
          } else {
            commit('SET_SYSTEM_LOGS', [])
            commit('SET_SYSTEM_TOTAL', 0)
          }
          commit('SET_LOADING', false)
          resolve(response)
        })
        .catch(error => {
          commit('SET_LOADING', false)
          reject(error)
        })
    })
  },

  // 获取最近的日志
  getRecentLogs({ commit }) {
    return new Promise((resolve, reject) => {
      api.getRecentLogs()
        .then(response => {
          const { data } = response
          // 优先使用logs字段，如果没有则合并syncLogs和systemLogs
          if (data.logs) {
            commit('SET_RECENT_LOGS', data.logs)
          } else if (data.syncLogs || data.systemLogs) {
            // 合并同步日志和系统日志
            const syncLogs = (data.syncLogs || []).map(log => ({
              id: log.id,
              action: log.action,
              message: log.message,
              createdAt: log.createdAt,
              type: 'sync'
            }))

            const systemLogs = (data.systemLogs || []).map(log => ({
              id: log.id,
              action: log.module + '.' + log.action,
              message: log.message,
              createdAt: log.createdAt,
              type: 'system',
              level: log.level
            }))

            // 合并日志并按时间排序
            const logs = [...syncLogs, ...systemLogs]
              .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
              .slice(0, 5) // 只取前5条

            commit('SET_RECENT_LOGS', logs)
          } else {
            commit('SET_RECENT_LOGS', [])
          }
          resolve(response)
        })
        .catch(error => {
          reject(error)
        })
    })
  }
}

const mutations = {
  SET_SYNC_LOGS(state, logs) {
    state.syncLogs = logs
  },
  SET_SYSTEM_LOGS(state, logs) {
    state.systemLogs = logs
  },
  SET_RECENT_LOGS(state, logs) {
    state.recentLogs = logs
  },
  SET_SYNC_TOTAL(state, total) {
    state.total.sync = total
  },
  SET_SYSTEM_TOTAL(state, total) {
    state.total.system = total
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
