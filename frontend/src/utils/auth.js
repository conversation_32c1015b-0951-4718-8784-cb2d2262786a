/**
 * 获取Token
 * @returns {string} Token
 */
export function getToken() {
  return localStorage.getItem('token')
}

/**
 * 设置Token
 * @param {string} token Token
 */
export function setToken(token) {
  localStorage.setItem('token', token)
}

/**
 * 移除Token
 */
export function removeToken() {
  localStorage.removeItem('token')
}

/**
 * 获取用户信息
 * @returns {Object} 用户信息
 */
export function getUserInfo() {
  const userStr = localStorage.getItem('user')
  return userStr ? JSON.parse(userStr) : {}
}

/**
 * 设置用户信息
 * @param {Object} user 用户信息
 */
export function setUserInfo(user) {
  localStorage.setItem('user', JSON.stringify(user))
}

/**
 * 移除用户信息
 */
export function removeUserInfo() {
  localStorage.removeItem('user')
}

/**
 * 清除所有认证信息
 */
export function clearAuth() {
  removeToken()
  removeUserInfo()
}
