/**
 * 格式化文件大小
 * @param {number} size 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(size) {
  if (!size) return '0 B'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let i = 0
  while (size >= 1024 && i < units.length - 1) {
    size /= 1024
    i++
  }
  
  return `${size.toFixed(2)} ${units[i]}`
}

/**
 * 格式化日期时间
 * @param {string|Date} date 日期时间
 * @param {string} format 格式化模式
 * @returns {string} 格式化后的日期时间
 */
export function formatDateTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return ''
  
  const d = new Date(date)
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化日期
 * @param {string|Date} date 日期
 * @returns {string} 格式化后的日期
 */
export function formatDate(date) {
  return formatDateTime(date, 'YYYY-MM-DD')
}

/**
 * 获取同步状态文本
 * @param {number} status 同步状态码
 * @returns {string} 状态文本
 */
export function getSyncStatusText(status) {
  const statusMap = {
    0: '未同步',
    1: '同步中',
    2: '已同步',
    3: '失败'
  }
  return statusMap[status] || '未知'
}

/**
 * 获取同步状态类型
 * @param {number} status 同步状态码
 * @returns {string} 状态类型
 */
export function getSyncStatusType(status) {
  const statusMap = {
    0: 'info',    // 未同步
    1: 'warning', // 同步中
    2: 'success', // 已同步
    3: 'danger'   // 同步失败
  }
  return statusMap[status] || 'info'
}
