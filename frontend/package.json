{"name": "school-package-system-frontend", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --skip-plugins @vue/cli-plugin-eslint", "build": "vue-cli-service build --skip-plugins @vue/cli-plugin-eslint"}, "dependencies": {"axios": "^1.4.0", "core-js": "^3.30.2", "element-plus": "^2.3.6", "@element-plus/icons-vue": "^2.1.0", "vue": "^3.3.4", "vue-router": "^4.2.2", "vuex": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.22.1", "@babel/eslint-parser": "^7.22.1", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "~5.0.8", "eslint": "^8.42.0", "eslint-plugin-vue": "^9.14.1", "sass": "^1.63.3", "sass-loader": "^13.3.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": [], "parserOptions": {"parser": "@babel/eslint-parser", "requireConfigFile": false}, "rules": {"no-console": "off", "no-debugger": "off", "vue/multi-word-component-names": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}