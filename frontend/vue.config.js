const { defineConfig } = require('@vue/cli-service')
const path = require('path')

module.exports = defineConfig({
  transpileDependencies: true,
  // 设置基础路径，生产环境下为 /web/，开发环境下为 /
  publicPath: process.env.NODE_ENV === 'production' ? '/web/' : '/',
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:18080',
        changeOrigin: true
      }
    }
  },
  outputDir: '../backend/web/dist',
  assetsDir: 'static',
  productionSourceMap: false,
  configureWebpack: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    }
  },
  // 禁用ESLint
  lintOnSave: false
})
