import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/* Router Modules */

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
 roles: ['admin','editor']    control the page roles (you can set multiple roles)
 title: 'title'               the name show in sidebar and breadcrumb (recommend set)
 icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
 noCache: true                if set true, the page will no be cached(default is false)
 affix: true                  if set true, the tag will affix in the tags-view
 breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
 activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
 }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/auth-redirect',
    component: () => import('@/views/login/auth-redirect'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error-page/401'),
    hidden: true
  },
  {
    path: '/test',
    component: () => import('@/views/test/xueshi'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index'),
        name: 'Dashboard',
        meta: {title: 'dashboard', icon: 'dashboard', breadcrumb: false}
      }
    ]
  }

]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [

  /** when your routing map is too long, you can split it into small modules **/
  // 用户管理
  {
    path: '/exp/school',
    component: Layout,
    redirect: 'noRedirect',
    name: 'schoolManage',
    alwaysShow: true,
    meta: {
      title: '实验平台-用户管理',
      icon: 'nested',
      roles: ['administrator']
    },
    children: [
      {
        path: 'school',
        component: () => import('@/views/exp/school/school'),
        name: 'schoolMain',
        meta: {title: '学校管理', noCache: true, roles: ['administrator']},
      },
      {
        path: 'teacher',
        component: () => import('@/views/exp/school/teacherIndex'),
        name: 'teacherMain',
        meta: {title: '教师管理', noCache: true},
      },
      {
        path: 'teacherInfo',
        component: () => import('@/views/exp/school/teacherInfo'),
        name: 'teacherInfo',
        hidden: true,
        meta: {title: '教师信息', noCache: true},
      },
      {
        path: 'enumDepartment',
        component: () => import('@/views/exp/school/enumDepartment'),
        hidden: true,
        name: 'schoolEnumDepartment',
        meta: {title: '学院信息', noCache: true}
      },
      {
        path: 'major',
        component: () => import('@/views/exp/school/major'),
        hidden: true,
        name: 'schoolMajor',
        meta: {title: '专业信息', noCache: true}
      },
      {
        path: 'department',
        component: () => import('@/views/exp/school/department'),
        hidden: true,
        name: 'schoolDepartment',
        meta: {title: '学院信息', noCache: true}
      },
      {
        path: 'studentClazz',
        component: () => import('@/views/exp/school/studentClazz'),
        name: 'schoolStudentClazz',
        meta: {title: '学生管理', noCache: true}
      },
      {
        path: 'studentClazzInfo',
        component: () => import('@/views/exp/school/studentClazzInfo'),
        hidden: true,
        name: 'schoolStudentClazzInfo',
        meta: {title: '班级详情', noCache: true}
      },
      {
        path: 'studentList',
        hidden: true,
        component: () => import('@/views/exp/school/studentList'),
        name: 'schoolStudentList',
        meta: {title: '学生列表', noCache: true}
      },
    ]
  },
  // 实验课程管理
  {
    path: '/exp/experiment',
    component: Layout,
    redirect: 'noRedirect',
    name: 'experimentManage',
    alwaysShow: true,
    meta: {
      title: '实验平台-实验管理',
      icon: 'guide',
      roles: ['administrator', 'pico']
    },
    children: [
      {
        path: 'list',
        component: () => import('@/views/exp/experiment/list'),
        name: 'experimentList',
        meta: {title: '实验列表', noCache: true, roles: ['administrator', 'pico']}
      },
      {
        path: 'detail',
        component: () => import('@/views/exp/experiment/detail'),
        name: 'experimentDetail',
        hidden: true,
        meta: {title: '实验详情', noCache: true, roles: ['administrator']}
      },
      {
        path: 'setting',
        component: () => import('@/views/exp/experiment/settingNew'),
        name: 'experimentSetting',
        meta: {title: '实验安排', noCache: true, roles: ['administrator']}
      },
      {
        path: 'settingDetail',
        hidden: true,
        component: () => import('@/views/exp/experiment/settingDetail'),
        name: 'experimentSettingDetail',
        meta: {title: '实验安排详情', noCache: true, roles: ['administrator']}
      },
      {
        path: 'settingEdit',
        hidden: true,
        component: () => import('@/views/exp/experiment/settingEdit'),
        name: 'experimentSettingEdit',
        meta: {title: ' ', noCache: true, roles: ['administrator']}
      },
      // 快速新增学校和实验安排
      {
        path: 'expFast',
        component: () => import('@/views/exp/experiment/expFast'),
        name: 'experimentExpFast',
        meta: {title: '快速实验安排', noCache: true, roles: ['administrator']}
      },
    ]
  },
  // 实验管理后台
  {
    path: '/exp/setting',
    component: Layout,
    redirect: 'noRedirect',
    name: 'experimentSettings',
    alwaysShow: true,
    meta: {
      title: '实验平台-后台配置',
      icon: 'example',
      roles: ['administrator']
    },
    children: [
      {
        path: 'login',
        component: () => import('@/views/exp/setting/login'),
        name: 'settingLogin',
        meta: {title: '登录页配置', noCache: true}
      },
      {
        path: 'know',
        component: () => import('@/views/exp/setting/know'),
        name: 'settingKnow',
        meta: {title: '实验须知设置', noCache: true}
      },
      {
        path: 'message',
        component: () => import('@/views/exp/setting/message'),
        name: 'settingMessage',
        meta: {title: '消息推送', noCache: true}
      },
      {
        path: 'feedback',
        component: () => import('@/views/exp/setting/feedback'),
        name: 'settingFeedback',
        meta: {title: '问题反馈', noCache: true}
      },
      {
        path: 'feedbackView',
        component: () => import('@/views/exp/setting/feedbackView'),
        name: 'settingFeedbackView',
        hidden: true,
        meta: {title: '问题反馈详情', noCache: true}
      },
      {
        path: 'setting',
        component: () => import('@/views/exp/setting/setting'),
        name: 'settingSetting',
        meta: {title: '其他设置', noCache: true}
      },
    ]
  },
  // 实验平台统计
  {
    path: '/exp/statistics',
    component: Layout,
    redirect: 'noRedirect',
    name: 'experimentSettings',
    alwaysShow: true,
    meta: {
      title: '实验平台-统计',
      icon: 'chart',
      roles: ['administrator']
    },
    children: [
      {
        path: 'schoolComprehensiveStatistics',
        component: () => import('@/views/exp/statistics/schoolComprehensiveStatistics'),
        name: 'schoolComprehensiveStatistics',
        meta: {title: '学校综合统计', noCache: true}
      },
    ]
  },
  // 包管理系统
  {
    path: '/packageSystem/',
    component: Layout,
    redirect: 'noRedirect',
    name: 'packageSystem',
    alwaysShow: true,
    meta: {
      title: '包管理系统',
      icon: 'vr',
      roles: ['administrator','game']
    },
    children: [
      {
        path: 'experimentSubVersion',
        component: () => import('@/views/packageSystem/experimentVersion.vue'),
        name: 'experimentSubVersion',
        meta: {title: '1、实验版本', noCache: true}
      },
      {
        path: 'packageVersion',
        component: () => import('@/views/packageSystem/packageVersion.vue'),
        name: 'packageVersion',
        meta: {title: '2、包版本和推送', noCache: true}
      },
      {
        path: 'packagePush',
        component: () => import('@/views/packageSystem/packagePush.vue'),
        name: 'packagePush',
        meta: {title: '3、包推送记录', noCache: true}
      },
      {
        path: 'packageSystemSchool',
        component: () => import('@/views/packageSystem/packageSystemSchool.vue'),
        name: 'packageSystemSchool',
        meta: {title: '学校用户管理', noCache: true}
      },
      {
        path: 'packageClientUpdate',
        component: () => import('@/views/packageSystem/packageClientUpdate.vue'),
        name: 'packageClientUpdate',
        meta: {title: '客户端更新管理', noCache: true}
      },
    ]
  },
  // 设备管理
  {
    path: '/exp/regCode/',
    component: Layout,
    redirect: 'noRedirect',
    name: 'experimentRegCode',
    alwaysShow: true,
    meta: {
      title: '实验平台-设备管理',
      icon: 'vr',
      roles: ['administrator']
    },
    children: [
      {
        path: 'list',
        component: () => import('@/views/exp/regCode/list'),
        name: 'expRegCodeList',
        meta: {title: '注册码列表', noCache: true}
      },
      {
        path: 'logList',
        component: () => import('@/views/exp/regCode/logList'),
        name: 'expRegCodeLogList',
        meta: {title: '注册码日志', noCache: true}
      },
    ]
  },
  // 日志管理
  {
    path: '/logs/',
    component: Layout,
    redirect: 'noRedirect',
    name: 'logsSetting',
    alwaysShow: true,
    meta: {
      title: '实验平台-日志管理',
      icon: 'skill',
      roles: ['administrator']
    },
    children: [
      // 综合日志
      {
        path: 'expLog',
        component: () => import('@/views/logs/expLog'),
        name: 'expLog',
        meta: {title: '在线新平台综合日志', noCache: true}
      },
      {
        path: 'expLoginLog',
        component: () => import('@/views/logs/expLoginLog'),
        name: 'expLoginLog',
        meta: {title: '在线新平台程序实验日志', noCache: true}
      },
      {
        path: 'expNewLoginLog',
        component: () => import('@/views/logs/expNewLoginLog'),
        name: 'expNewLoginLog',
        meta: {title: '（新）在线新平台登录日志', noCache: true}
      },
      {
        path: 'expInnerLoginLog',
        component: () => import('@/views/logs/expInnerLoginLog'),
        name: 'expInnerLoginLog',
        meta: {title: '员工软件异常登录告警', noCache: true}
      },
    ]
  },
  // 账号管理
  {
    path: '/admin',
    component: Layout,
    redirect: 'noRedirect',
    name: 'adminManage',
    alwaysShow: true,
    meta: {
      title: '账号管理',
      icon: 'peoples',
      roles: ['administrator', 'pico']
    },
    children: [
      {
        path: 'list',
        component: () => import('@/views/admin/list'),
        name: 'adminList',
        meta: {title: '账号列表', noCache: true, roles: ['administrator', 'pico']}
      },
      {
        path: 'myInfo',
        component: () => import('@/views/admin/myInfo'),
        name: 'adminMyInfo',
        // todo 所有角色都需要看到
        meta: {title: '我的信息', noCache: true, roles: ['administrator', 'pico']}
      },
    ]
  },
  // 对外资源平台
  {
    path: '/resourcePlatform',
    component: Layout,
    redirect: 'noRedirect',
    name: 'resourcePlatformManage',
    alwaysShow: true,
    meta: {
      title: '对外资源平台管理',
      icon: 'international',
      roles: ['administrator']
    },
    children: [
      {
        path: 'systemSetting',
        component: () => import('@/views/resourcePlatform/systemSetting'),
        name: 'systemSetting',
        meta: {title: '系统设置', noCache: true}
      },
      {
        path: 'userList',
        component: () => import('@/views/resourcePlatform/userList'),
        name: 'userList',
        meta: {title: '用户列表', noCache: true}
      },
      {
        path: 'index',
        component: () => import('@/views/resourcePlatform/index'),
        name: 'index',
        meta: {title: '成果案例', noCache: true}
      },
      {
        path: 'freeTrial',
        component: () => import('@/views/resourcePlatform/freeTrial.vue'),
        name: 'freeTrial',
        meta: {title: '免费试用', noCache: true}
      },
      {
        path: 'resource_experiment',
        component: () => import('@/views/resourcePlatform/resource_experiment.vue'),
        name: 'resource_experiment',
        meta: {title: '资源管理_虚拟仿真课程', noCache: true}
      },
      {
        path: 'resource_3dVideo',
        component: () => import('@/views/resourcePlatform/resource_3dVideo.vue'),
        name: 'resource_3dVideo',
        meta: {title: '资源管理_3D微视频', noCache: true}
      },
      {
        path: 'resource_panorama',
        component: () => import('@/views/resourcePlatform/resource_panorama.vue'),
        name: 'resource_panorama',
        meta: {title: '资源管理_全景资源', noCache: true}
      },
      {
        path: 'resource_redRide',
        component: () => import('@/views/resourcePlatform/resource_redRide.vue'),
        name: 'resource_redRide',
        meta: {title: '资源管理_红色骑行', noCache: true}
      },
      {
        path: 'resource_shortMovie',
        component: () => import('@/views/resourcePlatform/resource_shortMovie.vue'),
        name: 'resource_shortMovie',
        meta: {title: '资源管理_微电影/录课', noCache: true}
      },
      {
        path: 'resource_showConstruct',
        component: () => import('@/views/resourcePlatform/resource_showConstruct.vue'),
        name: 'resource_showConstruct',
        meta: {title: '资源管理_展陈建设', noCache: true}
      },
      {
        path: 'resource_partyBuilding',
        component: () => import('@/views/resourcePlatform/resource_partyBuilding.vue'),
        name: 'resource_partyBuilding',
        meta: {title: '资源管理_党建干训', noCache: true}
      },
      {
        path: 'resource_other',
        component: () => import('@/views/resourcePlatform/resource_other.vue'),
        name: 'resource_other',
        meta: {title: '资源管理_其它', noCache: true}
      },
    ]
  },
  // AI客户端商用版管理
  {
    path: '/ai_client',
    component: Layout,
    redirect: 'noRedirect',
    name: 'aiClientManage',
    alwaysShow: true,
    meta: {
      title: 'AI客户端管理',
      icon: 'international',
      roles: ['administrator']
    },
    children: [
      {
        path: 'organ',
        component: () => import('@/views/aiClient/organ.vue'),
        name: 'organ',
        meta: {title: '机构管理', noCache: true}
      },
      {
        path: 'user',
        component: () => import('@/views/aiClient/user.vue'),
        name: 'user',
        meta: {title: '用户管理', noCache: true}
      },
      {
        path: 'llmConfigManager',
        component: () => import('@/views/aiClient/llmConfigManager.vue'),
        name: 'llmConfigManager',
        meta: {title: '模型配置管理', noCache: true}
      },
      {
        path: 'systemSettings',
        component: () => import('@/views/aiClient/SystemSettings.vue'),
        name: 'systemSettings',
        meta: {title: '系统设置', noCache: true}
      },
    ]
  },
  // 剧本杀管理
  {
    path: '/jbs',
    component: Layout,
    redirect: 'noRedirect',
    name: 'jbsManage',
    alwaysShow: true,
    meta: {
      title: '剧本杀管理',
      icon: 'international',
      roles: ['administrator']
    },
    children: [
      {
        path: 'orderList',
        component: () => import('@/views/jbs/orderList'),
        name: 'orderList',
        meta: {title: '订单管理', noCache: true}
      },
    ]
  },
  //官网管理
  {
    path: '/officialWeb',
    component: Layout,
    redirect: 'noRedirect',
    name: 'officialWebManage',
    alwaysShow: true,
    meta: {
      title: '官网管理',
      icon: 'international',
      roles: ['administrator']
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/officialWeb/index'),
        name: 'indexManage',
        meta: {title: '首页管理', noCache: true}
      },
      {
        path: 'product',
        component: () => import('@/views/officialWeb/product'),
        name: 'productManage',
        meta: {title: '产品中心', noCache: true}
      },
      {
        path: 'newsManage',
        component: () => import('@/views/officialWeb/news'),
        name: 'newsManage',
        meta: {title: '新闻管理', noCache: true}
      },
      {
        path: 'newsEdit',
        component: () => import('@/views/officialWeb/newsEdit'),
        name: 'newsManageEdit',
        hidden: true,
        meta: {title: '新闻编辑', noCache: true}
      },
      {
        path: 'aboutManage',
        component: () => import('@/views/officialWeb/about'),
        name: 'aboutManage',
        meta: {title: '关于我们', noCache: true}
      },
    ]
  },
  // 薪火印记官网管理
  {
    path: '/officialWeb_xhyj',
    component: Layout,
    redirect: 'noRedirect',
    name: 'officialWebManage_xhyj',
    alwaysShow: true,
    meta: {
      title: '薪火印记官网管理',
      icon: 'international',
      roles: ['administrator']
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/xhyjWeb/index'),
        name: 'indexManage',
        meta: {title: '首页管理', noCache: true}
      },
      {
        path: 'product',
        component: () => import('@/views/xhyjWeb/product'),
        name: 'productManage',
        meta: {title: '产品中心', noCache: true}
      },
      {
        path: 'newsManage',
        component: () => import('@/views/xhyjWeb/news'),
        name: 'newsManage',
        meta: {title: '新闻管理', noCache: true}
      },
      {
        path: 'newsEdit',
        component: () => import('@/views/xhyjWeb/newsEdit'),
        name: 'newsManageEditXhyj',
        hidden: true,
        meta: {title: '新闻编辑', noCache: true}
      },
      {
        path: 'aboutManage',
        component: () => import('@/views/xhyjWeb/about'),
        name: 'aboutManage',
        meta: {title: '关于我们', noCache: true}
      },
    ]
  },
  // 活动管理
  {
    path: '/activity',
    component: Layout,
    redirect: 'noRedirect',
    name: 'activityManage',
    alwaysShow: true,
    meta: {
      title: '活动管理',
      icon: 'component',
      roles: ['administrator', 'seller']
    },
    children: [
      // {
      //   path: '54study',
      //   component: () => import('@/views/activity/54study'),
      //   name: 'activity54study',
      //   meta: {title: '党史活动管理', noCache: true, roles: ['administrator']}
      // },
      // {
      //   path: '54studySeller',
      //   component: () => import('@/views/activity/54studySeller'),
      //   name: 'activity54studySeller',
      //   meta: {title: '党史活动管理', noCache: true, roles: ['seller']}
      // },
      {
        path: 'xicaiMeeting',
        component: () => import('@/views/activity/xicaiMeeting'),
        name: 'activityXicaiMeeting',
        meta: {title: '西财会议管理', noCache: true, roles: ['administrator', 'seller']}
      },
    ]
  },
  // 版本管理
  // {
  //   path: '/versionManager',
  //   component: Layout,
  //   redirect: 'noRedirect',
  //   name: 'versionManager',
  //   alwaysShow: true,
  //   meta: {
  //     title: '版本管理',
  //     icon: 'component',
  //     roles: ['administrator', 'game', 'downloader']
  //   },
  //   children: [
  //     {
  //       path: 'gamePackageIndex',
  //       component: () => import('@/views/versionManager/gamePackageIndex'),
  //       name: 'versionManagerGamePackageIndex',
  //       meta: {title: '实验包管理', noCache: true, roles: ['administrator', 'game', 'downloader']}
  //     },
  //     {
  //       path: 'gamePackageExperiment',
  //       hidden: true,
  //       component: () => import('@/views/versionManager/gamePackageExperiment'),
  //       name: 'versionManagerGamePackageExperiment',
  //       meta: {title: '实验包版本管理', noCache: true, roles: ['administrator', 'game']}
  //     },
  //     {
  //       path: 'gamePackageAllView',
  //       hidden: true,
  //       component: () => import('@/views/versionManager/gamePackageAllView'),
  //       name: 'versionManagerGamePackageAllView',
  //       meta: {title: '实验包版本查看', noCache: true, roles: ['administrator', 'game', 'downloader']}
  //     }
  //   ]
  // },
  // 开发管理
  {
    path: '/dev',
    component: Layout,
    redirect: 'noRedirect',
    name: 'devManage',
    alwaysShow: true,
    meta: {
      title: '开发管理',
      icon: 'component',
      roles: ['administrator', 'game']
    },
    children: [
      {
        path: '/devExpList',
        component: () => import('@/views/dev/expList'),
        name: 'devExpList',
        meta: {title: '实验列表', noCache: true, roles: ['administrator', 'game']}
      },
    ]
  },
  // 网站统计管理
  {
    path: '/webAnalytics',
    component: Layout,
    redirect: 'noRedirect',
    name: 'webAnalyticsManage',
    alwaysShow: true,
    meta: {
      title: '网站统计管理',
      icon: 'component',
      roles: ['administrator']
    },
    children: [
      {
        path: 'overview',
        component: () => import('@/views/webAnalytics/overview'),
        name: 'overview',
        meta: {title: '统计概览', noCache: true, roles: ['administrator']}
      },
      {
        path: 'viewDetail',
        component: () => import('@/views/webAnalytics/viewDetail'),
        name: 'viewDetail',
        meta: {title: '访问列表', noCache: true, roles: ['administrator']}
      },
      {
        path: 'event',
        component: () => import('@/views/webAnalytics/event'),
        name: 'event',
        meta: {title: '事件列表', noCache: true, roles: ['administrator']}
      },
    ]
  },
  // 视频平台管理
  {
    path: '/videoPlatform',
    component: Layout,
    redirect: 'noRedirect',
    name: 'videoPlatformManage',
    alwaysShow: true,
    meta: {
      title: '视频平台管理',
      icon: 'component',
      roles: ['administrator']
    },
    children: [
      {
        path: 'list',
        component: () => import('@/views/videoPlatform/list'),
        name: 'overview',
        meta: {title: '视频列表', noCache: true, roles: ['administrator']}
      },
    ]
  },

  // Pico管理
  // {
  //   path: '/pico',
  //   component: Layout,
  //   redirect: 'noRedirect',
  //   name: 'picoManage',
  //   alwaysShow: true,
  //   meta: {
  //     title: 'Pico管理',
  //     icon: 'vr',
  //     roles: ['administrator', 'pico']
  //   },
  //   children: [
  //     {
  //       path: 'useInfoIndex',
  //       component: () => import('@/views/pico/useInfoIndex'),
  //       name: 'useInfoIndex',
  //       meta: {title: '使用数据', noCache: true}
  //     },
  //     {
  //       path: 'useInfoDealer',
  //       component: () => import('@/views/pico/useInfoDealer'),
  //       name: 'useInfoDealer',
  //       hidden: true,
  //       meta: {title: '使用数据 / 经销商数据', noCache: true}
  //     },
  //     {
  //       path: 'useInfoDetail',
  //       component: () => import('@/views/pico/useInfoDetail'),
  //       name: 'useInfoDetail',
  //       hidden: true,
  //       meta: {title: '使用数据 / 经销商数据 / 设备详情', noCache: true}
  //     },
  //     {
  //       path: 'picoBind',
  //       component: () => import('@/views/pico/picoBind'),
  //       name: 'picoBand',
  //       meta: {title: 'PICO绑定', noCache: true}
  //     },
  //     {
  //       path: 'picoBindInfo',
  //       component: () => import('@/views/pico/picoBindInfo'),
  //       name: 'picoBandInfo',
  //       hidden: true,
  //       meta: {title: 'PICO绑定-添加/绑定', noCache: true}
  //     },
  //     {
  //       path: 'picoRole',
  //       component: () => import('@/views/pico/picoRole'),
  //       name: 'picoRole',
  //       meta: {title: '体验者身份', noCache: true}
  //     },
  //     {
  //       path: 'picoPackageIndex',
  //       component: () => import('@/views/pico/package/index'),
  //       name: 'picoPackageIndex',
  //       meta: {title: 'PICO应用管理', noCache: true}
  //     },
  //     {
  //       path: 'picoPackageJob',
  //       component: () => import('@/views/pico/package/job'),
  //       name: 'picoPackageJob',
  //       hidden: true,
  //       meta: {title: 'PICO应用管理-执行任务', noCache: true}
  //     },
  //   ]
  // },
  // 其他管理
  {
    path: '/other',
    component: Layout,
    redirect: 'noRedirect',
    name: 'otherManage',
    alwaysShow: true,
    meta: {
      title: '其它管理',
      icon: 'component',
      roles: ['administrator', 'temp']
    },
    children: [
      {
        path: 'uploadFile',
        component: () => import('@/views/other/uploadFile'),
        name: 'uploadFile',
        meta: {title: '上传文件', noCache: true}
      },
      {
        path: 'librarySetting',
        component: () => import('@/views/other/librarySetting'),
        name: 'librarySetting',
        meta: {title: '库文件管理系统', noCache: true, roles: ['administrator']}
      },
      {
        path: 'xcsdcs',
        component: () => import('@/views/other/xcsdcs'),
        name: 'xcsdcs',
        meta: {title: '西财实验空间判分', noCache: true, roles: ['administrator', 'temp']}
      },
      {
        path: 'xueshi',
        component: () => import('@/views/test/xueshi'),
        name: 'xueshi',
        meta: {title: '学时', noCache: true, roles: ['administrator']}
      },

      // {
      //   path: 'xicai0602',
      //   component: () => import('@/views/other/xicai0602'),
      //   name: 'xicai0602',
      //   meta: {title: '西财马院评审页', noCache: true}
      // },
      // {
      //   path: 'chexian0602',
      //   component: () => import('@/views/other/chexian0602'),
      //   name: 'chexian0602',
      //   meta: {title: '西财车险评审页', noCache: true}
      // },
    ]
  },


  // 404 page must be placed at the end !!!
  {path: '*', redirect: '/404', hidden: true}
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({y: 0}),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
