import $ from "jquery";

/**
 * 自定义设置面包导航
 */
export const breadList_School_enumDepartment = [
  {
    path: '/exp/school',
    redirect: "noRedirect",
    meta: {title: '用户管理'}
  },
  {
    path: 'school',
    meta: {title: '学校管理'},
  },
  {
    path: 'enumDepartment',
    meta: {title: '学院信息'}
  },
]
export const breadList_School_major = [
  {
    path: '/exp/school',
    redirect: "noRedirect",
    meta: {title: '用户管理'}
  },
  {
    path: 'school',
    meta: {title: '学校管理'},
  },
  {
    path: 'enumDepartment',
    meta: {title: '学院信息'}
  },
  {
    path: 'major',
    meta: {title: '专业信息'}
  },
]
export const breadList_School_teacher = [
  {
    path: '/exp/school',
    redirect: "noRedirect",
    meta: {title: '用户管理'}
  },
  {
    path: 'teacher',
    meta: {title: '教师管理'},
  },
  {
    path: 'department',
    meta: {title: '学院信息'}
  },
]
export const breadList_School_teacherInfo = [
  {
    path: '/exp/school',
    redirect: "noRedirect",
    meta: {title: '用户管理'}
  },
  {
    path: 'teacher',
    meta: {title: '教师管理'},
  },
  {
    path: 'teacherInfo',
    meta: {title: '教师信息'}
  },
]
export const breadList_School_studentList = [
  {
    path: '/exp/school',
    redirect: "noRedirect",
    meta: {title: '用户管理'}
  },
  {
    path: 'studentClazz',
    meta: {title: '学生管理'},
  },
  {
    path: 'department',
    meta: {title: '学生列表'}
  },
]
export const breadList_experimentListDetail = [
  {
    path: '/exp/experiment',
    redirect: "noRedirect",
    meta: {title: '实验课程管理'}
  },
  {
    path: 'list',
    meta: {title: '实验列表'},
  },
  {
    path: 'detail',
    meta: {title: '实验详情'}
  },
]
export const breadList_experimentSettingDetail = [
  {
    path: '/exp/experiment',
    redirect: "noRedirect",
    meta: {title: '实验课程管理'}
  },
  {
    path: 'setting',
    meta: {title: '实验安排'},
  },
  {
    path: 'settingDetail',
    meta: {title: '实验安排详情'}
  },
]
export const breadList_experimentEdit = [
  {
    path: '/exp/experiment',
    redirect: "noRedirect",
    meta: {title: '实验课程管理'}
  },
  {
    path: 'setting',
    meta: {title: '实验安排'},
  },
  {
    path: 'settingEdit',
    meta: {title: '授权实验详情'}
  },
]

// 处理自定义面包屑显示
export function customBreadShow(path) {
  if (path === "/exp/school/enumDepartment") {
    sessionStorage.setItem("breadNavList", JSON.stringify(breadList_School_enumDepartment))
  } else if (path === "/exp/school/major") {
    sessionStorage.setItem("breadNavList", JSON.stringify(breadList_School_major))
  } else if (path === "/exp/school/department") {
    sessionStorage.setItem("breadNavList", JSON.stringify(breadList_School_teacher))
  } else if (path === "/exp/school/teacherInfo") {
    sessionStorage.setItem("breadNavList", JSON.stringify(breadList_School_teacherInfo))
  } else if (path === "/exp/school/studentList") {
    sessionStorage.setItem("breadNavList", JSON.stringify(breadList_School_studentList))
  } else if (path === "/exp/experiment/detail") {
    sessionStorage.setItem("breadNavList", JSON.stringify(breadList_experimentListDetail))
  } else if (path === "/exp/experiment/settingDetail") {
    sessionStorage.setItem("breadNavList", JSON.stringify(breadList_experimentSettingDetail))
  } else if (path === "/exp/experiment/settingEdit") {
    sessionStorage.setItem("breadNavList", JSON.stringify(breadList_experimentEdit))
  } else {
    // 实现项目的子目录导航，不改变颜色
    $(".nest-menu li:not([class='el-menu-item is-active']) span").css("color", "rgb(191, 203, 217)")
    $(".nest-menu li[class='el-menu-item is-active'] span").css("color", "rgb(235, 63, 47)")
  }
  return
}
