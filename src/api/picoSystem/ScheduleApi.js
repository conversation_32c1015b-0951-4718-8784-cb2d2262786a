import {request_async} from "@/utils/requestAsync";
import {API_URL_PICO_SYSTEM} from "@/model/ConfigModel";

// 获取一个
export async function getOne(data) {
  return request_async(API_URL_PICO_SYSTEM + "/v1/schedule/", "get", data)
}

// 新增修改
export async function addOrEdit(data) {
  return request_async(API_URL_PICO_SYSTEM + "/v1/schedule/", "post_body", data);
}

// 获取列表-分页
export async function getPageList(page, size, sort, data) {
  return request_async(API_URL_PICO_SYSTEM + `/v1/schedule/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取使用信息首页列表-分页
export async function getUseInfoIndexList(page, size, sort, data) {
  return request_async(API_URL_PICO_SYSTEM + `/v1/schedule/getUseInfoIndexList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取列表-不分页
export async function getList(data) {
  return request_async(API_URL_PICO_SYSTEM + `/v1/schedule/list`, "post_json", data);
}

// 删除一个
export async function deleteOne(data) {
  return request_async(API_URL_PICO_SYSTEM + "/v1/schedule/", "delete", data);
}

// 获取新增安排时的设备列表
export async function getEquipmentListWhenScheduleAdd(data){
  return request_async(API_URL_PICO_SYSTEM + "/v1/schedule/equipmentListWhenScheduleAdd", "post_json", data);
}

// 获取编辑安排时的设备列表
export async function getEquipmentListWhenScheduleEdit(data){
  return request_async(API_URL_PICO_SYSTEM + "/v1/schedule/equipmentListWhenScheduleEdit", "post_json", data);
}

// 获取使用数据详情页列表
export async function getUseInfoDetailList(data){
  return request_async(API_URL_PICO_SYSTEM + "/v1/schedule/getUseInfoDetailList", "post_json", data);
}
