import {request_async} from "@/utils/requestAsync";
import {API_URL_PICO_SYSTEM} from "@/model/ConfigModel";

// 新增修改
export async function addOrEdit(data) {
  return request_async(API_URL_PICO_SYSTEM + "/v1/picoTaskLog/", "post_body", data);
}

// 获取列表-分页
export async function getPageList(page, size, sort, data) {
  return request_async(API_URL_PICO_SYSTEM + `/v1/picoTaskLog/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取列表-不分页
export async function getList(data) {
  return request_async(API_URL_PICO_SYSTEM + `/v1/picoTaskLog/list`, "post_json", data);
}

// 删除一个
export async function deleteOne(data) {
  return request_async(API_URL_PICO_SYSTEM + "/v1/picoTaskLog/", "delete", data);
}
