import {request_async} from "@/utils/requestAsync";
import {API_URL_PICO_SYSTEM} from "@/model/ConfigModel";

// 获取一个
export async function getOne(data) {
  return request_async(API_URL_PICO_SYSTEM + "/v1/equipment/", "get", data)
}

// 新增修改
export async function addOrEdit(data) {
  return request_async(API_URL_PICO_SYSTEM + "/v1/equipment/", "post_body", data);
}

// 获取列表-分页
export async function getPageList(page, size, sort, data) {
  return request_async(API_URL_PICO_SYSTEM + `/v1/equipment/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取列表-不分页
export async function getList(data) {
  return request_async(API_URL_PICO_SYSTEM + `/v1/equipment/list`, "post_json", data);
}

// 删除一个
export async function deleteOne(data) {
  return request_async(API_URL_PICO_SYSTEM + "/v1/equipment/", "delete", data);
}

// 解除绑定
export async function unbind(data) {
  return request_async(API_URL_PICO_SYSTEM + "/v1/equipment/unbind", "get", data)
}
