import {request_async} from "@/utils/requestAsync";
import {API_URL_ERP} from "@/model/ConfigModel";


// 获取所有实验列表及其版本信息
export async function getAllExperimentList() {
  return request_async(API_URL_ERP + "/v1/packageSystem/experimentVersion/allExperimentList", "get");
}

// 新增修改
export async function addOrEdit(data) {
  return request_async(API_URL_ERP + "/v1/packageSystem/experimentVersion/", "post_body", data);
}

// 获取列表-分页
export async function getPageList(page, size, sort, data) {
  return request_async(API_URL_ERP + `/v1/packageSystem/experimentVersion/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取列表-不分页
export async function getList(data) {
  return request_async(API_URL_ERP + `/v1/packageSystem/experimentVersion/list`, "post_body", data);
}

// 获取一个
export async function getOne(data) {
  return request_async(API_URL_ERP + `/v1/packageSystem/experimentVersion/`, "get", data);
}

// 删除一个
export async function deleteOne(data) {
  return request_async(API_URL_ERP + "/v1/packageSystem/experimentVersion/", "delete", data);
}
