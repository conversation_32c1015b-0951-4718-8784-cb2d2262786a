import {request_async} from "@/utils/requestAsync";
import {API_URL_ERP} from "@/model/ConfigModel";

// 创建推送
export async function createPush(data) {
  return request_async(API_URL_ERP + "/v1/packageSystem/packagePush/createPush", "post_body", data);
}

// 新增修改
export async function addOrEdit(data) {
  return request_async(API_URL_ERP + "/v1/packageSystem/packagePush/", "post_body", data);
}

// 获取列表-分页
export async function getPageList(page, size, sort, data) {
  return request_async(API_URL_ERP + `/v1/packageSystem/packagePush/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取列表-分页-带包版本信息
export async function getPageListWithPackageVersion(page, size, sort, data) {
  return request_async(API_URL_ERP + `/v1/packageSystem/packagePush/pageListWithPackageVersion?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}


// 获取列表-不分页
export async function getList(data) {
  return request_async(API_URL_ERP + `/v1/packageSystem/packagePush/list`, "post_body", data);
}

// 获取一个
export async function getOne(id) {
  return request_async(API_URL_ERP + `/v1/packageSystem/packagePush/${id}`, "get");
}

// 删除一个
export async function deleteOne(data) {
  return request_async(API_URL_ERP + "/v1/packageSystem/packagePush/", "delete", data);
}
