import {request_async} from "@/utils/requestAsync";
import {API_URL_AI_CLIENT} from "@/model/ConfigModel";

// 获取配置信息
export async function getConfig(key, field) {
  return request_async(API_URL_AI_CLIENT + `/v1/config/hash`, "get", {
    key, field
  });
}

// 设置配置信息
export async function editConfig(key, field, info) {
  return request_async(API_URL_AI_CLIENT + `v1/config/hash?key=${key}&field=${field}&expire=-1`, "put_body", info);
}
