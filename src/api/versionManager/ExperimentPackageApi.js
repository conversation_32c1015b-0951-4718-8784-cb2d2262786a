import {request_async} from "@/utils/requestAsync";
import {API_URL_VERSION_MANAGER} from "@/model/ConfigModel";

// 新增修改
export async function addOrEdit(data) {
  return request_async(API_URL_VERSION_MANAGER + "/v1/experimentPackage/", "post_body", data);
}

// 获取列表-分页
export async function getPageList(page, size, sort, data) {
  return request_async(API_URL_VERSION_MANAGER + `/v1/experimentPackage/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取列表-不分页
export async function getList(data) {
  return request_async(API_URL_VERSION_MANAGER + `/v1/experimentPackage/list`, "post_body", data);
}

// 删除一个
export async function deleteOne(data) {
  return request_async(API_URL_VERSION_MANAGER + "/v1/experimentPackage/", "delete", data);
}

// 新增下载日志
export async function addDownloadLog(data) {
  return request_async(API_URL_VERSION_MANAGER + "/v1/experimentPackage/addDownloadLog", "post_body", data);
}
