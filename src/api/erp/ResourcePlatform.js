import {request_async} from "@/utils/requestAsync";
import {API_URL_ERP} from "@/model/ConfigModel";

// 新增修改
export async function addOrEdit(data) {
  return request_async(API_URL_ERP + "/v1/resourcePlatform/resourcePlatform/", "post_body", data);
}

// 新增用户
export async function addUser(data) {
  return request_async(API_URL_ERP + "/v1/resourcePlatform/resourcePlatform/addUser", "post_body", data);
}


// 获取列表-分页
export async function getPageList(page, size, sort, data) {
  return request_async(API_URL_ERP + `/v1/resourcePlatform/resourcePlatform/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}


// 获取列表-不分页
export async function getList(data) {
  return request_async(API_URL_ERP + `/v1/resourcePlatform/resourcePlatform/list`, "post_body", data);
}

// 删除某个
export async function deleteOne(data) {
  return request_async(API_URL_ERP + "/v1/resourcePlatform/resourcePlatform/", "delete", data);
}

// 获取列表-获取给定id的列表
export async function getListByIds(data) {
  return request_async(API_URL_ERP + `/v1/resourcePlatform/resourcePlatform/listByIds`, "post_body", data);
}
