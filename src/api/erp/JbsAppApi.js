import {request_async} from "@/utils/requestAsync";
import {API_URL_ERP} from "@/model/ConfigModel";

// 获取一个
export async function getOne(data) {
  return request_async(API_URL_ERP + "/v1/jbs/app/", "get", data);
}

// 新增修改
export async function addOrEdit(data) {
  return request_async(API_URL_ERP + "/v1/jbs/app/", "post_body", data);
}

// 获取列表-分页
export async function getPageList(page, size, sort, data) {
  return request_async(API_URL_ERP + `/v1/jbs/app/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取列表-不分页
export async function getList(data) {
  return request_async(API_URL_ERP + `/v1/jbs/app/list`, "post_body", data);
}

// 删除某个
export async function deleteOne(data) {
  return request_async(API_URL_ERP + "/v1/jbs/app/", "delete", data);
}
