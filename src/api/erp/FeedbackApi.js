import {request_async} from "@/utils/requestAsync";
import {API_URL_ERP} from "@/model/ConfigModel";

// 新增修改
export async function addOrEdit(platformId, data) {
  return request_async(API_URL_ERP + "/v1/feedback/?platformId=" + platformId, "put_body", data);
}

// 获取列表-不分页
export async function getList(platformId, data) {
  return request_async(API_URL_ERP + "/v1/feedback/list?platformId=" + platformId, "post_body", data);
}

// 获取列表-分页
export async function getPageList(platformId, page, size, sort, data) {
  return request_async(API_URL_ERP + `/v1/feedback/pageList?platformId=${platformId}&page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 删除某个
export async function deleteOne(data) {
  return request_async(API_URL_ERP + "/v1/feedback/", "delete", data);
}
