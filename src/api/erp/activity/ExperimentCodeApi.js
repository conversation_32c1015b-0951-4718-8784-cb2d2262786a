import {request_async} from "@/utils/requestAsync";
import {API_URL_ERP} from "@/model/ConfigModel";

// 创建体验码
export async function createCodes(data) {
  return request_async(API_URL_ERP + "/v1/activity/experimentCode/newCodes", "post_body", data);
}

// 获取体验码详情
export async function getExperimentCodeInfo(experimentCode) {
  return request_async(API_URL_ERP + "/v1/activity/experimentCode/info?experimentCode=" + experimentCode, "get", "");
}

// 获取体验码列表
export async function getList(data){
  return request_async(API_URL_ERP + `/v1/activity/experimentCode/list`, "post_body", data);
}
