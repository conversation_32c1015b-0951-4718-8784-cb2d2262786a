import {request_async} from "@/utils/requestAsync";
import {API_URL_ERP} from "@/model/ConfigModel";

// 新增修改
export async function addOrEdit(data) {
  return request_async(API_URL_ERP + "/v1/webAnalytics/", "post_body", data);
}

// 获取列表-分页
export async function getPageList(page, size, sort, data) {
  return request_async(API_URL_ERP + `/v1/webAnalytics/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取列表-不分页
export async function getList(data) {
  return request_async(API_URL_ERP + `/v1/webAnalytics/list`, "post_body", data);
}

// 批量获取某个网站满足条件的事件数量
export async function oneWebOneEventNumberByInfo(data) {
  return request_async(API_URL_ERP + `/v1/webAnalytics/oneWebOneEventNumberByInfo`, "post_body", data);
}

// 删除某个
export async function deleteOne(data) {
  return request_async(API_URL_ERP + "/v1/webAnalytics/", "delete", data);
}
