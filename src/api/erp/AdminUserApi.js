import {request_async} from "@/utils/requestAsync";
import {API_URL_ERP} from "@/model/ConfigModel";

// 新增
export async function add(username, phoneNumber, password, data) {
  return request_async(API_URL_ERP + `/v1/adminUser/user?username=${username}&phoneNumber=${phoneNumber}&password=${password}`, "post_body", data);
}

// 新增修改
export async function addOrEdit(data) {
  return request_async(API_URL_ERP + "/v1/adminUser/user", "put_body", data);
}

// 获取列表-不分页
export async function getList(data) {
  return request_async(API_URL_ERP + "/v1/adminUser/user/list", "post_body", data);
}

// 获取列表-不分页-某个平台的
export async function getListByPlatformId(data, platformId) {
  return request_async(API_URL_ERP + "/v1/adminUser/user/list/byPlatformId?platformId=" + platformId, "post_body", data);
}

// 获取列表-分页
export async function getPageList(page, size, sort, data, platformId) {
  return request_async(API_URL_ERP + `/v1/adminUser/user/pageList?page=${page}&size=${size}&sort=${sort}&platformId=${platformId}`, "post_body", data);
}

// 删除某个
export async function deleteOne(data) {
  return request_async(API_URL_ERP + "/v1/adminUser/user", "delete", data);
}

// 直接生成某个平台某个管理员的token
export async function generateAdminToken(data) {
  return request_async(API_URL_ERP + "/v1/adminUser/generateAdminToken", "get", data);
}

// pico经销商-重置密码
export async function resetPicoPassword(dealerId) {
  return request_async(API_URL_ERP + "/v1/adminUser/pico/resetPassword", "get", {
    dealerId
  });
}

// pico经销商-重置密码
export async function picoLogicDeleteDealer(dealerId) {
  return request_async(API_URL_ERP + "/v1/adminUser/pico/dealer", "delete", {
    dealerId
  });
}
