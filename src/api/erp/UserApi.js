import {request_async} from "@/utils/requestAsync";
import {API_URL_ERP} from "@/model/ConfigModel";

// 获取用户新
export async function getMineInfo(data) {
  return request_async(API_URL_ERP+"/v1/adminUser/mineInfo", "get", data);
}

// 用户进行登录
export async function login(data) {
  return request_async(API_URL_ERP+"/v1/adminUser/token", "get", data);
}

// 用户退出登录
export async function loginOut(data) {
  return request_async(API_URL_ERP+"/v1/adminUser/token", "delete", data);
}
