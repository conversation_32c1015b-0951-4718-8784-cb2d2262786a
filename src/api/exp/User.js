import {request_async} from "@/utils/requestAsync";
import {API_URL_EXP_ADMIN, API_URL_EXP_GO} from "@/model/ConfigModel";

// 获取列表-分页
export async function getList(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/user/list`, "get", params);
}

// 通过条件获取列表-不分页
export async function conditionQueryList(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/user/conditionQueryList`, "get", params);
}

// 获取某一个
export async function getOne(id) {
  return request_async(API_URL_EXP_ADMIN + `admin/user/infoByUserId/1?userid=` + id, "get", {});
}

// 新增
export async function save(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/user/save`, "post_json", params);
}

// 修改
export async function update(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/user/update`, "post_json", params);
}

// 删除档案
export async function deleteOne(data) {
  return request_async(API_URL_EXP_ADMIN + `admin/user/delete`, "post_json", data);
}

// 移出本班
export async function removeOne(data) {
  return request_async(API_URL_EXP_ADMIN + `admin/user/remove`, "post_json", data);
}

// 获取重置学生密码用户列表
export async function getResetPasswordList(params) {
  return request_async(API_URL_EXP_GO + `user/20/resetPasswordList`, "get", params);
}

// 重置密码
export async function resetUserPassword(params) {
  return request_async(API_URL_EXP_GO + `user/20/resetPassword`, "get", params);
}

// 批量重置某个学校用户密码
export async function multipleResetSchoolPassword(schoolId,accountList) {
  return request_async(API_URL_EXP_GO + `user/20/multipleResetSchoolPassword?schoolId=${schoolId}`, "post_body", accountList);
}

// 获取某个学校的用户数量,总数、教师、学生
export async function getOneSchoolUserNumber(schoolId) {
  return request_async(API_URL_EXP_ADMIN + `admin/user/oneSchoolUserNumber?schoolId=${schoolId}`, "get", {});
}
