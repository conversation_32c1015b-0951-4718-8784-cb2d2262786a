import {request_async} from "@/utils/requestAsync";
import {API_URL_EXP_ADMIN, API_URL_EXP_GO} from "@/model/ConfigModel";

// 获取西财四渡赤水实验记录列表
export async function getXcSykjSdcsReportList(offset,order,experimentId) {
  return request_async(API_URL_EXP_GO + `experiment/22/xcSykjSdcsReportList?offset=${offset}&order=${order}&experimentId=${experimentId}`, "get", {});
}

// 设置西财四渡赤水实验记录分数
export async function SetXcSykjSdcsReport(id,score) {
  return request_async(API_URL_EXP_GO + `experiment/22/xcSykjSdcsReportSet?id=${id}&score=${score}`, "get", {});
}


// 设置西财四渡赤水实验记录分数-实验报告中没有成绩截图
export async function SetXcSykjSdcsReportNoScore(id) {
  return request_async(API_URL_EXP_GO + `experiment/22/SetXcSykjSdcsReportNoScore?id=${id}`, "get", {});
}

// 获取登录日志列表
export async function getLoginLogList(params) {
  return request_async(API_URL_EXP_GO + `loginLog/list_for_admin_241209`, "post_json", params);
}