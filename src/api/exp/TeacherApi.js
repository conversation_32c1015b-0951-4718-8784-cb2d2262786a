import {request_async} from "@/utils/requestAsync";
import {API_URL_EXP_ADMIN} from "@/model/ConfigModel";

// 获取列表
export async function getList(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/teacher/list`, "get", params);
}

// 教师管理-首页列表-分页
export async function getIndexList(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/teacher/indexList`, "get", params);
}

// 新增或修改
export async function save(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/teacher/save`, "post_json", params);
}

// 修改
export async function update(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/teacher/update`, "post_json", params);
}

// 获取一个
export async function getOne(id) {
  return request_async(API_URL_EXP_ADMIN + `admin/teacher/info/`+id, "get", {});
}

// 删除
export async function remove(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/teacher/delete`, "post_json", params);
}

// 重置密码
export async function resetPwd(account) {
  return request_async(API_URL_EXP_ADMIN + `admin/teacher/resetPwd/${account}`, "get", {});
}
