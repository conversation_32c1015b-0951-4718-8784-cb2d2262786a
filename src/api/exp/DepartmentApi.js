import {request_async} from "@/utils/requestAsync";
import {API_URL_EXP_ADMIN} from "@/model/ConfigModel";

// 获取列表
export async function getList(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/department/list`, "get", params);
}

// 新增或修改
export async function save(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/department/save`, "post_json", params);
}

// 修改
export async function update(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/department/update`, "post_json", params);
}

// 删除
export async function remove(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/department/delete`, "post_json", params);
}
