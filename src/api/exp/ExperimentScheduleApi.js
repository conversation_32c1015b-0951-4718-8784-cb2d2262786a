import {request_async} from "@/utils/requestAsync";
import {API_URL_EXP_ADMIN} from "@/model/ConfigModel";

// 获取实验安排列表
export async function getExperimentScheduleList(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/schoolschedule/list`, "get", params);
}

// 获取实验安排列表-新
export async function getExperimentScheduleListNew(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/schoolschedule/newList`, "get", params);
}

// 获取实验安排详情列表
export async function getExperimentScheduleDetail(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/schoolschedule/details`, "get", params);
}

// 新增
export async function save(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/schoolschedule/save`, "post_json", params);
}

// 修改
export async function update(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/schoolschedule/update`, "post_json", params);
}
