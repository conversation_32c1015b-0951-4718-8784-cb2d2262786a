import {request_async} from "@/utils/requestAsync";
import {API_URL_EXP_ADMIN} from "@/model/ConfigModel";

// 获取列表
export async function getList(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/experiment/list`, "get", params);
}

// 获取某一个
export async function getOne(id) {
  return request_async(API_URL_EXP_ADMIN + `admin/experiment/info/` + id, "get", {});
}

// 新增
export async function save(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/experiment/save`, "post_json", params);
}

// 修改
export async function update(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/experiment/update`, "post_json", params);
}

// 删除
export async function remove(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/experiment/delete`, "post_json", params);
}

// 获取列表-系列
export async function getSeriesList(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/series/list`, "get", params);
}

// 新增-系列
export async function saveSeries(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/series/save`, "post_json", params);
}

// 修改-系列
export async function updateSeries(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/series/update`, "post_json", params);
}

// 获取某个学校的实验信息列表
export async function getOneSchoolExperimentInfoList(schoolId) {
  return request_async(API_URL_EXP_ADMIN + `admin/experiment/oneSchoolExperimentInfoList?schoolId=${schoolId}`, "get", {});
}
