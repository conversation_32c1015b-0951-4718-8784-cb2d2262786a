import {request_async} from "@/utils/requestAsync";
import {API_URL_EXP_ADMIN} from "@/model/ConfigModel";

// 获取列表-分页
export async function getList(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/semester/list`, "get", params);
}

// 通过条件获取列表-不分页
export async function conditionQueryList(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/semester/conditionQueryList`, "get", params);
}

// 新增
export async function save(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/semester/save`, "post_json", params);
}

// 修改
export async function update(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/semester/update`, "post_json", params);
}

// 删除
export async function remove(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/semester/delete`, "post_json", params);
}
