import {request_async} from "@/utils/requestAsync";
import {API_URL_EXP_ADMIN} from "@/model/ConfigModel";

// 获取学校列表-旧版，一个学校直接对应多个多个学院
export async function getList(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/school/list`, "get", params);
}

// 获取学校列表-一个学校对应一个学院
export async function getListNew(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/school/newList`, "get", params);
}

// 学校输入后续提示-学校列表
export async function getSchoolListByName(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/school/conditionQuery`, "get", params);
}

// 新增或修改
export async function save(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/school/save`, "post_json", params);
}

// 修改
export async function update(params) {
  return request_async(API_URL_EXP_ADMIN + `admin/school/update`, "post_json", params);
}

// 删除一个
export async function deleteOne(id) {
  return request_async(API_URL_EXP_ADMIN + `admin/school/delete/${id}`, "delete", {});
}
