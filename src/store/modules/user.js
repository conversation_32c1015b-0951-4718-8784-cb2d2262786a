import {AdminUserModel} from '@/model/erp/AdminUserModel'
import {loginOut} from '@/api/erp/UserApi'
import {getToken, setToken, removeToken} from '@/utils/auth'
import router, {resetRouter} from '@/router'
import {PLATFORM_ID_ERP} from "@/model/ConfigModel";
import {msg_err} from "@/utils/ele_component";

const state = {
  token: getToken(),
  name: '',
  userName: '',
  avatar: '',
  introduction: '',
  roles: [],
  id: ''
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_USERNAME: (state, userName) => {
    state.userName = userName
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_ID: (state, id) => {
    state.id = id
  }
}

const actions = {
  // 用户登录
  async login({commit}, result) {
    commit('SET_TOKEN', result["token"]);
    setToken(result["token"])
  },

  // 每次刷新页面时重新获取用户信息，填充store
  async getInfo({commit, state}) {
    return new Promise(async (resolve, reject) => {
      // 获取用户信息
      const user = await AdminUserModel.getMineInfo()
      if (user) {
        const roles = user.roles[PLATFORM_ID_ERP]
        commit('SET_NAME', user.realName)
        commit('SET_USERNAME', user.username)
        commit('SET_AVATAR', 'http://image.cdzyhd.com/9caa3614-204a-4b9d-a2e7-c1351fc54f54.png')
        commit('SET_INTRODUCTION', '')
        commit('SET_ROLES', roles)
        commit('SET_ID', user.adminUserId)
        resolve({'roles': roles})
      }
    });

  },

  // user logout
  logout({commit, state, dispatch}) {
    return new Promise((resolve, reject) => {
      loginOut({token: getToken()}).then(() => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        removeToken()
        resetRouter()
        // 清空session
        sessionStorage.clear()
        // reset visited views and cached views
        // to fixed https://github.com/PanJiaChen/vue-element-admin/issues/2485
        dispatch('tagsView/delAllViews', null, {root: true})

        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // remove token
  resetToken({commit}) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      removeToken()
      resolve()
    })
  },

  // dynamically modify permissions
  async changeRoles({commit, dispatch}, role) {
    const token = role + '-token'

    commit('SET_TOKEN', token)
    setToken(token)

    const {roles} = await dispatch('getInfo')

    resetRouter()

    // generate accessible routes map based on roles
    const accessRoutes = await dispatch('permission/generateRoutes', roles, {root: true})
    // dynamically add accessible routes
    router.addRoutes(accessRoutes)

    // reset visited views and cached views
    dispatch('tagsView/delAllViews', null, {root: true})
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
