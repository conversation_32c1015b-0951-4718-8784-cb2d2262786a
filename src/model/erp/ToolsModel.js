import {getIpInfoOnline1, getIpInfoOnline2} from "@/api/erp/ToolsApi"

class ToolsModel {
  // 获取ip信息
  static async getIpInfoOnline1(ip, date) {
    // 保存本次缓存结果
    if (!window.ipCache) {
      window.ipCache = {}
    }
    if (window.ipCache.hasOwnProperty(ip)) {
      return window.ipCache[ip]
    }
    let [data] = await getIpInfoOnline1({
      ip
    })
    let result = data.data
    let resultStr = ""
    if (result["code"] === 200) {
      if (result.province === "") {
        resultStr = result["country"]
        window.ipCache[ip] = resultStr
      } else {
        resultStr = result["province"] + "_" + result["city"] + result["area"] + "__" + result["isp"]
        window.ipCache[ip] = resultStr
      }
      return resultStr
    } else {
      return "获取失败"
    }
  }

  // 获取ip信息-erp数据库
  static async getIpInfoOnline2(ip) {
    // 保存本次缓存结果
    if (!window.ipCache) {
      window.ipCache = {}
    }
    if (window.ipCache.hasOwnProperty(ip)) {
      return window.ipCache[ip]
    }
    let [data] = await getIpInfoOnline2({
      ip
    })
    let result = data.data
    if (data["code"] === "000000") {
      window.ipCache[ip] = result
      return result
    } else {
      return "获取失败"
    }
  }
}

export {ToolsModel}
