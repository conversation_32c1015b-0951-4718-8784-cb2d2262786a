import {CommonModel} from "@/model/CommonModel";
import {getList, getPageList, addOrEdit, deleteOne} from "@/api/erp/FeedbackApi";

/**
 * 反馈model
 */
class FeedbackModel {
  // 新增或修改
  static async addOrEdit(platformId, entity) {
    let [data] = await addOrEdit(platformId, entity);
    return data;
  }

  // 获取列表
  static async getPageList(platformId, page, size, sort, document) {
    let [data] = await getPageList(platformId,page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(platformId, document) {
    let [data] = await getList(platformId,document);
    return data;
  }

  // 删除某个
  static async deleteOne(feedbackId) {
    let [data] = await deleteOne({
      feedbackId
    });
    return data;
  }
}

export {FeedbackModel}
