import {CommonModel} from "@/model/CommonModel";
import {getPageList, addOrEdit, getList, deleteOne, oneWebOneEventNumberByInfo} from "@/api/erp/WebAnalyticsApi";

/**
 * 网站统计-webAnalytics-model
 */
class WebAnalyticsModel {
  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 批量获取某个网站满足条件的事件数量
  static async oneWebOneEventNumberByInfo(document) {
    let [data] = await oneWebOneEventNumberByInfo(document);
    return data.data;
  }

  // 删除某个
  static async deleteOne(webAnalyticsId) {
    let [data] = await deleteOne({
      webAnalyticsId
    });
    return data;
  }
}

export {WebAnalyticsModel}
