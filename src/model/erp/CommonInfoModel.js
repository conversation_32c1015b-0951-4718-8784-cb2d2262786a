import {CommonModel} from "@/model/CommonModel";
import {getPageList, addOrEdit, getList} from "@/api/erp/CommonInfoApi";

/**
 * 通用实体model
 */
class CommonInfoModel {
  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    document.type = "54studyApplyInfo"
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList( document) {
    document.type = "54studyApplyInfo"
    let [data] = await getList(document);
    return data.data;
  }
}

export {CommonInfoModel}
