import {CommonModel} from "@/model/CommonModel";
import {getConfig, editConfig} from "@/api/erp/ConfigApi";

/**
 * 配置model
 */
class ConfigModel {
  // 获取配置
  static async getEdit(key, field) {
    let [data] = await getConfig(key, field);
    return data.data;
  }

  // 设置配置
  static async editEdit(key, field, info) {
    let [data] = await editConfig(key, field, info);
    return true;
  }
}

export {ConfigModel}
