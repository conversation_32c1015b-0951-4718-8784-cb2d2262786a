import {CommonModel} from "@/model/CommonModel";
import {getPageList, addOrEdit, getList, deleteOne,getListByIds,addUser} from "@/api/erp/ResourcePlatform";

/**
 * 资源平台-通用实体
 */
class ResourcePlatformModel {
  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    if(data.code==="000000"){
      return data.data
    }else {
      return false
    }
  }

  // 新增用户
  static async addUser(entity) {
    let [data] = await addUser(entity);
    if(data.code==="000000"){
      return true
    }else {
      return false
    }
  }

  // 获取分页列表
  static async getPageList(type, page, size, sort, document,asAdmin) {
    document = Object.assign({
      type,
      deleted:0,// 没有被逻辑删除的
      asAdmin:!!asAdmin,// 标记是否是后台请求
    }, document) // 合并type
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除某个
  static async deleteOne(info) {
    let [data] = await deleteOne(info);
    return data;
  }

  // 获取列表-获取给定id的列表
  static async getListByIds(document) {
    document = Object.assign(document) // 合并type
    let [data] = await getListByIds(document);
    return data.data;
  }
}

export {ResourcePlatformModel}
