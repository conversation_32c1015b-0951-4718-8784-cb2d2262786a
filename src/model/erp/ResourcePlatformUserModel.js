import {CommonModel} from "@/model/CommonModel";
import {getPageList, addOrEdit, getList, deleteOne} from "@/api/erp/ResourcePlatformUser";

/**
 * 资源平台-用户
 */
class ResourcePlatformUserModel {
  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    if(data.code==="000000"){
      return data.data
    }else {
      return false
    }
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    document = Object.assign({
    }, document) // 合并type
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除某个
  static async deleteOne(info) {
    let [data] = await deleteOne(info);
    return data;
  }
}

export {ResourcePlatformUserModel}
