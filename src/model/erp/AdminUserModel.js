import {
  login,
  getMineInfo,
} from '@/api/erp/UserApi'
import {CommonModel} from "@/model/CommonModel";
import {
  getList,
  getPageList,
  add,
  addOrEdit,
  deleteOne,
  generateAdminToken,
  getListByPlatformId,
  resetPicoPassword, picoLogicDeleteDealer
} from "@/api/erp/AdminUserApi";

/**
 * 管理员用户管理model
 */
class AdminUserModel {
  // 用户登录
  static async userLogin(account, password) {
    let [data] = await login({
      account,
      password
    })
    if (data.code === "000000") {

      return data.data;
    } else {
      return false;
    }
  }

  // 判断已登录用户自己的用户信息
  static async getMineInfo() {
    let [data] = await getMineInfo({})
    if (data.code === "000000") {
      return data.data;
    } else {
      return false;
    }
  }

  // 新增
  static async add(user) {
    user.extraInfo = {} // 设置额外信息
    let [data] = await add(user.username, user.phoneNumber, user.password, user);
    if (data.code === "000000") {
      return data.data;
    } else {
      return false;
    }
  }

  // 新增或修改
  static async addOrEdit(user) {
    let [data] = await addOrEdit(user);
    return data;
  }

  // 获取列表
  static async getPageList(page, size, sort, document, platformId) {
    if (!platformId) {
      platformId = "all"
    }
    let [data] = await getPageList(page, size, sort, document, platformId);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data;
  }

  // 获取不分页列表-某个平台的
  static async getListByPlatformId(document, platformId) {
    let [data] = await getListByPlatformId(document, platformId);
    return data.data;
  }

  // 删除某个
  static async deleteOne(adminUserId) {
    let [data] = await deleteOne({
      adminUserId
    });
    return data;
  }

  // 直接获取某个平台某个管理员的token
  static async generateAdminToken(platformId, adminUserId) {
    let [data] = await generateAdminToken({
      platformId, adminUserId
    });
    return data;
  }

  // pico-重置经销商密码
  static async resetPicoDealerPassword(dealerId) {
    let [data] = await resetPicoPassword(dealerId);
    if (data.code === "000000") {
      return true
    } else {
      return false;
    }
  }

  // pico-经销商逻辑删除
  static async picoLogicDeleteDealer(dealerId) {
    let [data] = await picoLogicDeleteDealer(dealerId);
    if (data.code === "000000") {
      return true
    } else {
      return false;
    }
  }
}

export {AdminUserModel}
