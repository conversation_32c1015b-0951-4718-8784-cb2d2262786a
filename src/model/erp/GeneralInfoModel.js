import {CommonModel} from "@/model/CommonModel";
import {getPageList, addOrEdit, getList,deleteOne} from "@/api/erp/GeneralInfoApi";

/**
 * 通用实体-generalInfo-model
 */
class GeneralInfoModel {
  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除某个
  static async deleteOne(generalInfoId) {
    let [data] = await deleteOne({
      generalInfoId
    });
    return data;
  }
}

export {GeneralInfoModel}
