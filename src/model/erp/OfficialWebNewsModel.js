import {CommonModel} from "@/model/CommonModel";
import {getPageList, getOne, getList, addOrEdit, deleteOne, postUrlToBaidu} from "@/api/erp/OfficialWebNewsApi";

/**
 * 官网新闻model
 */
class OfficialWebNewsModel {
  // 获取详情
  static async getOne(id) {
    let [data] = await getOne(id);
    return data.data;
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 推送百度收录
  static async postUrlToBaidu(info) {
    let [data] = await postUrlToBaidu(info);
    return data;
  }

  // 删除某个
  static async deleteOne(id) {
    let [data] = await deleteOne({
      officialWebNewsId: id
    });
    return data;
  }
}

export {OfficialWebNewsModel}
