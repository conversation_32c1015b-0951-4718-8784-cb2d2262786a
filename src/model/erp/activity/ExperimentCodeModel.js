import {createCodes, getExperimentCodeInfo,getList} from "@/api/erp/activity/ExperimentCodeApi";

/**
 * ExperimentCodeModel
 */
class ExperimentCodeModel {
  // 创建体验码
  static async createCodes(codeNumber, number, remarks) {
    let [data] = await createCodes({
      codeNumber,
      number,
      remarks
    });
    if (data.code === "000000") {
      return data.data
    } else {
      return false
    }
  }

  // 获取体验码详情
  static async getExperimentCodeInfo(code) {
    let [data] = await getExperimentCodeInfo(code);
    return data.data
  }

  // 获取不分页列表
  static async getList( document) {
    let [data] = await getList(document);
    return data.data;
  }

}

export {ExperimentCodeModel}
