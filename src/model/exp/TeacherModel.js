import {getList, save, update, remove, resetPwd, getIndexList, getOne} from "@/api/exp/TeacherApi";
import {CommonModel} from "@/model/CommonModel";
import axios from "axios";
import {API_URL_EXP_ADMIN, API_URL_EXP_CONSUMER} from "@/model/ConfigModel";
import {msg_err, msg_success} from "@/utils/ele_component";

/**
 教师model
 */
class TeacherModel {
  // 获取列表
  static async getList(page, size, query) {
    let params = {
      page: page,
      size: size,
    }
    params = Object.assign(params, query)
    let [data] = await getList(params)
    if (size === 0) {  // 不分页
      return data.data;
    } else { // 分页
      let list = CommonModel.generateListMybatisPlus(data.data)
      // 遍历config转换为对象
      let listData = list[0]
      return [listData, list[1]];
    }
  }

  // 教师管理-首页-列表
  static async getIndexList(page, size, query) {
    let params = {
      page: page,
      size: size,
    }
    params = Object.assign(params, query)
    let [data] = await getIndexList(params)
    if (size === 0) {  // 不分页
      return data.data;
    } else { // 分页
      let list = CommonModel.generateListMybatisPlus(data.data)
      // 遍历config转换为对象
      let listData = list[0]
      return [listData, list[1]];
    }
  }

  // 新增
  static async save(entity) {
    let [res] = await save(entity)
    if (res.code === 20000) {
      return true
    } else {
      return false
    }
  }

  // 修改
  static async update(entity) {
    let [res] = await update(entity)
    if (res.code === 20000) {
      return true
    } else {
      return false
    }
  }

  // 获取一个
  static async getOne(id) {
    let [res] = await getOne(id)
    if (res.code === 20000) {
      return res.data
    } else {
      return false
    }
  }

  // 删除
  static async remove(idArray, departmentid) {
    let [res] = await remove({
      ids: idArray,
      departmentid,
    })
    if (res.code === 20000) {
      return true
    } else {
      return false
    }
  }

  // 重置密码
  static async resetPwd(id) {
    let [res] = await resetPwd(id)
    if (res.code === 20000) {
      return true
    } else {
      return false
    }
  }

  // 导出某个学院的教师excel
  static exportTeacherList(departmentId, search) {
    const url = API_URL_EXP_ADMIN + 'admin/teacher/export/teacher/information/' + departmentId + "/" + search
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.setAttribute('download', "教师列表.xls")
    document.body.appendChild(link)
    link.click()
    link.remove()
  }

  // 导出所有学院的教师excel
  static exportTeacherIndexList(schoolName, departmentName) {
    if (!schoolName) {
      schoolName = "null"
    }
    if (!departmentName) {
      departmentName = "null"
    }
    const url = API_URL_EXP_ADMIN + `admin/teacher/exportIndex/${schoolName}/${departmentName}`
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.setAttribute('download', "教师列表.xls")
    document.body.appendChild(link)
    link.click()
    link.remove()
  }

  // 导入教师
  static importTeacher(file, departmentid) {
    return new Promise((resolve, reject) => {
      const formData = new FormData()
      formData.append('multipartFile', file)
      formData.append('questionBankId', departmentid)
      axios.create({
        baseURL: API_URL_EXP_ADMIN
      }).request({
        url: `admin/teacher/import/teacher/information/${departmentid}`,
        method: 'post',
        headers: {'Content-Type': 'multipart/form-data'},
        data: formData
      }).then(response => {
        if (response.status === 200) {
          if (response.data.code === 20000) {
            resolve(true)
          } else {
            msg_err(response.data.msg)
            resolve(false)
          }
        } else {
          msg_err(response.data.msg)
          resolve(false)
        }
      })
    })
  }

  // 导入教师-首页用
  static importTeacherIndex(file) {
    return new Promise((resolve, reject) => {
      const formData = new FormData()
      formData.append('multipartFile', file)
      axios.create({
        baseURL: API_URL_EXP_ADMIN
      }).request({
        url: `admin/teacher/importIndex`,
        method: 'post',
        headers: {'Content-Type': 'multipart/form-data'},
        data: formData
      }).then(response => {
        if (response.status === 200) {
          if (response.data.code === 20000) {
            resolve(true)
          } else {
            msg_err(response.data.msg)
            resolve(false)
          }
        } else {
          msg_err(response.data.msg)
          resolve(false)
        }
      })
    })
  }

  // 批量导入班级
  static importClazz(file, departmentid) {
    return new Promise((resolve, reject) => {
      const formData = new FormData()
      formData.append('multipartFile', file)
      formData.append('departmentid', departmentid)
      formData.append('startNum', "1")
      axios.create({
        baseURL: API_URL_EXP_CONSUMER
      }).request({
        url: `consumer/import/multipleAddClazz`,
        method: 'post',
        headers: {'Content-Type': 'multipart/form-data'},
        data: formData
      }).then(response => {
        if (response.status === 200) {
          if (response.data.code === 20000) {
            resolve(true)
          } else {
            msg_err(response.data.msg)
            resolve(false)
          }
        } else {
          msg_err(response.data.msg)
          resolve(false)
        }
      })
    })
  }



}

export {TeacherModel}
