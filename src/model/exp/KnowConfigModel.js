/**
 * 实验须知model
 */
import {getKnowConfig, saveKnowConfig} from "@/api/exp/KonwConfigApi";

class KnowConfigModel {
  // 获取信息
  static async getInfo() {
    let [data] = await getKnowConfig()
    return data.data;
  }

  // 设置信息
  static async saveInfo(dataInfo) {
    let [data] = await saveKnowConfig({
      content:dataInfo
    })
    if (data.code === 20000) {
      return true;
    } else {
      return false;
    }

  }
}

export {KnowConfigModel}
