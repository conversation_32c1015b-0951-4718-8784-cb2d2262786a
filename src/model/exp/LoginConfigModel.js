/**
 * 登录须知model
 */
import {getLoginConfig, saveLoginConfig} from "@/api/exp/LoginConfigApi";
import {CommonModel} from "@/model/CommonModel";

class LoginConfigModel {
  // 获取配置列表
  static async getList(page, size,query) {
    let params = {
      page: page,
      size: size,
    }
    params=Object.assign(params,query)
    let [data] = await getLoginConfig(params)
    let list = CommonModel.generateListMybatisPlus(data.data)
    // 遍历config转换为对象
    let listData = list[0]
    for (let i = 0; i < listData.length; i++) {
      if (listData[i].configs) {
        listData[i].configs = JSON.parse(listData[i].configs)
        if(!listData[i].configs.title){
          listData[i].configs.title=""
        }
      } else { // 没有设置过的为null 需要初始化
        listData[i].configs = {
          schoolid:listData[i].id,
          title:""
        }
      }
    }
    return [listData, list[1]];
  }

  // 获取公共配置
  static async getCommonConfig() {
    let [data] = await getLoginConfig(null)
    if (data.code === 20000) {
      let config = JSON.parse(data.data)
      config.schoolid = null
      return ({
        id: null,
        configs: config,
        name: "公共配置"
      })
    } else {
      return {
        id: null,
        configs: {
          schoolid: null
        },
        name: "公共配置"
      }
    }
  }

  // 设置信息
  static async saveInfo(dataInfo) {
    let [data] = await saveLoginConfig(dataInfo)
    if (data.code === 20000) {
      return true;
    } else {
      return false;
    }

  }
}

export {LoginConfigModel}
