import {
  getList,
  save,
  conditionQueryList,
  update,
  deleteOne,
  getOne,
  removeOne,
  getOneSchoolUserNumber
} from "@/api/exp/User";
import {CommonModel} from "@/model/CommonModel";
import axios from "axios";
import {API_URL_EXP_ADMIN} from "@/model/ConfigModel";
import {msg_err} from "@/utils/ele_component";

/**
 用户model
 */
class UserModel {
  // 获取列表-分页
  static async getList(page, size, query) {
    let params = {
      page: page,
      size: size,
    }
    params = Object.assign(params, query)
    let [data] = await getList(params)
    if (size === 0) {  // 不分页
      return data.data;
    } else { // 分页
      let list = CommonModel.generateListMybatisPlus(data.data)
      // 遍历config转换为对象
      let listData = list[0]
      return [listData, list[1]];
    }
  }

  // 通过条件获取列表-不分页
  static async conditionQueryList(name) {
    let query = {
      name: name
    }
    let [data] = await conditionQueryList(query)
    return data.data
  }

  // 新增
  static async save(info) {
    let [res] = await save(info)
    if (res.code === 20000) {
      return true
    } else {
      return false
    }
  }

  // 修改
  static async update(info) {
    let [res] = await update(info)
    if (res.code === 20000) {
      return true
    } else {
      return false
    }
  }

  // 删除档案
  static async deleteOne(info) {
    let [res] = await deleteOne(info)
    if (res.code === 20000) {
      return true
    } else {
      return false
    }
  }

  // 移出本班
  static async RemoveOne(info) {
    let [res] = await removeOne(info)
    if (res.code === 20000) {
      return true
    } else {
      return false
    }
  }

  // 获取一个
  static async getOne(info) {
    let [res] = await getOne(info)
    if (res.code === 20000) {
      return res.data
    } else {
      return false
    }
  }

  // 批量导入学生-班级内
  static importStudent(file, clazzId) {
    return new Promise((resolve, reject) => {
      const formData = new FormData()
      formData.append('multipartFile', file)
      formData.append('clazzId', clazzId)
      formData.append('startNum', "1")
      axios.create({
        baseURL: API_URL_EXP_ADMIN
      }).request({
        url: `admin/administrationClazz/multipleImportStudent`,
        method: 'post',
        headers: {'Content-Type': 'multipart/form-data'},
        data: formData
      }).then(response => {
        if (response.status === 200) {
          if (response.data.code === 20000) {
            resolve(true)
          } else {
            msg_err(response.data.msg)
            resolve(false)
          }
        } else {
          msg_err(response.data.msg)
          resolve(false)
        }
      })
    })
  }

  // 批量导入学生-首页
  static importStudentIndex(file) {
    return new Promise((resolve, reject) => {
      const formData = new FormData()
      formData.append('multipartFile', file)
      formData.append('startNum', "1")
      axios.create({
        baseURL: API_URL_EXP_ADMIN
      }).request({
        url: `admin/administrationClazz/multipleImportStudentIndex`,
        method: 'post',
        headers: {'Content-Type': 'multipart/form-data'},
        data: formData
      }).then(response => {
        if (response.status === 200) {
          if (response.data.code === 20000) {
            resolve(true)
          } else {
            msg_err(response.data.msg)
            resolve(false)
          }
        } else {
          msg_err(response.data.msg)
          resolve(false)
        }
      })
    })
  }

  // 导出某个班级的学生
  static exportStudentList(clazzId, search) {
    const url = API_URL_EXP_ADMIN + 'admin/administrationClazz/multipleExportStudent/' + clazzId + "/" + search
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.setAttribute('download', "学生列表.xls")
    document.body.appendChild(link)
    link.click()
    link.remove()
  }

  // 导出所有学生
  static exportStudentListIndex(schoolName, departmentName, majorName, gradeName, name) {
    if (!schoolName) {
      schoolName = "null"
    }
    if (!departmentName) {
      departmentName = "null"
    }
    if (!majorName) {
      majorName = "null"
    }
    if (!gradeName) {
      gradeName = "null"
    }
    if (!name) {
      name = "null"
    }
    const url = API_URL_EXP_ADMIN + `admin/administrationClazz/multipleExportStudentIndex/${schoolName}/${departmentName}/${majorName}/${gradeName}/${name}`
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.setAttribute('download', "学生列表.xls")
    document.body.appendChild(link)
    link.click()
    link.remove()
  }

  // 获取某个学校的用户数量,总数、教师、学生
  static async getOneSchoolUserNumber(schoolId) {
    let [res] = await getOneSchoolUserNumber(schoolId)
    if (res.code === 20000) {
      return res.data
    } else {
      return false
    }
  }
}

export {UserModel}
