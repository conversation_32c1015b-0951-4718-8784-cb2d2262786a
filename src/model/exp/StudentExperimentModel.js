
import {getOneSchoolOneExperimentOverview} from "@/api/exp/StudentExperimentApi";
import {API_URL_EXP_CONSUMER} from "@/model/ConfigModel";

class StudentExperimentModel {
  // 获取某个学校的用户数量,总数、教师、学生
  static async getOneSchoolOneExperimentOverview(schoolId, experimentId) {
    let [res] = await getOneSchoolOneExperimentOverview(schoolId, experimentId)
    if (res.code === 20000) {
      return res.data
    } else {
      return false
    }
  }

  // 导出全校所有学生成绩excel
  static exportOneSchoolAllStudentScore(schoolId,experimentId) {
    let url = API_URL_EXP_CONSUMER + `consumer/performanceManagement/export/Score/oneSchoolAllStudentExperimentResult/${schoolId}/${experimentId}`
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.setAttribute('download', "列表.xls")
    document.body.appendChild(link)
    link.click()
    link.remove()
  }
}

export {
  StudentExperimentModel
}
