import {
  getList,
  save,
  update,
  remove,
  getSeriesList,
  updateSeries,
  saveSeries,
  getOne,
  getOneSchoolExperimentInfoList
} from "@/api/exp/ExperimentApi";
import {CommonModel} from "@/model/CommonModel";

/**
 实验model
 */
class ExperimentModel {
  // 获取列表
  static async getList(page, size, query) {
    let params = {
      page: page,
      size: size,
    }
    params = Object.assign(params, query)
    let [data] = await getList(params)
    if (size === 0) {  // 不分页
      return data.data;
    } else { // 分页
      let list = CommonModel.generateListMybatisPlus(data.data)
      // 遍历config转换为对象
      let listData = list[0]
      return [listData, list[1]];
    }
  }

  // 获取一个
  static async getOne(id) {
    let [res] = await getOne(id)
    if (res.code === 20000) {
      return res.data
    } else {
      return false
    }
  }

  // 新增
  static async save(entity) {
    let [res] = await save(entity)
    if (res.code === 20000) {
      return true
    } else {
      return false
    }
  }

  // 修改
  static async update(entity) {
    let [res] = await update(entity)
    if (res.code === 20000) {
      return true
    } else {
      return false
    }
  }

  // 删除
  static async remove(idArray) {
    let [res] = await remove(idArray)
    if (res.code === 20000) {
      return true
    } else {
      return false
    }
  }

  // 获取列表-实验系列
  static async getSeriesList(page, size, query) {
    let params = {
      page: page,
      size: size,
    }
    params = Object.assign(params, query)
    let [data] = await getSeriesList(params)
    if (size === 0) {  // 不分页
      return data.data;
    } else { // 分页
      let list = CommonModel.generateListMybatisPlus(data.data)
      // 遍历config转换为对象
      let listData = list[0]
      return [listData, list[1]];
    }
  }

  // 新增-系列
  static async saveSeries(entity) {
    let [res] = await saveSeries(entity)
    if (res.code === 20000) {
      return true
    } else {
      return false
    }
  }

  // 修改-实验系列
  static async updateSeries(entity) {
    let [res] = await updateSeries(entity)
    if (res.code === 20000) {
      return true
    } else {
      return false
    }
  }

  // 获取某个学校的实验信息列表
  static async getOneSchoolExperimentInfoList(schoolId) {
    let [res] = await getOneSchoolExperimentInfoList(schoolId)
    if (res.code === 20000) {
      return res.data
    } else {
      return false
    }
  }
}

export {ExperimentModel}
