import {getList,save,remove} from "@/api/exp/DepartmentApi";
import {CommonModel} from "@/model/CommonModel";

/**
 学院model
 */
class DepartmentModel {
  // 获取列表
  static async getList(page, size, query) {
    let params = {
      page: page,
      size: size,
    }
    params = Object.assign(params, query)
    let [data] = await getList(params)
    if (size === 0) {  // 不分页
      return data.data;
    } else { // 分页
      let list = CommonModel.generateListMybatisPlus(data.data)
      // 遍历config转换为对象
      let listData = list[0]
      return [listData, list[1]];
    }
  }

  // 新增
  static async save(entity) {
    let res = await save(entity)
    if (res.code === 20000) {
      return true
    } else {
      return false
    }
  }

  // 删除
  static async remove(ids) {
    let res = await remove(ids)
    if (res.code === 20000) {
      return res.data
    } else {

    }
  }
}

export {DepartmentModel}
