import {
  getExperimentScheduleList,
  getExperimentScheduleListNew,
  getExperimentScheduleDetail,
  update,
  save
} from "@/api/exp/ExperimentScheduleApi";
import {CommonModel} from "@/model/CommonModel";

/**
 实验安排Model
 */
class ExperimentScheduleModel {
  // 获取列表
  static async getExperimentScheduleList(page, size, query) {
    let params = {
      page: page,
      size: size,
    }
    params = Object.assign(params, query)
    let [data] = await getExperimentScheduleList(params)
    if (size === 0) {  // 不分页
      return data.data;
    } else { // 分页
      let list = CommonModel.generateListMybatisPlus(data.data)
      // 遍历config转换为对象
      let listData = list[0]
      return [listData, list[1]];
    }
  }

  // 获取列表-新
  static async getExperimentScheduleListNew(page, size, query) {
    let params = {
      page: page,
      size: size,
    }
    params = Object.assign(params, query)
    let [data] = await getExperimentScheduleListNew(params)
    if (size === 0) {  // 不分页
      return data.data;
    } else { // 分页
      let list = CommonModel.generateListMybatisPlus(data.data)
      // 遍历config转换为对象
      let listData = list[0]
      return [listData, list[1]];
    }
  }

  // 获取详情
  static async getDetail(page, size, query) {
    let params = {
      page: page,
      size: size,
    }
    params = Object.assign(params, query)
    let [data] = await getExperimentScheduleDetail(params)
    if (size === 0) {  // 不分页
      return data.data;
    } else { // 分页
      let list = CommonModel.generateListMybatisPlus(data.data)
      // 遍历config转换为对象
      let listData = list[0]
      return [listData, list[1]];
    }
  }

  // 新增
  static async save(entity) {
    let [res] = await save(entity)
    if (res.code === 20000) {
      return true
    } else {
      return false
    }
  }

  // 修改
  static async update(entity) {
    let [res] = await update(entity)
    if (res.code === 20000) {
      return true
    } else {
      return false
    }
  }

}

export {ExperimentScheduleModel}
