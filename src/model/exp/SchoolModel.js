import {getList, save, getListNew, getSchoolListByName, update, deleteOne} from "@/api/exp/SchoolApi";
import {CommonModel} from "@/model/CommonModel";
import {msg_err} from "@/utils/ele_component";

/**
 学校model
 */
class SchoolModel {
  // 获取学校列表
  static async getList(page, size, query) {
    let params = {
      page: page,
      size: size,
    }
    params = Object.assign(params, query)
    let [data] = await getList(params)
    if (size === 0) {  // 不分页
      return data.data;
    } else { // 分页
      let list = CommonModel.generateListMybatisPlus(data.data)
      // 遍历config转换为对象
      let listData = list[0]
      return [listData, list[1]];
    }
  }

  // 获取学校列表
  static async getListNew(page, size, query) {
    let params = {
      page: page,
      size: size,
    }
    params = Object.assign(params, query)
    let [data] = await getListNew(params)
    if (size === 0) {  // 不分页
      return data.data;
    } else { // 分页
      let list = CommonModel.generateListMybatisPlus(data.data)
      // 遍历config转换为对象
      let listData = list[0]
      return [listData, list[1]];
    }
  }

  // 学校名称搜索提示
  static async getSchoolListByName(name) {
    let query = {
      schoolname: name
    }
    let [data] = await getSchoolListByName(query)
    return data.data
  }

  // 新增
  static async save(info) {
    let [res] = await save(info)
    if (res.code === 20000) {
      return true
    } else {
      return false
    }
  }

  // 修改
  static async update(info) {
    let [res] = await update(info)
    if (res.code === 20000) {
      return true
    } else {
      return false
    }
  }

  // 删除一个
  static async deleteOne(info) {
    let [res] = await deleteOne(info)
    if (res.code === 20000) {
      return true
    } else {
      return false
    }
  }
}

export {SchoolModel}
