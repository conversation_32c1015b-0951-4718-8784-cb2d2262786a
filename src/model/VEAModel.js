// 操作Vue-Element-Admin框架
import $ from 'jquery'

class VEAModel {
  // 设置面包导航-第二级可点击标签名称
  static setBreadSecondTitle(title) {
    const breadElement = document.querySelector("#breadcrumb-container .el-breadcrumb__item:nth-child(2) a")
    breadElement.innerHTML = title
  }

  // 设置面包导航-第二级可点击标签跳转路径
  static setBreadSecondUrl(url) {
    const breadElement = document.querySelector("#breadcrumb-container .el-breadcrumb__item:nth-child(2) a")
    breadElement.href = url
  }

  // 设置面包导航-第二级可点击标签点击事件
  static setBreadSecondClick(fn) {
    const breadElement = document.querySelector("#breadcrumb-container .el-breadcrumb__item:nth-child(2) a")
    breadElement.onclick = fn
  }

  // 设置面包导航-第三级标签名称
  static setBreadThirdTitle(title) {
    const breadElement = document.querySelector("#breadcrumb-container .el-breadcrumb__item:nth-child(3) span.no-redirect")
    breadElement.innerHTML = title
  }

  // 通用底部悬浮按钮组-跟随app-container改变width
  static bottomButtonContainerWidthChange() {
    let maxWidth = $(".app-main").width()
    $(".bottom-button-container").css("width", maxWidth)
    // 监听窗口变化
    $(window).resize(function () {
      let maxWidth = $(".app-main").width()
      $(".bottom-button-container").css("width", maxWidth)
    });
  }
}

export {VEAModel}
