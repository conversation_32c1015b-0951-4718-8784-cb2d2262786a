import { getList,getExperimentInfo } from '@/api/picoSystem/ExperimentApi'
import {get} from "echarts/src/component/toolbox/featureManager";

/**
 * Experiment model
 */
class ExperimentModel {
  // 获取所有pico实验列表
  static async getList() {
    let [data] = await getList()
    return data.data
  }

  // 获取实验详情
  static async getExperimentInfo(data1) {
    let [data] = await getExperimentInfo(data1)
    return data.data
  }
}

export { ExperimentModel }
