import {CommonModel} from '@/model/CommonModel'
import {
  getPageList,
  addOrEdit,
  getList,
  deleteOne,
  getOne,
  getEquipmentListWhenScheduleAdd,
  getEquipmentListWhenScheduleEdit, getUseInfoIndexList, getUseInfoDetailList
} from '@/api/picoSystem/ScheduleApi'

/**
 * Schedule model
 */
class ScheduleModel {
  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity)
    return data
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document)
    return CommonModel.generateListMongo(data.data)
  }

  // 获取使用数据首页-分页列表
  static async getUseInfoIndexList(page, size, sort, document) {
    let [data] = await getUseInfoIndexList(page, size, sort, document)
    return CommonModel.generateListMongo(data.data)
  }

  // 获取使用数据首页-分页列表
  static async getUseInfoDetailList(query) {
    let [data] = await getUseInfoDetailList(query)
    return data.data
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document)
    return data.data
  }

  // 删除一个
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === '000000') {
      return res.data
    } else {
      return false
    }
  }

  // 获取一个
  static async getOne(data) {
    let [res] = await getOne(data)
    if (res.code === '000000') {
      return res.data
    } else {
      return false
    }
  }

  // 获取新增安排时的设备列表
  static async getEquipmentListWhenScheduleAdd(dealerId, startTime, endTime) {
    let data = {
      dealerId,
      startTime,
      endTime
    }
    let [res] = await getEquipmentListWhenScheduleAdd(data)
    if (res.code === '000000') {
      return res.data
    } else {
      return false
    }
  }

  // 获取新增安排时的设备列表
  static async getEquipmentListWhenScheduleEdit(scheduleId, dealerId, startTime, endTime) {
    let data = {
      scheduleId,
      dealerId,
      startTime,
      endTime
    }
    let [res] = await getEquipmentListWhenScheduleEdit(data)
    if (res.code === '000000') {
      return res.data
    } else {
      return false
    }
  }
}

export {ScheduleModel}
