import {CommonModel} from "@/model/CommonModel";
import {getPageList, addOrEdit, getList, deleteOne} from "@/api/picoSystem/PicoTaskApi";

/**
 * PicoTask model
 */
class PicoTaskModel {
  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除一个
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 文件下载存储路径选项
  static downloadStoreOptionList = [
    {label: "自定义", value: ""},
    {label: "播控图标目录", value: "/pre_resource/broadcast/appicons/"},
    {label: "播控配置目录", value: "/pre_resource/broadcast/config/"},
    {label: "根目录", value: "/"},
    {label: "下载目录", value: "/Download/"},
  ]
}

export {PicoTaskModel}
