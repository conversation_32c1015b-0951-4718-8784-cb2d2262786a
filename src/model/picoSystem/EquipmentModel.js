import {CommonModel} from "@/model/CommonModel";
import {getPageList, addOrEdit, getList, deleteOne, getOne, unbind} from "@/api/picoSystem/EquipmentApi";
import axios from "axios";
import {API_URL_EXP_ADMIN, API_URL_PICO_SYSTEM} from "@/model/ConfigModel";
import {msg_err} from "@/utils/ele_component";
import {getToken} from "@/utils/auth";

const ONLINE_JUDGE_TIME = 60000

/**
 * Equipment model
 */
class EquipmentModel {
  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除一个
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 获取一个
  static async getOne(data) {
    let [res] = await getOne(data)
    if (res.code === "000000") {
      let equipment = res.data
      // 判断是否在线
      let dateNow = new Date().getTime()
      equipment.onLine=!(!equipment.lastLiveDate || dateNow - equipment.lastLiveDate > ONLINE_JUDGE_TIME)
      return equipment
    } else {
      return false
    }
  }

  // 根据设备编号，判断设备类型
  static getEquipmentTypeById(equipmentId) {
    let type = "未知"
    if (equipmentId.startsWith("PA7A")) {
      type = "G2 4K"
    }
    if (equipmentId.startsWith("PA7B")) {
      type = "Neo2"
    }
    if (equipmentId.startsWith("PA7H")) {
      type = "Neo3"
    }
    if (equipmentId.startsWith("PA7L")) {
      type = "Neo3 Pro"
    }
    return type
  }

  // 解除绑定
  static async unbind(equipmentId) {
    let [res] = await unbind({equipmentId})
    return res.data
  }

  // 批量导入Pico
  static importPico(file) {
    return new Promise((resolve, reject) => {
      const formData = new FormData()
      formData.append('multipartFile', file)
      formData.append('startNum', "1")
      axios.create({
        baseURL: API_URL_PICO_SYSTEM
      }).request({
        url: `/v1/equipment/multipleImportPico`,
        method: 'post',
        headers: {'Content-Type': 'multipart/form-data', 'Authorization': 'Bearer ' + getToken()},
        data: formData
      }).then(response => {
        if (response.status === 200) {
          if (response.data.code === "000000") {
            resolve(true)
          } else {
            msg_err(response.data.msg)
            resolve(false)
          }
        } else {
          msg_err(response.data.msg)
          resolve(false)
        }
      })
    })
  }
}

export {EquipmentModel}
