import {CommonModel} from "@/model/CommonModel";
import {getMessageLogPageList} from "@/api/microService/MessageLogApi";

class MessageLogModel {
  // 获取列表
  static async getMessageLogPageList(platformId, page, size, sort, document) {
    let [data] = await getMessageLogPageList(platformId, page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

}

export {MessageLogModel}
