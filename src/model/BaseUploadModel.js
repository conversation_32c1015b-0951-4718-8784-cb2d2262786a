import * as qiniu from 'qiniu-js'
import {getQiNiuToken} from "@/api/UploadApi";
import {msg_err} from '@/utils/ele_component'
import axios from "axios";
import {date_format} from "@/utils/common";

class BaseUploadModel {
  // 数组转为upload数组
  static arrayToUploadArray(arr) {
    let arr_return = []
    arr.forEach(function (item) {
      arr_return.push({
        url: item
      })
    })
    return arr_return
  }

  // upload数组转为普通数组
  static uploadArrayToArray(arr) {
    let arr_return = []
    arr.forEach(function (item) {
      arr_return.push(item.url)
    })
    return arr_return
  }

  // 七牛云不同文件存不同bucket对应的域名
  static getBucketDomain(bucket) {
    let domain = {
      "zyhd-package-system": "http://package-system-file.cdzyhd.com",
      "zyhd-video": "http://video-file.cdzyhd.com/",
      "other": "http://resouce.cdzyhd.com",
    }
    let returnDomain = "";
    if (domain.hasOwnProperty(bucket)) {
      returnDomain = domain[bucket];
    } else {
      returnDomain = domain["other"];
    }
    return returnDomain
  }

  // 获取七牛云上传token
  static async getQiniuUploadToken(bucket) {
    // 文件类型不同上传到不同的bucket
    if (!bucket) {
      bucket = "zyhd-resouce"
    }
    // "zyhd-video" 视频平台
    // "zyhd-package-system" 包管理系统
    let [data] = await getQiNiuToken(bucket);
    return data.data;
  }

  // 获取随机文件名
  static getUuid() {
    let s = []
    let hexDigits = '0123456789abcdef'
    for (let i = 0; i < 36; i++) {
      s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
    }
    s[14] = '4'  // bits 12-15 of the time_hi_and_version field to 0010
    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)  // bits 6-7 of the clock_seq_hi_and_reserved to 01
    s[8] = s[13] = s[18] = s[23] = '-'

    let uuid = s.join('')
    return uuid
  }

  // 七牛云上传
  static async qiNiuUpload(file, f, bucket) {
    // 获取上传token
    let token = await this.getQiniuUploadToken(bucket)
    let nameArr = file['name'].split('.')
    let type = nameArr[nameArr.length - 1]
    let key = this.getUuid() + '.' + type  // 上传后文件资源名以设置的 key 为主，如果 key 为 null 或者 undefined，则文件资源名会以 hash 值作为资源名。
    // 240606 上传到日期文件夹
    let date = date_format(new Date(), "yyyy-MM-dd")
    key = date + "/" + key

    let config = {
      concurrentRequestLimit: 1,
      useCdnDomain: false,   //表示是否使用 cdn 加速域名，为布尔值，true 表示使用，默认为 false。
      region: qiniu.region.z2     // 根据具体提示修改上传地区,当为 null 或 undefined 时，自动分析上传域名区域
    }

    let putExtra = {
      fname: file.name,  //文件原文件名
      params: {}, //用来放置自定义变量
      mimeType: null  //用来限制上传文件类型，为 null 时表示不对文件类型限制；限制类型放到数组里： ["image/png", "image/jpeg", "image/gif"]
    }
    let observable = qiniu.upload(file, key, token, putExtra, config)
    observable.subscribe(f)
  }

  // 包管理系统-七牛云上传
  static async qiNiuUploadForPackageSystem(file, experimentId, experimentVersionId, f) {
    // 获取上传token
    let token = await this.getQiniuUploadToken("zyhd-package-system")
    let nameArr = file['name'].split('.')
    let type = nameArr[nameArr.length - 1]
    let key = this.getUuid()+"_"+file.name
    // 240606 上传到日期文件夹
    let date = date_format(new Date(), "yyyy-MM-dd")
    key = experimentId + "/" + experimentVersionId + "/" + date + "/" + key // 规则 实验id/实验版本id/日期/文件名

    let config = {
      concurrentRequestLimit: 1,
      useCdnDomain: false,   //表示是否使用 cdn 加速域名，为布尔值，true 表示使用，默认为 false。
      region: qiniu.region.z2     // 根据具体提示修改上传地区,当为 null 或 undefined 时，自动分析上传域名区域
    }

    let putExtra = {
      fname: file.name,  //文件原文件名
      params: {}, //用来放置自定义变量
      mimeType: null  //用来限制上传文件类型，为 null 时表示不对文件类型限制；限制类型放到数组里： ["image/png", "image/jpeg", "image/gif"]
    }
    let observable = qiniu.upload(file, key, token, putExtra, config)
    observable.subscribe(f)
  }

  // 各个场景的文件大小限制 单位KB
  static fileSizeLimit = {
    // 图片
    img: 2048,
    // 视频
    video: 10240,
    // 音频
    audio: 10240,
    // 其它
    other: 10240,
  }

  static fileTypeEnum = {
    'image': ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/bmp'],
    'video': ['video/mp4', 'video/avi'],
    'audio': ['audio/ogg', 'audio/mp3']
  }
  static fileSuffixEnum = {
    'image': ['jpg', 'png', 'jpeg', 'bmp', 'gif'],
    'video': ['mp4', 'ogg', 'avi', 'mov', 'rm', 'rmvb', '3gp', 'flv', 'mkv', 'm4v', 'mpg', 'mpeg', 'wmv', 'asf', 'asx'],
    'audio': ['ogg', 'mp3']
  }

  // 获取文件对应的文件类型
  static getFileType(file) {
    let fileType = file.type
    //console.log(fileType);
    let fileTypeMain = fileType.split('/')[0]
    let fileTypeSub = fileType.split('/')[1]
    return [fileTypeMain, fileTypeSub]
  }

  // 检测文件大小限制
  static checkFileSize(sizeLimit, file) {
    let size = file.size
    let sizeInput = parseInt(size / 1024)
    if (sizeLimit < sizeInput) {
      msg_err('文件必须小于' + sizeLimit + 'KB，当前文件大小是' + sizeInput + 'KB')
      return false
    } else {
      return true
    }
  }

  // 限制文件类型
  static fileTypeLimit(file, type) {
    let [fileType] = this.getFileType(file)
    return fileType === type;
  }

  // 获取文件后缀名
  static getSuffix(v) {
    let nameArr = v.split('.')
    let fileSuffix = nameArr[nameArr.length - 1]
    return fileSuffix
  }

  // element upload的上传方法
  // tartget 默认七牛云qiniu  测试服务器zy0
  static async uploadRequest(upload, target) {
    let file = upload.file
    return await new Promise((resolve, reject) => {
      if (target === 'zy0') {
        const formData = new FormData()
        formData.append('file', file)
        axios.create().request({
          url: `http://static.xhyjbj.com/api/uploadOneFile`,
          // url: `http://127.0.0.1:10810/api/uploadOneFile`,
          method: 'post',
          headers: {'Content-Type': 'multipart/form-data'},
          data: formData
        }).then(response => {
          if (response.status === 200) {
            resolve({data: "http://static.xhyjbj.com/files" + response.data.url})
          } else {
            msg_err(response.data)
            resolve(false)
          }
        })
      }
      if (!target || target === "qiniu") {
        BaseUploadModel.qiNiuUpload(file, {
          next: (result) => {
          },
          error: (errResult) => {
            console.log(errResult)
            msg_err('上传失败')
          },
          complete: (result) => {
            let domain = BaseUploadModel.getBucketDomain(file)
            let url = domain + '/' + result.key + ''
            resolve({data: url})
          }
        })
      }
    })
  }

  // 判断是否是要导入的excel文件
  static isImportExcelFile(file) {
    console.log(file.type)
    if (file.type !== "application/vnd.ms-excel" && file.type !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {
      msg_err("请选择后缀名为xls的Excel导入文件")
      return false
    } else {
      return true
    }
  }
}

export {BaseUploadModel}
