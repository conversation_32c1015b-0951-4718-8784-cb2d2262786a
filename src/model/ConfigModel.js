// api地址-erp后端 必须要要/结尾，不然包管理系统会出错
const API_URL_ERP = "https://api.cdzyhd.com/system/erp/"; // 正式
// const API_URL_ERP = "http://localhost:8900/"; // 本地

// api地址-exp后端-Admin
// const API_URL_EXP_ADMIN = "http://*************:9900/" // 本地
// const API_URL_EXP_ADMIN = "http://*************:9900/" // 线上测试
const API_URL_EXP_ADMIN = "https://api.cdzyhd.com/system/exp_admin/" // 线上正式

// api地址-exp前端-Consumer
// const API_URL_EXP_CONSUMER = "http://*************:9901/" // 本地
// const API_URL_EXP_CONSUMER = "http://*************:9901/" // 线上测试
const API_URL_EXP_CONSUMER = "https://api.cdzyhd.com/system/exp_consumer/" // 线上正式

// api地址-exp后端-GO版本
const API_URL_EXP_GO = "https://api.cdzyhd.com/system/exp_go/"
// const API_URL_EXP_GO = "http://localhost:8999/"

// api地址-versionManager
const API_URL_VERSION_MANAGER = "https://api.cdzyhd.com/system/versionManager/"

// api地址-AI客户端商用版
const API_URL_AI_CLIENT = "https://api.cdzyhd.com/system/ai_client/"
// const API_URL_AI_CLIENT = "http://localhost:10077/"

// 平台id erp
const PLATFORM_ID_ERP = "erp" // 正式
// const PLATFORM_ID_ERP = "erp_test" // 测试
// 平台id exp
const PLATFORM_ID_EXP = "exp" // 正式
// const PLATFORM_ID_EXP = "exp_test" // 测试

// 配置名称-EXP
const CONFIG_NAME_EXP = "config_exp" // 正式
// const CONFIG_NAME_EXP = "config_exp_test" // 测试
// 配置名称-官网
const CONFIG_NAME_OFFICIAL_WEB = "config_officialWeb" // 正式
// const CONFIG_NAME_OFFICIAL_WEB = "config_officialWeb_test" // 正式
// 配置名称-活动页
const CONFIG_NAME_ACTIVITY = "config_activity" // 正式
// const CONFIG_NAME_ACTIVITY = "config_activity_test" // 测试
// 配置名称-VersionManager
const CONFIG_NAME_VERSION_MANAGER = "config_versionManager" // 正式
// 配置名称-官网-薪火印记
const CONFIG_NAME_OFFICIAL_WEB_XHYJ = "config_officialWeb_xhyj"
// 配置名称-库文件管理系统
const CONFIG_NAME_LIBRARY = "config_library" // 正式
// 官网正式地址
const OFFICIAL_WEB_URL = "https://www.cdzyhd.com/"

// 配置名称-资源平台
// const CONFIG_NAME_RESOURCE_PLATFORM = "config_resourcePlatform_test" // 测试
const CONFIG_NAME_RESOURCE_PLATFORM = "config_resourcePlatform" // 正式


export {
  API_URL_EXP_GO,
  API_URL_ERP,
  API_URL_EXP_ADMIN,
  API_URL_EXP_CONSUMER,
  PLATFORM_ID_ERP,
  PLATFORM_ID_EXP,
  CONFIG_NAME_EXP,
  CONFIG_NAME_OFFICIAL_WEB,
  CONFIG_NAME_ACTIVITY,
  API_URL_VERSION_MANAGER,
  CONFIG_NAME_VERSION_MANAGER,
  OFFICIAL_WEB_URL,
  CONFIG_NAME_OFFICIAL_WEB_XHYJ,
  CONFIG_NAME_LIBRARY,
  CONFIG_NAME_RESOURCE_PLATFORM,
  API_URL_AI_CLIENT,
}
