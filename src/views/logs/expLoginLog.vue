<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success" v-permission="['administrator']" @click="accountMark.dialog=true"
          >标记账号
          </el-button>
          <el-button class="el-button" type="success" v-permission="['administrator']"
                     @click="AccountMarkListMethods().open()"
          >标记账号列表
          </el-button>
          <el-button class="el-button" type="success" v-permission="['administrator']" @click="exportExcel"
          >导出日志
          </el-button>
        </div>
      </div>
    </div>

    <!--列表-->
    <div class="userInfo" v-if="userInfo.hasOwnProperty('name')">
      <div><span>账号：</span>{{ userInfo.account }}</div>
      <div><span>姓名：</span>{{ userInfo.name }}</div>
      <div><span>创建/更新时间：</span>{{ date_format(userInfo.createTime, 'yyyy-MM-dd HH:mm:ss') }}</div>
      <div><span>学校：</span>{{ userInfo.schoolName }}</div>
    </div>
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;"
              @sort-change="sort=>ListMethods().sortChange(sort)">
      <el-table-column label="实验名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.experimentName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.date }}</span>
        </template>
      </el-table-column>
      <el-table-column label="ip" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.ip }}-{{ scope.row.ipInfo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分数" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.score ? scope.row.score : "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用时" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.useTime ? scope.row.useTime + "分钟" : "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="登录结果" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.commite }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!--弹窗账号标记-->
    <el-dialog
      title="查找要标记的账号"
      :visible.sync="accountMark.dialog"
      width="900px"
      center
      :close-on-click-moda="false"
      v-el-drag-dialog>
      <!--搜索组件-->
      <div class="search-box flex flex-end" style="margin-bottom: 20px;width: 300px;">
        <el-input v-model="accountMark.account">
          <el-button slot="append" @click="AccountMarkMethods().clickSearchBtn()">搜索</el-button>
        </el-input>
      </div>
      <el-table :data="accountMark.list" v-loading="accountMark.loading" element-loading-text="加载中" border fit
                style="width: 100%;">
        <el-table-column label="账号" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.account }}</span>
          </template>
        </el-table-column>
        <el-table-column label="姓名" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属学校" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.schoolName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button @click="AccountMarkMethods().clickMarkBtn(scope.row)">加入标记列表</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!--弹窗-已标记账号列表-->
    <el-dialog
      title="已标记账号列表"
      :visible.sync="accountMarkList.dialog"
      width="1300px"
      center
      :close-on-click-moda="false"
    >
      <!--搜索组件-->
      <div class="search-box flex flex-end" style="margin-bottom: 20px;width: 300px;">
        <el-input v-model="accountMarkList.search" placement="">
          <el-button slot="append" @click="AccountMarkListMethods().clickSearchBtn()">搜索</el-button>
        </el-input>
      </div>
      <el-table :data="accountMarkList.lists.list" v-loading="accountMarkList.lists.loading" element-loading-text="加载中"
                border fit
                style="width: 100%;">
        <el-table-column label="账号" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.info.account }}</span>
          </template>
        </el-table-column>
        <el-table-column label="姓名" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.info.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属学校" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.info.schoolName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注信息" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.info.remark }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.info.createTime | dateFormat }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="250px">
          <template slot-scope="scope">
            <el-button @click="AccountMarkListMethods().clickLogBtn(scope.row)">实验日志</el-button>
            <el-button @click="AccountMarkListMethods().clickDeleteBtn(scope.row)" type="text">取消标记</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--列表分页-->
      <div class="pagination-container">
        <el-pagination background @current-change="(number)=>AccountMarkListMethods().pageChange(number)"
                       :current-page.sync="accountMarkList.lists.pages.number"
                       :page-size.sync="accountMarkList.lists.pages.size"
                       layout="total,prev, pager, next,jumper,sizes" :total="accountMarkList.lists.pages.totalElements"
                       :page-sizes="[10,20,50,100,200,500]"
                       @size-change="(size)=>AccountMarkListMethods().pageLimitChange(size)"
                       :page-count="accountMarkList.lists.pages.totalPages">
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {MessageLogModel} from "@/model/microService/MessageLogModel";
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, getQuery} from "@/utils/common";
import enums from "@/views/exp/school/studentClazz";
import {SchoolModel} from "@/model/exp/SchoolModel";
import {msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {CommonModel} from "@/model/CommonModel";
import {ExperimentModel} from "@/model/exp/ExperimentModel";
import {excel_export_from_json} from "@/utils/excel";
import {UserModel} from "@/model/exp/UserModel";
import {getResetPasswordList} from "@/api/exp/User";
import {GeneralInfoModel} from "@/model/erp/GeneralInfoModel";
import fa from "element-ui/src/locale/lang/fa";
import {ToolsModel} from "@/model/erp/ToolsModel";

export default {
  name: "expLog",
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles
    })
  },
  filters: {dateFormat},
  data() {
    let $this = this;
    return {
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      // 枚举
      enums: enums,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '学号',
              key: 'info.args.account',
              value: ''
            },
          ],
          filter: [
            {
              type: 'select',
              label: '学校',
              key: 'info.args.schoolname',
              value: '',
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              },
              format: function (value) {
                return {
                  "$or": [
                    {"info.args.schoolId": value},
                    {"info.args.schoolname": $this.lists.searchFilter.filter[0]["dataObject"][value]}
                  ]
                }
              }
            },
            {
              type: 'select',
              label: '实验',
              key: 'info.args.experimentid',
              value: '',
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
          ]
        }
      },
      userInfo: {},
      // 弹窗-账号标记
      accountMark: {
        dialog: false
      },
      accountMarkList: {
        dialog: false,
        lists: {
          list: [],
          loading: false,
          query: {},
          queryBase: {},
          sort: "",
          pages: {
            size: 20
          },
        },
      }
    }
  },
  mounted() {
    this.ListMethods().initFilter(0)
    this.ListMethods().initFilter(1)
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let queryBase = {
            "$or": [
              {"info.url": "/consumer/ums/loginExe"},
              {"info.url": "/consumer/ums/programAddScore"},
              {"info.url": "/consumer/ums/programAddScore211104"}
            ]
          }
          if (query.hasOwnProperty('$or')) {
            let queryOr = JSON.parse(JSON.stringify(query["$or"]))
            delete query["$or"]
            query["$and"] = []
            query["$and"].push(queryBase)
            query["$and"][1] = {}
            query["$and"][1]["$or"] = queryOr
          } else {
            query = Object.assign(query, queryBase)
          }
          let lists = [];
          [lists] = await MessageLogModel.getMessageLogPageList("emp", page - 1, size, "createTime,asc", query);
          // 遍历列表，整理信息
          let listLast = [];
          let userInfo = null;
          // todo 通过account和学校id获取信息，现在是先遍历一遍，传分时的userid获取
          for (let i = 0; i < lists.length; i++) {
            let li = lists[i]["info"];
            // 获取用户信息
            if (li["args"][0].hasOwnProperty("userId")) {
              userInfo = await UserModel.getOne(li["args"][0]["userId"])
              let info = {
                "account": li["args"][0]["account"],
                "name": userInfo["name"],
                "createTime": userInfo["createTime"],
                "schoolName": $this.lists.searchFilter["filter"][0]["dataObject"][userInfo["schoolId"]],
              }
              $this.$set($this, "userInfo", info)
              break;
            }
          }
          for (let i = 0; i < lists.length; i++) {
            let li = lists[i]["info"];
            li["commite"] = li["commite"].replace("执行完成（", "").replace("）", "")
            // 计算分数和登录时间
            console.log(li)
            if (li["method"] === "程序登录") { //登录
              let experimentId = ""
              if (li["args"].length === 1) { // 兼容老日志格式
                experimentId = li["args"][0]["experimentid"]
              }
              if (li["args"].length === 2) {
                experimentId = li["args"][1]["experimentid"]
              }
              li["experimentName"] = $this.lists.searchFilter["filter"][1]["dataObject"][experimentId]
              // 标记ip
              li["ipInfo"] = await ToolsModel.getIpInfoOnline2(li["ip"])
              if (i + 1 < lists.length) {
                let liNext = lists[i + 1]["info"]
                // 下一个记录是传分，且是同一个实验
                if (liNext["args"].length === 2 && liNext["args"][1]["experimentid"] === experimentId && liNext["method"].indexOf("提交分数") > -1) {
                  // 获取分数
                  li["score"] = liNext["args"][1]["score"]
                  // 获取耗时
                  let startTime = new Date(li["date"]).getTime()
                  let endTime = new Date(liNext["date"]).getTime()
                  li["useTime"] = Math.ceil((endTime - startTime) / 1000 / 60)
                  listLast.push(li)
                  i++
                } else {
                  listLast.push(li)
                }
              } else {
                listLast.push(li)
              }
            }
          }
          console.log(listLast)
          $this.$set($this.lists, "list", listLast.reverse())
          $this.lists.loading = false;
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter(type) {
          if (type === 0) {
            // 获取学校列表
            let schoolList = await SchoolModel.getList(0, 0, {})
            let generateListFilter0 = CommonModel.generateListFilterOptions('name', 'id', schoolList, true)
            $this.$set($this.lists.searchFilter.filter[0], "data", generateListFilter0[0])
            $this.$set($this.lists.searchFilter.filter[0], "dataObject", generateListFilter0[1])
            $this.$set($this.lists.searchFilter.filter[0], "dataOrigin", schoolList)
          }
          if (type === 1) {
            // 获取实验列表
            let [experimentList] = await ExperimentModel.getList(1, 10000, {})
            let generateListFilter3 = CommonModel.generateListFilterOptions('name', 'id', experimentList, true)
            $this.$set($this.lists.searchFilter.filter[1], "data", generateListFilter3[0])
            $this.$set($this.lists.searchFilter.filter[1], "dataObject", generateListFilter3[1])
            $this.$set($this.lists.searchFilter.filter[1], "dataOrigin", experimentList)
          }
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          // 必须先输入学号，选择学校
          if ($this.lists.searchFilter["search"][0].value && $this.lists.searchFilter["filter"][0].value) {
            $this.lists.query = query
            this.getList(0, $this.lists.pages.size, $this.lists.query)
          } else {
            msg_err("请先输入学号和选择学校");
          }
        },
      }
    },
    // 导出excel
    async exportExcel() {
      // 生成查询参数
      let list = this.lists.list;
      if (this.lists.list.length > 0) {
        // map reduce生成arr
        function formatJson(filterVal, jsonData) {
          return jsonData.map(v => filterVal.map(j => {
            let value = '';
            switch (j) {
              case "score":
                value = v["score"] ? v["score"] : "--"
                break;
              case "useTime":
                value = v["useTime"] ? v["useTime"] + "分钟" : "--"
                break;
              case "ip":
                value = v["ip"] + " - " + v["ipInfo"]
                break;
              default:
                value = v[j]
            }
            return value
          }))
        }

        const header = ['实验名称', '时间', 'ip', '分数', "用时", "登录结果"];
        const filter = ["experimentName", "date", "ip", "score", 'useTime', 'commite'];
        // 导出excel
        excel_export_from_json(list, header, filter, formatJson, "账号实验日志-" + date_format(new Date(), "yyyy-MM-dd HH:mm:ss"))
      } else {
        msg_err('要导出的列表为空')
      }
    },
    // 账号标记
    AccountMarkMethods() {
      let $this = this;
      return {
        // 获取账号列表
        async clickSearchBtn() {
          if ($this.accountMark.account) {
            $this.accountMark.loading = true;
            let [data,] = await getResetPasswordList({
              account: $this.accountMark.account
            })
            if (!data.data) {
              data.data = []
            }
            $this.$set($this.accountMark, "list", data.data)
            $this.accountMark.loading = false;
          }
        },
        // 点击加入标记按钮
        async clickMarkBtn(user) {
          // 判断该账号是否已经被标记过
          let searchResult = await GeneralInfoModel.getList({
            type: "expAccountMark",
            "info.account": user.account,
            "info.schoolId": user.schoolId
          })
          if (searchResult.length > 0) {
            msg_err("该账号已标记过!")
            return
          }
          let remark = await msg_input("输入该账号备注信息", "", "")
          if (!remark) {
            msg_err("标记失败，请输入备注信息!");
            return
          }
          let saveResult = await GeneralInfoModel.addOrEdit({
            type: "expAccountMark",// 信息类型-新实验平台标记账号
            info: {
              account: user.account,
              schoolId: user.schoolId,
              schoolName: user.schoolName,
              name: user.name,
              createTime: new Date(user.userCreateTime).getTime(),
              userId: user.userId,
              remark: remark
            }
          })
          if (saveResult) {
            msg_success("标记该用户成功！")
            $this.accountMark.dialog = false
          }
        }
      }
    },
    // 账号标记列表
    AccountMarkListMethods() {
      let $this = this;
      return {
        async clickLogBtn(info) {
          let query = {
            "info.args.account": info.info.account,
            "$or": [{"info.args.schoolId": info.info.schoolId}, {"info.args.schoolname": info.info.schoolName}]
          }
          $this.ListMethods().getList(0, 1000, query)
          $this.accountMarkList.dialog = false
        },
        open() {
          $this.accountMarkList.dialog = true;
          this.getList(0, 20, {})
        },
        async getList(page, size, query) {
          $this.accountMarkList.lists.loading = true;
          query = Object.assign({
            type: "expAccountMark"
          }, query);
          [$this.accountMarkList.lists.list, $this.accountMarkList.lists.pages] = await GeneralInfoModel.getPageList(page - 1, size, "", query)
          $this.accountMarkList.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.accountMarkList.lists.pages.size, $this.accountMarkList.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.accountMarkList.lists.pages.page, size, $this.accountMarkList.lists.query)
        },
        // 点击搜索按钮
        clickSearchBtn() {
          $this.accountMarkList.lists.query = {
            "$or": [
              {
                "info.account": {'$regex': `.*${$this.accountMarkList.search}.*`}
              },
              {
                "info.name": {'$regex': `.*${$this.accountMarkList.search}.*`}
              }, {
                "info.remark": {'$regex': `.*${$this.accountMarkList.search}.*`}
              }
            ]
          }
          this.getList(0, $this.accountMarkList.lists.pages.size, $this.accountMarkList.lists.query)
        },
        // 点击取消标记按钮
        async clickDeleteBtn(generalInfo) {
          if (await GeneralInfoModel.deleteOne(generalInfo.generalInfoId)) {
            this.getList(0, $this.accountMarkList.lists.pages.size, $this.accountMarkList.lists.query)
          }
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.userInfo {
  width: 300px;
  margin: 0 auto;

  div {
    margin-bottom: 15px;
    color: #666;
  }

  span {
    display: inline-block;
    width: 130px;
    text-align: right;
    margin-right: 10px;
    color: #333;
    font-weight: bold;
  }
}
</style>
