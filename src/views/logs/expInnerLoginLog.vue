<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success" v-permission="['administrator']"
                     @click="companyIpMethods().open()"
          >已标记公司IP列表
          </el-button>
          <el-button class="el-button" type="success" v-permission="['administrator']" @click="exportExcel"
          >导出日志
          </el-button>
        </div>
      </div>
    </div>

    <!--列表-->
    <div class="userInfo" v-if="userInfo.hasOwnProperty('name')" style="display: none">
      <div><span>账号：</span>{{ userInfo.account }}</div>
      <div><span>姓名：</span>{{ userInfo.name }}</div>
      <div><span>创建/更新时间：</span>{{ date_format(userInfo.createTime, 'yyyy-MM-dd HH:mm:ss') }}</div>
      <div><span>学校：</span>{{ userInfo.schoolName }}</div>
    </div>
    <div class="log-select flex flex-start" style="margin-bottom: 15px">
      <span>日志显示范围：</span>
      <el-radio-group v-model="lists.showRange" @change="v=>ListMethods().showRangeChange(v)">
        <el-radio :label="'all'">全部</el-radio>
        <el-radio :label="'unusual'">仅异常</el-radio>
        <el-radio :label="'normal'">仅正常</el-radio>
      </el-radio-group>
    </div>
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;"
              @sort-change="sort=>ListMethods().sortChange(sort)">
      <el-table-column label="是否异常" align="center" width="80px">
        <template slot-scope="scope">
          <div :class="scope.row.unusual?'unusual':''">{{ scope.row.unusual ? "是" : "否" }}</div>
        </template>
      </el-table-column>
      <el-table-column label="账号" align="center">
        <template slot-scope="scope">
          <span>{{ userInfo.account }}</span>
        </template>
      </el-table-column>
      <el-table-column label="姓名" align="center">
        <template slot-scope="scope">
          <span>{{ userInfo.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="软件名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.experimentName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" width="160px">
        <template slot-scope="scope">
          <span>{{ scope.row.date }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" width="160px">
        <template slot-scope="scope">
          <span>{{ scope.row.endTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分数" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.score ? scope.row.score : "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用时" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.useTime ? scope.row.useTime + "分钟" : "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="登录结果" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.commite }}</span>
        </template>
      </el-table-column>
      <el-table-column label="IP" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.ip }}</span>
        </template>
      </el-table-column>
      <el-table-column label="IP归属地" align="center">
        <template slot-scope="scope">
          <span style="margin-right: 10px">{{ scope.row.ipInfo }}</span>
          <el-button type="text" size="mini"
                     @click="ListMethods().clickIpView(scope.row,scope.$index)">线上查询
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--弹窗-已标记账号列表-->
    <el-dialog
      title="已标记公司外网IP列表"
      :visible.sync="companyIpList.dialog"
      width="1300px"
      center
      :close-on-click-moda="false"
    >
      <div class="flex flex-end" style="margin-bottom: 10px">
        <el-button type="primary" @click="companyIpMethods().clickMarkBtn()">标记一个公司外网IP</el-button>
      </div>
      <el-table :data="companyIpList.lists.list" v-loading="companyIpList.lists.loading" element-loading-text="加载中"
                border fit
                style="width: 100%;">
        <el-table-column label="IP" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.info.ip }}</span>
          </template>
        </el-table-column>
        <el-table-column label="添加时间" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime | dateFormat }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="250px">
          <template slot-scope="scope">
            <el-button @click="companyIpMethods().clickDeleteBtn(scope.row)" type="text">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {MessageLogModel} from "@/model/microService/MessageLogModel";
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, getQuery} from "@/utils/common";
import enums from "@/views/exp/school/studentClazz";
import {SchoolModel} from "@/model/exp/SchoolModel";
import {msg_confirm, msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {CommonModel} from "@/model/CommonModel";
import {ExperimentModel} from "@/model/exp/ExperimentModel";
import {excel_export_from_json} from "@/utils/excel";
import {UserModel} from "@/model/exp/UserModel";
import {getResetPasswordList} from "@/api/exp/User";
import {GeneralInfoModel} from "@/model/erp/GeneralInfoModel";
import fa from "element-ui/src/locale/lang/fa";
import {ToolsModel} from "@/model/erp/ToolsModel";

export default {
  name: "expInnerLoginLog",
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles
    })
  },
  filters: {dateFormat},
  data() {
    let $this = this;
    return {
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      // 枚举
      enums: enums,
      // 列表
      lists: {
        showRange: 'all',
        list: [],
        listOrigin: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: 'IP',
              key: 'info.ip',
              value: ''
            },
          ],
          filter: [
            {
              type: 'select',
              label: '人员',
              key: 'info.args.account',
              value: '',
              data: [
                {label: "段俊民", value: "djm"},
                {label: "王凯龙", value: "wkl"},
                {label: "黄俊文", value: "hjw"},
                {label: "王博", value: "wb"},
                {label: "杨子蕴", value: "yzy"},
                {label: "周靓雯", value: "zlw"},
                {label: "邓辰汐", value: "dcx"},
                {label: "晏朝爽", value: "10005"},
                {label: "李川", value: "10006"},
                {label: "伍富强", value: "10008"},
                {label: "胡捷", value: "hj"},
                {label: "代云康", value: "66"},
              ],
              dataObject: {
                "djm": "段俊民",
                "wkl": "王凯龙",
                "hjw": "黄俊文",
                "wb": "王博",
                "yzy": "杨子蕴",
                "zlw": "周靓雯",
                "dcx": "邓辰汐",
                "hj": "胡捷",
                "66": "代云康",
                "10005":"晏朝爽",
                "10006":"李川",
                "10008":"伍富强"
              },
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
            {
              type: 'select',
              label: '软件',
              key: 'info.args.experimentid',
              value: '',
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
            {
              type: 'timeRange',
              label: ['开始时间', '结束时间', '时间范围'],
              value: '',
              data: [],
              change: function (value) {
              },
              format: function (value) {
                if (value.length === 2) {
                  const start = new Date(value[0]).getTime()
                  const end = new Date(value[1]).getTime()
                  const query = {
                    '$and': [
                      {"createTime": {'$gte': start}},
                      {"createTime": {'$lte': end}},
                    ]
                  }
                  return query
                }
              },
            },
          ]
        }
      },
      userInfo: {},
      // 弹窗-账号标记
      accountMark: {
        dialog: false
      },
      companyIpList: {
        dialog: false,
        lists: {
          list: [],
          ipList: [],
          loading: false,
        },
      }
    }
  },
  mounted() {
    this.ListMethods().initFilter(1);
    this.companyIpMethods().getList();
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 显示范围修改
        showRangeChange(v) {
          let listOrigin = $this.lists.listOrigin;
          let listShow = []
          if (v === "all") {
            listShow = listOrigin
          } else {
            listOrigin.forEach(li => {
              if (v === "normal" && li["unusual"] === false) { // 如果只显示正常
                listShow.push(li)
              }
              if (v === "unusual" && li["unusual"] === true) { // 如果只显示异常
                listShow.push(li)
              }
            })
          }
          $this.$set($this.lists, "list", listShow)
        },
        // 点击了ip地址查看按钮
        clickIpView(row, index) {
          window.open("https://ip.900cha.com/" + row.ip + ".html")
        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let queryBase = {
            "$or": [
              {"info.url": "/consumer/ums/loginExe"},
              {"info.url": "/consumer/ums/programAddScore"},
              {"info.url": "/consumer/ums/programAddScore211104"}
            ]
          }
          if (query.hasOwnProperty("$and")) {// 兼容选择日期选项
            query["$and"].push(queryBase)
            query["$and"][2] = {}
            query["$and"][2]["$or"] = [
              {"info.args.schoolId": "*****************"}, {"info.args.schoolname": "测试学校"}
            ]
          } else {
            query["$and"] = []
            query["$and"].push(queryBase)
            query["$and"][1] = {}
            query["$and"][1]["$or"] = [
              {"info.args.schoolId": "*****************"}, {"info.args.schoolname": "测试学校"}
            ]
          }
          let lists = [];
          [lists] = await MessageLogModel.getMessageLogPageList("emp", page - 1, size, "createTime,asc", query);
          // 遍历列表，整理信息
          let listLast = [];
          let userInfo = null;
          // todo 通过account和学校id获取信息，现在是先遍历一遍，传分时的userid获取
          for (let i = 0; i < lists.length; i++) {
            let li = lists[i]["info"];
            // 获取用户信息
            if (li["args"][0].hasOwnProperty("userId")) {
              userInfo = await UserModel.getOne(li["args"][0]["userId"])
              let info = {
                "account": li["args"][0]["account"],
                "name": userInfo["name"],
                "createTime": userInfo["createTime"],
                "schoolName": $this.lists.searchFilter["filter"][0]["dataObject"][userInfo["schoolId"]],
              }
              $this.$set($this, "userInfo", info)
              break;
            }
          }
          for (let i = 0; i < lists.length; i++) {
            let li = lists[i]["info"];
            li["commite"] = li["commite"].replace("执行完成（", "").replace("）", "")
            // 计算分数和登录时间
            //console.log(li)
            if (li["method"] === "程序登录") { //登录
              let experimentId = ""
              if (li["args"].length === 1) { // 兼容老日志格式
                experimentId = li["args"][0]["experimentid"]
              }
              if (li["args"].length === 2) {
                experimentId = li["args"][1]["experimentid"]
              }
              li["experimentName"] = $this.lists.searchFilter["filter"][1]["dataObject"][experimentId]
              // 标记ip
              li["ipInfo"] = await ToolsModel.getIpInfoOnline2(li["ip"])
              let ip = li["ip"];
              // 根据ip判断是否是异常登录
              li["unusual"] = true
              if ($this.companyIpList.lists.ipList.indexOf(ip) > -1 || ip.indexOf('192.168') > -1) {
                li["unusual"] = false
              }
              if (i + 1 < lists.length) {
                let liNext = lists[i + 1]["info"]
                li["endTime"] = "-"
                li["startTime"] = li["date"]
                // 下一个记录是传分，且是同一个软件
                if (liNext["args"].length === 2 && liNext["args"][1]["experimentid"] === experimentId && liNext["method"].indexOf("提交分数") > -1) {
                  // 获取分数
                  li["score"] = liNext["args"][1]["score"]
                  // 获取耗时
                  let startTime = new Date(li["date"]).getTime()
                  let endTime = new Date(liNext["date"]).getTime()
                  li["endTime"] = date_format(endTime, "yyyy-MM-dd HH:mm:ss")
                  li["useTime"] = Math.ceil((endTime - startTime) / 1000 / 60)
                  listLast.push(li)
                  i++
                } else {
                  listLast.push(li)
                }
              } else {
                listLast.push(li)
              }
            }
          }
          //console.log(listLast)
          let listLast1 = listLast.reverse()
          $this.$set($this.lists, "listOrigin", listLast1)
          $this.ListMethods().showRangeChange($this.lists.showRange)
          $this.lists.loading = false;
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter(type) {
          // if (type === 0) {
          //   // 获取学校列表
          //   let schoolList = await SchoolModel.getList(0, 0, {})
          //   let generateListFilter0 = CommonModel.generateListFilterOptions('name', 'id', schoolList, true)
          //   $this.$set($this.lists.searchFilter.filter[0], "data", generateListFilter0[0])
          //   $this.$set($this.lists.searchFilter.filter[0], "dataObject", generateListFilter0[1])
          //   $this.$set($this.lists.searchFilter.filter[0], "dataOrigin", schoolList)
          // }
          if (type === 1) {
            // 获取软件列表
            let [experimentList] = await ExperimentModel.getList(1, 10000, {})
            let generateListFilter3 = CommonModel.generateListFilterOptions('name', 'id', experimentList, true)
            $this.$set($this.lists.searchFilter.filter[1], "data", generateListFilter3[0])
            $this.$set($this.lists.searchFilter.filter[1], "dataObject", generateListFilter3[1])
            $this.$set($this.lists.searchFilter.filter[1], "dataOrigin", experimentList)
          }
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          // 必须先选择员工账号
          if ($this.lists.searchFilter["filter"][0].value) {
            $this.lists.query = query
            this.getList(0, $this.lists.pages.size, $this.lists.query)
          } else {
            msg_err("请先选择人员！");
          }
        },
      }
    },
    // 公司ipMethods
    companyIpMethods() {
      let $this = this;
      return {
        async open() {
          $this.companyIpList.dialog = true

        },
        // 获取已标记列表
        async getList() {
          $this.companyIpList.lists.loading = true;
          $this.$set($this.companyIpList.lists, "ipList", []);
          let query = {
            type: "companyExternalIP"
          };
          let list = await GeneralInfoModel.getList(query)
          list.forEach(li => {
            $this.companyIpList.lists.ipList.push(li.info.ip) // 加入ip列表
          })
          $this.companyIpList.lists.list = list
          $this.companyIpList.lists.loading = false
        },
        // 点击删除ip按钮
        async clickDeleteBtn(row, index) {
          if (await msg_confirm("确认是否删除该IP？")) {
            await GeneralInfoModel.deleteOne(row.generalInfoId)
            await $this.companyIpMethods().getList()
            msg_success("删除此IP成功！")
          }
        },
        // 点击加入标记按钮
        async clickMarkBtn() {
          let ip = await msg_input("输入要标记的公司IP地址", "", "")
          if (!ip) {
            msg_err("标记失败，请输入IP!");
            return
          }
          // 判断该账号是否已经被标记过
          let searchResult = await GeneralInfoModel.getList({
            type: "companyExternalIP",
            "info.ip": ip,
          })
          if (searchResult.length > 0) {
            msg_err("该IP已标记过!")
            return
          }


          let saveResult = await GeneralInfoModel.addOrEdit({
            type: "companyExternalIP",// 信息类型-公司外网IP
            info: {
              ip: ip
            }
          })
          if (saveResult) {
            msg_success("标记该用户成功！")
            await $this.companyIpMethods().getList()
          }
        }
      }
    },
    // 导出excel
    async exportExcel() {
      let $this = this;
      // 生成查询参数
      let list = this.lists.list;
      if (this.lists.list.length > 0) {
        // map reduce生成arr
        function formatJson(filterVal, jsonData) {
          return jsonData.map(v => filterVal.map(j => {
            let value = '';
            switch (j) {
              case "unusual":
                value = v["unusual"] ? "是" : "否"
                break;
              case "account":
                value = $this.userInfo.account
                break;
              case "name":
                value = $this.userInfo.name
                break;
              case "startTime":
                value = date_format(v["startTime"], "yyyy-MM-dd HH:mm:ss")
                break;
              case "endTime":
                value = v["endTime"] !== '-' ? date_format(v["endTime"], "yyyy-MM-dd HH:mm:ss") : "-"
                break;
              case "ip":
                value = v["ip"]
                break;
              default:
                value = v[j]
            }
            return value
          }))
        }

        const header = ['是否异常', '账号', '姓名', '软件名称', "开始时间", "结束时间", 'IP', "IP归属地"];
        const filter = ["unusual", "account", "name", "experimentName", 'startTime', 'endTime', 'ip', 'ipInfo'];
        // 导出excel
        excel_export_from_json(list, header, filter, formatJson, this.userInfo.name + "账号软件日志-" + date_format(new Date(), "yyyy-MM-dd HH:mm:ss"))
      } else {
        msg_err('要导出的列表为空')
      }
    },
  }
}
</script>

<style scoped lang="scss">
.unusual {
  background-color: red;
  color: white;
}

.userInfo {
  width: 300px;
  margin: 0 auto;

  div {
    margin-bottom: 15px;
    color: #666;
  }

  span {
    display: inline-block;
    width: 130px;
    text-align: right;
    margin-right: 10px;
    color: #333;
    font-weight: bold;
  }
}
</style>
