<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success" v-permission="['administrator']" @click="exportExcel"
          >导出日志
          </el-button>
        </div>
      </div>
    </div>

    <!--列表-->
    <div style="margin-bottom: 10px;">
      记录数量：{{ lists.list.length }}
    </div>
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;"
              >
              <el-table-column label="用户账号" align="center" min-width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.account }}</span>
              </template>
            </el-table-column>
            <el-table-column label="用户姓名" align="center" min-width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="学校" align="center" min-width="150">
              <template slot-scope="scope">
                <span>{{ scope.row.schoolName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="行政班级" align="center" min-width="150">
              <template slot-scope="scope">
                <span>{{ scope.row.administrationClazzName || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="教学班级" align="center" min-width="150">
              <template slot-scope="scope">
                <span>{{ scope.row.clazzName || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="登录类型" align="center" min-width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.type === 1 ? '平台' : '实验' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="登录IP" align="center" min-width="120">
              <template slot-scope="scope">
                <span>{{ scope.row.ip }}</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" align="center" min-width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.status === 0 ? '成功' : '失败' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="消息" align="center" min-width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.msg }}</span>
              </template>
            </el-table-column>
            <el-table-column label="登录时间" align="center" min-width="160">
              <template slot-scope="scope">
                <span>{{ date_format(scope.row.createTime, "yyyy-MM-dd HH:mm:ss") }}</span>
              </template>
            </el-table-column>
            <el-table-column label="实验名称" align="center" min-width="150">
              <template slot-scope="scope">
                <span>{{ scope.row.experimentName || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="实验分数" align="center" min-width="100">
              <template slot-scope="scope">
                <span v-if="scope.row.scoreTime">{{ scope.row.score }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="提交时间" align="center" min-width="160">
              <template slot-scope="scope">
                <span v-if="scope.row.scoreTime">{{ date_format(scope.row.scoreTime, "yyyy-MM-dd HH:mm:ss") }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="实验用时" align="center" min-width="100">
              <template slot-scope="scope">
                <span v-if="scope.row.scoreTime">{{ formatDuration(scope.row.usedTime) }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
    </el-table>
  </div>
</template>

<script>
import {MessageLogModel} from "@/model/microService/MessageLogModel";
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, getQuery} from "@/utils/common";
import enums from "@/views/exp/school/studentClazz";
import {SchoolModel} from "@/model/exp/SchoolModel";
import {AdministrationClazzModel} from "@/model/exp/AdministrationClazzModel";
import {msg_confirm, msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {CommonModel} from "@/model/CommonModel";
import {ExperimentModel} from "@/model/exp/ExperimentModel";
import {CommonInfoModel} from "@/model/erp/CommonInfoModel";
import {excel_export_from_json} from "@/utils/excel";
import {getLoginLogList} from "@/api/exp/ExpGoApi";

export default {
  name: "expLog",
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles
    })
  },
  filters: {dateFormat},
  data() {
    return {
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      // 枚举
      enums: enums,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        sort: "",
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '学号',
              key: 'account',
              value: ''
            },
          ],
          filter: [
            {
              type: 'select',
              label: '学校',
              key: 'schoolId',
              value: '',
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
            {
              type: 'select',
              label: '实验',
              key: 'experimentId',
              value: '',
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
                
              },
              format: (v) => {
                return {
                  experimentId:[v]
                }
              }
            },
            {
              type: 'select',
              label: '类型',
              key: 'type',
              value: '',
              data: [
                {
                  label: "全部",
                  value: ""
                },
                {
                  label: "平台登录",
                  value: 1
                },
                {
                  label: "程序登录",
                  value: 2
                },
              ],
              dataObject: {
                "": "全部",
                1: "平台登录",
                2: "程序登录",
              },
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {

              },
            },
            {
              type: 'timeHourRange',
              label: ['开始时间', '结束时间', '日志时间'],
              value: [new Date().setHours(0, 0, 0, 0), new Date().setHours(23, 59, 59, 999)].map(time => new Date(time)),
              data: [],
              change: function (value) {
              },
              format: function (value) {
                if (value.length === 2) {
                  return ({
                    startTime: new Date(value[0].getTime()+8*60*60*1000),
                    endTime: new Date(value[1].getTime()+8*60*60*1000),
                  })
                }
              },
            },
          ]
        }
      },
    }
  },
  mounted() {
    this.ListMethods().initFilter(0)
    this.ListMethods().initFilter(1)
  },
  methods: {
    formatDuration(seconds) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    },
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(query) {
          $this.lists.loading = true;
            let [data,] = await getLoginLogList(query)
            if (!data.data) {
              data.data = []
            }
            $this.$set($this.lists, "list", data.data)
            $this.lists.loading = false;
        },
        // 初始化筛选列表
        async initFilter(type) {
          if (type === 0) {
            // 获取学校列表
            let schoolList = await SchoolModel.getList(0, 0, {})
            let generateListFilter0 = CommonModel.generateListFilterOptions('name', 'id', schoolList, true)
            $this.$set($this.lists.searchFilter.filter[0], "data", generateListFilter0[0])
            $this.$set($this.lists.searchFilter.filter[0], "dataObject", generateListFilter0[1])
            $this.$set($this.lists.searchFilter.filter[0], "dataOrigin", schoolList)
          }
          if (type === 1) {
            // 获取实验列表
            let [experimentList] = await ExperimentModel.getList(1, 10000, {})
            let generateListFilter3 = CommonModel.generateListFilterOptions('name', 'id', experimentList, true)
            $this.$set($this.lists.searchFilter.filter[1], "data", generateListFilter3[0])
            $this.$set($this.lists.searchFilter.filter[1], "dataObject", generateListFilter3[1])
            $this.$set($this.lists.searchFilter.filter[1], "dataOrigin", experimentList)
          }
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList($this.lists.query)
        },
      }
    },
    // 导出excel
    async exportExcel() {
      let $this = this
      if (this.lists.list.length === 0) {
        this.$message.warning('暂无数据可导出')
        return
      }

      function formatJson(filterVal, jsonData) {
        return jsonData.map(v => filterVal.map(j => {
          
          switch (j) {
            case "type":
              return v[j] === 1 ? '平台' : '实验'
            case "status":
              return v[j] === 0 ? '成功' : '失败'
            case "createTime":
            case "scoreTime":
              return date_format(v[j], "yyyy-MM-dd HH:mm:ss")
            case "usedTime":
              return v[j] ? $this.formatDuration(v[j]) : '-'
            default:
              return v[j]
          }
        }))
      }

      const header = ['用户账号', '用户姓名', '学校', '行政班级', '教学班级', '登录类型', '登录IP', 
        '状态',"消息", '登录时间', '实验名称', '实验分数', '提交时间', '实验用时']
      const filter = ['account', 'name', 'schoolName', 'administrationClazzName', 'clazzName', 
        'type', 'ip', 'status', 'msg','createTime', 'experimentName', 'score', 'scoreTime', 'usedTime']

      excel_export_from_json(this.lists.list, header, filter, formatJson, 
        "登录日志导出_" + date_format(new Date(), "yyyyMMdd_HHmmss"))
    },
  }
}
</script>

<style scoped lang="scss">

</style>
