<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success" v-permission="['administrator']" @click="exportExcel"
          >导出日志
          </el-button>
        </div>
      </div>
    </div>

    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;"
              @sort-change="sort=>ListMethods().sortChange(sort)">
      <el-table-column label="模块" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.info.module }}</span>
        </template>
      </el-table-column>
      <el-table-column label="方法" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.info.method }}</span>
        </template>
      </el-table-column>
      <el-table-column label="账号" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.info.args[0] != null ? scope.row.info.args[0].account : "" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="学校" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.info.args[0] != null ? scope.row.info.args[0].schoolname : "" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="时间" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.info.date }}</span>
        </template>
      </el-table-column>
      <el-table-column label="ip" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.info.ip }}</span>
        </template>
      </el-table-column>
      <el-table-column label="参数" align="center" v-if="getQuery('param')">
        <template slot-scope="scope">
          <span>{{ JSON.stringify(scope.row.info.args) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="执行结果" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.info.commite }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import {MessageLogModel} from "@/model/microService/MessageLogModel";
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, getQuery} from "@/utils/common";
import enums from "@/views/exp/school/studentClazz";
import {SchoolModel} from "@/model/exp/SchoolModel";
import {AdministrationClazzModel} from "@/model/exp/AdministrationClazzModel";
import {msg_confirm, msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {CommonModel} from "@/model/CommonModel";
import {ExperimentModel} from "@/model/exp/ExperimentModel";
import {CommonInfoModel} from "@/model/erp/CommonInfoModel";
import {excel_export_from_json} from "@/utils/excel";

export default {
  name: "expLog",
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles
    })
  },
  filters: {dateFormat},
  data() {
    return {
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      // 枚举
      enums: enums,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        sort: "",
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '学号',
              key: 'info.args.account',
              value: ''
            },
          ],
          filter: [
            {
              type: 'select',
              label: '学校',
              key: 'info.args.schoolname',
              value: '',
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
            {
              type: 'select',
              label: '实验',
              key: 'info.args.experimentid',
              value: '',
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
            {
              type: 'select',
              label: '接口',
              key: 'info.url',
              value: '',
              data: [
                {
                  label: "全部",
                  value: ""
                },
                {
                  label: "平台登录",
                  value: "/consumer/ums/login"
                },
                {
                  label: "程序登录",
                  value: "/consumer/ums/loginExe"
                },
                {
                  label: "提交分数（老）",
                  value: "/consumer/ums/programAddScore"
                },
                {
                  label: "提交分数（新）",
                  value: "/consumer/ums/programAddScore211104"
                },
                {
                  label: "提交报告",
                  value: "/consumer/studentexperiment/addExperimentReport"
                }
              ],
              dataObject: {
                "": "全部",
                "/consumer/ums/login": "平台登录",
                "/consumer/ums/loginExe": "程序登录",
                "/consumer/ums/programAddScore": "提交分数（老）",
                "/consumer/ums/programAddScore211104": "提交分数（新）",
                "/consumer/studentexperiment/addExperimentReport": "提交报告",
              },
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {

              },
            }
          ]
        }
      },
      // 导入学生
      importStudent: {
        dialog: false,
        doing: false,
      }
    }
  },
  mounted() {
    this.ListMethods().initFilter(0)
    this.ListMethods().initFilter(1)
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          [$this.lists.list, $this.lists.pages] = await MessageLogModel.getMessageLogPageList("emp", page - 1, size, "createTime,desc", query)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter(type) {
          if (type === 0) {
            // 获取学校列表
            let schoolList = await SchoolModel.getList(0, 0, {})
            let generateListFilter0 = CommonModel.generateListFilterOptions('name', 'name', schoolList, true)
            $this.$set($this.lists.searchFilter.filter[0], "data", generateListFilter0[0])
            $this.$set($this.lists.searchFilter.filter[0], "dataObject", generateListFilter0[1])
            $this.$set($this.lists.searchFilter.filter[0], "dataOrigin", schoolList)
          }
          if (type === 1) {
            // 获取实验列表
            let [experimentList] = await ExperimentModel.getList(1, 10000, {})
            let generateListFilter3 = CommonModel.generateListFilterOptions('name', 'id', experimentList, true)
            $this.$set($this.lists.searchFilter.filter[1], "data", generateListFilter3[0])
            $this.$set($this.lists.searchFilter.filter[1], "dataObject", generateListFilter3[1])
            $this.$set($this.lists.searchFilter.filter[1], "dataOrigin", experimentList)
          }
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
      }
    },
    // 导出excel
    async exportExcel() {
      // 生成查询参数
      let query = this.lists.query
      let list = [];
      let pages = {};
      let number = await msg_input("导出数量", '请输入要导出的日志数量', 100);
      if (parseInt(number) <= 0) {
        msg_err("请输入大于0的数字");
        return
      }
      [list, pages] = await MessageLogModel.getMessageLogPageList("emp", -1, number, "createTime,desc", query);
      if (list.length > 0) {
        // map reduce生成arr
        function formatJson(filterVal, jsonData) {
          return jsonData.map(v => filterVal.map(j => {
            let value = '';
            switch (j) {
              case "module":
                value = v["info"]["module"];
                break;
              case "method":
                value = v["info"]["method"];
                break;
              case "account":
                value = v["info"].args[0] != null ? v["info"].args[0].account : "";
                break;
              case "school":
                value = v["info"].args[0] != null ? v["info"].args[0].schoolname : "";
                break;
              case "date":
                value = v["info"]["date"];
                break;
              case "ip":
                value = v["info"]["ip"];
                break;
              case "args":
                value = '';
                if(getQuery('param')){
                  value = JSON.stringify(v["info"]["args"]);
                }
                break;
              case "commite":
                value = v["info"]["commite"];
                break;
              default:
                value = v[j]
            }
            return value
          }))
        }

        const header = ['模块', '方法', '账号', '学校', "时间", "ip", '执行结果'];
        const filter = ["module", "method", "account", "school", 'date', 'ip', 'commite'];
        // 导出excel
        excel_export_from_json(list, header, filter, formatJson, "日志-" + date_format(new Date(), "yyyy-MM-dd HH:mm:ss"))
      } else {
        msg_err('要导出的列表为空')
      }
    },
  }
}
</script>

<style scoped lang="scss">

</style>
