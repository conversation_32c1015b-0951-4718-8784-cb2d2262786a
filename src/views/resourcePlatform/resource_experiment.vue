<template>
  <div class="app-container">
    <el-tabs>
      <el-tab-pane label="虚拟仿真课程">
        <!--筛选-->
        <div class="header-container clearfix" style="margin-bottom: 15px">
          <div class="filter-list">
            <div class="flex flex-start flex-wrap" style="margin-top: 10px">
              <!--资源名称-自动搜索-->
              <div class="search-item">
                <span class="title">{{ filterList.name.label }}：</span>
                <el-input
                  onKeypress="javascript:if(event.keyCode == 32 || event.keyCode == 43)event.returnValue = false;"
                  v-model="filterList.name.value"
                  :style="filterList.name.width?`width:${filterList.name.width}`:'width:auto;'"

                  :placeholder="filterList.name.placeholder">
                </el-input>
                <!--
                :fetch-suggestions="(query,callback)=>filterList.taskName.searchFunction(query,callback)"
                -->
              </div>
            </div>
            <!--  搜索按钮  -->
            <div class="search-bottom flex flex-between">
              <div class="flex flex-start" style="color: #666;font-size: 15px">
                <div class="flex flex-start">
                  <span>课程总数：{{ lists.pages.totalElements }}</span>
                </div>
              </div>

              <div class="flex flex-start">
                <el-button class="button" icon="el-icon-search"
                           @click="experimentMethods().clickSearchBtn()"
                           type="success">查询
                </el-button>
                <el-button class="button" plain icon="el-icon-delete"
                           @click="experimentMethods().clickCleanBtn()"
                           type="default">重置
                </el-button>
                <el-button class="button" plain icon="el-icon-refresh"
                           @click="experimentMethods().clickRefreshBtn()"
                           type="default">刷新页面
                </el-button>
              </div>
              <div class="tool-btn">
                <el-button type="success" @click="experimentMethods().clickAddBtn()"
                >新增课程
                </el-button>
                <el-button type="success" @click="experimentMethods().clickImportBtn()"
                >导入课程
                </el-button>
              </div>
            </div>

            <div class="tip-box">
              <div>为节省服务器带宽，已关闭视频自动加载，点击预览按钮可以观看。</div>
            </div>
          </div>
        </div>
        <resource-experiment-table ref="experimentManageList"
                                   @onClickExperimentMoveUpBtn="v=>experimentMethods().clickExperimentMoveUpBtn(v)"
                                   @onClickExperimentMoveDownBtn="v=>experimentMethods().clickExperimentMoveDownBtn(v)"
                                   @onClickExperimentDeleteBtn="v=>experimentMethods().clickExperimentDeleteBtn(v)"
                                   @onImgUploadSuccess="v=>experimentMethods().onImgUploadSuccess(v)"
                                   @onImgUploadSuccessV2="v=>experimentMethods().onImgUploadSuccessV2(v)"
                                   @onImgDelete="v=>experimentMethods().onImgDelete(v)"
                                   @onImgDeleteV2="v=>experimentMethods().onImgDeleteV2(v)"
                                   @onClickImageAddBtn="v=>experimentMethods().clickAddImageBtn(v)"
                                   :list="lists.list" :tags="tags"></resource-experiment-table>

        <!--列表分页-->
        <div class="pagination-container">
          <el-pagination background @current-change="(number)=>experimentMethods().pageChange(number)"
                         :current-page.sync="lists.pages.number"
                         :page-size.sync="lists.pages.size"
                         :page-sizes="[10, 20, 50, 100,200]"
                         layout="total,prev, pager, next,jumper,sizes"
                         :total="lists.pages.totalElements"
                         @size-change="(size)=>experimentMethods().pageLimitChange(size)"
                         :page-count="lists.pages.totalPages">
          </el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane label="设置">
       <div class="flex flex-start">
         <el-card class="box-card" style="width: 550px;margin-bottom: 30px;margin-right: 30px;">
           <div slot="header" class="clearfix">
             <span>信息设置</span>
           </div>
           <div class="clearfix" style="margin-bottom: 10px">

           </div>
           <div style="width:500px">
             <el-form label-width="120px">
               <el-form-item label="页面顶部标题">
                 <el-input v-model="resource_experiment.headerInfo.title"></el-input>
               </el-form-item>
               <el-form-item label="页面顶部介绍">
                 <el-input v-model="resource_experiment.headerInfo.des" type="textarea"></el-input>
               </el-form-item>
               <el-form-item label="视频密码：" prop="companyName">
                 <el-input v-model="resource_experiment_videoPassword_240612" placeholder="请输入视频播放密码">
                 </el-input>
               </el-form-item>
             </el-form>
           </div>
         </el-card>
         <el-card class="box-card" style="width: 500px;margin-bottom: 30px">
           <div slot="header" class="clearfix">
             <span>课程标签设置</span>
           </div>
           <div>
             <div class="block">
               <el-tree
                 :data="tags"
                 node-key="id"
                 default-expand-all
                 :expand-on-click-node="false">
                <span class="custom-tree-node" slot-scope="{ node, data }">
                  <span class="node_label" style="margin-right: 10px">
                    <i class="name">{{ data.label }}</i>
                    <!--                    <i class="id">{{ data.id }}</i>-->
                  </span>
                  <span>
                  <el-button v-if="node.level===1"
                             type="text"
                             size="mini"
                             @click="() => TagsMethods().append(node,data)">
                    新增
                  </el-button>
                  <el-button
                    type="text"
                    size="mini"
                    @click="() => TagsMethods().rename(node, data)">
                    修改名称
                  </el-button>
                  <el-button v-if="node.level!==1"
                             type="text"
                             size="mini"
                             @click="() => TagsMethods().remove(node, data)">
                    删除
                  </el-button>
                     <el-button type="text" size="mini" v-if="node.level!==1"
                                @click="TagsMethods().clickUpBtn(node,data)">上移</el-button>
                    <el-button type="text" size="mini" v-if="node.level!==1"
                         @click="TagsMethods().clickDownBtn(node,data)">下移
                  </el-button>
                  </span>
                </span>
               </el-tree>
             </div>
           </div>
         </el-card>
       </div>
        <el-card class="box-card" style="width: 100%;margin-bottom: 30px">
          <div slot="header" class="clearfix">
            <span>资源系列-顶部轮播</span>
          </div>
          <div class="card-container">
            <div class="btn-box clearfix" style="margin-bottom: 10px">
              <el-button size="small fr" type="primary" @click="FocusMethods().clickAddOne('video')">新增视频轮播
              </el-button>
              <el-button size="small fr" type="primary" @click="FocusMethods().clickAddOne('image')"
                         style="margin-right: 10px">新增图片轮播
              </el-button>
            </div>
            <el-table :data="focus.list" border fit>
              <el-table-column label="图片或视频" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.row.type==='image'">
                    <erp-uploader-one-pic style="margin-right: 30px" :img-in="scope.row.image"
                                          :uploader-id="'image'"
                                          uploader-title="图片" :uploader-size="[200,100]" :pixel-limit="[1920,500]"
                                          :size-limit="2000"
                                          @uploadSuccess="data=>fileUpload(data,focus.list[scope.$index],'focus')"
                                          @afterDelete="data=>fileDelete(data,focus.list[scope.$index],'focus')"></erp-uploader-one-pic>
                  </div>
                  <div v-if="scope.row.type==='video'">
                    <erp-uploader-one-video style="margin-right: 30px" :video-in="scope.row.video"
                                            :uploader-id="'video'"
                                            upload-target="zy0"
                                            :uploader-size="[200,100]" :pixel-limit="[1920,500]"
                                            :size-limit="20480"
                                            @afterDelete="data=>fileDelete(data,focus.list[scope.$index])"
                                            @uploadSuccess="data=>fileUpload(data,focus.list[scope.$index])"></erp-uploader-one-video>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="轮播类型" align="center" width="135">
                <template slot-scope="scope">
                  {{ {'video': "视频", "image": "图片"}[scope.row.type] }}
                </template>
              </el-table-column>
              <el-table-column label="是否显示" align="center" width="135">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.show">
                    <el-option label="显示" :value="true"></el-option>
                    <el-option label="隐藏" :value="false"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="跳转链接" align="center">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.link"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="120">
                <template slot-scope="scope">
                  <el-button size="mini" type="default" @click="FocusMethods().clickDeleteOne(scope.$index)">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
    <!--按钮组-->
    <div class="bottom-button-container flex flex-center">
      <el-button type="primary" @click="clickSaveBtn()">保 存</el-button>
    </div>
  </div>
</template>

<script>
import {BaseUploadModel} from "@/model/BaseUploadModel";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import {msg_confirm, msg_confirm_choose, msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {ConfigModel} from "@/model/erp/ConfigModel";
import {CONFIG_NAME_RESOURCE_PLATFORM} from "@/model/ConfigModel";
import {VEAModel} from "@/model/VEAModel";
import $ from "jquery"
import elDragDialog from "@/directive/el-drag-dialog";
import resourceExperimentTable from "@/views/resourcePlatform/components/resourceExperimentTable.vue";
import {ResourcePlatformModel} from "@/model/erp/ResourcePlatformModel";
import {find_obj_from_arr_by_id} from "@/utils/common";
import {OfficialWebExperimentModel} from "@/model/erp/OfficialWebExperimentModel";
import erpUploaderOneVideo from "@/views/components/erpUploaderOneVideo.vue";
import {WebAnalyticsModel} from "@/model/erp/WebAnalyticsModel";

export default {
  name: "resourceExperiment",
  directives: {
    elDragDialog
  },
  components: {erpUploaderOneVideo, erpUploaderOnePic, resourceExperimentTable},
  data() {
    return {
      uploadRequest: BaseUploadModel.uploadRequest,
      resource_experiment: {
        headerInfo: {
          title: "",
          des: ""
        },
      },
      resource_experiment_videoPassword_240612:"",
      // 顶部轮播
      focus: {
        list: [],// 如果默认没有这个，就不会刷新
        cardShow: true,
        // 表单检测
        formRules: {},
      },
      // 虚仿课程
      lists: {
        list: [],
        loading: false,
        query: {},
        sort: "",
        pages: {
          size: 10
        },
      },
      filterList: {
        // 名称
        name: {
          label: '课程名称',
          placeholder: "",
          key: 'info.name',
          type: "search",
          width: "270px",
          value: '',
          data: [],
          dataObject: [],
          dataOrigin: [],// 存储数据库返回的默认列表
          searchFunction: async function (query) {
            // todo 任务名称搜索
          },
          async change(v) {
            // todo 任务名称变动
          },
          format: function (value) {
            return {
              '$regex': `.*${value}.*`
            }
          }
        },
      },
      // 标签设置
      tags: [],

      // 错误跳转标记,用于跳转到最开始错误
      errAJumped: false,
    }
  },
  mounted() {
    // 读取配置信息
    this.getConfig()
    // 底部按钮组大小监听
    VEAModel.bottomButtonContainerWidthChange()
  },
  methods: {
    // 读取配置
    async getConfig() {
      let data = await ConfigModel.getEdit(CONFIG_NAME_RESOURCE_PLATFORM, "resource_experiment")
      let resource_experiment = JSON.parse(data)
      this.$set(this, "resource_experiment", resource_experiment)
      // 设置各项配置
      this.$set(this, "tags", resource_experiment.tags)
      this.$set(this, "focus", resource_experiment.focus)
      this.resource_experiment_videoPassword_240612=await ConfigModel.getEdit(CONFIG_NAME_RESOURCE_PLATFORM, "resource_experiment_videoPassword_240612")
      // 获取课程列表
      this.experimentMethods().getList(0, this.lists.pages.size, {})
    },
    // 通用-文件上传成功
    fileUpload(params, target, formRef) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 通用-文件删除
    fileDelete(params, target, formRef) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 点击保存按钮
    async clickSaveBtn(floors) {
      if (!floors) {
        floors = ['tags', 'focus']
      }
      // 每个项目的合法性检测
      this.errAJumped = false
      if (true) {
        // 公司信息检测
        if (floors.indexOf('tags') > -1 && false) {
          this.$refs["tags"].validate(validate => {
            if (validate) {

            } else {
              if (!this.errAJumped) {
                this.tags.cardShow === false ? this.tags.cardShow = true : ""
                // location.hash = "baseInfo";
                this.errAJumped = true
              }
            }
          })
        }

        if (this.errAJumped) { // 表单验证有误
          setTimeout(() => {
            // 跳转到第一个错误处
            let firstErrEl = $("body .el-form-item__error")
            let top = firstErrEl.offset().top - 200
            // 跳转到指定位置
            document.body.scrollTop = top;
            document.documentElement.scrollTop = top;
          }, 600)
          return
        }
      }
      //
      let saveButtonChoose = await msg_confirm_choose("是否确认更改？", "确认保存", "我再想想", '确认提交')

      // 保存实验列表里的每个实验
      this.lists.list.forEach(li => {
        ResourcePlatformModel.addOrEdit(li)
      })

      if (saveButtonChoose === "right") {
        let resource_experiment = this.resource_experiment
        // 保存前调用
        if (floors.indexOf('tags') > -1) {
          resource_experiment.tags = JSON.parse(JSON.stringify(this.tags))
          // 删除不必要元素
          delete resource_experiment.tags.formRules
        }
        if (floors.indexOf('focus') > -1) {
          resource_experiment.focus = JSON.parse(JSON.stringify(this.focus))
          // 删除不必要元素
          delete resource_experiment.focus.formRules
        }
        // 保存接口
        if (await ConfigModel.editEdit(CONFIG_NAME_RESOURCE_PLATFORM, "resource_experiment", resource_experiment)) {
          await ConfigModel.editEdit(CONFIG_NAME_RESOURCE_PLATFORM, "resource_experiment_videoPassword_240612", this.resource_experiment_videoPassword_240612)
          msg_success("保存成功")
        }
      }
    },
    // 轮播相关
    FocusMethods() {
      let $this = this;
      return {
        // 点击新增一个
        clickAddOne(type) {
          $this.focus.list.push({
            type: type
          })
        },
        // 点击删除一个
        async clickDeleteOne(index) {
          if (await msg_confirm("确定要删除吗？")) {
            $this.focus.list.splice(index, 1)
          }
        }
      }
    },
    // 标签方法组
    TagsMethods() {
      let $this = this;
      return {
        append(node, data) {
          let id = new Date().getTime() + "";
          const newChild = {id: id, label: '新标签'};
          if (!data.children) {
            $this.$set(data, 'children', []);
          }
          data.children.push(newChild);
        },
        async rename(node, data) {
          let label = await msg_input("标签名称", "请输入该标签名称", data.label);
          if (!label) {
            return
          }
          $this.$set(data, 'label', label);
        },
        remove(node, data) {
          const parent = node.parent;
          const children = parent.data.children || parent.data;
          const index = children.findIndex(d => d.id === data.id);
          children.splice(index, 1);
        },
        // 点击上移按钮
        async clickUpBtn(node,data) {
          const parent = node.parent;
          const children = parent.data.children || parent.data;
          const index = children.findIndex(d => d.id === data.id);
          if(index>0){
            let one = children[index - 1];
            let dataStr=JSON.parse(JSON.stringify(data))
            $this.$set(data, 'id', one.id);
            $this.$set(data, 'label', one.label);
            $this.$set(one, 'id', dataStr.id);
            $this.$set(one, 'label', dataStr.label);
          }else{
           msg_err("不能再上移了")
          }
        },
        // 点击下移按钮
        async clickDownBtn(node,data) {
          const parent = node.parent;
          const children = parent.data.children || parent.data;
          const index = children.findIndex(d => d.id === data.id);

          if(index<children.length-1){
            let one = children[index + 1];
            let dataStr=JSON.parse(JSON.stringify(data))
            $this.$set(data, 'id', one.id);
            $this.$set(data, 'label', one.label);
            $this.$set(one, 'id', dataStr.id);
            $this.$set(one, 'label', dataStr.label);
          }else{
            msg_err("不能再下移了")
          }
        },
      }
    },
    // 实验相关方法
    experimentMethods() {
      let $this = this;
      return {
        // 点击搜索按钮
        clickSearchBtn() {
          let query = {};
          // 遍历searchFilter
          Object.keys($this.filterList).forEach(key => {
            let li = $this.filterList[key];
            if (li.hasOwnProperty("key")) {
              if (li.value === 0 || li.value) {
                if (li.format) {
                  if (li.type === "filter") {
                    let formatResult = li.format(li.value)
                    if (formatResult) {
                      if (formatResult.hasOwnProperty("$and") && query.hasOwnProperty("$and")) {// 合并and
                        query["$and"] = formatResult["$and"].concat(query["$and"])
                      } else {
                        query = Object.assign(query, formatResult);
                      }
                    }
                  }
                  if (li.type === "search") {
                    query[li.key] = li.format(li.value);
                  }
                } else {
                  query[li.key] = li.value;
                }
              }
            }

          });
          $this.$set($this.lists, "query", query)
          this.getList(0, $this.lists.pages.size, query)
        },
        // 点击刷新页面按钮
        clickRefreshBtn() {
          window.location.reload()
        },
        // 点击清空搜索按钮
        clickCleanBtn() {
          let query = {};
          // 遍历searchFilter
          Object.keys($this.filterList).forEach(key => {
            let li = $this.filterList[key];
            if (li.hasOwnProperty("key")) {

              if (li.value === 0 || li.value) {
                if (Array.isArray(li.value)) {
                  li.value = []
                } else {
                  li.value = ""
                }
              }
            }

          });
          $this.$set($this.lists, "query", query)
          this.getList(0, $this.lists.pages.size, query)
        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let sort = "sortOrder,desc&sort=createTime,desc";
          [$this.lists.list, $this.lists.pages] = await ResourcePlatformModel.getPageList("experiment", page - 1, size, sort, query,true)
          $this.lists.loading = false
          // 批量获取-浏览量
          let countQuery=[]
          $this.lists.list.forEach(li=>{
            countQuery.push(
              {
                "webSiteId":"006",
                "eventName":"resourceExperimentClickView",
                "queryName1":"info.info.id",
                "queryValue1":li.resourcePlatformId
              }
            )
          })
          let countResult=await WebAnalyticsModel.oneWebOneEventNumberByInfo(countQuery)
          countResult.forEach((li,index)=>{
            $this.$set($this.lists.list[index].info,"viewNumber",li)
          })
          countQuery=[]
          $this.lists.list.forEach(li=>{
            countQuery.push(
              {
                "webSiteId":"006",
                "eventName":"resourceExperimentClickFav",
                "queryName1":"info.info.id",
                "queryValue1":li.resourcePlatformId
              }
            )
          })
          countResult=await WebAnalyticsModel.oneWebOneEventNumberByInfo(countQuery)
          countResult.forEach((li,index)=>{
            $this.$set($this.lists.list[index].info,"favNumber",li)
          })
          countQuery=[]
          $this.lists.list.forEach(li=>{
            countQuery.push(
              {
                "webSiteId":"006",
                "eventName":"resourceExperimentVideoPlay",
                "queryName1":"info.info.id",
                "queryValue1":li.resourcePlatformId
              }
            )
          })
          countResult=await WebAnalyticsModel.oneWebOneEventNumberByInfo(countQuery)
          countResult.forEach((li,index)=>{
            $this.$set($this.lists.list[index].info,"videoPlayNumber",li)
          })
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter(type) {
        },
        // 检测-实验填写合法性检测
        validateExperiment(experiment, experimentIndex, callback, categoryIndex, seriesIndex) {
          let rules = [
            {key: 'name', name: "产品名称"},
            {key: 'subName', name: "副标题"},
            {key: 'description', name: "产品简介"},
            {key: 'experimentDescription', name: "实验简介"},
            {key: 'product_bg', name: "产品封面"},
            {key: 'product_icon', name: "产品Icon"},
            // {key: 'product_iconLeft', name: "左上角Icon"},
            // {key: 'product_info_bg', name: "产品详情-头图"},
            // {key: 'product_video', name: "产品详情-视频"},
            {key: 'product_info_img_1', name: "产品详情-图片介绍第1张"},
            {key: 'product_info_img_2', name: "产品详情-图片介绍第2张"},
            {key: 'product_info_img_3', name: "产品详情-图片介绍第3张"},
            {key: 'product_info_img_4', name: "产品详情-图片介绍第4张"},
          ]
          rules.forEach((rule, index) => {
            if (!experiment[rule.key]) {
              callback(new Error(`第${experimentIndex + 1}行未设置${rule.name}`))
            }
          })
        },
        // 工厂-新建实验
        async factoryCreateNewExperiment() {
          let experiment = await ResourcePlatformModel.addOrEdit({
            type: "experiment",
            info: {
              tags: {},
              videos:[],
              images:[],
              show:true,
              videoPassword:false,
            }
          })
          return experiment
        },
        // 点击-实验-新建按钮
        async clickAddBtn() {
          let experiment = await this.factoryCreateNewExperiment()
          this.clickRefreshBtn()
        },
        // 点击-导入课程按钮
        async clickImportBtn() {
          let id = await msg_input("官网实验id", "请输入测试官网实验id。在测试官网-某个实验的详情弹窗，点击图标会显示id。")
          if (!id) {
            return
          }
          let experiment = await OfficialWebExperimentModel.getOneExperimentConfig(id)
          if (!experiment) {
            msg_err("未找到！")
            return
          }
          experiment.tags = {}
          experiment.show=true
          experiment.videoPassword=false
          if(experiment.product_video){
            experiment.videos=[{src:experiment.product_video}]
          }else{
            experiment.videos=[]
          }
          let images=[]
          if(experiment.product_info_img_1){
            images.push({
              src:experiment.product_info_img_1
            })
          }
          if(experiment.product_info_img_2){
            images.push({
              src:experiment.product_info_img_2
            })
          }
          if(experiment.product_info_img_3){
            images.push({
              src:experiment.product_info_img_3
            })
          }
          if(experiment.product_info_img_4){
            images.push({
              src:experiment.product_info_img_4
            })
          }
          experiment.images=images
          let entity = await ResourcePlatformModel.addOrEdit({
            type: "experiment",
            info: experiment
          })
          this.clickRefreshBtn()
        },
        // 点击-实验-删除按钮
        async clickExperimentDeleteBtn(experimentParams) {
          let id = experimentParams.id
          let experimentIndex = experimentParams.index
          if (await msg_confirm(`确认要删除该项吗？`)) {
            let entity = $this.lists.list[experimentIndex]
            entity.deleted = 1
            if (await ResourcePlatformModel.addOrEdit(entity)) {
              msg_success("删除成功！")
              this.getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
            } else {
              msg_err("删除失败！")
            }
          }
        },
        // 点击-实验-上移按钮
        async clickExperimentMoveUpBtn(experimentParams) {
          let experimentIndex = experimentParams.index
          let edit = $this.lists.list[experimentIndex]
          let $index = parseInt(find_obj_from_arr_by_id("resourcePlatformId", edit.resourcePlatformId, $this.lists.list)[0])
          let one = $this.lists.list[$index - 1];
          let oneSortOrder = one.sortOrder
          one.sortOrder = edit.sortOrder
          edit.sortOrder = oneSortOrder
          await ResourcePlatformModel.addOrEdit(edit)
          await ResourcePlatformModel.addOrEdit(one)
          this.getList($this.lists.pages.page, $this.lists.pages.size, $this.lists.query)
        },
        // 点击-实验-下移按钮
        async clickExperimentMoveDownBtn(experimentParams) {
          let experimentIndex = experimentParams.index
          let edit = $this.lists.list[experimentIndex]
          let $index = parseInt(find_obj_from_arr_by_id("resourcePlatformId", edit.resourcePlatformId, $this.lists.list)[0])
          let one = $this.lists.list[$index + 1];
          let oneSortOrder = one.sortOrder
          one.sortOrder = edit.sortOrder
          edit.sortOrder = oneSortOrder
          await ResourcePlatformModel.addOrEdit(edit)
          await ResourcePlatformModel.addOrEdit(one)
          this.getList($this.lists.pages.page, $this.lists.pages.size, $this.lists.query)
        },
        // 监听-图片上传成功
        onImgUploadSuccess(experimentParams) {
          let experimentIndex = experimentParams.index // 实验编号
          let imgSrc = experimentParams.params[1] // 成功后图片地址
          // 判断上传的什么图片
          let uploaderId = experimentParams.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          $this.$set($this.lists.list[experimentIndex]["info"], uploaderKey, imgSrc)
        },
        // 监听-图片上传成功-v2
        onImgUploadSuccessV2(experimentParams) {
          let id = experimentParams.params[0]
          let imgSrc = experimentParams.params[1] // 成功后图片地址
          let target=experimentParams.target
          $this.$set(target, id, imgSrc)
        },
        // 监听-图片删除
        onImgDelete(experimentParams) {
          let experimentIndex = experimentParams.index // 实验编号
          // 判断上传的什么图片
          let uploaderId = experimentParams.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          $this.$set($this.lists.list[experimentIndex]["info"], uploaderKey, "")
        },
        // 监听-图片删除-V2
        onImgDeleteV2(experimentParams) {
          console.log(experimentParams)
          let list=experimentParams.list
          let index=experimentParams.index
          list.splice(index,1)
        },
        // 监听-点击新增图片或视频按钮
        clickAddImageBtn(experimentParams) {
          let experimentIndex = experimentParams.index
          let type=experimentParams.type
          if (experimentParams.params === undefined) {
            if(type==='images'){
              $this.$set($this.lists.list[experimentIndex]["info"], "images", [{
                src: ""
              }])
            }
            if(type==='videos'){
              $this.$set($this.lists.list[experimentIndex]["info"], "videos", [{
                src: ""
              }])
            }
          }else{
            experimentParams.params.push({
              src: ""
            })
          }
        }
      }
    },
  }
}
</script>

<style>

</style>
<style scoped lang="scss">
.app-container {
  padding-bottom: 150px;
}

// 筛选和搜索列表
.filter-list {
  .title {
    font-size: 15px;
    color: #999;
  }

  .search-item, .filter-item {
    margin-right: 15px;

    span.title {
      font-size: 14px;
      margin-right: 10px;
    }
  }

  .select-append {
    float: left;
    margin-right: 5px;

    .select {
      input {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }

    .button {
      margin-left: -6px;
      background-color: #f5f7fa;
      color: #939b9f;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }

  .search-bottom {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}


.images-list{
  .el-upload{
    width: 50px;
    height: 50px;
  }
}
</style>
