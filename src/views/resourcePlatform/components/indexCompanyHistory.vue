<template>
  <div class="index-company-history common-center-container">
    <div class="left-line"></div>
    <div class="slogan-box flex flex-between">
      <span contenteditable="true">{{texts[0]}}</span>
      <span contenteditable="true">{{texts[1]}}</span>
      <span contenteditable="true">{{texts[2]}}</span>
    </div>
    <div class="describe-box flex flex-between">
      <img :src="images[0]" alt="" class="left edit">
      <el-button type="danger" @click="clickChangeImage(0)">换图（670X375）</el-button>
      <div class="right-box">
        <div class="text" contenteditable="true">{{texts[3]}}</div>
        <div class="clearfix">
          <div class="btn-more fr" @click="window.open('https://www.cdzyhd.com')">查看更多</div>
        </div>
      </div>
    </div>
    <div class="history-box clearfix">
      <div class="clearfix">
        <div class="li li-1 fr">
          <div class="text" contenteditable="true">{{texts[4]}}</div>
          <div class="number fr" contenteditable="true">{{texts[5]}}</div>
        </div>
      </div>
      <div class="clearfix">
        <div class="li li-2 fr">
          <div class="text" contenteditable="true">{{texts[6]}}</div>
          <div class="number fr" contenteditable="true">{{texts[7]}}</div>
        </div>
      </div>
    </div>

    <!--背景图片上传input-->
    <input
      id="imagesInput"
      type="file"
      style="display: none"
      @change="(files)=>{imagesFileChange(files)}"
    >
  </div>
</template>

<script>
import {BaseUploadModel} from "@/model/BaseUploadModel";
import {msg_err} from "@/utils/ele_component";
import $ from "jquery";

export default {
  name: "indexCompanyHistory",
  props:{
    texts:{
      type:Array,
      default:()=>{
        return []
      }
    },
    images:{
      type:Array,
      default:()=>{
        return []
      }
    }
  },
  data() {
    return {
      window:window,
      // 当前正在上传的图片id
      uploadImagesIndex: 0,
    }
  },
  mounted() {

  },
  methods: {
    // 点击更新按钮
    async clickUpdateBtn() {
      // 获取所有可编辑的文本，组成数组
      let allTextElement = $("[contenteditable=true]");
      let texts = []
      for (let i = 0; i < allTextElement.length; i++) {
        let ele = allTextElement[i]
        texts.push($(ele).text())
      }
      // 获取所有图片地址
      let allImageElement = $("img.edit");
      let images = []
      for (let i = 0; i < allImageElement.length; i++) {
        let ele = allImageElement[i]
        let url = $(ele).attr("src")
        images.push(url)
      }
      console.log(images)
      // 触发父组件事件
      this.$emit("onUpdate",{texts,images})
    },
    // 点击换图按钮
    async clickChangeImage(index) {
      const uploader = document.getElementById('imagesInput')
      uploader.click()
      this.uploadImagesIndex = index
    },
    // 背景图上传文件改变
    async imagesFileChange(files) {
      const file = files.target.files[0]
      document.getElementById('imagesInput').value = ''
      await BaseUploadModel.qiNiuUpload(file, {
        next: (result) => {
        },
        error: (errResult) => {
          console.log(errResult)
          msg_err('上传失败')
        },
        complete: (result) => {
          let domain = BaseUploadModel.getBucketDomain(file)
          let url = domain + '/' + result.key + ''
          $(`img.edit:eq(${this.uploadImagesIndex})`).attr("src",url)
        }
      })
    },
  }
}
</script>

<style scoped lang="less">
</style>
