// home-公司历史
.index-company-history {
  border-left: 1px solid #D4D4D4;
  padding: 0px 50px;
  z-index: 2;
  margin-top: 80px;
  position: relative;
  .left-line{
    width: 5px;
    height: 232px;
    background-color: #69BDFE;
    position: absolute;
    left: -2.5px;
    top: 0px;
  }
  .slogan-box {
    width: 950px;
    height: 50px;
    margin: 0 auto;
    margin-top: -50px;
    span{
      color:#4F9EEC;
      font-size: 38px;
    }
  }
  .describe-box{
    margin-top: 50px;
    width: 100%;
    img.left{
      width: 670px;
      height: 375px;
    }
    .right-box{
      .text{
        text-indent: 2em;
        width: 475px;
        color: #707070;
        font-size: 16px;
        line-height: 25px;
      }
      .btn-more{
        margin-top: 30px;
        width: 120px;
        height: 35px;
        line-height: 35px;
        text-align: center;
        color: #fff;
        font-size: 15px;
        background-color: #4F9EEC;
        border-radius: 10px;
        cursor: pointer;
      }
    }
  }
  .history-box{
    margin-top: 30px;
    .li{
      border-bottom: 2px solid #707070;
      width: 100%;
      position: relative;
      .text{
        color: #000;
      }
    }
    .li-1{
      width: 700px;
      height: 100px;
      margin-right: 150px;
      .text{
        font-size: 22px;
        position: absolute;
        left: 0px;
        bottom:20px;
      }
      .number{
        font-size: 86px;
        height: 100px;
        color: #4F9EEC;
      }
    }
    .li-2{
      width: 500px;
      height: 100px;
      margin-top: 20px;
      .text{
        font-size: 18px;
        position: absolute;
        left: 0px;
        bottom:10px;
      }
      .number{
        font-size: 86px;
        height: 50px;
        color: #D8D8D8;
      }
    }
  }
}