<template>
  <div class="table-form">
    <el-table :data="list" border fit>

      <el-table-column :label="'*课程名称\n(30字以内)'" align="center">
        <template slot-scope="scope">
          <el-form :ref="'form_name$'+scope.$index" :model="scope.row.info" :rules="formRules">
            <el-form-item prop="name">
              <span class="input_box ellipsis"
                    @click="openTextEditor('name',scope.$index,30,'课程名称(30字以内)')">{{
                  scope.row.info.name
                }}</span>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column :label="'*课程副标题\n(18个字以内)'" align="center">
        <template slot-scope="scope">
          <el-form :ref="'form_subName$'+scope.$index" :model="scope.row.info" :rules="formRules">
            <el-form-item prop="subName">
               <span class="input_box ellipsis"
                     @click="openTextEditor('subName',scope.$index,18,'课程副标题(18个字以内)')">{{
                   scope.row.info.subName
                 }}</span>
            </el-form-item>
          </el-form>

        </template>
      </el-table-column>
      <el-table-column :label="'*H5分享副标题\n(29个字以内)'" align="center">
        <template slot-scope="scope">
          <el-form :ref="'form_subName_h5$'+scope.$index" :model="scope.row.info" :rules="formRules">
            <el-form-item prop="subName_h5">
               <span class="input_box ellipsis"
                     @click="openTextEditor('subName_h5',scope.$index,29,'H5分享副标题(29个字以内)')">{{
                   scope.row.info.subName_h5
                 }}</span>
            </el-form-item>
          </el-form>

        </template>
      </el-table-column>
      <el-table-column :label="'*课程简介\n(60字以内)'" align="center">
        <template slot-scope="scope">
          <el-form :ref="'form_description$'+scope.$index" :model="scope.row.info" :rules="formRules">
            <el-form-item prop="description">
                      <span class="input_box ellipsis"
                            @click="openTextEditor('description',scope.$index,60,'课程简介(60字以内)')">{{
                          scope.row.info.description
                        }}</span>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column :label="'*课程详情-实验简介\n(800字以内)'" align="center">
        <template slot-scope="scope">
          <el-form :ref="'form_experimentDescription$'+scope.$index" :model="scope.row.info" :rules="formRules">
            <el-form-item prop="experimentDescription">
                      <span class="input_box ellipsis"
                            @click="openTextEditor('experimentDescription',scope.$index,800,'课程详情-实验简介(800字以内)')">{{
                          scope.row.info.experimentDescription
                        }}</span>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column :label="'*课程封面\n(400*250,400KB以内)'" align="center">
        <template slot-scope="scope">
          <el-form :ref="'form_product_bg$'+scope.$index" :model="scope.row.info" :rules="formRules">
            <el-form-item prop="product_bg">
              <erp-uploader-one-pic
                :img-in="scope.row.info.product_bg?scope.row.info.product_bg+'?imageView2/1/w/80/h/80/q/75':''"
                :uploader-id="'product_bg__'+scope.row.resourcePlatformId"
                :show-des="false"
                :uploader-size="[80,80]" :pixel-limit="[400,250]"
                :size-limit="400"
                @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"
                @afterDelete="v=>onImgDelete(v,scope.$index)"
              ></erp-uploader-one-pic>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column :label="'*课程Icon\n(400*400,400KB以内)'" align="center">
        <template slot-scope="scope">
          <el-form :ref="'form_product_icon$'+scope.$index" :model="scope.row.info" :rules="formRules">
            <el-form-item prop="product_icon">
              <erp-uploader-one-pic
                :img-in="scope.row.info.product_icon?scope.row.info.product_icon+'?imageView2/1/w/80/h/80/q/75':''"
                :uploader-id="'product_icon__'+scope.row.resourcePlatformId"
                :show-des="false"
                :uploader-size="[80,80]" :pixel-limit="[400,400]"
                @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"
                @afterDelete="v=>onImgDelete(v,scope.$index)"
                :size-limit="400"></erp-uploader-one-pic>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <!--      <el-table-column :label="'*课程详情-头图\n(1200x330,3MB以内)'" align="center">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-form :ref="'form_product_info_bg$'+scope.$index" :model="scope.row.info" :rules="formRules">-->
      <!--            <el-form-item prop="product_info_bg">-->
      <!--              <erp-uploader-one-pic :img-in="scope.row.info.product_info_bg?scope.row.info.product_info_bg+'?imageView2/1/w/80/h/80/q/75':''"-->
      <!--                                    :uploader-id="'product_info_bg__'+scope.row.resourcePlatformId"-->
      <!--                                    :show-des="false"-->
      <!--                                    :uploader-size="[80,80]" :pixel-limit="[1200,330]"-->
      <!--                                    @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"-->
      <!--                                    @afterDelete="v=>onImgDelete(v,scope.$index)"-->
      <!--                                    :size-limit="3076"></erp-uploader-one-pic>-->
      <!--            </el-form-item>-->
      <!--          </el-form>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <!--      <el-table-column :label="'课程详情-视频介绍\n(720p,20MB以内)'" align="center">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-form :ref="'form_product_video$'+scope.$index" :model="scope.row.info" :rules="formRules">-->
      <!--            <el-form-item prop="product_video">-->
      <!--              <erp-uploader-one-video :video-in="scope.row.info.product_video" :uploader-id="'product_video__'+scope.row.resourcePlatformId"-->
      <!--                                      upload-target="zy0"-->
      <!--                                      :show-des="false"-->
      <!--                                      :uploader-size="[100,50]" :pixel-limit="[2560,1080]"-->
      <!--                                      @uploadSuccess="v=>onVideoUploadSuccess(v,scope.$index)"-->
      <!--                                      @afterDelete="v=>onVideoDelete(v,scope.$index)"-->
      <!--                                      :size-limit="204800"></erp-uploader-one-video>-->
      <!--            </el-form-item>-->
      <!--          </el-form>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column :label="'课程详情-视频列表\n(720p,20MB以内)'" align="center">
        <template slot-scope="scope">
          <el-form :ref="'form_product_video$'+scope.$index" :model="scope.row.info" :rules="formRules">
            <el-form-item prop="product_video">
              <div class="upload-list video-upload-list" v-for="(item,index) in scope.row.info.videos">
                <erp-uploader-one-video :video-in="item.src" :uploader-id="'src'"
                                        upload-target="zy0"
                                        :show-des="false"
                                        :uploader-size="[100,50]" :pixel-limit="[2560,1080]"
                                        @uploadSuccess="v=>onImgUploadSuccessV2(v,item)"
                                        @afterDelete="v=>onImgDeleteV2(v,scope.row.info.videos,index)"
                                        :size-limit="204800"></erp-uploader-one-video>
                <el-switch
                  class="show-switch"
                  v-model="item.show"
                  active-text="显示"
                  inactive-text="">
                </el-switch>
              </div>
              <div>
                <el-button type="success" size="small" style="margin-top: 10px;"
                           @click="clickAddImageBtn(scope.row.info.videos,scope.$index,'videos')">新增视频
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column label="视频密码" align="center" width="100">
        <template slot-scope="scope">
          <el-select v-model="scope.row.info.videoPassword">
            <el-option label="需要" :value="true"></el-option>
            <el-option label="不要" :value="false"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="视频权限" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="scope.row.info.videoRoles=['admin','developer','seller']">所有人</el-button>
          <el-select v-model="scope.row.info.videoRoles" multiple="multiple">
            <el-option label="管理员" :value="'admin'"></el-option>
            <el-option label="研发" :value="'developer'"></el-option>
            <el-option label="销售" :value="'seller'"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <!--      <el-table-column :label="'课程详情-大图介绍\n(3MB以内)'" align="center">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-form :ref="'form_product_info_img$'+scope.$index" :model="scope.row.info" :rules="formRules">-->
      <!--            <el-form-item prop="product_info_img">-->
      <!--              <erp-uploader-one-pic :img-in="scope.row.info.product_info_img?scope.row.info.product_info_img+'?imageView2/1/w/50/h/50/q/75':''"-->
      <!--                                    :uploader-id="'product_info_img__'+scope.row.resourcePlatformId"-->
      <!--                                    :show-des="false"-->
      <!--                                    :uploader-size="[50,50]" :pixel-limit="[1200,330]"-->
      <!--                                    @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"-->
      <!--                                    @afterDelete="v=>onImgDelete(v,scope.$index)"-->
      <!--                                    :size-limit="3076"></erp-uploader-one-pic>-->
      <!--            </el-form-item>-->
      <!--          </el-form>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <!--      <el-table-column :label="'*课程详情-图片列表\n(3MB以内)'" align="center" width="170px">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-form :ref="'form_product_info_imgs$'+scope.$index" :model="scope.row.info" :rules="formRules">-->
      <!--            <el-form-item prop="product_info_imgs">-->
      <!--              <div class="flex flex-around flex-wrap">-->
      <!--                <erp-uploader-one-pic :img-in="scope.row.info.product_info_img_1?scope.row.info.product_info_img_1+'?imageView2/1/w/50/h/50/q/75':''"-->
      <!--                                      :uploader-id="'product_info_img_1__'+scope.row.resourcePlatformId"-->
      <!--                                      :show-des="false"-->
      <!--                                      :uploader-size="[50,50]" :pixel-limit="[1200,330]"-->
      <!--                                      @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"-->
      <!--                                      @afterDelete="v=>onImgDelete(v,scope.$index)"-->
      <!--                                      :size-limit="3076"></erp-uploader-one-pic>-->
      <!--                <erp-uploader-one-pic :img-in="scope.row.info.product_info_img_2?scope.row.info.product_info_img_2+'?imageView2/1/w/50/h/50/q/75':''"-->
      <!--                                      :uploader-id="'product_info_img_2__'+scope.row.resourcePlatformId"-->
      <!--                                      :show-des="false"-->
      <!--                                      :uploader-size="[50,50]" :pixel-limit="[1200,330]"-->
      <!--                                      @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"-->
      <!--                                      @afterDelete="v=>onImgDelete(v,scope.$index)"-->
      <!--                                      :size-limit="3076"></erp-uploader-one-pic>-->
      <!--                <erp-uploader-one-pic :img-in="scope.row.info.product_info_img_3?scope.row.info.product_info_img_3+'?imageView2/1/w/50/h/50/q/75':''"-->
      <!--                                      :uploader-id="'product_info_img_3__'+scope.row.resourcePlatformId"-->
      <!--                                      :show-des="false"-->
      <!--                                      :uploader-size="[50,50]" :pixel-limit="[1200,330]"-->
      <!--                                      @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"-->
      <!--                                      @afterDelete="v=>onImgDelete(v,scope.$index)"-->
      <!--                                      :size-limit="3076"></erp-uploader-one-pic>-->
      <!--                <erp-uploader-one-pic :img-in="scope.row.info.product_info_img_4?scope.row.info.product_info_img_4+'?imageView2/1/w/50/h/50/q/75':''"-->
      <!--                                      :uploader-id="'product_info_img_4__'+scope.row.resourcePlatformId"-->
      <!--                                      :show-des="false"-->
      <!--                                      :uploader-size="[50,50]" :pixel-limit="[1200,330]"-->
      <!--                                      @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"-->
      <!--                                      @afterDelete="v=>onImgDelete(v,scope.$index)"-->
      <!--                                      :size-limit="3076"></erp-uploader-one-pic>-->
      <!--              </div>-->
      <!--            </el-form-item>-->
      <!--          </el-form>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column :label="'*课程详情-图片列表\n(3MB以内)'" align="center" width="170px">
        <template slot-scope="scope">
          <el-form :ref="'form_product_info_images$'+scope.$index" :model="scope.row.info" :rules="formRules">
            <el-form-item prop="product_info_images">
              <div class="flex flex-around flex-wrap upload-list">
                <erp-uploader-one-pic v-for="(item,index) in scope.row.info.images" :img-in="item.src"
                                      :uploader-id="'src'"
                                      :show-des="false"
                                      :uploader-size="[100,50]" :pixel-limit="[1200,330]"
                                      @uploadSuccess="v=>onImgUploadSuccessV2(v,item)"
                                      @afterDelete="v=>onImgDeleteV2(v,scope.row.info.images,index)"
                                      :size-limit="3076"></erp-uploader-one-pic>
              </div>
              <div>
                <el-button type="success" size="small" style="margin-top: 10px;"
                           @click="clickAddImageBtn(scope.row.info.images,scope.$index,'images')">新增图片
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column :label="'课程标签'" align="center">
        <template slot-scope="scope">
          <div class="flex flex-dr flex-center" v-for="item in tags">
            <span>{{ item.label }}</span>
            <el-select multiple="multiple" v-model="scope.row.info.tags[item.id]">
              <el-option v-for="li in item['children']" :label="li.label" :value="li.id"></el-option>
            </el-select>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="是否显示" align="center" width="100">
        <template slot-scope="scope">
          <el-select v-model="scope.row.info.show">
            <el-option label="显示" :value="true"></el-option>
            <el-option label="隐藏" :value="false"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="浏览权限" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="scope.row.info.viewRoles=['admin','developer','seller']">所有人</el-button>
          <el-select v-model="scope.row.info.viewRoles" multiple="multiple">
            <el-option label="管理员" :value="'admin'"></el-option>
            <el-option label="研发" :value="'developer'"></el-option>
            <el-option label="销售" :value="'seller'"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="统计" align="center" width="80">
        <template slot-scope="scope">
          <div>浏览：{{scope.row.info.viewNumber}}</div>
          <div>收藏：{{scope.row.info.favNumber}}</div>
          <div>视频播放：{{scope.row.info.videoPlayNumber}}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="60px">
        <template slot-scope="scope">
          <!--          <div>-->
          <!--            <el-button size="small" type="text"-->
          <!--                       @click="validateForm(scope.row.resourcePlatformId,scope.$index)">验证-->
          <!--            </el-button>-->
          <!--          </div>-->
          <div>
            <el-button :disabled="scope.$index===0" size="small" type="text"
                       @click="clickMoveUpBtn(scope.row.resourcePlatformId,scope.$index)">上移
            </el-button>
          </div>
          <div>
            <el-button size="small" type="text" @click="clickDeleteBtn(scope.row.resourcePlatformId,scope.$index)"
            >删除
            </el-button>
          </div>
          <div>
            <el-button :disabled="scope.$index===list.length-1" size="small" type="text"
                       @click="clickMoveDownBtn(scope.row.resourcePlatformId,scope.$index)">下移
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!--弹窗文字编辑-->
    <el-dialog
      :title="textEditorTypeName"
      :visible.sync="textEditorShow"
      width="500px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <!--todo 不允许换行和中间输入空格-->
        <el-input v-if="!textEditorTrim" type="textarea" v-model="textEditorValue" show-word-limit
                  :maxlength="textEditorMaxLength"
                  @input="onTextEditorValueChange"
                  rows="20"></el-input>
        <el-input v-if="textEditorTrim" type="textarea" v-model.trim="textEditorValue" show-word-limit
                  :maxlength="textEditorMaxLength"
                  @input="onTextEditorValueChange"
                  rows="20"></el-input>

      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="textEditorShow=false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import erpUploaderOneVideo from "@/views/components/erpUploaderOneVideo";
import {msg_confirm} from "@/utils/ele_component";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {validateMaxLength} from "@/utils/validate";
import $ from "jquery";

export default {
  name: "resourceExperimentTable",
  directives: {
    elDragDialog
  },
  components: {erpUploaderOneVideo, erpUploaderOnePic},
  props: {
    tags: {
      type: Array,
      default() {
        return [];
      }
    },
    list: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      // form-rule
      formRules: {
        // 'name': {
        //   required: true,
        //   trigger: 'blur',
        //   message: "请输入课程名称",
        // },
        // 'subName': {
        //   required: true,
        //   trigger: 'blur',
        //   message: "请输入课程副标题",
        // },
        // 'subName_h5': {
        //   required: true,
        //   trigger: 'blur',
        //   message: "请输入H5分享副标题",
        // },
        // 'description': {
        //   required: true,
        //   trigger: 'blur',
        //   message: "请输入课程简介",
        // },
        // 'experimentDescription': {
        //   required: true,
        //   trigger: 'blur',
        //   message: "请输入实验简介",
        // },
        // 'product_bg': {
        //   required: true,
        //   trigger: 'blur',
        //   message: "请设置课程封面",
        // },
        // 'product_icon': {
        //   required: true,
        //   trigger: 'blur',
        //   message: "请设置课程Icon",
        // },
        // 'product_info_bg': {
        //   required: true,
        //   trigger: 'blur',
        //   message: "请设置课程头图",
        // },
        // // 'product_video': {
        // //   required: true,
        // //   trigger: 'blur',
        // //   message: "请设置课程详情-视频介绍",
        // // },
        // 'product_info_img': {
        //   required: true,
        //   trigger: 'blur',
        //   message: "请设置课程详情-大图",
        // },
      },
      // 文本弹窗编辑
      textEditorShow: false,
      textEditorValue: "",
      textEditorIndex: 0,
      textEditorType: "",
      textEditorMaxLength: 0,
      textEditorTypeName: "",
      textEditorTrim: false,
      // 课程简介弹窗编辑
      descriptionDialogShow: false,
      // 课程详情-实验简介
      experimentDescriptionDialogShow: false
    }
  },
  methods: {
    // 表单检测-课程详情-4张介绍图片
    validate_product_info_imgs(rule, value, callback, $index) {
      if (!this.list[$index].product_info_img_1) {
        callback(new Error("第1张未设置"))
      }
      if (!this.list[$index].product_info_img_2) {
        callback(new Error("第2张未设置"))
      }
      if (!this.list[$index].product_info_img_3) {
        callback(new Error("第3张未设置"))
      }
      if (!this.list[$index].product_info_img_4) {
        callback(new Error("第4张未设置"))
      }
      callback()
    },
    // 打开文本弹窗编辑
    openTextEditor(type, index, maxLength, typeName) {
      this.textEditorType = type
      this.textEditorValue = this.list[index]["info"][type]
      this.textEditorIndex = index
      this.textEditorMaxLength = maxLength
      this.textEditorTypeName = typeName
      this.textEditorShow = true
    },
    // 改变文本弹窗编辑值
    onTextEditorValueChange(v) {
      this.$set(this.list[this.textEditorIndex]["info"], this.textEditorType, v)
    },
    // 表格中的表单验证
    validateForm() {
      let checkResult = true
      let $this = this
      // 获取所有refs
      let refs = this.$refs
      // 遍历
      for (let k in refs) {
        let $index = k.split("$")[1]
        let name = k.split("$")[0]
        // 非直接变量 需要单独验证器
        if (name === "form_product_info_imgs") {
          this.formRules.product_info_imgs = {
            required: true,
            trigger: 'blur',
            validator: (r, v, c) => $this.validate_product_info_imgs(r, v, c, $index)
          }
        }
        if (refs[k]) {
          refs[k].validate(validate => {
            if (!validate) {
              checkResult = false
            }
          })
        }

      }
      // 返回检测结果
      // console.log(checkResult)
      this.$emit("onFormCheckResult", {result: checkResult})
      return checkResult
    },
    // 点击-上移按钮
    /**
     * params {{id 实验id,index 实验排序号}}
     */
    clickMoveUpBtn(id, index) {
      this.$emit("onClickExperimentMoveUpBtn", {id, index})
    },
    // 点击-下移按钮
    clickMoveDownBtn(id, index) {
      this.$emit("onClickExperimentMoveDownBtn", {id, index})
    },
    // 点击删除按钮
    async clickDeleteBtn(id, index) {
      this.$emit("onClickExperimentDeleteBtn", {id, index})
    },
    // 图片上传成功
    onImgUploadSuccess(params, index) {
      this.$emit("onImgUploadSuccess", {
        params, index
      })
    },
    // 图片上传成功-V2
    onImgUploadSuccessV2(params, target) {
      this.$emit("onImgUploadSuccessV2", {
        params, target
      })
    },
    // 图片删除
    onImgDelete(params, index) {
      this.$emit("onImgDelete", {
        params, index
      })
    },
    // 图片删除-V2
    onImgDeleteV2(params, list, index) {
      this.$emit("onImgDeleteV2", {
        params, list, index
      })
    },
    // 视频上传成功
    onVideoUploadSuccess(params, index) {
      this.$emit("onImgUploadSuccess", {
        params, index
      })
    },
    // 图片删除
    onVideoDelete(params, index) {
      this.$emit("onImgDelete", {
        params, index
      })
    },
    // 点击新增图片按钮
    clickAddImageBtn(params, index, type) {
      this.$emit("onClickImageAddBtn", {
        params, index, type
      })
    }
  }
}
</script>

<style>
.table-form .el-form-item__error {
  position: relative;
}

.upload-list {
  .erp-uploader-one .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    width: 100px;
    height: 50px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .erp-uploader-one .el-upload:hover {
    border-color: #409EFF;
  }

  .erp-uploader-one .uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 50px;
    line-height: 50px;
    text-align: center;
  }

  .erp-uploader-one .img-show {
    width: 100px;
    height: 50px;
    display: block;
  }

  .erp-uploader-one .video-show {
    width: 100px;
    height: 50px;
    display: block;
  }
}
</style>
<style scoped lang="scss">
// 输入框-点击弹窗大窗编辑
.input_box {
  background: #FFFFFF;
  line-height: initial;
  display: inline-block;
  padding: 5px 5px;
  border-radius: 5px;
  border: 1px solid #dcdfe6;
  width: 120px;
  cursor: pointer;
  height: 32px;
}

.video-upload-list {
  position: relative;

  .show-switch {
    display: block;
    width: 100px;
    margin: 0 auto;
    font-size: 14px;
    margin-bottom: 5px;
  }
}

</style>
