<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
        @clickSearchFilterBtn="query => ListMethods().clickSearchFilterBtn(query)"
        @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success" @click="ListMethods().clickAddBtn()">新增用户
          </el-button>
        </div>
      </div>
    </div>
    <!--列表-->
    <el-table class="lists" :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
      style="width: 100%;">
      <el-table-column label="用户名" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.username }}</span>
        </template>
      </el-table-column>
      <el-table-column label="角色" align="center">
        <template slot-scope="scope">
          <span>{{ resourcePlatform_roles[scope.row.role] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="已绑定设备数量" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.deviceIds.length }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最大允许绑定设备数量" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.maxDeviceNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最后使用IP" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.otherInfo.ip }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最后使用区域" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.otherInfo.ipAddress === "0_0" ? "未知" : scope.row.otherInfo.ipAddress }}</span>
          <el-button type="text" size="mini" style="margin-left: 5px"
            @click="ListMethods().clickIpView(scope.row, scope.$index)">查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="收藏列表" align="center">
        <template slot-scope="scope">
          <div>虚仿课程：{{ scope.row.favExperiment.length }}</div>
          <div>3D微视频：{{ scope.row.fav3dVideo.length }}</div>
          <div>全景：{{ scope.row.favPanorama.length }}</div>
          <div>
            <el-button type="text" size="mini"
              @click="FavMethods().getFavList(scope.row.favExperiment, scope.row.fav3dVideo, scope.row.favPanorama)">查看列表
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="是否启用" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.deleted === 0 ? "启用" : "禁用" }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="success" size="mini" round style="margin-top: 10px;"
            @click="ListMethods().clickResetPassword(scope.row)" :loading="scope.row.editLoading">重置密码
          </el-button>
          <el-button type="success" size="mini" round style="margin-top: 10px;"
            @click="ListMethods().clickChangeStatus(scope.row)" :loading="scope.row.editLoading">{{ scope.row.deleted === 0 ? "禁用" : "启用" }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number) => ListMethods().pageChange(number)"
        :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
        layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
        @size-change="(size) => ListMethods().pageLimitChange(size)" :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--收藏列表弹窗-->
    <el-dialog :title="fav.title" :visible.sync="fav.dialog" append-to-body width="1200px" center v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px">
          <el-form-item label="虚仿课程:" prop="title">
            <div class="flex flex-start flex-wrap">
              <span v-for="(item, index) in fav.favExperimentList">{{ item.info.name }}<i
                  v-if="index !== fav.favExperimentList.length - 1">、</i></span>
            </div>
          </el-form-item>
          <el-form-item label="3D微视频:" prop="title">
            <div class="flex flex-start flex-wrap">
              <span v-for="(item, index) in fav.fav3dVideoList">{{ item.info.name }}<i
                  v-if="index !== fav.fav3dVideoList.length - 1">、</i></span>
            </div>
          </el-form-item>
          <el-form-item label="全景:" prop="title">
            <div class="flex flex-start flex-wrap">
              <span v-for="(item, index) in fav.favPanoramaList">{{ item.info.name }}<i
                  v-if="index !== fav.favPanoramaList.length - 1">、</i></span>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>

    <!--新增用户弹窗-->
    <el-dialog :title="entityInfo.title" :visible.sync="entityInfo.dialog" append-to-body width="500px" center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
          <el-form-item label="用户名:" prop="username">
            <el-input v-model="entityInfo.edit.username"></el-input>
          </el-form-item>
          <el-form-item label="角色:" prop="role">
            <el-select v-model="entityInfo.edit.role">
              <el-option v-for="item in resourcePlatformRolesOptions" :key="item.value" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default" @click="entityInfo.dialog = false">取 消</el-button>
        <el-button type="success" @click="EntityInfoMethods().clickAddBtn()" :loading="entityInfo.loading">提
          交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import { mapState } from "vuex";
import { dateFormat, timeFormat } from "@/filters";
import { date_format, getQuery, objectToLVArr } from "@/utils/common";
import { WebAnalyticsModel } from "@/model/erp/WebAnalyticsModel";
import { GeneralInfoModel } from "@/model/erp/GeneralInfoModel";
import { validateMaxLength } from "@/utils/validate";
import { SchoolModel } from "@/model/exp/SchoolModel";
import { msg_confirm, msg_success } from "@/utils/ele_component";
import erpUploaderOneVideo from "@/views/components/erpUploaderOneVideo";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import { ResourcePlatformUserModel } from "@/model/erp/ResourcePlatformUserModel";
import { ResourcePlatformModel } from "@/model/erp/ResourcePlatformModel";
import { resourcePlatform_roles } from "@/enums/resourcePlatform";
import { CommonModel } from "@/model/CommonModel";
export default {
  name: "viewDetail",
  components: {
    ListSearchFilter, erpUploaderOneVideo, erpUploaderOnePic
  },
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission,
      adminUserId: state => state.user.id
    })
  },
  filters: { dateFormat, timeFormat },
  data: function () {
    let $this = this;
    return {
      resourcePlatform_roles: resourcePlatform_roles,
      resourcePlatformRolesOptions: [],
      window: window,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '用户名',
              placeholder: "请输入用户名",
              key: 'username',
              value: '',
              format: (v) => {
                return { '$regex': `.*${v}.*` }
              }
            },
          ],
          filter: []
        }
      },
      entityInfo: {
        dialog: false,
        title: "新增视频",
        type: "add",
        loading: false,
        uploading: false,
        edit: {
          info: {}
        },
        // 输入检测
        formRules: {
          username: [
            { required: true, message: '请输入用户名', trigger: 'blur' },
            { validator: (r, v, c) => validateMaxLength(r, v, c, 20, '用户名'), trigger: 'blur' }
          ],
          role: [
            { required: true, message: '请选择角色', trigger: 'change' }
          ]
        },
      },
      fav: {
        dialog: false,
        title: "收藏列表",
        favExperimentList: [],
        fav3dVideoList: [],
        favPanoramaList: [],
      },
    }
  },
  async mounted() {
    this.ListMethods().getList(0, 20, {})
    this.resourcePlatformRolesOptions = Object.keys(resourcePlatform_roles).map(key => ({
      label: resourcePlatform_roles[key],
      value: key
    }))
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 重置密码
        async clickResetPassword(user) {
          user.password = "369a2180651ab03c53647967a92b2cc4fb0085979444206d"
          await ResourcePlatformUserModel.addOrEdit(user)
          msg_success("重置密码成功")
          await $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
        },
        // 禁用或启用
        async clickChangeStatus(user) {
          user.deleted = user.deleted === 0 ? 1 : 0
          await ResourcePlatformUserModel.addOrEdit(user)
          msg_success("操作成功")
          await $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
        },
        // 点击了ip地址查看按钮
        clickIpView(row, index) {
          window.open("https://ip.900cha.com/" + row.otherInfo.ip + ".html")
        },
        // 点击搜索重置按钮
        clickCleanBtn() {

        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          query = Object.assign({}, query);
          [list, $this.lists.pages] = await ResourcePlatformUserModel.getPageList(page - 1, size, "", query)
          // 获取该pageId关联的事件数量
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter(type) {

        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击删除按钮
        async clickDeleteBtn(row) {
          if (await msg_confirm("确认要删除该视频吗？删除后不能恢复！")) {
            await GeneralInfoModel.deleteOne(row.generalInfoId)
            msg_success("删除成功")
            await $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
          }
        },
        // 点击添加按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "新增用户"
          $this.entityInfo.edit = {};
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
        // 点击列表的编辑按钮
        clickEditBtn(edit, $index) {
          $this.entityInfo.edit = JSON.parse(JSON.stringify(edit))
          $this.entityInfo.type = 'edit'
          $this.entityInfo.title = "修改视频"
          $this.entityInfo.$index = $index
          $this.entityInfo.dialog = true
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
      }
    },
    // 实体Methods
    EntityInfoMethods() {
      let $this = this;
      return {
        // 点击提交新增按钮
        clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              $this.entityInfo.loading = true
              if (await ResourcePlatformModel.addUser($this.entityInfo.edit).catch(err => {
                $this.entityInfo.loading = false
              })) {
                msg_success('新增用户成功')
                $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                $this.entityInfo.loading = false
                $this.entityInfo.dialog = false
              }
            }
          })
        },
        // 点击修改按钮
        clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              $this.entityInfo.loading = true
              if (await ResourcePlatformUserModel.addOrEdit($this.entityInfo.edit).catch(err => {
                $this.entityInfo.loading = false
              })) {
                msg_success('保存成功')
                $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
                $this.entityInfo.dialog = false
                $this.entityInfo.loading = false
              }
            }
          })
        },
      }
    },
    // 收藏Methods
    FavMethods() {
      let $this = this;
      return {
        // 获取收藏列表
        async getFavList(e, v, p) {
          $this.fav.dialog = true
          $this.fav.favExperimentList = await ResourcePlatformModel.getListByIds(e)
          $this.fav.fav3dVideoList = await ResourcePlatformModel.getListByIds(v)
          $this.fav.favPanoramaList = await ResourcePlatformModel.getListByIds(p)
        }
      }
    },
    // 通用-文件上传前
    beforeUpload(params) {
      this.$set(this.entityInfo, "uploading", true)
    },
    // 通用-文件上传成功
    fileUpload(params, target, formRef) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
      msg_success("上传成功！")
      this.$set(this.entityInfo, "uploading", false)
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 通用-文件删除
    fileDelete(params, target, formRef) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
  }
}
</script>
<style scoped lang="scss">
.lists {
  a {
    color: #004DCC;
  }

  .can-click {
    color: #004DCC;
    cursor: pointer;
  }
}
</style>
