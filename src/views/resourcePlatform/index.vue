<template>
  <div class="app-container">
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>顶部轮播</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="focus.cardShow=!focus.cardShow">
          <i class="el-icon-arrow-up" v-show="focus.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!focus.cardShow"></i>
          {{ focus.cardShow ? '收起' : '展开' }}
        </el-button>
      </div>
      <div class="card-container" v-show="focus.cardShow">
        <div class="btn-box clearfix" style="margin-bottom: 10px">
          <el-button size="small fr" type="primary" @click="FocusMethods().clickAddOne('video')">新增视频轮播
          </el-button>
          <el-button size="small fr" type="primary" @click="FocusMethods().clickAddOne('image')"
                     style="margin-right: 10px">新增图片轮播
          </el-button>
        </div>
        <el-table :data="focus.list" border fit>
          <el-table-column label="图片或视频" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.type==='image'">
                <erp-uploader-one-pic style="margin-right: 30px" :img-in="scope.row.image"
                                      :uploader-id="'image'"
                                      uploader-title="图片" :uploader-size="[200,100]" :pixel-limit="[1920,750]"
                                      :size-limit="2000"
                                      @uploadSuccess="data=>fileUpload(data,focus.list[scope.$index],'focus')"
                                      @afterDelete="data=>fileDelete(data,focus.list[scope.$index],'focus')"></erp-uploader-one-pic>
              </div>
              <div v-if="scope.row.type==='video'">
                <erp-uploader-one-video style="margin-right: 30px" :video-in="scope.row.video"
                                        :uploader-id="'video'"
                                        upload-target="zy0"
                                        :uploader-size="[200,100]" :pixel-limit="[1920,750]"
                                        :size-limit="20480"
                                        @afterDelete="data=>fileDelete(data,focus.list[scope.$index])"
                                        @uploadSuccess="data=>fileUpload(data,focus.list[scope.$index])"></erp-uploader-one-video>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="轮播类型" align="center" width="135">
            <template slot-scope="scope">
             <el-select v-model="scope.row.type">
               <el-option value="image" label="图片"></el-option>
               <el-option value="video" label="视频"></el-option>
             </el-select>
            </template>
          </el-table-column>
          <el-table-column label="是否显示" align="center" width="135">
            <template slot-scope="scope">
              <el-select v-model="scope.row.show">
                <el-option label="显示" :value="true"></el-option>
                <el-option label="隐藏" :value="false"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="跳转链接" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.link"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="120">
            <template slot-scope="scope">
              <el-button size="mini" type="default" @click="FocusMethods().clickDeleteOne(scope.$index)">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>公司历史</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="companyHistory.cardShow=!companyHistory.cardShow">
          <i class="el-icon-arrow-up" v-show="companyHistory.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!companyHistory.cardShow"></i>
          {{ companyHistory.cardShow ? '收起' : '展开' }}
        </el-button>
      </div>
      <div class="card-container" v-show="companyHistory.cardShow">
        <index-company-history @onUpdate="data=>CompanyHistoryMethods().onUpdate(data)" :texts="companyHistory.texts"
                               :images="companyHistory.images" ref="indexCompanyHistory"></index-company-history>
      </div>
    </el-card>
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>校企合作成果</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="schoolCooperation.cardShow=!schoolCooperation.cardShow">
          <i class="el-icon-arrow-up" v-show="schoolCooperation.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!schoolCooperation.cardShow"></i>
          {{ schoolCooperation.cardShow ? '收起' : '展开' }}
        </el-button>
      </div>
      <div class="card-container" v-show="schoolCooperation.cardShow">
        <div class="clearfix">
          <el-button style="margin-bottom: 10px" class="fr" type="success" size="small"
                     @click="SchoolCooperationMethods().clickOpenAddBtn()">新增合作成果
          </el-button>
        </div>
        <!--列表-->
        <el-table :data="schoolCooperation.lists.list" v-loading="schoolCooperation.lists.loading"
                  element-loading-text="加载中" border fit
                  style="width: 100%;">
          <el-table-column label="标题" align="center">
            <template slot-scope="scope">
              <span>({{scope.$index+1}}){{ scope.row.info.title }}</span>
            </template>
          </el-table-column>
          <el-table-column label="简介" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.info.des }}</span>
            </template>
          </el-table-column>
          <el-table-column label="类型" align="center" width="60">
            <template slot-scope="scope">
              <span>{{ {"link": "外链", "content": "内容"}[scope.row.info.type] }}</span>
            </template>
          </el-table-column>
          <el-table-column label="打开次数" align="center" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.info.clickNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="120"
                           class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <div>
                <el-button type="success" size="mini" round style="margin-top: 10px;"
                           @click="SchoolCooperationMethods().clickOpenEditBtn(scope.row)">
                  编辑
                </el-button>
              </div>
              <div>
                <el-button type="danger" size="mini" round style="margin-top: 10px;"
                           @click="SchoolCooperationMethods().clickDeleteBtn(scope.row,scope.$index)">
                  删除
                </el-button>
              </div>
              <el-button type="text" size="mini" round style="margin-top: 10px;" :disabled="scope.$index===0"
                         @click="SchoolCooperationMethods().clickUpBtn(scope.row)">
                上移
              </el-button>
              <el-button type="text" size="mini" round style="margin-top: 10px;"
                         :disabled="scope.$index===schoolCooperation.lists.list.length-1"
                         @click="SchoolCooperationMethods().clickDownBtn(scope.row)"
              >
                下移
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--列表分页-->
        <div class="pagination-container">
          <el-pagination background @current-change="(number)=>SchoolCooperationMethods().pageChange(number)"
                         :current-page.sync="schoolCooperation.lists.pages.number"
                         :page-size.sync="schoolCooperation.lists.pages.size"

                         layout="total,prev, pager, next,jumper,sizes"
                         :total="schoolCooperation.lists.pages.totalElements"
                         @size-change="(size)=>SchoolCooperationMethods().pageLimitChange(size)"
                         :page-count="schoolCooperation.lists.pages.totalPages">
          </el-pagination>
        </div>
        <!--编辑和新增弹窗-->
        <el-dialog
          :title="schoolCooperation.entityInfo.title"
          :visible.sync="schoolCooperation.entityInfo.dialog"
          :close-on-click-modal="false"
          :append-to-body="true"
          width="1200px"
          center
          v-el-drag-dialog>
          <div class="dialog-container" style="max-height: 600px;overflow-y: scroll;padding:10px;">
            <el-form label-width="120px" ref="schoolCooperation_entityInfoForm"
                     :model="schoolCooperation.entityInfo.edit" :rules="schoolCooperation.entityInfo.formRules">
              <el-form-item label="标题:" prop="title">
                <el-input v-model="schoolCooperation.entityInfo.edit.info.title">
                </el-input>
              </el-form-item>
              <el-form-item label="介绍:" prop="des">
                <el-input v-model="schoolCooperation.entityInfo.edit.info.des" type="textarea">
                </el-input>
              </el-form-item>
              <el-form-item label="学校机构:" prop="organ">
                <el-input v-model="schoolCooperation.entityInfo.edit.info.organ">
                </el-input>
              </el-form-item>
              <el-form-item label="新闻头图:" prop="headImgUrl">
                <erp-uploader-one-pic :img-in="schoolCooperation.entityInfo.edit.info.headImg" style="width: 200px"
                                      uploader-id="headImg"
                                      uploader-title="头图" :uploader-size="[200,150]" :pixel-limit="[315,180]"
                                      :size-limit="512"
                                      @uploadSuccess="data=>fileUpload(data,schoolCooperation.entityInfo.edit.info)"
                                      @afterDelete="data=>fileDelete(data,schoolCooperation.entityInfo.edit.info)"></erp-uploader-one-pic>
              </el-form-item>
              <el-form-item label="大图列表:" >
                <div>
                  <el-button type="primary"  size="small" style="margin-bottom: 10px" @click="SchoolCooperationMethods().clickAddImgBtn()">新增图片</el-button>
                </div>
                <div  class="flex flex-start flex-wrap">
                  <div class="li" v-for="(item,index) in schoolCooperation.entityInfo.edit.info.imageList" style="text-align: center">
                    <erp-uploader-one-pic :img-in="item" style="width: 200px;margin-right: 10px;"
                                          :uploader-id="index"
                                          uploader-title="头图" :uploader-size="[200,150]" :pixel-limit="[1920,1080]"
                                          :size-limit="5120"
                                          @uploadSuccess="data=>fileUpload(data,schoolCooperation.entityInfo.edit.info.imageList)"
                                          @afterDelete="data=>fileDelete(data,schoolCooperation.entityInfo.edit.info.imageList)"></erp-uploader-one-pic>
                    <el-button size="mini" type="danger" @click="SchoolCooperationMethods().clickDelImageBtn(index)">删除</el-button>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="发布时间:" prop="publishTime">
                <el-date-picker
                  v-model="schoolCooperation.entityInfo.edit.info.publishTime"
                  type="datetime"
                  placeholder="选择发布时间">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="类型:" prop="des">
                <el-select v-model="schoolCooperation.entityInfo.edit.info.type">
                  <el-option value="link" label="外链"></el-option>
                  <el-option value="content" label="内容"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="跳转链接:" prop="url" v-show="schoolCooperation.entityInfo.edit.info.type==='link'">
                <el-input v-model="schoolCooperation.entityInfo.edit.info.url">
                </el-input>
              </el-form-item>
              <el-form-item label="新闻内容:" prop="content"
                            v-show="schoolCooperation.entityInfo.edit.info.type==='content'">
                <tinymce
                  ref="tinymce_content"
                  v-model="schoolCooperation.entityInfo.edit.info.content"
                  :height="500"
                />
              </el-form-item>
            </el-form>
          </div>
          <div slot="footer" class="dialog-footer">
            <el-button type="default"
                       @click="schoolCooperation.entityInfo.dialog=false">取 消
            </el-button>
            <el-button type="success" @click="SchoolCooperationMethods().clickAddBtn()"
                       v-if="schoolCooperation.entityInfo.type==='add'">提 交
            </el-button>
            <el-button type="success" @click="SchoolCooperationMethods().clickEditBtn()"
                       v-if="schoolCooperation.entityInfo.type==='edit'">提 交
            </el-button>
          </div>
        </el-dialog>
      </div>
    </el-card>
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>优势框组</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="advantage.cardShow=!advantage.cardShow">
          <i class="el-icon-arrow-up" v-show="advantage.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!advantage.cardShow"></i>
          {{ advantage.cardShow ? '收起' : '展开' }}
        </el-button>
      </div>
      <div class="card-container advantage-container" v-show="advantage.cardShow">
        <div class="clearfix" style="margin-bottom: 10px">
          <el-button class="fr" @click="AdvantageMethods().clickAddOne()">新增一组</el-button>
        </div>
        <el-form v-for="(group,index) in advantage.list" label-width="120px;" style="border-top:2px solid red;">
          <div>
            <el-button size="small" style="margin-top: 10px" type="danger"
                       @click="AdvantageMethods().clickDeleteOne(index)">删除本组
            </el-button>
          </div>
          <el-form-item label="名称:">
            <el-input v-model="group.name"></el-input>
          </el-form-item>
          <el-form-item label="图标:">
            <div style="width: 200px">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="group.image"
                                    :uploader-id="'image'"
                                    uploader-title="图片" :uploader-size="[200,100]" :pixel-limit="[77,69]"
                                    :size-limit="200"
                                    @uploadSuccess="data=>fileUpload(data,group,'advantage')"
                                    @afterDelete="data=>fileDelete(data,group,'advantage')"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label="子项:">
            <div style="margin-bottom: 10px">
              <el-button @click="AdvantageMethods().clickAddOneItem(group.list)">新增一项</el-button>
            </div>
            <el-table :data="group.list" border fit>
              <el-table-column label="图标" align="center">
                <template slot-scope="scope">
                  <erp-uploader-one-pic style="margin-right: 30px" :img-in="scope.row.image"
                                        :uploader-id="'image'"
                                        uploader-title="图片" :uploader-size="[200,100]" :pixel-limit="[145,145]"
                                        :size-limit="200"
                                        @uploadSuccess="data=>fileUpload(data,scope.row,'advantage')"
                                        @afterDelete="data=>fileDelete(data,scope.row,'advantage')"></erp-uploader-one-pic>
                </template>
              </el-table-column>
              <el-table-column label="文字" align="center">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.text"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="编辑" align="center" width="100">
                <template slot-scope="scope">
                  <el-button size="mini" @click="AdvantageMethods().clickDeleteOneItem(group.list,scope.$index)">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>友情链接</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="friendLink.cardShow=!friendLink.cardShow">
          <i class="el-icon-arrow-up" v-show="friendLink.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!friendLink.cardShow"></i>
          {{ friendLink.cardShow ? '收起' : '展开' }}
        </el-button>
      </div>
      <div class="card-container friendLink-container" v-show="friendLink.cardShow">
        <div class="flex flex-start">
          <ul class="friendLinkList clearfix">
            <li v-for="item in friendLink.list" style="list-style-type: none;float:left;margin-bottom: 20px"
                v-if="item.img">
              <erp-uploader-one-pic :key="item.id"
                                    style="margin-right: 30px;margin-bottom: 20px"
                                    :img-in="item.img"
                                    :uploader-id="'friendLink'+item.id"
                                    @afterDelete="data=>FriendLinkMethods().afterDelete(data)"
                                    :show-des="false"
                                    :uploader-size="[260,60]" :pixel-limit="[260,60]"
                                    :size-limit="200"
                                    @uploadSuccess="data=>FriendLinkMethods().fileUploadSuccess(data)"></erp-uploader-one-pic>
              <el-input v-model="item.name" class="friendLink-name"></el-input>
              <el-input v-model="item.url" class="friendLink-url"></el-input>
            </li>
            <erp-uploader-one-pic :key="friendLink.list[friendLink.list.length-1].id"
                                  style="margin-right: 30px;margin-bottom: 20px;float:left"
                                  :img-in="friendLink.list[friendLink.list.length-1].img"
                                  :uploader-id="'friendLink'+friendLink.list[friendLink.list.length-1].id"
                                  @afterDelete="data=>FriendLinkMethods().afterDelete(data)"
                                  :show-des="false"
                                  :uploader-size="[260,60]" :pixel-limit="[260,60]"
                                  :size-limit="200"
                                  @uploadSuccess="data=>FriendLinkMethods().fileUploadSuccess(data)"></erp-uploader-one-pic>
          </ul>

        </div>
      </div>
    </el-card>
    <!--按钮组-->
    <div class="bottom-button-container flex flex-center">
      <el-button type="primary" @click="clickSaveBtn()">保 存</el-button>
      <el-button @click="clickCancelBtn">取 消</el-button>
    </div>
  </div>
</template>

<script>
/**
 * 首页管理
 */
import {BaseUploadModel} from "@/model/BaseUploadModel";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import erpUploaderOneVideo from "@/views/components/erpUploaderOneVideo";
import {
  find_obj_from_arr_by_id,
  findObjectArrSomeObjFirstOne, randomNumber,
} from "@/utils/common";
import Sortable from "sortablejs";
import {validateMaxLength} from "@/utils/validate";
import {msg_confirm, msg_confirm_choose, msg_err, msg_success} from "@/utils/ele_component";
import {ConfigModel} from "@/model/erp/ConfigModel";
import {CONFIG_NAME_RESOURCE_PLATFORM} from "@/model/ConfigModel";
import {VEAModel} from "@/model/VEAModel";
import $ from "jquery"
import elDragDialog from "@/directive/el-drag-dialog";
import indexCompanyHistory from "@/views/resourcePlatform/components/indexCompanyHistory.vue";
import "./components/resourcePlatform_view_components.scss"
import {ResourcePlatformModel} from "@/model/erp/ResourcePlatformModel";
import Tinymce from "@/components/Tinymce/index.vue";
import {WebAnalyticsModel} from "@/model/erp/WebAnalyticsModel";

export default {
  name: "index",
  directives: {
    elDragDialog
  },
  components: {Tinymce, erpUploaderOnePic, erpUploaderOneVideo, indexCompanyHistory},
  data() {
    return {
      uploadRequest: BaseUploadModel.uploadRequest,
      index: {},
      // 顶部轮播
      focus: {
        list: [],// 如果默认没有这个，就不会刷新
        cardShow: true,
        // 表单检测
        formRules: {},
      },
      // 公司历史
      companyHistory: {
        cardShow: true,
        texts: [],
        images: [],
        // 表单检测
        formRules: {},
      },
      // 合作学校
      schoolCooperation: {
        cardShow: true,
        // 列表
        lists: {
          list: [],
          loading: false,
          query: {},
          sort: "",
          pages: {
            size: 100
          },
          searchFilter: {
            search: [],
            filter: []
          }
        },
        // 实体
        entityInfo: {
          title: "",
          dialog: false,
          edit: {
            info: {}
          },
          formRules: {},
        },
        // 表单检测
        formRules: {},
      },
      // 优势框组
      advantage: {
        cardShow: true,
        list: [],
        // 表单检测
        formRules: {},
      },
      // 友情链接
      friendLink: {
        cardShow: true,
        list: [
          {
            id: new Date().getTime(),
          }
        ],
        // 表单检测
        formRules: {},
      },


      // 错误跳转标记,用于跳转到最开始错误
      errAJumped: false,
    }
  },
  mounted() {
    // 读取配置信息
    this.getConfig()
    // 底部按钮组大小监听
    VEAModel.bottomButtonContainerWidthChange()

    //this.test()
  },
  methods: {
    async test(){
      let lists=await ResourcePlatformModel.getPageList("schoolCooperation", 0, 100, "createTime,desc", {})
      let list=lists[0]
      list.forEach((li)=>{
        li.info.imageList=[]
        ResourcePlatformModel.addOrEdit(li)
      })
    },
    // 通用-楼层在前台显示和隐藏
    async onFloorChangeShow(v, target) {
      if (v) {
        if (!await msg_confirm("是否确认开启该楼层，开启该楼层后，该楼层将在官网上可见")) {
          this.$set(target, "show", !v)
          this.$forceUpdate()
        }
      } else {
        if (!await msg_confirm("是否确认隐藏该楼层，隐藏该楼层后，该楼层将在官网上不可见")) {
          this.$set(target, "show", !v)
          this.$forceUpdate()
        }
      }
    },
    // 读取配置
    async getConfig() {
      let data = await ConfigModel.getEdit(CONFIG_NAME_RESOURCE_PLATFORM, "index")
      let index = JSON.parse(data)
      this.index = JSON.parse(data)
      // 设置各项配置
      this.focus = Object.assign(this.focus, index.focus) // 轮播
      this.companyHistory = Object.assign(this.companyHistory, index.companyHistory) // 公司历史
      this.advantage = Object.assign(this.advantage, index.advantage) // 优势框组
      this.friendLink = Object.assign(this.friendLink, index.friendLink) // 优势框组
      setTimeout(() => {
        Sortable.create(document.querySelector('.friendLinkList'))
        this.FriendLinkMethods().setFloorSort()
      }, 1000)
      // 获取校企合作成果列表
      this.SchoolCooperationMethods().getList(0, this.schoolCooperation.lists.pages.size, {})

      this.$forceUpdate()
    },
    // 通用-文件上传成功
    fileUpload(params, target, formRef) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 通用-文件删除
    fileDelete(params, target, formRef) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 轮播相关
    FocusMethods() {
      let $this = this;
      return {
        // 点击新增一个
        clickAddOne(type) {
          $this.focus.list.push({
            type: type
          })
        },
        // 点击删除一个
        async clickDeleteOne(index) {
          if (await msg_confirm("确定要删除吗？")) {
            $this.focus.list.splice(index, 1)
          }
        }
      }
    },
    // 公司历史相关
    CompanyHistoryMethods() {
      let $this = this;
      return {
        onUpdate(updateObject) {
          $this.$set($this.companyHistory, "texts", updateObject["texts"])
          $this.$set($this.companyHistory, "images", updateObject["images"])
        }
      }
    },
    // 合作学校Methods
    SchoolCooperationMethods() {
      let $this = this
      return {
        // 点击新增图片按钮
        clickAddImgBtn(){
          $this.schoolCooperation.entityInfo.edit.info.imageList.push("")
        },
        // 点击删除图片按钮
        clickDelImageBtn(index){
          $this.schoolCooperation.entityInfo.edit.info.imageList.splice(index,1)
        },
        // 获取列表
        async getList(page, size, query) {
          $this.schoolCooperation.lists.loading = true;
          let sort = "sortOrder,desc&sort=createTime,desc";
          [$this.schoolCooperation.lists.list, $this.schoolCooperation.lists.pages] = await ResourcePlatformModel.getPageList("schoolCooperation", page - 1, size, sort, query)
          $this.schoolCooperation.lists.loading = false
          // 批量获取-每个链接的点击次数
          let clickCountQuery=[]
          $this.schoolCooperation.lists.list.forEach(li=>{
            clickCountQuery.push(
              {
                "webSiteId":"006",
                "eventName":"schoolCooperationClickUrl",
                "queryName1":"info.info.id",
                "queryValue1":li.resourcePlatformId
              }
            )
          })
          let clickCountResult=await WebAnalyticsModel.oneWebOneEventNumberByInfo(clickCountQuery)
          clickCountResult.forEach((li,index)=>{
            $this.$set($this.schoolCooperation.lists.list[index].info,"clickNumber",li)
          })
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.schoolCooperation.lists.pages.size, $this.schoolCooperation.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.schoolCooperation.lists.pages.number - 1, size, $this.schoolCooperation.lists.query)
        },
        // 初始化筛选列表
        async initFilter(type) {
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {

        },
        // 点击创建按钮
        async clickOpenAddBtn() {
          $this.schoolCooperation.entityInfo.type = "add"
          $this.schoolCooperation.entityInfo.title = "创建合作成果"
          $this.schoolCooperation.entityInfo.edit = {
            type: "schoolCooperation",
            info: {
              imageList:[]
            }
          };
          setTimeout(() => {
            $this.$refs['schoolCooperation_entityInfoForm'].clearValidate()
          }, 400)
          $this.schoolCooperation.entityInfo.dialog = true;
        },
        // 点击编辑按钮
        async clickOpenEditBtn(entity, index) {
          $this.schoolCooperation.entityInfo.type = "edit"
          $this.schoolCooperation.entityInfo.title = `编辑合作成果`
          $this.$set($this.schoolCooperation.entityInfo, "edit", entity);
          $this.schoolCooperation.entityInfo.dialog = true;
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 400)
        },
        // 点击删除按钮
        async clickDeleteBtn(entity, index) {
          if (await msg_confirm(`确认要删除该项吗？`)) {
            if (await ResourcePlatformModel.deleteOne({
              resourcePlatformId: entity.resourcePlatformId
            })) {
              msg_success("删除成功！")
              this.getList($this.schoolCooperation.lists.pages.number, $this.schoolCooperation.lists.pages.size, $this.schoolCooperation.lists.query)
            } else {
              msg_err("删除失败！")
            }
          }
        },
        // 点击创建按钮
        async clickAddBtn() {
          $this.$refs['schoolCooperation_entityInfoForm'].validate(async validate => {
            if (validate) {
              $this.schoolCooperation.entityInfo.postLoading = true
              let result = await ResourcePlatformModel.addOrEdit($this.schoolCooperation.entityInfo.edit).catch(err => {
                $this.schoolCooperation.entityInfo.postLoading = false
              })
              if (result) {
                msg_success("创建成功！")
                this.getList(0, $this.schoolCooperation.lists.pages.size, $this.schoolCooperation.lists.query)
                $this.schoolCooperation.entityInfo.postLoading = false
                $this.schoolCooperation.entityInfo.dialog = false
              }
            }
          });
        },
        // 点击编辑按钮
        async clickEditBtn() {
          $this.$refs['schoolCooperation_entityInfoForm'].validate(async validate => {
            if (validate) {
              if (await msg_confirm("确定要保存吗？")) {
                $this.schoolCooperation.entityInfo.postLoading = true
                let result = await ResourcePlatformModel.addOrEdit($this.schoolCooperation.entityInfo.edit).catch(err => {
                  $this.schoolCooperation.entityInfo.postLoading = false
                })
                if (result) {
                  msg_success("编辑成功！")
                  // todo 编辑成功后-只更新编辑后的
                  this.getList($this.schoolCooperation.lists.pages.number, $this.schoolCooperation.lists.pages.size, $this.schoolCooperation.lists.query)
                  $this.schoolCooperation.postLoading = false
                  $this.schoolCooperation.entityInfo.dialog = false
                }
              }
            }
          });
        },
        // 点击上移按钮
        async clickUpBtn(edit) {
          let $index = parseInt(find_obj_from_arr_by_id("resourcePlatformId", edit.resourcePlatformId, $this.schoolCooperation.lists.list)[0])
          let one = $this.schoolCooperation.lists.list[$index - 1];
          let oneSortOrder = one.sortOrder
          one.sortOrder = edit.sortOrder
          edit.sortOrder = oneSortOrder
          await ResourcePlatformModel.addOrEdit(edit)
          await ResourcePlatformModel.addOrEdit(one)
          this.getList($this.schoolCooperation.lists.pages.page, $this.schoolCooperation.lists.pages.size, $this.schoolCooperation.lists.query)
        },
        // 点击下移按钮
        async clickDownBtn(edit) {
          let $index = parseInt(find_obj_from_arr_by_id("resourcePlatformId", edit.resourcePlatformId, $this.schoolCooperation.lists.list)[0])
          let one = $this.schoolCooperation.lists.list[$index + 1];
          let oneSortOrder = one.sortOrder
          one.sortOrder = edit.sortOrder
          edit.sortOrder = oneSortOrder
          await ResourcePlatformModel.addOrEdit(edit)
          await ResourcePlatformModel.addOrEdit(one)
          this.getList($this.schoolCooperation.lists.pages.page, $this.schoolCooperation.lists.pages.size, $this.schoolCooperation.lists.query)
        },
      }
    },
    // 优势框组相关
    AdvantageMethods() {
      let $this = this;
      return {
        // 新增一组
        clickAddOne() {
          $this.advantage.list.push(
            {
              list: [{}]
            },
          )
        },
        // 删除一组
        clickDeleteOne(index) {
          $this.advantage.list.splice(index, 1)
        },
        // 组里面新增一项
        clickAddOneItem(group) {
          group.push({})
        },
        // 删除组里的一项
        clickDeleteOneItem(group, index) {
          group.splice(index, 1)
        }
      }
    },
    // 友情链接相关
    FriendLinkMethods() {
      let $this = this;
      return {
        // 设置拖动排序
        setFloorSort() {
          const el = document.querySelectorAll(
            ".friendLinkList"
          )[0];
          Sortable.create(el, {
            ghostClass: "sortable-ghost", // Class name for the drop placeholder,
            setData: function (dataTransfer) {
              dataTransfer.setData("Text", "");
              // to avoid Firefox bug
              // Detail see : https://github.com/RubaXa/Sortable/issues/1012
            },
            onEnd: (evt) => {
              const targetRow = $this.friendLink.list.splice(evt.oldIndex, 1)[0];// 拖动的对象
              $this.friendLink.list.splice(evt.newIndex, 0, targetRow);// 插入
              $this.$set($this.friendLink, "list", $this.friendLink.list)
            },
          });
        },
        fileUploadSuccess(data) {
          let id = Number(data[0].replace("friendLink", ""))
          let index = find_obj_from_arr_by_id("id", id, $this.friendLink.list)[0]
          if (!$this.friendLink.list[index].hasOwnProperty('img')) {
            $this.friendLink.list.push({
              id: new Date().getTime() + randomNumber(1, 1000),
            })
          }
          $this.$set($this.friendLink.list[index], "img", data[1])
        },
        afterDelete(data) {
          let id = Number(data[0].replace("friendLink", ""))
          let index = find_obj_from_arr_by_id("id", id, $this.friendLink.list)[0]
          $this.friendLink.list.splice(index, 1)
        },
        // 保存时调用 todo 也许不需要这样，拖动时应该可以直接改变list顺序
        beforeSave() {
          let list = []
          $("ul.friendLinkList img.img-show").each(function () {
            let url = $(this).attr("src")
            let item=find_obj_from_arr_by_id("img", url, $this.friendLink.list)[1];
            if (url) {
              list.push({
                id: new Date().getTime() + randomNumber(1, 1000),
                img: url,
                name:item ["name"],
                url: item["url"]
              })
            }
          })
          list.push({
            id: new Date().getTime() + randomNumber(1, 1000),
          })
          return list;
        }
      }
    },
    // 点击保存按钮
    async clickSaveBtn(floors) {
      if (!floors) {
        floors = ['focus', "companyHistory", "advantage", "friendLink"]
      }
      // 每个项目的合法性检测
      this.errAJumped = false
      if (true) {
        // 轮播
        if (floors.indexOf('focus') > -1 && false) {
          this.$refs["focus"].validate(validate => {
            if (validate) {

            } else {
              if (!this.errAJumped) {
                this.focus.cardShow === false ? this.focus.cardShow = true : ""
                // location.hash = "focus";
                this.errAJumped = true
              }
            }
          })
        }
        // 公司历史
        if (floors.indexOf('companyHistory') > -1 && false) {
          this.$refs["companyHistory"].validate(validate => {
            if (validate) {

            } else {
              if (!this.errAJumped) {
                this.focus.cardShow === false ? this.focus.cardShow = true : ""
                // location.hash = "focus";
                this.errAJumped = true
              }
            }
          })
        }
        // 优势框组
        if (floors.indexOf('advantage') > -1 && false) {
          this.$refs["advantage"].validate(validate => {
            if (validate) {

            } else {
              if (!this.errAJumped) {
                this.focus.cardShow === false ? this.focus.cardShow = true : ""
                // location.hash = "focus";
                this.errAJumped = true
              }
            }
          })
        }
        // 友情鏈接
        if (floors.indexOf('friendLink') > -1 && false) {
          this.$refs["friendLink"].validate(validate => {
            if (validate) {

            } else {
              if (!this.errAJumped) {
                this.focus.cardShow === false ? this.focus.cardShow = true : ""
                // location.hash = "focus";
                this.errAJumped = true
              }
            }
          })
        }

        if (this.errAJumped) { // 表单验证有误
          setTimeout(() => {
            // 跳转到第一个错误处
            let firstErrEl = $("body .el-form-item__error")
            let top = firstErrEl.offset().top - 200
            // 跳转到指定位置
            document.body.scrollTop = top;
            document.documentElement.scrollTop = top;
          }, 600)
          return
        }
      }
      // 保存
      let saveButtonChoose = await msg_confirm_choose("确定要保存修改吗？", "确认保存", "我再想想", '确认提交')
      if (saveButtonChoose === "right") {
        let index = this.index
        // 保存前调用
        // 轮播
        if (floors.indexOf('focus') > -1) {
          index.focus = JSON.parse(JSON.stringify(this.focus))
          // 删除不必要元素
          delete index.focus.formRules
        }
        // 公司历史
        if (floors.indexOf('companyHistory') > -1) {
          // 调用子组件更新方法
          this.$refs["indexCompanyHistory"].clickUpdateBtn()
          index.companyHistory = JSON.parse(JSON.stringify(this.companyHistory))
          // 删除不必要元素
          delete index.companyHistory.formRules
        }
        // 优势框组
        if (floors.indexOf('advantage') > -1) {
          index.advantage = JSON.parse(JSON.stringify(this.advantage))
          // 删除不必要元素
          delete index.advantage.formRules
        }
        // 友情链接
        if (floors.indexOf('friendLink') > -1) {
          index.friendLink = JSON.parse(JSON.stringify(this.friendLink))
          index.friendLink.list = this.FriendLinkMethods().beforeSave()
          // 删除不必要元素
          delete index.friendLink.formRules
        }
        // 保存接口
        if (await ConfigModel.editEdit(CONFIG_NAME_RESOURCE_PLATFORM, "index", index)) {
          msg_success("保存成功")
          // setTimeout(() => {
          //   window.location.href = '#/resourcePlatform/index';
          //   window.location.reload()
          // }, 1000)
        }
      } else {// 点击了我再想想或关闭按钮

      }
    },
    // 点击取消按钮
    async clickCancelBtn() {
      if (await msg_confirm_choose("是否取消更改？", "取消更改", "我再想想", '确认取消') === "right") {
        msg_success("取消成功")
        setTimeout(() => {
          window.location.href = '#/resourcePlatform/index';
          window.location.reload()
        }, 1000)
      }
    },
  }
}
</script>

<style>

</style>
<style scoped lang="scss">
// 解决新建后排版跳动问题 上传器大小改变后要改height值
.friendLink-container .erp-uploader-one-pic {
  height: 60px !important;
}

.friendLink-container .friendLink-name {
  width: 200px;
  margin: 0 auto;
  display: block;
  margin-top: 40px;
  text-align: center
}
.friendLink-container .friendLink-url {
  width: 200px;
  margin: 0 auto;
  display: block;
  margin-top: 10px;
  text-align: center
}
</style>
