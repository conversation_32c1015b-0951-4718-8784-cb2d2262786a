<template>
  <div class="app-container">
    <!--基础信息-->
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>基础信息</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="baseInfo.cardShow=!baseInfo.cardShow">
          <i class="el-icon-arrow-up" v-show="baseInfo.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!baseInfo.cardShow"></i>
          {{ baseInfo.cardShow ? '收起' : '展开' }}
        </el-button>
      </div>
      <div class="card-container" v-show="baseInfo.cardShow">
        <a href="#" name="baseInfo"></a>
        <el-form label-width="120px">
          <el-form-item label="网站标题：" prop="companyName">
            <el-input v-model="baseInfo.webName" placeholder="请输入网站标题" style="width: 500px">
            </el-input>
          </el-form-item>
          <el-form-item label="网站小标题：" prop="companyName">
            <el-input v-model="baseInfo.webSubName" placeholder="请输网站小标题" style="width: 500px">
            </el-input>
          </el-form-item>
          <el-form-item label="联系手机号：" prop="beiAnNumber">
            <el-input v-model="baseInfo.phoneNumber" placeholder="请输入手机号" style="width: 500px">
            </el-input>
          </el-form-item>
          <el-form-item label="联系微信二维码:">
            <erp-uploader-one-pic style="margin-right: 30px;width: 200px;" :img-in="baseInfo.weixinQrCode"
                                  :uploader-id="'weixinQrCode'"
                                  uploader-title="图片" :uploader-size="[200,200]" :pixel-limit="[500,500]"
                                  :size-limit="400"
                                  @uploadSuccess="data=>fileUpload(data,baseInfo)"
                                  @afterDelete="data=>fileDelete(data,baseInfo)"></erp-uploader-one-pic>
          </el-form-item>
          <el-form-item label="公众号二维码:">
            <erp-uploader-one-pic style="margin-right: 30px;width: 200px;" :img-in="baseInfo.officialQrCode"
                                  :uploader-id="'officialQrCode'"
                                  uploader-title="图片" :uploader-size="[200,200]" :pixel-limit="[500,500]"
                                  :size-limit="400"
                                  @uploadSuccess="data=>fileUpload(data,baseInfo)"
                                  @afterDelete="data=>fileDelete(data,baseInfo)"></erp-uploader-one-pic>
          </el-form-item>
          <el-form-item label="公司名称：" prop="companyName">
            <el-input v-model="baseInfo.companyName" placeholder="请输入公司名称" style="width: 500px">
            </el-input>
          </el-form-item>
          <el-form-item label="公司地址：" prop="companyAddress">
            <el-input v-model="baseInfo.companyAddress" placeholder="请输入公司地址" style="width: 500px">
            </el-input>
          </el-form-item>
          <el-form-item label="备案号：" prop="beiAnNumber">
            <el-input v-model="baseInfo.beiAnNumber" placeholder="请输入备案号" style="width: 500px">
            </el-input>
          </el-form-item>
          <el-form-item label="收藏页背景图:">
            <erp-uploader-one-pic style="margin-right: 30px;width: 200px;" :img-in="baseInfo.userFavBackgroundImg"
                                  :uploader-id="'userFavBackgroundImg'"
                                  uploader-title="图片" :uploader-size="[200,200]" :pixel-limit="[1920,750]"
                                  :size-limit="1024"
                                  @uploadSuccess="data=>fileUpload(data,baseInfo)"
                                  @afterDelete="data=>fileDelete(data,baseInfo)"></erp-uploader-one-pic>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <!--按钮组-->
    <div class="bottom-button-container flex flex-center">
      <el-button type="primary" @click="clickSaveBtn()">保 存</el-button>
      <el-button @click="clickCancelBtn">取 消</el-button>
    </div>
  </div>
</template>

<script>
/**
 * 首页管理
 */
import {BaseUploadModel} from "@/model/BaseUploadModel";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import {msg_confirm, msg_confirm_choose, msg_err, msg_success} from "@/utils/ele_component";
import {ConfigModel} from "@/model/erp/ConfigModel";
import {CONFIG_NAME_RESOURCE_PLATFORM} from "@/model/ConfigModel";
import {VEAModel} from "@/model/VEAModel";
import $ from "jquery"
import elDragDialog from "@/directive/el-drag-dialog";

export default {
  name: "systemSetting",
  directives: {
    elDragDialog
  },
  components: {erpUploaderOnePic},
  data() {
    return {
      uploadRequest: BaseUploadModel.uploadRequest,
      system: {},
      // 信息管理
      baseInfo: {
        webName:"",
        webSubName:"",
        phoneNumber:"",
        weixinQrCode:"",
        companyName:"",
        beiAnNumber:"",
        companyAddress:"",
        cardShow: true,
      },

      // 错误跳转标记,用于跳转到最开始错误
      errAJumped: false,
    }
  },
  mounted() {
    // 读取配置信息
    this.getConfig()
    // 底部按钮组大小监听
    VEAModel.bottomButtonContainerWidthChange()
  },
  methods: {
    // 通用-楼层在前台显示和隐藏
    async onFloorChangeShow(v, target) {
      if (v) {
        if (!await msg_confirm("是否确认开启该楼层，开启该楼层后，该楼层将在官网上可见")) {
          this.$set(target, "show", !v)
          this.$forceUpdate()
        }
      } else {
        if (!await msg_confirm("是否确认隐藏该楼层，隐藏该楼层后，该楼层将在官网上不可见")) {
          this.$set(target, "show", !v)
          this.$forceUpdate()
        }
      }
    },
    // 读取配置
    async getConfig() {
      let data = await ConfigModel.getEdit(CONFIG_NAME_RESOURCE_PLATFORM, "system")
      let system = JSON.parse(data)
      this.$set(this,"system",system)
      // 设置各项配置
      let baseInfo = Object.assign(this.baseInfo, system.baseInfo)
      this.$set(this,"baseInfo",baseInfo)
    },
    // 通用-文件上传成功
    fileUpload(params, target, formRef) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 通用-文件删除
    fileDelete(params, target, formRef) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 点击保存按钮
    async clickSaveBtn(floors) {
      if (!floors) {
        floors = ['baseInfo']
      }
      // 每个项目的合法性检测
      this.errAJumped = false
      if (true) {
        // 公司信息检测
        if (floors.indexOf('baseInfo') > -1&&false) {
          this.$refs["baseInfo"].validate(validate => {
            if (validate) {

            } else {
              if (!this.errAJumped) {
                this.baseInfo.cardShow === false ? this.baseInfo.cardShow = true : ""
                // location.hash = "baseInfo";
                this.errAJumped = true
              }
            }
          })
        }

        if (this.errAJumped) { // 表单验证有误
          setTimeout(() => {
            // 跳转到第一个错误处
            let firstErrEl = $("body .el-form-item__error")
            let top = firstErrEl.offset().top - 200
            // 跳转到指定位置
            document.body.scrollTop = top;
            document.documentElement.scrollTop = top;
          }, 600)
          return
        }
      }
      //
      let saveButtonChoose = await msg_confirm_choose("是否确认更改？", "确认保存", "我再想想", '确认提交')
      if (saveButtonChoose === "right") {
        let system = this.system
        // 保存前调用
        if (floors.indexOf('baseInfo') > -1) {
          system.baseInfo = JSON.parse(JSON.stringify(this.baseInfo))
          // 删除不必要元素
          delete system.baseInfo.formRules
        }
        // 保存接口
        if (await ConfigModel.editEdit(CONFIG_NAME_RESOURCE_PLATFORM, "system", system)) {
          msg_success("保存成功")
          // setTimeout(() => {
          //   window.location.href = '#/resourcePlatform/systemSetting';
          //   window.location.reload()
          // }, 1000)
        }
      } else {// 点击了我再想想或关闭按钮

      }
    },
    // 点击取消按钮
    async clickCancelBtn() {
      if (await msg_confirm_choose("是否取消更改？", "取消更改", "我再想想", '确认取消') === "right") {
        msg_success("取消成功")
        setTimeout(() => {
          window.location.href = '#/resourcePlatform/systemSetting';
          window.location.reload()
        }, 1000)
      }
    },
    // 公司信息相关
    infoMethods() {
      let $this = this;
      return {

      }
    },
  }
}
</script>

<style>

</style>
<style scoped lang="scss">
.app-container {
  padding-bottom: 150px;
}

</style>
