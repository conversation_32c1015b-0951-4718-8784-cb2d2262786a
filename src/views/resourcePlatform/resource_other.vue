<template>
  <div class="app-container">
    <el-tabs>
      <el-tab-pane label="其它视频">
        <!--筛选-->
        <div class="header-container clearfix" style="margin-bottom: 15px">
          <div class="filter-list">
            <div class="flex flex-start flex-wrap" style="margin-top: 10px">
              <!--资源名称-自动搜索-->
              <div class="search-item">
                <span class="title">{{ filterList.name.label }}：</span>
                <el-input
                  onKeypress="javascript:if(event.keyCode == 32 || event.keyCode == 43)event.returnValue = false;"
                  v-model="filterList.name.value"
                  :style="filterList.name.width?`width:${filterList.name.width}`:'width:auto;'"

                  :placeholder="filterList.name.placeholder">
                </el-input>
                <!--
                :fetch-suggestions="(query,callback)=>filterList.taskName.searchFunction(query,callback)"
                -->
              </div>
            </div>
            <!--  搜索按钮  -->
            <div class="search-bottom flex flex-between">
              <div class="flex flex-start" style="color: #666;font-size: 15px">
                <div class="flex flex-start">
                  <span>资源总数：{{ lists.pages.totalElements }}</span>
                </div>
              </div>

              <div class="flex flex-start">
                <el-button class="button" icon="el-icon-search"
                           @click="listsMethods().clickSearchBtn()"
                           type="success">查询
                </el-button>
                <el-button class="button" plain icon="el-icon-delete"
                           @click="listsMethods().clickCleanBtn()"
                           type="default">重置
                </el-button>
                <el-button class="button" plain icon="el-icon-refresh"
                           @click="listsMethods().clickRefreshBtn()"
                           type="default">刷新页面
                </el-button>
              </div>
              <div class="tool-btn">
                <el-button type="success" @click="listsMethods().clickAddBtn()"
                >新增其它视频
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <div class="tip-box" style="margin-bottom: 10px">
          <!--          <div>封面图尺寸:340x190</div>-->
        </div>
        <el-table :data="lists.list" border fit>
          <el-table-column :label="'标题'" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.info.name"></el-input>
            </template>
          </el-table-column>
          <el-table-column :label="'副标题'" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.info.subTitle"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="视频" align="center">
            <template slot-scope="scope">
              <erp-uploader-one-video style="margin-right: 30px" :video-in="scope.row.info.video"
                                      :show-des="false"
                                      upload-target="zy0"
                                      :uploader-id="'video'"
                                      :uploader-size="[200,200]" :pixel-limit="[1920,750]"
                                      :size-limit="1204800"
                                      @afterDelete="data=>fileDelete(data,lists.list[scope.$index].info)"
                                      @uploadSuccess="data=>fileUpload(data,lists.list[scope.$index].info)"></erp-uploader-one-video>
            </template>
          </el-table-column>
          <el-table-column :label="'是否显示'" align="center" width="120">
            <template slot-scope="scope">
              <el-select v-model="scope.row.info.show">
                <el-option label="显示" :value="true"></el-option>
                <el-option label="隐藏" :value="false"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column :label="'操作'" align="center" width="80">
            <template slot-scope="scope">
              <el-button :disabled="scope.$index===0" size="small" type="text"
                         @click="listsMethods().clickMoveUpBtn(scope.$index)">上移
              </el-button>
              <el-button size="mini" type="danger" @click="listsMethods().clickDeleteBtn(scope.$index,scope.row)">删除
              </el-button>
              <el-button :disabled="scope.$index===lists.list.length-1" size="small" type="text"
                         @click="listsMethods().clickMoveDownBtn(scope.$index)">下移
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!--列表分页-->
        <div class="pagination-container">
          <el-pagination background @current-change="(number)=>listsMethods().pageChange(number)"
                         :current-page.sync="lists.pages.number"
                         :page-size.sync="lists.pages.size"

                         layout="total,prev, pager, next,jumper,sizes"
                         :total="lists.pages.totalElements"
                         @size-change="(size)=>listsMethods().pageLimitChange(size)"
                         :page-count="lists.pages.totalPages">
          </el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane label="设置">
        <div class="flex flex-start flex-wrap" style="align-items: flex-start">
          <el-card class="box-card" style="width: 500px;margin-bottom: 30px;margin-right: 30px">
            <div slot="header" class="clearfix">
              <span>页面信息设置</span>
            </div>
            <div>
              <el-form label-width="120">
                <el-form-item label="页面顶部标题">
                  <el-input v-model="resource_other.headerInfo.title"></el-input>
                </el-form-item>
                <el-form-item label="页面顶部介绍">
                  <el-input v-model="resource_other.headerInfo.des" type="textarea"></el-input>
                </el-form-item>
              </el-form>
            </div>
          </el-card>
          <el-card class="box-card" style="width: 500px;margin-bottom: 30px;margin-right: 30px" v-if="false">
            <div slot="header" class="clearfix">
              <span>摄影支持信息</span>
            </div>
            <div>
              <el-form label-width="120">
                <el-form-item label="介绍大图">
                  <erp-uploader-one-pic style="margin-right: 30px" :img-in="resource_other.photograph.bigImage"
                                        :uploader-id="'bigImage'"
                                        uploader-title="介绍大图" :uploader-size="[200,200]" :pixel-limit="[800,695]"
                                        :size-limit="2000"
                                        @uploadSuccess="data=>fileUpload(data,resource_other.photograph)"
                                        @afterDelete="data=>fileDelete(data,resource_other.photograph)"></erp-uploader-one-pic>
                </el-form-item>
                <el-form-item label="多样化服务文字">
                  <el-input v-model="resource_other.photograph.text1" placeholder="标题"></el-input>
                  <el-input v-model="resource_other.photograph.text1_1" placeholder="文字"></el-input>
                </el-form-item>
                <el-form-item label="一体化服务文字">
                  <el-input v-model="resource_other.photograph.text2" placeholder="标题"></el-input>
                  <el-input v-model="resource_other.photograph.text2_1" placeholder="文字"></el-input>
                </el-form-item>
                <el-form-item label="强大团队文字">
                  <el-input v-model="resource_other.photograph.text3" placeholder="标题"></el-input>
                  <el-input v-model="resource_other.photograph.text3_1" placeholder="文字"></el-input>
                </el-form-item>
              </el-form>
            </div>
          </el-card>
          <el-card class="box-card" style="width: 100%;margin-bottom: 30px" v-if="false">
            <div slot="header" class="clearfix">
              <span>会议介绍视频列表</span>
            </div>
            <div class="clearfix" style="margin-bottom: 10px">
              <div class="fl">
                <span>是否显示整个会议视频列表：</span>
                <el-select v-model="resource_other.meetingVideosShow">
                  <el-option label="显示" :value="true"></el-option>
                  <el-option label="隐藏" :value="false"></el-option>
                </el-select>
              </div>
              <el-button type="success" class="fr" @click="SettingMethods().clickAddVideoBtn()">新增视频</el-button>
            </div>
            <el-table :data="resource_other.meetingVideos" border fit>
              <el-table-column :label="'视频'" align="center">
                <template slot-scope="scope">
                  <erp-uploader-one-video style="margin-right: 30px" :video-in="scope.row.video"
                                          :show-des="false"
                                          upload-target="zy0"
                                          :uploader-id="'video'"
                                          :uploader-size="[200,200]" :pixel-limit="[1920,750]"
                                          :size-limit="120480"
                                          @afterDelete="data=>fileDelete(data,resource_other.meetingVideos[scope.$index])"
                                          @uploadSuccess="data=>fileUpload(data,resource_other.meetingVideos[scope.$index])"></erp-uploader-one-video>
                </template>
              </el-table-column>
              <el-table-column :label="'视频名称'" align="center">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.name"></el-input>
                </template>
              </el-table-column>
              <el-table-column :label="'是否显示'" align="center" width="120">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.show">
                    <el-option label="显示" :value="true"></el-option>
                    <el-option label="隐藏" :value="false"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column :label="'操作'" align="center" width="80">
                <template slot-scope="scope">
                  <el-button size="mini" type="danger"
                             @click="SettingMethods().clickDeleteVideoBtn(scope.$index,scope.row)">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
          <el-card class="box-card" style="width: 100%;margin-bottom: 30px" v-if="false">
            <div slot="header" class="clearfix">
              <span>会议支持列表</span>
            </div>
            <div class="clearfix" style="margin-bottom: 10px">
              <el-button type="success" class="fr" @click="SettingMethods().clickAddMeetingBtn()">新增项目</el-button>
            </div>
            <el-table :data="resource_other.meeting" border fit>
              <el-table-column :label="'图片'" align="center">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.title"></el-input>
                </template>
              </el-table-column>
              <el-table-column :label="'介绍文字'" align="center">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.des"></el-input>
                </template>
              </el-table-column>
              <el-table-column :label="'图片'" align="center">
                <template slot-scope="scope">
                  <erp-uploader-one-pic style="margin-right: 30px" :img-in="scope.row.image"
                                        :uploader-id="'image'"
                                        uploader-title="图片" :uploader-size="[200,200]" :pixel-limit="[360,235]"
                                        :size-limit="2000"
                                        @uploadSuccess="data=>fileUpload(data,scope.row)"
                                        @afterDelete="data=>fileDelete(data,scope.row)"></erp-uploader-one-pic>
                </template>
              </el-table-column>
              <el-table-column :label="'操作'" align="center" width="80">
                <template slot-scope="scope">
                  <el-button size="mini" type="danger"
                             @click="SettingMethods().clickDeleteMeetingBtn(scope.$index,scope.row)">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>

      </el-tab-pane>

    </el-tabs>
    <!--按钮组-->
    <div class="bottom-button-container flex flex-center">
      <el-button type="primary" @click="clickSaveBtn()">保 存</el-button>
    </div>
  </div>
</template>

<script>
import {BaseUploadModel} from "@/model/BaseUploadModel";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import {msg_confirm, msg_confirm_choose, msg_err, msg_success} from "@/utils/ele_component";
import {ConfigModel} from "@/model/erp/ConfigModel";
import {CONFIG_NAME_RESOURCE_PLATFORM} from "@/model/ConfigModel";
import {VEAModel} from "@/model/VEAModel";
import elDragDialog from "@/directive/el-drag-dialog";
import resourceExperimentTable from "@/views/resourcePlatform/components/resourceExperimentTable.vue";
import {ResourcePlatformModel} from "@/model/erp/ResourcePlatformModel";
import {find_obj_from_arr_by_id} from "@/utils/common";
import erpUploaderOneVideo from "@/views/components/erpUploaderOneVideo.vue";

export default {
  name: "resourceShortMovie",
  directives: {
    elDragDialog
  },
  components: {erpUploaderOneVideo, erpUploaderOnePic, resourceExperimentTable},
  data() {
    return {
      uploadRequest: BaseUploadModel.uploadRequest,
      resource_other: {
        meetingVideos:[],
        headerInfo: {
          title: "",
          des: ""
        },
        photograph: {
          bigImage: "",
          text1: "",
          text1_1: "",
          text2: "",
          text2_1: "",
          text3: "",
          text3_1: "",
        },
        meeting: []
      },
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        sort: "",
        pages: {
          size: 10
        },
      },
      filterList: {
        // 名称
        name: {
          label: '名称',
          placeholder: "",
          key: 'info.name',
          type: "search",
          width: "270px",
          value: '',
          data: [],
          dataObject: [],
          dataOrigin: [],// 存储数据库返回的默认列表
          searchFunction: async function (query) {
            // todo 任务名称搜索
          },
          async change(v) {
            // todo 任务名称变动
          },
          format: function (value) {
            return {
              '$regex': `.*${value}.*`
            }
          }
        },
      },

      // 错误跳转标记,用于跳转到最开始错误
      errAJumped: false,
    }
  },
  mounted() {
    // 读取配置信息
    this.getConfig()
    // 底部按钮组大小监听
    VEAModel.bottomButtonContainerWidthChange()
  },
  methods: {
    // 读取配置
    async getConfig() {
      let data = await ConfigModel.getEdit(CONFIG_NAME_RESOURCE_PLATFORM, "resource_other")
      let resource_other = JSON.parse(data)
      this.$set(this, "resource_other", resource_other)
      // 设置各项配置
      // 获取列表
      this.listsMethods().getList(0, this.lists.pages.size, {})
    },
    // 通用-文件上传成功
    fileUpload(params, target, formRef) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 通用-文件删除
    fileDelete(params, target, formRef) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 点击保存按钮
    async clickSaveBtn() {
      let saveButtonChoose = await msg_confirm_choose("是否确认更改？", "确认保存", "我再想想", '确认提交')
      // 保存实验列表里的每个实验
      this.lists.list.forEach(li => {
        ResourcePlatformModel.addOrEdit(li)
      })

      if (saveButtonChoose === "right") {
        let resource_other = this.resource_other
        // 保存接口
        if (await ConfigModel.editEdit(CONFIG_NAME_RESOURCE_PLATFORM, "resource_other", resource_other)) {
          msg_success("保存成功")
        }
      }
    },
    // 列表
    listsMethods() {
      let $this = this;
      return {
        // 点击搜索按钮
        clickSearchBtn() {
          let query = {};
          // 遍历searchFilter
          Object.keys($this.filterList).forEach(key => {
            let li = $this.filterList[key];
            if (li.hasOwnProperty("key")) {
              if (li.value === 0 || li.value) {
                if (li.format) {
                  if (li.type === "filter") {
                    let formatResult = li.format(li.value)
                    if (formatResult) {
                      if (formatResult.hasOwnProperty("$and") && query.hasOwnProperty("$and")) {// 合并and
                        query["$and"] = formatResult["$and"].concat(query["$and"])
                      } else {
                        query = Object.assign(query, formatResult);
                      }
                    }
                  }
                  if (li.type === "search") {
                    query[li.key] = li.format(li.value);
                  }
                } else {
                  query[li.key] = li.value;
                }
              }
            }

          });
          $this.$set($this.lists, "query", query)
          this.getList(0, $this.lists.pages.size, query)
        },
        // 点击刷新页面按钮
        clickRefreshBtn() {
          window.location.reload()
        },
        // 点击清空搜索按钮
        clickCleanBtn() {
          let query = {};
          // 遍历searchFilter
          Object.keys($this.filterList).forEach(key => {
            let li = $this.filterList[key];
            if (li.hasOwnProperty("key")) {
              if (li.value === 0 || li.value) {
                if (Array.isArray(li.value)) {
                  li.value = []
                } else {
                  li.value = ""
                }
              }
            }

          });
          $this.$set($this.lists, "query", query)
          this.getList(0, $this.lists.pages.size, query)
        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let sort = "sortOrder,desc&sort=createTime,desc";
          [$this.lists.list, $this.lists.pages] = await ResourcePlatformModel.getPageList("otherVideo", page - 1, size, sort, query)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter(type) {
        },
        // 工厂-新建实验
        async factoryCreateNewExperiment() {
          return await ResourcePlatformModel.addOrEdit({
            type: "otherVideo",
            info: {}
          })
        },
        // 点击-新建按钮
        async clickAddBtn() {
          let experiment = await this.factoryCreateNewExperiment()
          $this.lists.list.unshift(experiment)
          //this.clickRefreshBtn()
        },
        // 点击-删除按钮
        async clickDeleteBtn(index) {
          if (await msg_confirm(`确认要删除该项吗？`)) {
            let entity = $this.lists.list[index]
            entity.deleted = 1
            if (await ResourcePlatformModel.addOrEdit(entity)) {
              msg_success("删除成功！")
              this.getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
            } else {
              msg_err("删除失败！")
            }
          }
        },
        // 点击-上移按钮
        async clickMoveUpBtn(index) {
          let edit = $this.lists.list[index]
          let $index = parseInt(find_obj_from_arr_by_id("resourcePlatformId", edit.resourcePlatformId, $this.lists.list)[0])
          let one = $this.lists.list[$index - 1];
          let oneSortOrder = one.sortOrder
          one.sortOrder = edit.sortOrder
          edit.sortOrder = oneSortOrder
          await ResourcePlatformModel.addOrEdit(edit)
          await ResourcePlatformModel.addOrEdit(one)
          this.getList($this.lists.pages.page, $this.lists.pages.size, $this.lists.query)
        },
        // 点击-下移按钮
        async clickMoveDownBtn(index) {
          let edit = $this.lists.list[index]
          let $index = parseInt(find_obj_from_arr_by_id("resourcePlatformId", edit.resourcePlatformId, $this.lists.list)[0])
          let one = $this.lists.list[$index + 1];
          let oneSortOrder = one.sortOrder
          one.sortOrder = edit.sortOrder
          edit.sortOrder = oneSortOrder
          await ResourcePlatformModel.addOrEdit(edit)
          await ResourcePlatformModel.addOrEdit(one)
          this.getList($this.lists.pages.page, $this.lists.pages.size, $this.lists.query)
        },
      }
    },
    // 设置相关方法
    SettingMethods() {
      let $this = this;
      return {
        // 新增顶部视频
        clickAddVideoBtn() {
          $this.resource_other.meetingVideos.push({
            id: new Date().getTime() + ""
          })
        },
        // 点击删除顶部视频按钮
        async clickDeleteVideoBtn(index) {
          if (await msg_confirm("确定要删除吗？")) {
            $this.resource_other.meetingVideos.splice(index, 1)
          }
        },
        // 新增会议
        clickAddMeetingBtn() {
          $this.resource_other.meeting.push({
            id: new Date().getTime() + ""
          })
        },
        // 点击删除会议
        async clickDeleteMeetingBtn(index) {
          if (await msg_confirm("确定要删除吗？")) {
            $this.resource_other.meeting.splice(index, 1)
          }
        },
      }
    }
  }
}
</script>

<style>

</style>
<style scoped lang="scss">
.app-container {
  padding-bottom: 150px;
}

// 筛选和搜索列表
.filter-list {
  .title {
    font-size: 15px;
    color: #999;
  }

  .search-item, .filter-item {
    margin-right: 15px;

    span.title {
      font-size: 14px;
      margin-right: 10px;
    }
  }

  .select-append {
    float: left;
    margin-right: 5px;

    .select {
      input {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }

    .button {
      margin-left: -6px;
      background-color: #f5f7fa;
      color: #939b9f;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }

  .search-bottom {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}
</style>
