<template>
  <div class="app-container">
    <div class="card-container freeTrial-container">
      <div class="flex flex-start">
        <ul class="freeTrialList clearfix">
          <li v-for="item in freeTrial.list" style="list-style-type: none;float:left;margin-bottom: 20px;margin-right: 50px"
              v-if="item.img">
            <erp-uploader-one-pic :key="item.id"
                                  style="margin-right:320px;margin-bottom: 20px"
                                  :img-in="item.img"
                                  :uploader-id="'freeTrial'+item.id"
                                  @afterDelete="data=>FreeTrailMethods().afterDelete(data)"
                                  :show-des="false"
                                  :uploader-size="[270,165]" :pixel-limit="[270,165]"
                                  :size-limit="200"
                                  @uploadSuccess="data=>FreeTrailMethods().fileUploadSuccess(data)"></erp-uploader-one-pic>
            <el-input v-model="item.name" class="freeTrial-name" placeholder="名称" style="margin-bottom: 10px"></el-input>
            <el-input v-model="item.url" class="freeTrial-url" placeholder="跳转链接" style="margin-bottom: 10px"></el-input>
            <el-input v-model="item.helpUrl" class="freeTrial-url" placeholder="操作手册链接"></el-input>
          </li>
          <erp-uploader-one-pic :key="freeTrial.list[freeTrial.list.length-1].id"
                                style="margin-right: 30px;margin-bottom: 20px;float:left"
                                :img-in="freeTrial.list[freeTrial.list.length-1].img"
                                :uploader-id="'freeTrial'+freeTrial.list[freeTrial.list.length-1].id"
                                @afterDelete="data=>FreeTrailMethods().afterDelete(data)"
                                :show-des="false"
                                :uploader-size="[270,165]" :pixel-limit="[270,165]"
                                :size-limit="200"
                                @uploadSuccess="data=>FreeTrailMethods().fileUploadSuccess(data)"></erp-uploader-one-pic>
        </ul>

      </div>
    </div>
    <!--按钮组-->
    <div class="bottom-button-container flex flex-center">
      <el-button type="primary" @click="clickSaveBtn()">保 存</el-button>
      <el-button @click="clickCancelBtn">取 消</el-button>
    </div>
  </div>
</template>

<script>
/**
 * 首页管理
 */
import {BaseUploadModel} from "@/model/BaseUploadModel";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import {msg_confirm, msg_confirm_choose, msg_err, msg_success} from "@/utils/ele_component";
import {ConfigModel} from "@/model/erp/ConfigModel";
import {CONFIG_NAME_RESOURCE_PLATFORM} from "@/model/ConfigModel";
import {VEAModel} from "@/model/VEAModel";
import $ from "jquery"
import elDragDialog from "@/directive/el-drag-dialog";
import Sortable from "sortablejs";
import {find_obj_from_arr_by_id, randomNumber} from "@/utils/common";

export default {
  name: "freeTrial",
  directives: {
    elDragDialog
  },
  components: {erpUploaderOnePic},
  data() {
    return {
      uploadRequest: BaseUploadModel.uploadRequest,
      freeTrial: {
        list:[
          {
            id:new Date().getTime()
          }
        ]
      },

      // 错误跳转标记,用于跳转到最开始错误
      errAJumped: false,
    }
  },
  mounted() {
    // 读取配置信息
    this.getConfig()
    // 底部按钮组大小监听
    VEAModel.bottomButtonContainerWidthChange()
  },
  methods: {
    // 读取配置
    async getConfig() {
      let data = await ConfigModel.getEdit(CONFIG_NAME_RESOURCE_PLATFORM, "freeTrial")
      let freeTrial = JSON.parse(data)
      this.$set(this, "freeTrial", freeTrial)

      setTimeout(() => {
        Sortable.create(document.querySelector('.freeTrialList'))
        this.FreeTrailMethods().setFloorSort()
      }, 1000)
    },
    // 通用-文件上传成功
    fileUpload(params, target, formRef) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 通用-文件删除
    fileDelete(params, target, formRef) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 点击保存按钮
    async clickSaveBtn(floors) {
      if (!floors) {
        floors = ['baseInfo']
      }
      // 每个项目的合法性检测
      this.errAJumped = false
      if (true) {
        let saveButtonChoose = await msg_confirm_choose("是否确认更改？", "确认保存", "我再想想", '确认提交')
        if (saveButtonChoose === "right") {
          let freeTrial = JSON.parse(JSON.stringify(this.freeTrial))
          freeTrial.list=this.FreeTrailMethods().beforeSave();
          // 保存接口
          if (await ConfigModel.editEdit(CONFIG_NAME_RESOURCE_PLATFORM, "freeTrial", freeTrial)) {
            msg_success("保存成功")
            // setTimeout(() => {
            //   window.location.href = '#/resourcePlatform/systemSetting';
            //   window.location.reload()
            // }, 1000)
          }
        } else {// 点击了我再想想或关闭按钮

        }
      }
    },
    // 点击取消按钮
    async clickCancelBtn() {
      if (await msg_confirm_choose("是否取消更改？", "取消更改", "我再想想", '确认取消') === "right") {
        msg_success("取消成功")
        setTimeout(() => {
          window.location.href = '#/resourcePlatform/systemSetting';
          window.location.reload()
        }, 1000)
      }
    },
    // 免费试用相关
    FreeTrailMethods() {
      let $this = this;
      return {
        // 设置拖动排序
        setFloorSort() {
          const el = document.querySelectorAll(
            ".freeTrialList"
          )[0];
          Sortable.create(el, {
            ghostClass: "sortable-ghost", // Class name for the drop placeholder,
            setData: function (dataTransfer) {
              dataTransfer.setData("Text", "");
              // to avoid Firefox bug
              // Detail see : https://github.com/RubaXa/Sortable/issues/1012
            },
            onEnd: (evt) => {
              const targetRow = $this.freeTrial.list.splice(evt.oldIndex, 1)[0];// 拖动的对象
              $this.freeTrial.list.splice(evt.newIndex, 0, targetRow);// 插入
              $this.$set($this.freeTrial, "list", $this.freeTrial.list)
            },
          });
        },
        fileUploadSuccess(data) {
          let id = Number(data[0].replace("freeTrial", ""))
          let index = find_obj_from_arr_by_id("id", id, $this.freeTrial.list)[0]
          if (!$this.freeTrial.list[index].hasOwnProperty('img')) {
            $this.freeTrial.list.push({
              id: new Date().getTime() + randomNumber(1, 1000),
            })
          }
          $this.$set($this.freeTrial.list[index], "img", data[1])
        },
        afterDelete(data) {
          let id = Number(data[0].replace("freeTrial", ""))
          let index = find_obj_from_arr_by_id("id", id, $this.freeTrial.list)[0];
          $this.freeTrial.list.splice(index, 1)
        },
        // 保存时调用 todo 也许不需要这样，拖动时应该可以直接改变list顺序
        beforeSave() {
          let list = []
          $("ul.freeTrialList img.img-show").each(function () {
            let url = $(this).attr("src")
            if (url) {
              let findOne=find_obj_from_arr_by_id("img", url, $this.freeTrial.list)[1]
              list.push({
                id: new Date().getTime() + randomNumber(1, 1000),
                img: url,
                name: findOne["name"],
                url:findOne["url"],
                helpUrl:findOne["helpUrl"],
              })
            }
          })
          list.push({
            id: new Date().getTime() + randomNumber(1, 1000),
          })
          return list;
        }
      }
    },
  }
}
</script>

<style>

</style>
<style scoped lang="scss">
.app-container {
  padding-bottom: 150px;
}

.freeTrail-container .friendLink-name {
  width: 270px;
  margin: 0 auto;
  display: block;
  margin-top: 40px;
  text-align: center
}
</style>
