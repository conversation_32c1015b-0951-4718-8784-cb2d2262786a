<template>
  <div class="app-container">
    <el-tabs>
      <el-tab-pane label="设置">
        <div class="flex flex-start flex-wrap" style="align-items: flex-start">
          <el-card class="box-card" style="width: 500px;margin-bottom: 30px;margin-right: 30px">
            <div slot="header" class="clearfix">
              <span>页面信息设置</span>
            </div>
            <div>
              <el-form label-width="120">
                <el-form-item label="页面顶部标题">
                  <el-input v-model="resource_redRide.headerInfo.title"></el-input>
                </el-form-item>
                <el-form-item label="页面顶部介绍">
                  <el-input v-model="resource_redRide.headerInfo.des" type="textarea"></el-input>
                </el-form-item>
              </el-form>
            </div>
          </el-card>
          <el-card class="box-card" style="width: 540px;margin-bottom: 30px">
            <div slot="header" class="clearfix">
              <span>页面顶部视频</span>
            </div>
            <div class="clearfix" style="margin-bottom: 10px">
              <el-button type="success" class="fr" @click="SettingMethods().clickAddVideoBtn()">新增视频</el-button>
            </div>
            <el-table :data="resource_redRide.headerVideos" border fit>
              <el-table-column :label="'视频'" align="center">
                <template slot-scope="scope">
                  <erp-uploader-one-video style="margin-right: 30px" :video-in="scope.row.video"
                                          upload-target="zy0"
                                          :show-des="false"
                                          :uploader-id="'video'"
                                          :uploader-size="[200,200]" :pixel-limit="[1920,750]"
                                          :size-limit="120480"
                                          @afterDelete="data=>fileDelete(data,resource_redRide.headerVideos[scope.$index])"
                                          @uploadSuccess="data=>fileUpload(data,resource_redRide.headerVideos[scope.$index])"></erp-uploader-one-video>
                </template>
              </el-table-column>
              <el-table-column :label="'操作'" align="center" width="80">
                <template slot-scope="scope">
                  <el-button size="mini" type="danger"
                             @click="SettingMethods().clickDeleteVideoBtn(scope.$index,scope.row)">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>

      </el-tab-pane>
    </el-tabs>
    <!--按钮组-->
    <div class="bottom-button-container flex flex-center">
      <el-button type="primary" @click="clickSaveBtn()">保 存</el-button>
    </div>
  </div>
</template>

<script>
import {BaseUploadModel} from "@/model/BaseUploadModel";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import {msg_confirm, msg_confirm_choose, msg_err, msg_success} from "@/utils/ele_component";
import {ConfigModel} from "@/model/erp/ConfigModel";
import {CONFIG_NAME_RESOURCE_PLATFORM} from "@/model/ConfigModel";
import {VEAModel} from "@/model/VEAModel";
import elDragDialog from "@/directive/el-drag-dialog";
import resourceExperimentTable from "@/views/resourcePlatform/components/resourceExperimentTable.vue";
import {ResourcePlatformModel} from "@/model/erp/ResourcePlatformModel";
import {find_obj_from_arr_by_id} from "@/utils/common";
import erpUploaderOneVideo from "@/views/components/erpUploaderOneVideo.vue";

export default {
  name: "resourceRedRide",
  directives: {
    elDragDialog
  },
  components: {erpUploaderOneVideo, erpUploaderOnePic, resourceExperimentTable},
  data() {
    return {
      uploadRequest: BaseUploadModel.uploadRequest,
      resource_redRide: {
        headerInfo: {
          title: "",
          des: ""
        },
        headerVideos: []
      },

      // 错误跳转标记,用于跳转到最开始错误
      errAJumped: false,
    }
  },
  mounted() {
    // 读取配置信息
    this.getConfig()
    // 底部按钮组大小监听
    VEAModel.bottomButtonContainerWidthChange()
  },
  methods: {
    // 读取配置
    async getConfig() {
      let data = await ConfigModel.getEdit(CONFIG_NAME_RESOURCE_PLATFORM, "resource_redRide")
      let resource_redRide = JSON.parse(data)
      this.$set(this, "resource_redRide", resource_redRide)
      // 设置各项配置
    },
    // 通用-文件上传成功
    fileUpload(params, target, formRef) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 通用-文件删除
    fileDelete(params, target, formRef) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 点击保存按钮
    async clickSaveBtn() {
      let saveButtonChoose = await msg_confirm_choose("是否确认更改？", "确认保存", "我再想想", '确认提交')

      if (saveButtonChoose === "right") {
        let resource_redRide = this.resource_redRide
        // 保存接口
        if (await ConfigModel.editEdit(CONFIG_NAME_RESOURCE_PLATFORM, "resource_redRide", resource_redRide)) {
          msg_success("保存成功")
        }
      }
    },
    // 设置相关方法
    SettingMethods() {
      let $this = this;
      return {
        // 新增顶部视频
        clickAddVideoBtn() {
          $this.resource_redRide.headerVideos.push({
            id: new Date().getTime() + ""
          })
        },
        // 点击删除顶部视频按钮
        async clickDeleteVideoBtn(index) {
          if (await msg_confirm("确定要删除吗？")) {
            $this.resource_redRide.headerVideos.splice(index, 1)
          }
        },
      }
    }
  }
}
</script>

<style>

</style>
<style scoped lang="scss">
.app-container {
  padding-bottom: 150px;
}

// 筛选和搜索列表
.filter-list {
  .title {
    font-size: 15px;
    color: #999;
  }

  .search-item, .filter-item {
    margin-right: 15px;

    span.title {
      font-size: 14px;
      margin-right: 10px;
    }
  }

  .select-append {
    float: left;
    margin-right: 5px;

    .select {
      input {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }

    .button {
      margin-left: -6px;
      background-color: #f5f7fa;
      color: #939b9f;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }

  .search-bottom {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}
</style>
