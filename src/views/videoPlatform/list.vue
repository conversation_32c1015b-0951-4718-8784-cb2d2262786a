<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success" @click="ListMethods().clickAddBtn()">新增视频
          </el-button>
        </div>
      </div>
    </div>
    <!--列表-->
    <el-table class="lists" :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="视频标题" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.info.title }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否上线" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.info.on ? '上线' : '下线' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column label="链接地址" align="center">
        <template slot-scope="scope">
          <span style="margin-right: 10px">http://video.cdzyhd.com?id={{ scope.row.generalInfoId }}</span>
          <a :href="'http://video.cdzyhd.com?id='+scope.row.generalInfoId" target="_blank">打开</a>
        </template>
      </el-table-column>
      <el-table-column label="浏览量" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.info.vn }}</span>
        </template>
      </el-table-column>
      <el-table-column label="完播量" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.info.cn }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="300"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="success" size="mini" round
                     style="margin-top: 10px;"
                     @click="ListMethods().clickEditBtn(scope.row,scope.$index)" :loading="scope.row.editLoading">详情
          </el-button>
          <el-button type="danger" size="mini" round
                     style="margin-top: 10px;"
                     @click="ListMethods().clickDeleteBtn(scope.row,scope.$index)" :loading="scope.row.editLoading">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--详情弹窗-->
    <el-dialog
      :title="entityInfo.title"
      :visible.sync="entityInfo.dialog"
      append-to-body
      width="1200px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
          <el-form-item label="上线:" prop="title">
            <el-select v-model="entityInfo.edit.info.on">
              <el-option :value="true" label="上线"></el-option>
              <el-option :value="false" label="下线"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="浏览量:" prop="title">
            <span>{{ entityInfo.edit.info.vn }}</span>
          </el-form-item>
          <el-form-item label="完播量:" prop="title">
            <span>{{ entityInfo.edit.info.cn }}</span>
          </el-form-item>
          <el-form-item label="视频标题:" prop="title">
            <el-input v-model="entityInfo.edit.info.title">
            </el-input>
          </el-form-item>
          <el-form-item label="视频简介:" prop="introduce">
            <el-input v-model="entityInfo.edit.info.introduce" type="textarea" autosize>
            </el-input>
          </el-form-item>
          <el-form-item label="视频上传：" prop="video">
            <div v-show="entityInfo.uploading">上传中，请稍后...</div>
            <erp-uploader-one-video style="margin-right: 30px" :video-in="entityInfo.edit.info.qiniuSrc"
                                    bucket="zyhd-video"
                                    uploader-id="qiniuSrc"
                                    :uploader-size="[300,200]" :pixel-limit="[1920,1080]"
                                    :size-limit="2048000"
                                    @beforeUpload="data=>beforeUpload(data)"
                                    @afterDelete="data=>fileDelete(data,entityInfo.edit.info)"
                                    @uploadSuccess="data=>fileUpload(data,entityInfo.edit.info)"></erp-uploader-one-video>
          </el-form-item>
          <el-form-item label="视频封面：" prop="poster">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="entityInfo.edit.info.poster"
                                    uploader-id="poster"
                                    :uploader-size="[200,100]" :pixel-limit="[1920,1080]"
                                    :size-limit="1024"
                                    @uploadSuccess="data=>fileUpload(data,entityInfo.edit.info)"
                                    @afterDelete="data=>fileDelete(data,entityInfo.edit.info)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label="微信分享标题：" prop="wxTitle">
            <el-input v-model="entityInfo.edit.info.wxTitle">
            </el-input>
          </el-form-item>
          <el-form-item label="微信分享文字：" prop="introdwxDesuce">
            <el-input v-model="entityInfo.edit.info.wxDes" type="textarea" autosize>
            </el-input>
          </el-form-item>
          <el-form-item label="微信分享图片：" prop="wxImg">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="entityInfo.edit.info.wxImg"
                                    uploader-id="wxImg"
                                    :uploader-size="[200,100]" :pixel-limit="[100,100]"
                                    :size-limit="200"
                                    @uploadSuccess="data=>fileUpload(data,entityInfo.edit.info)"
                                    @afterDelete="data=>fileDelete(data,entityInfo.edit.info)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="entityInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityInfoMethods().clickAddBtn()" :loading="entityInfo.loading"
                   v-if="entityInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityInfoMethods().clickEditBtn()" :loading="entityInfo.loading"
                   v-if="entityInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat, timeFormat} from "@/filters";
import {date_format, getQuery, objectToLVArr} from "@/utils/common";
import {WebAnalyticsModel} from "@/model/erp/WebAnalyticsModel";
import {GeneralInfoModel} from "@/model/erp/GeneralInfoModel";
import {validateMaxLength} from "@/utils/validate";
import {SchoolModel} from "@/model/exp/SchoolModel";
import {msg_confirm, msg_success} from "@/utils/ele_component";
import erpUploaderOneVideo from "@/views/components/erpUploaderOneVideo";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";

export default {
  name: "viewDetail",
  components: {
    ListSearchFilter, erpUploaderOneVideo, erpUploaderOnePic
  },
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission,
      adminUserId: state => state.user.id
    })
  },
  filters: {dateFormat, timeFormat},
  data: function () {
    let $this = this;
    return {
      window: window,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '视频标题',
              placeholder: "请输入视频标题",
              key: 'info.title',
              value: '',
              format: (v) => {
                return {'$regex': `.*${v}.*`}
              }
            },
          ],
          filter: []
        }
      },
      entityInfo: {
        dialog: false,
        title: "新增视频",
        type: "add",
        loading: false,
        uploading: false,
        edit: {
          info: {}
        },
        // 输入检测
        formRules: {
          'info.title': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 50, '视频名称'),
            trigger: 'blur'
          },
          'info.introduce': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 10000, '视频简介'),
            trigger: 'blur'
          }
        },
      },
    }
  },
  async mounted() {
    this.ListMethods().getList(0, 20, {})
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 点击搜索重置按钮
        clickCleanBtn() {

        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          query = Object.assign({
            type: "zyhdVideoPlatform"
          }, query);
          [list, $this.lists.pages] = await GeneralInfoModel.getPageList(page - 1, size, "", query)
          // 获取该pageId关联的事件数量
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter(type) {

        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击删除按钮
        async clickDeleteBtn(row) {
          if (await msg_confirm("确认要删除该视频吗？删除后不能恢复！")) {
            await GeneralInfoModel.deleteOne(row.generalInfoId)
            msg_success("删除成功")
            await $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
          }
        },
        // 点击添加按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "新增视频"
          $this.entityInfo.edit = {
            type: "zyhdVideoPlatform",
            info: {
              viewNumber: 0, // 浏览量
              completeNumber: 0, // 完播量
              on: true,// 上线
            }
          };
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
        // 点击列表的编辑按钮
        clickEditBtn(edit, $index) {
          $this.entityInfo.edit = JSON.parse(JSON.stringify(edit))
          $this.entityInfo.type = 'edit'
          $this.entityInfo.title = "修改视频"
          $this.entityInfo.$index = $index
          $this.entityInfo.dialog = true
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
      }
    },
    // 实体Methods
    EntityInfoMethods() {
      let $this = this;
      return {
        // 点击提交新增按钮
        clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              $this.entityInfo.loading = true
              if (await GeneralInfoModel.addOrEdit($this.entityInfo.edit).catch(err => {
                $this.entityInfo.loading = false
              })) {
                msg_success('新增视频成功')
                $this.ListMethods().getList(1, $this.lists.pages.size, $this.lists.query)
                $this.entityInfo.loading = false
                $this.entityInfo.dialog = false
              }
            }
          })
        },
        // 点击修改按钮
        clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              $this.entityInfo.loading = true
              if (await GeneralInfoModel.addOrEdit($this.entityInfo.edit).catch(err => {
                $this.entityInfo.loading = false
              })) {
                msg_success('保存成功')
                $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
                $this.entityInfo.dialog = false
                $this.entityInfo.loading = false
              }
            }
          })
        },
      }
    },
    // 通用-文件上传前
    beforeUpload(params) {
      this.$set(this.entityInfo, "uploading", true)
    },
    // 通用-文件上传成功
    fileUpload(params, target, formRef) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
      msg_success("上传成功！")
      this.$set(this.entityInfo, "uploading", false)
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 通用-文件删除
    fileDelete(params, target, formRef) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
  }
}
</script>
<style scoped lang="scss">
.lists {
  a {
    color: #004DCC;
  }

  .can-click {
    color: #004DCC;
    cursor: pointer;
  }
}
</style>
