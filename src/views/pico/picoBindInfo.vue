<template>
  <div class="app-container">
    <!--顶部-->
    <div style="text-align: right">
      <el-button class="el-button" type="success"
                 @click="importPico.dialog=true">批量导入及绑定PICO
      </el-button>
    </div>
    <!--查询框-->
    <el-form>
      <el-form-item label="PICO编号：" label-width="120px">
        <div>
          <el-input placeholder="请输入PICO编号" style="width: 300px" v-model="detail.edit.equipmentId">
          </el-input>
          <el-button style="margin-left: 15px" type="primary" @click="DetailMethods().clickPicoEquipmentIdSearchBtn()">
            查询
          </el-button>
        </div>
        <div class="pico-info-box flex flex-between" v-show="detail.info.show">
          <div class="left-box">
            <div class="equipmentId">
              {{ detail.info.equipmentId }}
            </div>
            <div class="typeAndDealer" v-if="detail.info.exist">
              <span class="type" style="margin-right: 20px">{{ detail.info.equipmentType }}</span>
              <span class="dealer">{{ detail.info.dealerName ? detail.info.dealerName : "未绑定经销商" }}</span>
            </div>
            <div class="no" v-if="!detail.info.exist">暂无该设备</div>
          </div>
          <div class="right-box">
            <el-button type="text" v-if="!detail.info.exist" @click="DetailMethods().clickPicoInfoAddBtn()">添加该设备
            </el-button>
            <el-button type="text" v-if="detail.info.exist" @click="DetailMethods().clickAddToListBtn()">加入列表
            </el-button>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="">
        <!--列表框-->
        <div style="padding-left: 120px;">
          <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
                    highlight-current-row style="width: 100%;" @selection-change="v=>ListMethods().onSelected(v)">
            <el-table-column
              type="selection"
              width="55"
              align="center">
            </el-table-column>
            <el-table-column label="设备编号" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.equipmentId }}</span>
              </template>
            </el-table-column>
            <el-table-column label="设备类型" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.equipmentType }}</span>
              </template>
            </el-table-column>
            <el-table-column label="绑定经销商" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.dealerName ? scope.row.dealerName : "--" }}</span>
              </template>
            </el-table-column>
            <el-table-column label="绑定时间" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.bindDate | dateFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column label="有效开始时间" align="center">
              <template slot-scope="scope">
                <span v-if="scope.row.validityStartTime">{{ scope.row.validityStartTime | dateFormat }}</span>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column label="有效截止时间" align="center">
              <template slot-scope="scope">
                <span v-if="scope.row.validityEndTime">{{ scope.row.validityEndTime | dateFormat }}</span>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-button v-if="scope.row.dealerId===''" type="text"
                           @click="ListMethods().clickListBindBtn(scope.row,scope.$index)">绑定
                </el-button>
                <!--                <el-button v-if="scope.row.dealerId!==''" type="text"-->
                <!--                           @click="ListMethods().clickListUnbindBtn(scope.row,scope.$index)">解绑-->
                <!--                </el-button>-->
                <el-button v-if="scope.row.dealerId!==''" type="text"
                           @click="ListMethods().clickListChangeDealerBtn(scope.row,scope.$index)">更换经销商
                </el-button>
                <el-button v-if="scope.row.dealerId!==''" type="text"
                           @click="ListMethods().clickListChangeValidityBtn(scope.row,scope.$index)">修改有效期
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

      </el-form-item>
      <el-form-item label="批量设置经销商：" label-width="130px">
        <el-select v-model="lists.dealerId" style="width: 300px">
          <el-option v-for="item in lists.searchFilter.filter[0].data" :value="item.value" :label="item.label"
                     :key="item.value"></el-option>
        </el-select>
        <div>
          <el-button style="margin-left: 0px" type="text"
                     @click="DetailMethods().clickAddEditDealerBtn()">添加、修改经销商
          </el-button>
        </div>
      </el-form-item>
      <el-form-item label="批量设置有效期：" label-width="130px">
        <el-date-picker
          v-model="lists.validity"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00','23:59:59']"
          type="datetimerange"
          range-separator="至"
          start-placeholder="请选择生效日期"
          end-placeholder="请选择失效日期">
        </el-date-picker>
      </el-form-item>
    </el-form>
    <!--按钮组-->
    <div class="bottom-button-container flex flex-center">
      <el-button @click="ListMethods().clickCancelBtn()">取 消</el-button>
      <el-button type="primary" @click="ListMethods().clickSaveBtn()" :loading="lists.multiChangeLoading">提 交
      </el-button>
    </div>
    <!--详情弹窗-->
    <el-dialog
      :title="detail.title"
      :visible.sync="detail.dialog"
      :close-on-click-modal="false"
      width="650px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="150px" ref="editForm" :model="detail.edit" :rules="detail.formRules">
          <el-form-item label="PICO编号:">
            <span>{{ detail.edit.equipmentId }}</span>
          </el-form-item>
          <el-form-item label="设备类型:" prop="equipmentType">
            <!--            <el-select v-model="detail.edit.equipmentType">-->
            <!--              <el-option v-for="item in lists.searchFilter.filter[1].data" :value="item.value" :label="item.label"-->
            <!--                         :key="item.value"></el-option>-->
            <!--            </el-select>-->
            <span>{{ detail.edit.equipmentType }}</span>
          </el-form-item>
          <el-form-item label="绑定经销商:" prop="equipmentType">
            <el-select v-if="['changeDealer','bind','add'].includes(detail.type)" v-model="detail.edit.dealerId"
                       @change="v=>DetailMethods().onChangeDealer(v)">

              <el-option v-for="item in lists.searchFilter.filter[0].data" :value="item.value" :label="item.label"
                         :key="item.value"></el-option>
            </el-select>
            <span v-else>{{ detail.edit.dealerName ? detail.edit.dealerName : "暂未绑定" }}</span>
          </el-form-item>
          <el-form-item label="有效期:" prop="equipmentType">
            <el-date-picker
              v-if="['changeValidity','bind'].includes(detail.type)"
              v-model="detail.edit.validityTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00','23:59:59']"
              type="datetimerange"
              range-separator="至"
              @change="e=>DetailMethods().validityDateChange(e)"
              start-placeholder="请选择生效日期"
              end-placeholder="请选择失效日期">
            </el-date-picker>
            <span v-else>{{ detail.edit.validityStartTime | dateFormat }} - {{
                detail.edit.validityEndTime | dateFormat
              }}</span>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="detail.dialog=false">取 消</el-button>
        <el-button type="primary" :loading="detail.loadingSure"
                   @click="DetailMethods().clickSureBtn()">确 认</el-button>
      </span>
    </el-dialog>
    <!--抽屉-经销商-->
    <el-drawer
      :visible.sync="drawer.dealerShow"
      direction="rtl"
      :destroy-on-close="true"
      :show-close="false"
      :modal-append-to-body="false"
      :append-to-body="true"
      @close="DetailMethods().onDealerDrawerClose()"
      size="50%">
      <div class="drawer-container">
        <admin-user-list :as-component="true" platform-id="picoSystem"
                         @afterEdit="drawer.dealerEdited=true"></admin-user-list>
      </div>
    </el-drawer>
    <!--Pico导入input-->
    <input
      id="importPicoFile"
      type="file"
      style="display: none"
      @change="(files)=>{ListMethods().importPicoFileChange(files)}"
    >
    <!--Pico导入弹窗-->
    <el-dialog
      title="批量导入Pico"
      :visible.sync="importPico.dialog"
      width="500px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form>
          <el-form-item label="导入模板(Excel):">
            <span style="margin-right: 15px">Pico导入列表.xls</span>
            <el-button type="default" size="mini" @click="ListMethods().clickDownloadBtn()">下载</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="importPico.dialog=false">取 消
        </el-button>
        <el-button type="success" :loading="importPico.doing"
                   @click="ListMethods().clickImportPicoBtn()">导入Pico
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {validateMaxLength} from "@/utils/validate";
import {AdminUserModel} from "@/model/erp/AdminUserModel";
import {PLATFORM_ID_PICO_SYSTEM} from "@/model/ConfigModel";
import {CommonModel} from "@/model/CommonModel";
import AdminUserList from "@/views/admin/list";
import {EquipmentModel} from "@/model/picoSystem/EquipmentModel";
import elDragDialog from "@/directive/el-drag-dialog";
import {downloadFile, objectToLVArr} from "@/utils/common";
import enumsPicoSystem from "@/enums/picoSystem";
import {msg_confirm, msg_confirm_choose, msg_err, msg_success} from "@/utils/ele_component";
import {dateFormat} from "@/filters";
import {BaseUploadModel} from "@/model/BaseUploadModel";
import {UserModel} from "@/model/exp/UserModel";

export default {
  name: "picoBindInfo",
  components: {AdminUserList},
  directives: {
    elDragDialog
  },
  filters: {dateFormat: dateFormat},
  data() {
    return {
      // 列表
      lists: {
        list: [],
        selectedList: [],
        multiChangeLoading: false,
        validity: [],
        dealerId: "",
        loading: false,
        query: {},
        pages: {
          size: 20
        },
        unBindPico: true,
        searchFilter: {
          search: [],
          filter: [
            {
              type: 'select',
              label: '绑定经销商',
              key: 'dealerId',
              hidden: false,
              value: '',
              data: [],
              dataObject: [],
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {

              }
            },
            {
              type: 'select',
              label: 'PICO类型',
              key: 'equipmentType',
              hidden: false,
              value: '',
              data: [],
              dataObject: [],
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {

              }
            }
          ]
        }
      },
      // 详情
      detail: {
        dialog: false,
        info: {
          show: false,
          exist: false,
          equipmentId: "",
          dealerId: "",
          dealerName: ""
        },
        edit: {
          equipmentId: ""
        },
        // 输入检测
        formRules: {
          'name': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 20, "体验者身份", true),
            trigger: 'blur'
          },
        },
      },
      // 抽屉
      drawer: {
        dealerShow: false,
      },
      // 批量导入pico
      importPico: {
        dialog: false,
        doing: false,
      }
    }
  },
  async mounted() {
    this.ListMethods().initFilter()
  },
  methods: {
    // 详情Methods
    ListMethods() {
      let $this = this
      return {
        // 点击下载导入列表按钮
        clickDownloadBtn() {
          // todo 做到配置文件里
          downloadFile("https://resouce.cdzyhd.com/20211011/1/Pico%E5%AF%BC%E5%85%A5%E5%88%97%E8%A1%A8.xlsx", "Pico导入列表.xls")
        },
        // 点击了Pico导入按钮
        clickImportPicoBtn() {
          const uploader = document.getElementById('importPicoFile')
          uploader.click()
        },
        // 导入Pico文件选择
        async importPicoFileChange(files) {
          const file = files.target.files[0]
          // 判断文件类型
          if (!BaseUploadModel.isImportExcelFile(file)) {
            return false
          }
          document.getElementById('importPicoFile').value = ''
          $this.importPico.doing = true
          // todo
          if (await EquipmentModel.importPico(file).catch(err => {
            $this.importClazz.dialog = false
            msg_err("批量导入Pico失败")
          })) {
            $this.importPico.dialog = false
            msg_success('批量导入Pico成功')
          }
          $this.importPico.doing = false
        },
        // 点击按钮组-取消按钮
        async clickCancelBtn() {
          if (await msg_confirm_choose("是否取消提交？", "取消提交", "我再想想", '确认取消') === "right") {
            msg_success("取消成功")
            setTimeout(() => {
              window.location.href = '#/pico/picoBind';
              // window.location.reload()
            }, 1000)
          }
        },
        // 点击修改有效期按钮
        async clickListChangeValidityBtn(equipment, index) {
          equipment = JSON.parse(JSON.stringify(equipment))
          $this.$set($this.detail, "edit", equipment)
          $this.$set($this.detail.edit, "$index", index)
          // 设置有效期
          let validityTime = []
          if (equipment.validityStartTime) {
            validityTime = [new Date(equipment.validityStartTime), new Date(equipment.validityEndTime)]
          }
          $this.$set($this.detail.edit, "validityTime", validityTime)
          $this.detail.type = "changeValidity"
          $this.detail.title = "修改有效期"
          $this.detail.dialog = true
        },
        // 点击按钮组-提交按钮
        async clickSaveBtn() {
          if ($this.lists.selectedList.length === 0) {
            msg_err("请先选择您要批量设置的设备！")
            return
          }
          // 如果要批量修改供应商
          let willChangeDealer = false
          if ($this.lists.dealerId !== undefined) {
            willChangeDealer = true
          }
          // 如果要批量修改
          let willChangeValidity = false
          if ($this.lists.validity.length === 2) {
            willChangeValidity = true
          }
          // 如果都未选择
          if (!willChangeValidity && !willChangeDealer) {
            msg_err("请先选择要批量设置的供应商或有效期！")
            return
          }

          $this.lists.multiChangeLoading = true
          let selectedSize = $this.lists.selectedList.length
          let changeMap = {}
          for (let i = 0; i < selectedSize; i++) {
            let entity = $this.lists.selectedList[i]
            let editEntity = {
              id: entity.id,
              equipmentId: entity.equipmentId,
            }
            if (willChangeDealer) {
              editEntity.dealerId = $this.lists.dealerId
            }
            if (willChangeValidity) {
              // 设置有效期
              editEntity["validityStartTime"] = $this.lists.validity[0]
              editEntity["validityEndTime"] = $this.lists.validity[1]
            }
            // 如果要批量修改
            let result = await EquipmentModel.addOrEdit(editEntity).catch(res => {
              $this.lists.multiChangeLoading = false;
            })
            if (result.data) {
              // 列表更新
              let index = $this.lists.list.indexOf(entity)
              let newEquipment = await EquipmentModel.getOne({equipmentId: entity.equipmentId})
              // 记录修改过的
              changeMap[index] = newEquipment
            }
            if (i === selectedSize - 1) {
              $this.lists.multiChangeLoading = false;
              msg_success("提交成功！")
            }
          }
          // 更新列表
          for (let i in changeMap) {
            $this.$set($this.lists.list, i, changeMap[i])
          }
        },
        // 批量选择
        onSelected(v) {
          $this.$set($this.lists, "selectedList", v)
        },
        // 初始化筛选列表
        async initFilter() {
          // 获取供应商列表
          let dealerList = await AdminUserModel.getListByPlatformId({}, PLATFORM_ID_PICO_SYSTEM)
          // 增加不绑定经销商选项
          dealerList.unshift({
            "adminUserId": "",
            "realName": "暂不绑定"
          })
          let dealerFilterList = CommonModel.generateListFilterOptions("realName", "adminUserId", dealerList, false)
          $this.$set($this.lists.searchFilter.filter[0], "dataOrigin", dealerList)
          $this.$set($this.lists.searchFilter.filter[0], "data", dealerFilterList[0])
          $this.$set($this.lists.searchFilter.filter[0], "dataObject", dealerFilterList[1])
          // 初始化Pico类型筛选框
          let picoTypeSelectArr = objectToLVArr(enumsPicoSystem.picoEquipmentType)
          $this.$set($this.lists.searchFilter.filter[1], "data", picoTypeSelectArr)
        },
        // 点击列表中的绑定按钮
        clickListBindBtn(equipment, index) {
          equipment = JSON.parse(JSON.stringify(equipment))
          // 设置有效期
          let validityTime = []
          if (equipment.validityStartTime) {
            validityTime = [new Date(equipment.validityStartTime), new Date(equipment.validityEndTime)]
          }
          equipment.validityTime = validityTime
          $this.$set($this.detail, "edit", equipment)
          $this.$set($this.detail.edit, "$index", index)
          $this.detail.type = "bind"
          $this.detail.title = "绑定设备"
          $this.detail.dialog = true
        },
        // 点击列表中的解绑按钮
        async clickListUnbindBtn(equipment, index) {
          equipment = JSON.parse(JSON.stringify(equipment))
          if (await msg_confirm("确认要解除与供应商 " + equipment.dealerName + " 的绑定吗？")) {
            if (await EquipmentModel.unbind(equipment.equipmentId)) {
              msg_success("解除绑定成功！")
              // 更新列表内的信息
              $this.$set($this.lists.list[index], "dealerId", "")
              $this.$set($this.lists.list[index], "dealerName", "")
            } else {
              msg_err("解除绑定失败！")
            }
          }
        },
        // 点击列表中的更换经销商按钮
        clickListChangeDealerBtn(equipment, index) {
          equipment = JSON.parse(JSON.stringify(equipment))
          $this.$set($this.detail, "edit", equipment)
          $this.$set($this.detail.edit, "$index", index)
          $this.$set($this.detail.edit, "oldDealerId", equipment.dealerId)
          $this.detail.type = "changeDealer"
          $this.detail.title = "更换经销商"
          $this.detail.dialog = true
        }
      }
    },
    // 详情Methods
    DetailMethods() {
      let $this = this
      return {
        // 经销商改变
        onChangeDealer(e) {
          $this.$forceUpdate()
        },
        // 有效期改变
        validityDateChange(e) {
          $this.$forceUpdate()
        },
        // 点击添加到列表按钮
        async clickAddToListBtn() {
          // 判断列表中是否已存在
          if ($this.lists.list.filter(li => {
            return li.equipmentId === $this.detail.info.equipmentId
          }).length > 0) {
            msg_err("列表中已存在此设备，不能再次加入！")
            return
          }
          $this.lists.list.push(JSON.parse(JSON.stringify($this.detail.info)))
          $this.detail.info = {}
        },
        // 点击pico编号查询按钮
        async clickPicoEquipmentIdSearchBtn() {
          let equipmentId = $this.detail.edit.equipmentId
          if (!equipmentId) {
            return
          }
          // 查询该设备信息
          let equipment = await EquipmentModel.getOne({equipmentId: equipmentId})
          if (equipment === false) {// 未在系统找到该设备信息
            let info = {
              exist: false,
              show: true,
              equipmentId: equipmentId
            }
            $this.$set($this.detail, "info", info)
          } else {
            let info = equipment
            info.show = true
            info.exist = true
            $this.$set($this.detail, "info", info)
          }
        },
        // 点击pico信息框-添加该设备按钮
        async clickPicoInfoAddBtn() {
          $this.detail.dialog = true
          $this.detail.title = "添加设备"
          $this.detail.type = "add"
          let edit = {
            dealerId: "",
            equipmentId:$this.detail.info.equipmentId
          }
          // 判断pico设备类型
          edit.equipmentType = EquipmentModel.getEquipmentTypeById($this.detail.info.equipmentId)
          $this.detail.edit = edit
        },
        // 点击弹窗确认按钮
        async clickSureBtn() {
          if ($this.detail.type === "add") {
            // 添加设备
            $this.detail.loadingSure = true;
            if (await EquipmentModel.addOrEdit({
              equipmentId: $this.detail.edit.equipmentId,
              dealerId: $this.detail.edit.dealerId,
              equipmentType: $this.detail.edit.equipmentType
            }).catch(res => {
              $this.detail.loadingSure = false;
            })) {
              msg_success('添加设备成功！')
              $this.detail.dialog = false
              $this.detail.loadingSure = false;
              // 信息框初始化
              $this.detail.info.show = false
              $this.detail.info.exist = true
            }
          }
          // 绑定设备
          if ($this.detail.type === "bind") {
            let validityStartTime = null
            let validityEndTime = null
            if ($this.detail.edit.validityTime !== null) {
              validityStartTime = new Date($this.detail.edit.validityTime[0]).getTime()
              validityEndTime = new Date($this.detail.edit.validityTime[1]).getTime()
            }
            $this.detail.loadingSure = true
            let entity = {
              id: $this.detail.edit.id,// 有此id表示编辑
              equipmentId: $this.detail.edit.equipmentId,
              dealerId: $this.detail.edit.dealerId,
              equipmentType: $this.detail.edit.equipmentType
            }
            if ($this.detail.edit.validityTime !== null) {
              entity = Object.assign(entity, {
                validityStartTime: validityStartTime,
                validityEndTime: validityEndTime
              })
            }
            let result = await EquipmentModel.addOrEdit(entity).catch(res => {
              $this.detail.loadingSure = false;
            })
            if (result.data) {
              msg_success('绑定设备成功！')
              $this.detail.dialog = false
              $this.detail.loadingSure = false;
              // 列表更新
              let index = $this.detail.edit.$index
              let newEquipment = await EquipmentModel.getOne({equipmentId: $this.detail.edit.equipmentId})
              $this.$set($this.lists.list, index, newEquipment)
            }
          }
          // 更换经销商
          if ($this.detail.type === "changeDealer") {
            if ($this.detail.edit.oldDealerId === $this.detail.edit.dealerId) {
              msg_err("您未选择新的供应商！")
              return
            }
            if (!(await msg_confirm("确定要更换经销商吗？"))) {
              return
            }
            $this.detail.loadingSure = true
            let result2 = await EquipmentModel.addOrEdit({
              id: $this.detail.edit.id,// 有此id表示编辑
              equipmentId: $this.detail.edit.equipmentId,
              dealerId: $this.detail.edit.dealerId,
              equipmentType: $this.detail.edit.equipmentType
            }).catch(res => {
              $this.detail.loadingSure = false;
            })
            if (result2.data) {
              msg_success('更换经销商成功！')
              $this.detail.dialog = false
              $this.detail.loadingSure = false;
              // 列表更新
              let index = $this.detail.edit.$index
              let newEquipment = await EquipmentModel.getOne({equipmentId: $this.detail.edit.equipmentId})
              $this.$set($this.lists.list, index, newEquipment)
            }
          }
          // 修改有效期
          if ($this.detail.type === "changeValidity") {
            let validityStartTime = null
            let validityEndTime = null
            if ($this.detail.edit.validityTime !== null) {
              validityStartTime = new Date($this.detail.edit.validityTime[0]).getTime()
              validityEndTime = new Date($this.detail.edit.validityTime[1]).getTime()
            }

            $this.detail.loadingSure = true
            let result = await EquipmentModel.addOrEdit({
              id: $this.detail.edit.id,// 有此id表示编辑
              equipmentId: $this.detail.edit.equipmentId,
              validityStartTime: validityStartTime,
              validityEndTime: validityEndTime
            }).catch(res => {
              $this.detail.loadingSure = false;
            })
            if (result.data) {
              msg_success('修改设备有效期成功！')
              $this.detail.dialog = false
              $this.detail.loadingSure = false;
              // 列表更新
              let index = $this.detail.edit.$index
              let newEquipment = await EquipmentModel.getOne({equipmentId: $this.detail.edit.equipmentId})
              $this.$set($this.lists.list, index, newEquipment)
            }
          }
        },
        // 点击添加、修改经销商按钮
        async clickAddEditDealerBtn() {
          $this.drawer.dealerShow = true
        },
        // 当经销商抽屉被关闭时
        onDealerDrawerClose() {
          if ($this.drawer.dealerEdited) {
            $this.$set($this.lists, "dealerId", "")
            $this.ListMethods().initFilter();
          }
        },
      }
    }
  }
}
</script>

<style scoped lang="scss">
.pico-info-box {
  width: 300px;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  padding: 10px 15px;
  margin-top: -1px;

  .equipmentId {
    color: #777;
  }

  span.type {
    color: #444;
  }

  span.dealer {

  }
}
</style>
