<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
        <div slot="filter-bottom" class="filter-bottom">
          <el-checkbox v-model="lists.unBindPico" @change="v=>ListMethods().onChangeBindFilter(v)">未绑定的PICO
          </el-checkbox>
        </div>
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success"
                     @click="ListMethods().clickAdd()">添加 / 绑定
          </el-button>
        </div>
      </div>
    </div>

    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              highlight-current-row
              style="width: 100%;">
      <el-table-column label="PICO编号" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.equipmentId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="PICO类型" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.equipmentType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="经销商" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.dealerName ? scope.row.dealerName : "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所含项目" align="center">
        <template slot-scope="scope">
          <el-tooltip placement="left" v-if="scope.row.installedAppListUpdated">
            <div slot="content">
              {{ scope.row.installedAppNameList.join(',') }}
            </div>
            <span class="ellipsis" style="width: 100px;overflow-x: hidden">{{
                scope.row.installedAppNameList.join(',')
              }}</span>
          </el-tooltip>
          <span v-else style="color: #999">PICO开机后自动获取</span>
        </template>
      </el-table-column>
      <el-table-column label="绑定时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.bindDate | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="350"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditBtn(scope.row,scope.$index)">修改
          </el-button>
          <el-button type="danger" size="mini" round
                     @click="ListMethods().clickDelBtn(scope.row.equipmentId,scope.$index)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
    <!--详情弹窗-->
    <el-dialog
      :title="detail.title"
      :visible.sync="detail.dialog"
      :close-on-click-modal="false"
      width="30%"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="150px" ref="editForm" :model="detail.edit" :rules="detail.formRules">
          <el-form-item label="PICO编号:">
            <span>{{ detail.edit.equipmentId }}</span>
          </el-form-item>
          <el-form-item label="设备类型:" prop="equipmentType">
            <el-select v-model="detail.edit.equipmentType">
              <el-option v-for="item in lists.searchFilter.filter[1].data" :value="item.value" :label="item.label"
                         :key="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="绑定经销商:" prop="equipmentType">
            <el-select v-model="detail.edit.dealerId">
              <el-option v-for="item in lists.searchFilter.filter[0].data" :value="item.value" :label="item.label"
                         :key="item.value"></el-option>
            </el-select>
            <el-button style="margin-left: 10px" type="text" size="mini" v-if="false"
                       @click="DetailMethods().clickAddEditDealerBtn()">添加、修改经销商
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="detail.dialog=false">取 消</el-button>
        <el-button v-if="detail.type==='edit'" type="primary"
                   @click="DetailMethods().clickEditBtn()">确 认</el-button>
        <el-button v-if="detail.type==='new'" type="primary"
                   @click="DetailMethods().clickAddBtn()">确 认</el-button>
      </span>
    </el-dialog>
    <!--抽屉-经销商-->
    <el-drawer
      :visible.sync="drawer.dealerShow"
      direction="rtl"
      :destroy-on-close="true"
      :show-close="false"
      :modal-append-to-body="false"
      :append-to-body="true"
      @close="DetailMethods().onDealerDrawerClose()"
      size="50%">
      <div class="drawer-container">
        <admin-user-list :as-component="true" platform-id="picoSystem"
                         @afterEdit="drawer.dealerEdited=true"></admin-user-list>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import enumsPicoSystem from '@/enums/picoSystem'
import elDragDialog from '@/directive/el-drag-dialog'
import {msg_confirm, msg_err, msg_success} from '@/utils/ele_component'
import {
  checkPhone,
  find_obj_from_arr_by_id,
  objectArrStoreOneSameKeyObject,
  objectToLVArr,
  searchWordFiltration
} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {validateMaxLength} from "@/utils/validate";
import {EquipmentModel} from "@/model/picoSystem/EquipmentModel";
import {dateFormat} from "@/filters";
import {CommonModel} from "@/model/CommonModel";
import {AdminUserModel} from "@/model/erp/AdminUserModel";
import {PLATFORM_ID_PICO_SYSTEM} from "@/model/ConfigModel";
import AdminUserList from "@/views/admin/list"
import {EnumDepartmentModel} from "@/model/exp/EnumDepartmentModel";


export default {
  name: 'picoBind',
  components: {ListSearchFilter, AdminUserList},
  directives: {
    elDragDialog, permission
  },
  filters: {
    dateFormat: dateFormat
  },
  computed: {
    ...mapState({})
  },
  data() {
    // 校检体验时长
    const validateMaxUseMinute = (rule, value, callback) => {
      let reg = /^[0-9]+$/
      if (!reg.test(value)) {
        callback(new Error('请输入1-999范围内的整数'))
      }
      if (value <= 0 || value > 999) {
        callback(new Error('请输入1-999范围内的整数'))
      }
      callback()
    }
    return {
      // 外部方法
      find_obj_from_arr_by_id: find_obj_from_arr_by_id,
      // 枚举
      enums: enums,
      enumsPicoSystem: enumsPicoSystem,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        pages: {
          size: 20
        },
        unBindPico: false,
        searchFilter: {
          search: [
            {
              type: 'input',
              label: 'PICO编号',
              placeholder: "请输入设备编号",
              key: 'equipmentId',
              value: '',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            },
          ],
          filter: [
            {
              type: 'select',
              label: '绑定经销商',
              key: 'dealerId',
              hidden: false,
              value: '',
              data: [],
              dataObject: [],
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {

              }
            },
            {
              type: 'select',
              label: 'PICO类型',
              key: 'equipmentType',
              hidden: false,
              value: '',
              data: [],
              dataObject: [],
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {

              }
            }
          ]
        }
      },
      // 详情弹窗
      detail: {
        title: "添加体验者",
        $index: 0,
        dialog: false,
        type: 'edit',// 编辑模式、新增模式
        edit: {},
        // 输入检测
        formRules: {
          'name': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 20, "体验者身份", true),
            trigger: 'blur'
          },
          'maxUseMinute': {required: true, validator: validateMaxUseMinute},
        },
      },
      // 抽屉
      drawer: {
        dealerShow: false,
      }
    }
  },
  async mounted() {
    // 获取列表
    this.ListMethods().getList(0, this.lists.pages.size, this.lists.query)
    // 初始化筛选
    this.ListMethods().initFilter()
  },
  methods: {
    // 列表Methods
    ListMethods() {

      let $this = this
      return {
        // 当绑定pico筛选改变时
        onChangeBindFilter(v) {
          $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
        },

        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          if ($this.lists.unBindPico === true) {
            query = Object.assign(query, {
              dealerId: ""
            })
          }else{
            if($this.lists.query.hasOwnProperty("dealerId")){

            }
          }
          [$this.lists.list, $this.lists.pages] = await EquipmentModel.getPageList(page - 1, size, "", query)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter() {
          // 获取供应商列表
          let dealerList = await AdminUserModel.getListByPlatformId({}, PLATFORM_ID_PICO_SYSTEM)
          // 增加不绑定经销商选项
          dealerList.unshift({
            "adminUserId": "",
            "realName": "暂不绑定"
          })
          let dealerFilterList = CommonModel.generateListFilterOptions("realName", "adminUserId", dealerList, false)


          $this.$set($this.lists.searchFilter.filter[0], "dataOrigin", dealerList)
          $this.$set($this.lists.searchFilter.filter[0], "data", dealerFilterList[0])
          $this.$set($this.lists.searchFilter.filter[0], "dataObject", dealerFilterList[1])
          // 初始化Pico类型筛选框
          let picoTypeSelectArr = objectToLVArr(enumsPicoSystem.picoEquipmentType)
          $this.$set($this.lists.searchFilter.filter[1], "data", picoTypeSelectArr)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击列表的编辑按钮
        async clickEditBtn(edit, $index) {
          $this.detail.edit = JSON.parse(JSON.stringify(edit))
          $this.detail.title = "修改PICO信息"
          $this.detail.type = 'edit'
          $this.detail.$index = $index
          $this.detail.dialog = true
          setTimeout(() => {
            $this.$refs['editForm'].clearValidate()
          }, 300)
          // 设置选择框数据
        },
        // 点击删除按钮
        async clickDelBtn(id, $index) {
          if (await msg_confirm('是否确认删除？')) {
            if (await EquipmentModel.deleteOne({
              equipmentId: id
            })) {
              $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
              msg_success('删除成功!')
              // 初始化筛选
              $this.ListMethods().initFilter()
            }
          }
        },
        // 点击新增按钮
        async clickAdd() {
          $this.$router.push("picoBindInfo")
        },
      }
    },
    // 详情Methods
    DetailMethods() {
      let $this = this
      return {
        // 点击确定修改按钮
        async clickEditBtn() {
          if (await msg_confirm("是否确认修改？")) {
            $this.$refs['editForm'].validate(async validate => {
              if (validate) {
                $this.detail.loadingEdit = true;
                if (await EquipmentModel.addOrEdit({
                  id: $this.detail.edit.id,
                  equipmentId: $this.detail.edit.equipmentId,
                  equipmentType: $this.detail.edit.equipmentType,
                  dealerId: $this.detail.edit.dealerId
                }).catch(res => {

                })) {
                  msg_success('修改成功')
                  $this.detail.loadingEdit = false;
                  // 修改列表信息
                  $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
                  $this.detail.dialog = false
                }
              }
            })
          }
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.$refs['editForm'].validate(async validate => {
            if (validate) {
              $this.detail.loadingAdd = true;
              if (await EquipmentModel.addOrEdit($this.detail.edit).catch(res => {
                $this.detail.loadingAdd = false;
              })) {
                msg_success('添加该设备成功')
                $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                $this.detail.dialog = false
                $this.detail.loadingAdd = false;
              }
            }
          })
        },
        // 点击添加、修改经销商按钮
        async clickAddEditDealerBtn() {
          $this.drawer.dealerShow = true
        },
        // 当经销商抽屉被关闭时
        onDealerDrawerClose() {
          if ($this.drawer.dealerEdited) {
            $this.$set($this.detail.edit, "dealerId", "")
            $this.ListMethods().initFilter();
          }
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.filter-bottom {
  margin-top: 20px;
}
</style>
