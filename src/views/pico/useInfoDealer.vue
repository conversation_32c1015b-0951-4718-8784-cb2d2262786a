<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">

      </div>
    </div>

    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              highlight-current-row
              style="width: 100%;">
      <el-table-column label="经销商编号" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.dealerId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="经销商名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.dealerName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="展出开始时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.startTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column label="展出结束时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.endTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column label="展出时长（分钟）" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.endTime - scope.row.startTime | minuteCal }}</span>
        </template>
      </el-table-column>
      <el-table-column label="累计实验展示时长（分钟）" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.totalUseTime">{{ scope.row.totalUseTime | minuteCal }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="展出地点" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.address }}</span>
        </template>
      </el-table-column>
      <el-table-column label="体验者身份" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.role }}</span>
        </template>
      </el-table-column>
      <el-table-column label="安排之外展出" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.createType==='auto'?"是":"否" }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="150"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickViewBtn(scope.row,scope.$index)">详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import enumsPicoSystem from '@/enums/picoSystem'
import elDragDialog from '@/directive/el-drag-dialog'
import {
  find_obj_from_arr_by_id,
  searchWordFiltration
} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {ScheduleModel} from "@/model/picoSystem/ScheduleModel";
import {dateFormat} from "@/filters";
import {AdminUserModel} from "@/model/erp/AdminUserModel";
import {PLATFORM_ID_PICO_SYSTEM} from "@/model/ConfigModel";
import {CommonModel} from "@/model/CommonModel";
import {msg_err} from "@/utils/ele_component";


export default {
  name: 'useInfoDealer',
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  filters: {
    dateFormat: dateFormat,
    minuteCal: (v) => {
      v = Math.floor(v / 1000 / 60)
      return v
    }
  },
  computed: {
    ...mapState({})
  },
  data() {
    return {
      dealerId: this.$route.query["dealerId"],
      // 外部方法
      find_obj_from_arr_by_id: find_obj_from_arr_by_id,
      // 枚举
      enums: enums,
      enumsPicoSystem: enumsPicoSystem,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        pages: {
          size: 20
        },
        unBindPico: true,
        searchFilter: {
          search: [
            // {
            //   type: 'input',
            //   label: '经销商编号',
            //   placeholder: "请输入经销商编号",
            //   key: 'dealerId',
            //   value: '',
            //   format: (v) => {
            //     v = searchWordFiltration(v, 'mongo')
            //     return {'$regex': `.*${v}.*`}
            //   }
            // },
            {
              type: 'input',
              label: '最近展出地点',
              placeholder: "请输入地点",
              key: 'address',
              value: '',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            },
          ],
          filter: [
            {
              type: 'timeHourRange',
              label: ['开始时间', '结束时间', '最近展出时间'],
              value: '',
              data: [],
              change: function (value) {
              },
              format: function (value) {
                return {
                  startTime: {
                    '$gte': value[0].getTime()
                  },
                  endTime: {
                    '$lte': value[1].getTime()
                  }
                }
              }
            }
          ]
        }
      },
    }
  },
  async mounted() {
    // 获取列表
    this.ListMethods().getList(0, this.lists.pages.size, this.lists.query)
    // 初始化筛选
    this.ListMethods().initFilter()
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          // 合并基础查询
          let queryBase = {
            dealerId: $this.dealerId
          };
          query = Object.assign(queryBase, query);
          [$this.lists.list, $this.lists.pages] = await ScheduleModel.getPageList(page - 1, size, "", query)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter() {
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击详情按钮
        clickViewBtn(entity) {
          // 判断展出状态
          // let startTime = entity.startTime
          // let endTime = entity.endTime
          // let dateNow = new Date().getTime()
          // let status = "unStart"
          // // 展出未开始
          // if (startTime > dateNow) {
          //   status = "unStart"
          //   msg_err("展出尚未开始，请在结束后再查看使用数据！")
          //   return
          // }
          // // 展出进行中
          // if (startTime <= dateNow && dateNow <= endTime) {
          //   status = "going"
          //   msg_err("展出正在进行中，请在结束后再查看使用数据！")
          //   return;
          // }
          // // 展出结束
          // if (dateNow > endTime) {
          //   status = "done"
          // }
          //
          // if (status === "done") {
          //   // 得到结束时间第二天凌晨3点的时间
          //   let endTimeNextDay = endTime + 86400000
          //   let endTimeNextDayStr = dateFormat(endTimeNextDay, "yyyy-MM-dd")
          //   endTimeNextDayStr += " 03:00:00"
          //   endTimeNextDay = new Date(endTimeNextDayStr).getTime()
          //   if (dateNow <= endTimeNextDay) {
          //     msg_err(`使用数据正在统计中，请在${endTimeNextDayStr}后再查看！`)
          //     return;
          //   }
          // }

          $this.$router.push({
            name: 'useInfoDetail',
            query: {
              scheduleId: entity.scheduleId
            }
          })
        }
      }
    },
  }
}
</script>

<style scoped lang="scss">
.filter-bottom {
  margin-top: 20px;
}
</style>
