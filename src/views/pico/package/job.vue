<template>
  <div class="app-container">
    <!--列表-->
    <el-table :data="list" element-loading-text="加载中" border fit v-loading="listLoading"
              highlight-current-row @selection-change="v=>onSelected(v)"
              style="width: 100%;">
      <el-table-column
        type="selection"
        width="55"
        align="center">
      </el-table-column>
      <el-table-column label="PICO编号" align="center" width="170">
        <template slot-scope="scope">
          <span>{{ scope.row.equipmentId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="PICO类型" align="center" width="90">
        <template slot-scope="scope">
          <span>{{ scope.row.equipmentType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属经销商" align="center" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.dealerName ? scope.row.dealerName : "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="已安装包" align="center">
        <template slot-scope="scope">
          <el-tooltip placement="left" v-if="scope.row.installedAppListUpdated">
            <div slot="content">
              {{ scope.row.installedAppNameList.join(',') }}
            </div>
            <span class="ellipsis" style="width: 100px;overflow-x: hidden">{{
                scope.row.installedAppNameList.join(',')
              }}</span>
          </el-tooltip>
          <span v-else style="color: #999">PICO开机后自动获取</span>
        </template>
      </el-table-column>
      <el-table-column label="待执行任务数" align="center" width="110">
        <template slot-scope="scope">
          <span v-if="scope.row.taskInfo.hasOwnProperty('todoTaskNum')">{{ scope.row.taskInfo['todoTaskNum'] }}</span>
          <span v-else>未知</span>
        </template>
      </el-table-column>
      <el-table-column label="在线状态" align="center" width="90">
        <template slot-scope="scope">
          <span v-if="scope.row.onLine" style="color:#67c23a"><i
            class="onLine-dot"></i>在线</span>
          <span v-else>离线</span>
        </template>
      </el-table-column>
      <el-table-column label="最后在线时间" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.lastLiveDate | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="230"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round @click="clickRefreshOneBtn(scope.row,scope.$index)">
            刷新
          </el-button>
          <el-button type="primary" size="mini" round @click="clickShowLogBtn(scope.row,scope.$index)">
            任务日志
          </el-button>
          <el-button type="danger" size="mini" round @click="clickDelBtn(scope.$index)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="flex flex-center" style="margin-top: 20px">
      <el-button type="primary" @click="clickRefreshBtn" :loading="refreshLoading">刷新整个列表</el-button>
      <el-button type="primary" @click="clickBackBtn">返回</el-button>
    </div>
    <!--底部按钮组-->
    <div class="bottom-button-container flex flex-center">
      <div>已选择（{{ selectedList.length }}）</div>
      <el-button type="primary" @click="clickOpenInstallBtn">安装程序</el-button>
      <el-button type="primary" @click="clickOpenUninstallBtn">卸载程序</el-button>
      <el-button type="primary" @click="clickOpenDownBtn">下载文件</el-button>
      <el-button type="primary" @click="clickPlayAudioBtn">播放声音</el-button>
      <el-button type="danger" @click="clickRebootSelectedBtn">重启</el-button>
      <el-button type="danger" @click="clickShutdownSelectedBtn">关机</el-button>
    </div>
    <!--安装详情弹窗-->
    <el-dialog
      :title="install.title"
      :visible.sync="install.dialog"
      :close-on-click-modal="false"
      width="30%"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="180px" ref="installForm" :model="install.edit" :rules="install.formRules">
          <el-form-item label="选择的Pico数量:">
            <span>{{ selectedList.length }}</span>
          </el-form-item>
          <el-form-item label="要安装的Pico程序:" prop="packageList">
            <el-select v-model="install.edit.packageList" multiple>
              <el-option v-for="item in picoPackageListFilter.dataId" :value="item.value" :label="item.label"
                         :key="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="选择pico包下载地址:" prop="downloadUrlType">
            <el-select v-model="install.edit.downloadUrlType">
              <el-option label="外网" value="out" key="out"></el-option>
              <el-option label="内网" value="in" key="in"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="重新安装已存在包:" prop="needReinstall">
            <el-select v-model="install.edit.needReinstall">
              <el-option label="是" :value="true" key="true"></el-option>
              <el-option label="否" :value="false" key="false"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="install.dialog=false">取 消</el-button>
        <el-button type="primary"
                   @click="InstallMethods().clickIssueTaskBtn()">下发任务</el-button>
      </span>
    </el-dialog>
    <!--卸载详情弹窗-->
    <el-dialog
      :title="uninstall.title"
      :visible.sync="uninstall.dialog"
      :close-on-click-modal="false"
      width="30%"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="150px" ref="uninstallForm" :model="uninstall.edit" :rules="uninstall.formRules">
          <el-form-item label="选择的Pico数量:">
            <span>{{ selectedList.length }}</span>
          </el-form-item>
          <el-form-item label="要卸载的Pico程序:" prop="packageList">
            <el-select v-model="uninstall.edit.packageList" multiple>
              <el-option v-for="item in picoPackageListFilter.dataId" :value="item.value" :label="item.label"
                         :key="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="uninstall.dialog=false">取 消</el-button>
        <el-button type="primary"
                   @click="UninstallMethods().clickIssueTaskBtn()">下发任务</el-button>
      </span>
    </el-dialog>
    <!--下载详情弹窗-->
    <el-dialog
      :title="download.title"
      :visible.sync="download.dialog"
      :close-on-click-modal="false"
      width="30%"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="150px" ref="downloadForm" :model="download.edit" :rules="download.formRules">
          <el-form-item label="选择的Pico数量:">
            <span>{{ selectedList.length }}</span>
          </el-form-item>
          <el-form-item label="上传文件：" prop="fileUrl">
            <el-upload
              drag
              action="dev"
              :show-file-list="false"
              :on-success="data=>DownloadMethods().uploadSuccess(data)"
              :http-request="upload=>DownloadMethods().uploadRequest(upload)"
              :before-upload="file=>DownloadMethods().beforeUpload(file)"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload>
            <div v-if="download.per>0" style="text-align: center">上传进度：{{ download.per }}%</div>
            <div>{{ download.edit.fileUrl }}</div>
          </el-form-item>
          <el-form-item label="存储路径快速选择:" prop="storeUrlSelect">
            <el-select v-model="download.edit.storeUrlSelect" @change="v=>DownloadMethods().onStoreUrlSelectChange(v)">
              <el-option v-for="item in download.storeFilter" :value="item.value" :label="item.label"
                         :key="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="存储路径:" prop="storeUrl">
            <div>
              <el-input v-model="download.edit.storeUrl" :disabled="download.edit.storeUrlSelect!==''"></el-input>
            </div>
            <div style="color:#999;size: 9px;">存储路径必须以 / 结尾</div>
          </el-form-item>
          <el-form-item label="存储文件名:" prop="storeFileName">
            <div>
              <el-input v-model="download.edit.storeFileName"></el-input>
            </div>
            <div style="color:#999;size: 9px;">将会替换已存在文件</div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="download.dialog=false">取 消</el-button>
        <el-button type="primary"
                   @click="DownloadMethods().clickIssueTaskBtn()">下发任务</el-button>
      </span>
    </el-dialog>
    <!--任务日志窗口-->
    <el-dialog
      :title="log.title"
      :visible.sync="log.dialog"
      :close-on-click-modal="false"
      width="1000px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-table :data="log.list" element-loading-text="加载中" border fit v-loading="log.listLoading"
                  highlight-current-row style="width: 100%;">
          <el-table-column label="类型" align="center" width="150">
            <template slot-scope="scope">
              <span>{{ scope.row.type }}</span>
            </template>
          </el-table-column>
          <el-table-column label="时间" align="center" width="180">
            <template slot-scope="scope">
              <span>{{ scope.row.createTime | dateFormat("yyyy/MM/dd HH:mm:ss ms") }}</span>
            </template>
          </el-table-column>
          <!--          <el-table-column label="状态" align="center" width="150">-->
          <!--            <template slot-scope="scope">-->
          <!--              <span>{{ scope.row.status }}</span>-->
          <!--            </template>-->
          <!--          </el-table-column>-->
          <el-table-column label="描述" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.des }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import enumsPicoSystem from '@/enums/picoSystem'
import elDragDialog from '@/directive/el-drag-dialog'
import {msg_confirm, msg_err, msg_success} from '@/utils/ele_component'
import {
  checkPhone,
  find_obj_from_arr_by_id,
  objectArrStoreOneSameKeyObject,
  objectToLVArr,
  searchWordFiltration
} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {validateMaxLength} from "@/utils/validate";
import {EquipmentModel} from "@/model/picoSystem/EquipmentModel";
import {dateFormat} from "@/filters";
import {CommonModel} from "@/model/CommonModel";
import {AdminUserModel} from "@/model/erp/AdminUserModel";
import {PLATFORM_ID_PICO_SYSTEM} from "@/model/ConfigModel";
import AdminUserList from "@/views/admin/list"
import {EnumDepartmentModel} from "@/model/exp/EnumDepartmentModel";
import {ExperimentModel} from "@/model/picoSystem/ExperimentModel";
import {PicoTaskModel} from "@/model/picoSystem/PicoTaskModel";
import {PicoTaskLogModel} from "@/model/picoSystem/PicoTaskLogModel";
import {BaseUploadModel} from "@/model/BaseUploadModel";

// 最后在线时间距现在的时间差，时间差内表示在线
const ONLINE_JUDGE_TIME = 60000

export default {
  name: 'picoPackageJob',
  components: {ListSearchFilter, AdminUserList},
  directives: {
    elDragDialog, permission
  },
  filters: {
    dateFormat: dateFormat,
    liveFormat(v) {
      let now = new Date().getTime()
      return now - v <= ONLINE_JUDGE_TIME;// 上次在线时间距现在小于60秒表示存活
    }
  },
  computed: {
    ...mapState({})
  },
  data() {
    return {
      // 外部方法
      find_obj_from_arr_by_id: find_obj_from_arr_by_id,
      // 枚举
      enums: enums,
      enumsPicoSystem: enumsPicoSystem,
      // 选择的列表
      list: [],
      listLoading: false,
      // 选择的设备id列表
      idList: [],
      // 最终选择的设备列表
      selectedList: [],
      // 刷新loading
      refreshLoading: false,
      // pico程序包列表
      picoPackageListFilter: {
        dataPackage: [],
        dataOrigin: [],
        dataPackageObject: {},
        dataId: [],
        dataIdObject: {}
      },
      // 安装相关
      install: {
        dialog: false,
        title: "安装程序",
        edit: {
          packageList: [],
          downloadUrlType: "out",
          needReinstall: true,
        },
        // 输入检测
        formRules: {
          'packageList': {
            required: true,
            trigger: 'blur',
            message: "请先选择Pico程序"
          },
          'downloadUrlType': {
            required: true,
            trigger: 'blur',
            message: "请先选择Pico包下载地址类型"
          },
          'needReinstall': {
            required: true,
            trigger: 'blur',
            message: "请先选择是否重新安装已存在包"
          },
        },
      },
      // 卸载相关
      uninstall: {
        dialog: false,
        title: "卸载程序",
        edit: {
          packageList: []
        },
        // 输入检测
        formRules: {
          'packageList': {
            required: true,
            trigger: 'blur',
            message: "请先选择Pico程序"
          },
        },
      },
      // 日志
      log: {
        dialog: false,
        list: [],
        pages: [],
        listLoading: false
      },
      // 下载相关
      download: {
        dialog: false,
        title: "下载文件",
        storeFilter: PicoTaskModel.downloadStoreOptionList,
        edit: {
          storeUrlSelect: ""
        },
        per: 0,
        // 输入检测
        formRules: {
          'storeUrl': {
            required: true,
            trigger: 'blur',
            message: "请先选择或输入存储路径"
          },
          'storeFileName': {
            required: true,
            trigger: 'blur',
            message: "请先输入存储文件名"
          },
          'fileUrl': {
            required: true,
            trigger: 'blur',
            message: "请先上传文件"
          },
        },
      },
      uploadRequest: BaseUploadModel.uploadRequest,
    }
  },
  async created() {
    // 从session中读取缓存
    let picoInstallSelectIdList = sessionStorage.getItem("picoInstallSelectIdList")
    if (picoInstallSelectIdList) {
      picoInstallSelectIdList = JSON.parse(picoInstallSelectIdList)
      this.idList = picoInstallSelectIdList
      // 从后端获取最新设备信息
      this.refreshLoading = true
      for (let i = 0; i < this.idList.length; i++) {
        let liNew = await EquipmentModel.getOne({
          equipmentId: this.idList[i]
        }).catch(res => {
          this.refreshLoading = false
        })
        this.list.push(liNew)
      }
      this.refreshLoading = false
    }
  },
  async mounted() {

  },
  methods: {
    // 遍历选择列表，仅能对在线设备发布任务
    checkSelectedList() {
      let list = this.selectedList
      let offList = list.filter(li => {
        return !li.onLine
      })
      return offList.length === 0
    },
    // 点击打开下载文件按钮
    async clickOpenDownBtn() {
      if (this.selectedList.length === 0) {
        msg_err("请先选择要操作的Pico！")
        return
      }
      // 遍历，仅能对在线设备发布任务
      if (!this.checkSelectedList()) {
        msg_err("只能选择在线的Pico！")
        return
      }
      this.download.dialog = true
      this.download.edit = {
        storeUrlSelect: "",
        fileUrl: "",
        storeUrl: "",
        storeFileName: ""
      }
    },
    // 点击查看任务日志按钮
    clickShowLogBtn(equipment, index) {
      this.log.title = `${equipment.equipmentId}的最新日志`;
      let query = {
        equipmentId: equipment.equipmentId
      }
      this.LogMethods().getList(0, 10, query)
      this.log.dialog = true
    },
    // 选择列表
    onSelected(v) {
      this.$set(this, "selectedList", v)
    },
    // 点击删除按钮
    async clickDelBtn(index) {
      if (await msg_confirm("确定要从列表中删除此设备吗?")) {
        this.list.splice(index, 1)
        this.idList.splice(index, 1)
        // 存入缓存
        sessionStorage.setItem("picoInstallSelectIdList", JSON.stringify(this.idList))
      }
    },
    // 点击刷新按钮
    async clickRefreshBtn() {
      this.refreshLoading = true
      for (let i = 0; i < this.list.length; i++) {
        let li = this.list[i]
        let liNew = await EquipmentModel.getOne({
          equipmentId: li.equipmentId
        }).catch(res => {
          this.refreshLoading = false
        })
        this.$set(this.list, i, liNew)
      }
      this.refreshLoading = false
    },
    // 点击刷新单个按钮
    async clickRefreshOneBtn(equipment, index) {
      let liNew = await EquipmentModel.getOne({
        equipmentId: equipment.equipmentId
      }).catch(res => {
      })
      this.$set(this.list, index, liNew)
    },
    // 初始化pico程序包列表
    async initPicoPackageList() {
      // 初始化pico包
      let experimentList = await ExperimentModel.getList()
      let experimentPackageFilterList = CommonModel.generateListFilterOptions('name', 'picoPackageName', experimentList, false)
      this.$set(this.picoPackageListFilter, 'dataOrigin', experimentList)
      this.$set(this.picoPackageListFilter, 'dataPackage', experimentPackageFilterList[0])
      this.$set(this.picoPackageListFilter, 'dataPackageObject', experimentPackageFilterList[1])
      let experimentIdFilterList = CommonModel.generateListFilterOptions('name', 'id', experimentList, false)
      this.$set(this.picoPackageListFilter, 'dataId', experimentIdFilterList[0])
      this.$set(this.picoPackageListFilter, 'dataIdObject', experimentIdFilterList[1])
    },
    // 点击底部安装程序按钮
    clickOpenInstallBtn() {
      if (this.selectedList.length === 0) {
        msg_err("请先选择要操作的Pico！")
        return
      }
      // 遍历，仅能对在线设备发布任务
      if (!this.checkSelectedList()) {
        msg_err("只能选择在线的Pico！")
        return
      }
      this.install.dialog = true
      this.install.edit = {
        packageList: [],
        downloadUrlType: "out",
        needReinstall: true,
      }
      this.initPicoPackageList()
    },
    // 点击底部卸载程序按钮
    clickOpenUninstallBtn() {
      if (this.selectedList.length === 0) {
        msg_err("请先选择要操作的Pico！")
        return
      }
      // 遍历，仅能对在线设备发布任务
      if (!this.checkSelectedList()) {
        msg_err("只能选择在线的Pico！")
        return
      }
      this.uninstall.dialog = true
      this.uninstall.edit = {
        packageList: []
      }
      this.initPicoPackageList()
    },
    // 点击底部重启按钮
    async clickRebootSelectedBtn() {
      if (this.selectedList.length === 0) {
        msg_err("请先选择要操作的Pico！")
        return
      }
      // 遍历，仅能对在线设备发布任务
      if (!this.checkSelectedList()) {
        msg_err("只能选择在线的Pico！")
        return
      }
      if (await msg_confirm("确定要将选择的设备 重新启动 吗？请谨慎操作！")) {
        // 生成任务信息
        let task = {
          taskType: "rebootDevice",
          equipmentList: this.selectedList.map(li => li.equipmentId),
          taskInfo: {}
        }
        // 上传任务信息
        if (await PicoTaskModel.addOrEdit(task).catch(res => {
          msg_err("下发批量重启任务失败！")
        })) {
          msg_success("下发批量重启任务成功！")
        }
      }
    },
    // 点击底部关机按钮
    async clickShutdownSelectedBtn() {
      if (this.selectedList.length === 0) {
        msg_err("请先选择要操作的Pico！")
        return
      }
      // 遍历，仅能对在线设备发布任务
      if (!this.checkSelectedList()) {
        msg_err("只能选择在线的Pico！")
        return
      }
      if (await msg_confirm("确定要将选择的设备 关机 吗？请谨慎操作！")) {
        // 生成任务信息
        let task = {
          taskType: "shutdownDevice",
          equipmentList: this.selectedList.map(li => li.equipmentId),
          taskInfo: {}
        }
        // 上传任务信息
        if (await PicoTaskModel.addOrEdit(task).catch(res => {
          msg_err("下发批量重启任务失败！")
        })) {
          msg_success("下发批量重启任务成功！")
        }
      }
    },
    // 安装相关方法
    InstallMethods() {
      let $this = this;
      return {
        // 点击下发任务按钮
        async clickIssueTaskBtn() {
          $this.$refs['installForm'].validate(async validate => {
            if (validate) {
              if (await msg_confirm("确定要下发此批量安装任务？")) {
                // 根据选择的pico程序和内外网下载地址生成安装信息
                // 生成任务详情
                let taskInfo = {}
                let list = []
                let downloadUrlOut = true;
                if ($this.install.edit.downloadUrlType !== 'out') {
                  downloadUrlOut = false;
                }
                $this.install.edit.packageList.forEach(experimentId => {
                  // 找到对应的实验信息
                  let experiment = find_obj_from_arr_by_id("id", experimentId, $this.picoPackageListFilter.dataOrigin)[1]
                  // 构建安装包列表
                  list.push({
                    name: experiment.name,
                    id: experiment.id,
                    packageName: experiment.picoPackageName,
                    downloadUrl: downloadUrlOut ? experiment.picoPackageUrlOut : experiment.picoPackageUrlIn,
                    needReinstall: $this.install.edit.needReinstall // todo 目前是给每个安装增加该属性，方便后期扩展单独选择
                  })
                })
                taskInfo.list = list
                // 生成任务信息
                let task = {
                  taskType: "installPackage",
                  equipmentList: $this.selectedList.map(li => li.equipmentId),
                  taskInfo: taskInfo
                }

                // 上传任务信息
                if (await PicoTaskModel.addOrEdit(task).catch(res => {
                  msg_err("下发批量安装任务失败！")
                })) {
                  msg_success("下发批量安装任务成功！")
                  $this.install.dialog = false
                }
              }
            }
          });
        }
      }
    },
    // 卸载相关方法
    UninstallMethods() {
      let $this = this;
      return {
        // 点击下发任务按钮
        clickIssueTaskBtn() {
          $this.$refs['uninstallForm'].validate(async validate => {
            if (validate) {
              if (await msg_confirm("确定要下发此批量卸载任务？")) {
                // 生成任务详情
                let taskInfo = {}
                let list = []
                $this.uninstall.edit.packageList.forEach(experimentId => {
                  // 找到对应的实验信息
                  let experiment = find_obj_from_arr_by_id("id", experimentId, $this.picoPackageListFilter.dataOrigin)[1]
                  // 构建安装包列表
                  list.push({
                    name: experiment.name,
                    id: experiment.id,
                    packageName: experiment.picoPackageName,
                  })
                })
                taskInfo.list = list
                // 生成任务信息
                let task = {
                  taskType: "uninstallPackage",
                  equipmentList: $this.selectedList.map(li => li.equipmentId),
                  taskInfo: taskInfo
                }
                // 上传任务信息
                if (await PicoTaskModel.addOrEdit(task).catch(res => {
                  msg_err("下发批量卸载任务失败！")
                })) {
                  msg_success("下发批量卸载任务成功！")
                  $this.uninstall.dialog = false
                }
              }
            }
          });
        }
      }
    },
    // 日志相关方法
    LogMethods() {
      let $this = this;
      return {
        // 获取日志列表
        async getList(page, size, query) {
          [$this.log.list, $this.log.pages] = await PicoTaskLogModel.getPageList(page - 1, size, "createTime,desc", query)
        }
      }
    },
    // 下载相关方法
    DownloadMethods() {
      let $this = this;
      return {
        onStoreUrlSelectChange(v) {
          $this.$set($this.download.edit, "storeUrl", v)
        },
        // 上传前检测
        beforeUpload(file) {
          $this.$set($this.download.edit, "fileUrl", "")
          $this.$set($this.download.edit, "storeFileName", file.name)
          return true
        },
        // 上传成功后
        uploadSuccess(data) {
          $this.$set($this.download.edit, "fileUrl", data.data)
          return true
        },
        // 上传请求
        async uploadRequest(upload) {
          let file = upload.file
          return await new Promise((resolve, reject) => {
            BaseUploadModel.qiNiuUpload(file, {
              next: (result) => {
                // 上传进度显示
                $this.download.per = result.total.percent.toFixed(0);
                if ($this.download.per == 100) {
                  $this.download.per = 0;
                }
              },
              error: (errResult) => {
                console.log(errResult)
                msg_err('上传失败')
              },
              complete: (result) => {
                let domain = BaseUploadModel.getBucketDomain(file)
                let url = domain + '/' + result.key + ''
                resolve({data: url})
              }
            })
          })
        },
        // 点击下发任务按钮
        clickIssueTaskBtn() {
          $this.$refs['downloadForm'].validate(async validate => {
            if (validate) {
              // 存储路径要以/结尾
              let storeUrl = $this.download.edit.storeUrl
              if (!storeUrl.endsWith("/")) {
                msg_err("存储路径必须以 / 结尾！")
                return
              }
              if (await msg_confirm("确定要下发此下载文件任务？")) {
                // 生成任务详情
                let taskInfo = {
                  fileUrl: $this.download.edit.fileUrl,
                  storeUrl: $this.download.edit.storeUrl,
                  storeFileName: $this.download.edit.storeFileName
                }
                // 生成任务信息
                let task = {
                  taskType: "downloadFile",
                  taskInfo: taskInfo,
                  equipmentList: $this.selectedList.map(li => li.equipmentId),
                }
                // 上传任务信息
                if (await PicoTaskModel.addOrEdit(task).catch(res => {
                  msg_err("下发批量下载文件任务失败！")
                })) {
                  msg_success("下发批量下载文件任务成功！")
                  $this.download.dialog = false
                }
              }
            }
          });
        }
      }
    },
    async clickPlayAudioBtn() {
      if (this.selectedList.length === 0) {
        msg_err("请先选择要操作的Pico！")
        return
      }
      // 遍历，仅能对在线设备发布任务
      if (!this.checkSelectedList()) {
        msg_err("只能选择在线的Pico！")
        return
      }
      if (await msg_confirm("确定要将在选择的设备上播放提示声音吗！")) {
        // 生成任务信息
        let task = {
          taskType: "playAudio",
          equipmentList: this.selectedList.map(li => li.equipmentId),
          taskInfo: {
            type: "findDevice"
          }
        }
        // 上传任务信息
        if (await PicoTaskModel.addOrEdit(task).catch(res => {
          msg_err("下发批量播放声音任务失败！")
        })) {
          msg_success("下发批量播放声音任务成功！")
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.filter-bottom {
  margin-top: 20px;
}

i.onLine-dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #67c23a;
  margin-right: 5px
}
</style>
