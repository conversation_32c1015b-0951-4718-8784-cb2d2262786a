<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
        <div slot="filter-bottom" class="filter-bottom">

        </div>
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
        </div>
      </div>
    </div>

    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              highlight-current-row @selection-change="v=>ListMethods().onSelected(v)"
              style="width: 100%;">
      <el-table-column
        type="selection"
        width="55"
        align="center">
      </el-table-column>
      <el-table-column label="PICO编号" align="center" width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.equipmentId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="PICO类型" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.equipmentType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="经销商" align="center" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.dealerName ? scope.row.dealerName : "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="已安装包" align="center">
        <template slot-scope="scope">
          <el-tooltip placement="left" v-if="scope.row.installedAppListUpdated">
            <div slot="content">
              {{ scope.row.installedAppNameList.join(',') }}
            </div>
            <span class="ellipsis" style="width: 100px;overflow-x: hidden">{{
                scope.row.installedAppNameList.join(',')
              }}</span>
          </el-tooltip>
          <span v-else style="color: #999">PICO开机后自动获取</span>
        </template>
      </el-table-column>
      <el-table-column label="在线状态" align="center" width="90">
        <template slot-scope="scope">
          <span v-if="new Date().getTime()-scope.row.lastLiveDate<ONLINE_JUDGE_TIME" style="color:#67c23a"><i
            class="onLine-dot"></i>在线</span>
          <span v-else>离线</span>
        </template>
      </el-table-column>
      <el-table-column label="最后在线时间" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.lastLiveDate | dateFormat }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container" style="margin-bottom: 100px">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
    <!--按钮组-->
    <div class="bottom-button-container flex flex-center">
      <el-button type="primary" @click="clickAddToListBtn">加入选择列表（{{ selectedIdList.length }}）</el-button>
      <el-button type="success" @click="clickStartJobBtn">开始执行任务</el-button>
      <el-button type="danger" @click="clickEmptyListBtn">清空列表</el-button>
    </div>
  </div>
</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import enumsPicoSystem from '@/enums/picoSystem'
import elDragDialog from '@/directive/el-drag-dialog'
import {msg_confirm, msg_err, msg_success} from '@/utils/ele_component'
import {
  checkPhone,
  find_obj_from_arr_by_id,
  objectArrStoreOneSameKeyObject,
  objectToLVArr,
  searchWordFiltration
} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {validateMaxLength} from "@/utils/validate";
import {EquipmentModel} from "@/model/picoSystem/EquipmentModel";
import {dateFormat} from "@/filters";
import {CommonModel} from "@/model/CommonModel";
import {AdminUserModel} from "@/model/erp/AdminUserModel";
import {PLATFORM_ID_PICO_SYSTEM} from "@/model/ConfigModel";
import AdminUserList from "@/views/admin/list"
import {EnumDepartmentModel} from "@/model/exp/EnumDepartmentModel";
import {ExperimentModel} from "@/model/picoSystem/ExperimentModel";

// 最后在线时间距现在的时间差，时间差内表示在线
const ONLINE_JUDGE_TIME = 60000

export default {
  name: 'picoPackageIndex',
  components: {ListSearchFilter, AdminUserList},
  directives: {
    elDragDialog, permission
  },
  filters: {
    dateFormat: dateFormat,
    liveFormat(v) {
      let now = new Date().getTime()
      return now - v <= ONLINE_JUDGE_TIME;// 上次在线时间距现在小于60秒表示存活
    }
  },
  computed: {
    ...mapState({})
  },
  data() {

    // 校检体验时长
    const validateMaxUseMinute = (rule, value, callback) => {
      let reg = /^[0-9]+$/
      if (!reg.test(value)) {
        callback(new Error('请输入1-999范围内的整数'))
      }
      if (value <= 0 || value > 999) {
        callback(new Error('请输入1-999范围内的整数'))
      }
      callback()
    }
    return {
      ONLINE_JUDGE_TIME: ONLINE_JUDGE_TIME,
      // 外部方法
      find_obj_from_arr_by_id: find_obj_from_arr_by_id,
      // 枚举
      enums: enums,
      enumsPicoSystem: enumsPicoSystem,
      // 列表
      lists: {
        list: [],
        // 已选择的列表
        selectedList: [],
        loading: false,
        query: {},
        pages: {
          size: 100
        },
        unBindPico: false,
        searchFilter: {
          search: [
            {
              type: 'input',
              label: 'PICO编号',
              placeholder: "请输入设备编号",
              key: 'equipmentId',
              value: '',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            },
          ],
          filter: [
            {
              type: 'select',
              label: '绑定经销商',
              key: 'dealerId',
              hidden: false,
              value: '',
              data: [],
              dataObject: [],
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {

              }
            },
            {
              type: 'select',
              label: 'PICO类型',
              key: 'equipmentType',
              hidden: false,
              value: '',
              data: [],
              dataObject: [],
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {

              }
            },
            {
              type: 'select',
              label: '已安装包',
              key: 'installedAppList',
              hidden: false,
              multiple: true,
              value: '',
              data: [],
              dataObject: [],
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {

              },
              format: function (value) {
                if (value.length > 0) {
                  return {
                    'installedAppList': {
                      '$all': value
                    }
                  }
                } else {

                }

              }
            },
            {
              type: 'select',
              label: '是否在线',
              key: 'lastLiveDate',
              hidden: false,
              value: '',
              data: [
                {value: true, label: "在线"},
                {value: false, label: "离线"},
              ],
              dataObject: [

              ],
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {

              },
              format: function (value) {
                if (value) {
                  return {
                    "lastLiveDate": {
                      "$gte": new Date().getTime() - ONLINE_JUDGE_TIME
                    }
                  }
                } else {
                  return {
                    "lastLiveDate": {
                      "$lte": new Date().getTime() - ONLINE_JUDGE_TIME
                    }
                  }
                }
              }
            },
          ]
        }
      },
      // 最终选择的设备id列表
      selectedIdList: []
    }
  },
  created() {
    let picoInstallSelectIdList = sessionStorage.getItem("picoInstallSelectIdList")
    if (picoInstallSelectIdList) {
      picoInstallSelectIdList = JSON.parse(picoInstallSelectIdList)
      this.selectedIdList = picoInstallSelectIdList
    }
  },
  async mounted() {
    // 获取列表
    this.ListMethods().getList(0, this.lists.pages.size, this.lists.query)
    // 初始化筛选
    this.ListMethods().initFilter()
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 批量选择
        onSelected(v) {
          $this.$set($this.lists, "selectedList", v)
        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          if ($this.lists.unBindPico === true) {
            query = Object.assign(query, {
              dealerId: ""
            })
          } else {
            if ($this.lists.query.hasOwnProperty("dealerId")) {

            }
          }
          [$this.lists.list, $this.lists.pages] = await EquipmentModel.getPageList(page - 1, size, "", query)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter() {
          // 获取供应商列表
          let dealerList = await AdminUserModel.getListByPlatformId({}, PLATFORM_ID_PICO_SYSTEM)
          // 增加不绑定经销商选项
          dealerList.unshift({
            "adminUserId": "",
            "realName": "暂不绑定"
          })
          let dealerFilterList = CommonModel.generateListFilterOptions("realName", "adminUserId", dealerList, false)
          $this.$set($this.lists.searchFilter.filter[0], "dataOrigin", dealerList)
          $this.$set($this.lists.searchFilter.filter[0], "data", dealerFilterList[0])
          $this.$set($this.lists.searchFilter.filter[0], "dataObject", dealerFilterList[1])
          // 初始化Pico类型筛选框
          let picoTypeSelectArr = objectToLVArr(enumsPicoSystem.picoEquipmentType)
          $this.$set($this.lists.searchFilter.filter[1], "data", picoTypeSelectArr)
          // 初始化pico包
          let experimentList = await ExperimentModel.getList()
          let experimentFilterList = CommonModel.generateListFilterOptions('name', 'picoPackageName', experimentList, false)
          $this.$set($this.lists.searchFilter.filter[2], 'dataOrigin', experimentList)
          $this.$set($this.lists.searchFilter.filter[2], 'data', experimentFilterList[0])
          $this.$set($this.lists.searchFilter.filter[2], 'dataObject', experimentFilterList[1])
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
      }
    },
    // 点击加入列表按
    clickAddToListBtn() {
      // 遍历选择列表
      let existedNum = 0;
      for (let i = 0; i < this.lists.selectedList.length; i++) {
        let li = this.lists.selectedList[i]
        let id = li.equipmentId
        if (!this.selectedIdList.includes(id)) {
          this.selectedIdList.push(id)
        } else {
          existedNum++;
        }
      }
      // 设置SessionStorage 存储已选择信息
      let picoInstallSelectIdList = this.selectedIdList
      sessionStorage.setItem("picoInstallSelectIdList", JSON.stringify(picoInstallSelectIdList))
      // 提示
      existedNum > 0 ? msg_success(`加入成功！已跳过${existedNum}个已存在设备。`) : msg_success("加入成功！");

    },
    // 点击清空按钮
    async clickEmptyListBtn() {
      if (await msg_confirm("确定要清空已选择的设备列表？")) {
        this.selectedIdList = []
        this.selectedList = []
      }
    },
    // 点击开始执行任务按钮
    clickStartJobBtn() {
      // 跳到到执行页
      this.$router.push({
        name: "picoPackageJob"
      })
    }
  }
}
</script>

<style scoped lang="scss">
.filter-bottom {
  margin-top: 20px;
}

i.onLine-dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #67c23a;
  margin-right: 5px
}
</style>
