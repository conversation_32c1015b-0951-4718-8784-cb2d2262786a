<template>
  <div class="app-container">
    <!--头部信息-->
    <div class="header-info flex flex-between" style="margin-top: 15px">
      <div><span>展出地点：</span>{{ schedule.address }}</div>
      <div><span>展出时间：</span>{{ schedule.startTime | dateFormat }} - {{ schedule.endTime | dateFormat }}</div>
      <div><span>经销商：</span>{{ schedule.dealerName }}</div>
      <div><span>总共设备数量：</span>{{ schedule.equipmentIdList.length }}</div>
    </div>

    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">

      </div>
    </div>


    <!--列表-按天分-->
    <div style="margin-top: 30px">
      <div v-for="(item,index) in lists.list">
        <div class="header-info flex flex-between" style="width: 900px">
          <div><span>展出时间：</span>{{ item.date }}</div>
          <div><span>当日累计实验时长：</span>{{ Math.floor(item.calInfo.totalUseTime / 1000 / 60) }}分钟</div>
          <div><span>当日参展设备数量：</span>{{ item.calInfo.useEquipmentList.length }}</div>
        </div>
        <vxe-table
          border
          :expand-config="{accordion: true}"
          :data="item.useInfo">
          <vxe-column field="equipmentId" title="Pico编号"></vxe-column>
          <vxe-column field="equipmentType" title="Pico型号"></vxe-column>
          <vxe-column field="totalUseTime" title="当时累计实验时长（分）">
            <template #default="{ row }">
              <span>{{ Math.floor(row.totalUseTime / 1000 / 60) }}</span>
            </template>
          </vxe-column>
          <vxe-column type="expand" width="80" title="查看详情">
            <template #content="{ row, rowIndex }">
              <div class="expand-wrapper">
                <vxe-table
                  border
                  :data="row.list">
                  <vxe-column field="createTime" title="展出时间">
                    <template #default="{ row }">
                      <span>{{
                          row.createTime | dateFormat("yyyy/MM/dd HH:mm:ss")
                        }}-{{ row.endTime | dateFormat("yyyy/MM/dd HH:mm:ss") }}</span>
                    </template>
                  </vxe-column>
                  <vxe-column field="useTime" title="实验时长（分）">
                    <template #default="{ row }">
                      <span>{{
                          Math.floor(row.useTime / 1000 / 60) < 0 ? 0 : Math.floor(row.useTime / 1000 / 60)
                        }}</span>
                    </template>
                  </vxe-column>
                  <vxe-column field="appName" title="展出实验"></vxe-column>
                  <vxe-column field="userRole" title="体验者身份"></vxe-column>
                </vxe-table>
              </div>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
    </div>
  </div>
</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import enumsPicoSystem from '@/enums/picoSystem'
import elDragDialog from '@/directive/el-drag-dialog'
import {
  find_obj_from_arr_by_id,
  searchWordFiltration
} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {ScheduleModel} from "@/model/picoSystem/ScheduleModel";
import {dateFormat} from "@/filters";
import {AdminUserModel} from "@/model/erp/AdminUserModel";
import {PLATFORM_ID_PICO_SYSTEM} from "@/model/ConfigModel";
import {CommonModel} from "@/model/CommonModel";


export default {
  name: 'useInfoDetail',
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  filters: {
    dateFormat: dateFormat,
    minuteCal: (v) => {
      v = Math.floor(v / 1000 / 60)
      return v
    }
  },
  computed: {
    ...mapState({})
  },
  data() {
    return {
      scheduleId: this.$route.query["scheduleId"],
      // 外部方法
      find_obj_from_arr_by_id: find_obj_from_arr_by_id,
      // 枚举
      enums: enums,
      enumsPicoSystem: enumsPicoSystem,
      // 计划详情
      schedule: {
        equipmentIdList: []
      },
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: 'Pico编号',
              placeholder: "请输入Pico编号",
              key: 'equipmentId',
              value: '',
              // format: (v) => {
              //   v = searchWordFiltration(v, 'mongo')
              //   return {'$regex': `.*${v}.*`}
              // }
            },
          ],
          filter: []
        }
      },
    }
  },
  async mounted() {
    // 获取计划详情
    this.schedule = await ScheduleModel.getOne({scheduleId: this.scheduleId})
    this.ListMethods().getList({})
    // 初始化筛选
    this.ListMethods().initFilter()
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取计划对应的按天和设备区分的设备列表
        async getList(query) {
          $this.lists.loading = true;
          let queryBase = {
            scheduleId: $this.scheduleId
          };
          query = Object.assign(query, queryBase);
          $this.lists.list = await ScheduleModel.getUseInfoDetailList(query)
          $this.lists.loading = false
        },
        // 初始化筛选列表
        async initFilter() {

        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList($this.lists.query)
        },
      }
    },
  }
}
</script>

<style scoped lang="scss">
.filter-bottom {
  margin-top: 20px;
}

.header-info {
  margin-bottom: 30px;
  color: #666;

  span {
    color: #333;
  }
}

.expand-wrapper {
  padding: 20px;
}
</style>
