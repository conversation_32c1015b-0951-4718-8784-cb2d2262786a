<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success"
                     @click="ListMethods().clickAdd()">添加体验者
          </el-button>
        </div>
      </div>
    </div>

    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              highlight-current-row
              style="width: 100%;">
      <el-table-column label="体验者身份" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="体验时长（分钟）" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.maxUseMinute }}</span>
        </template>
      </el-table-column>
      <el-table-column label="修改时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.editTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="350"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditBtn(scope.row,scope.$index)">修改
          </el-button>
          <el-button type="danger" size="mini" round
                     @click="ListMethods().clickDelBtn(scope.row.userRoleId,scope.row.name,scope.$index)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
    <!--详情弹窗-->
    <el-dialog
      :title="detail.title"
      :visible.sync="detail.dialog"
      :close-on-click-modal="false"
      width="30%"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="150px" ref="editForm" :model="detail.edit" :rules="detail.formRules">
          <el-form-item label="体验者身份:" prop="name">
            <el-input v-if="detail.edit.userRoleId!=='0'" v-model.trim="detail.edit.name"></el-input>
            <span v-else>{{ detail.edit.name }}</span>
          </el-form-item>
          <el-form-item label="体验时长（分钟）:" prop="maxUseMinute">
            <el-input v-model.trim="detail.edit.maxUseMinute"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="detail.dialog=false">取 消</el-button>
        <el-button v-if="detail.type==='edit'" type="primary"
                   @click="DetailMethods().clickEditBtn()">确 认</el-button>
        <el-button v-if="detail.type==='new'" type="primary"
                   @click="DetailMethods().clickAddBtn()">确 认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import elDragDialog from '@/directive/el-drag-dialog'
import {msg_confirm, msg_err, msg_success} from '@/utils/ele_component'
import {checkPhone, find_obj_from_arr_by_id} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {validateMaxLength} from "@/utils/validate";
import {UserRoleModel} from "@/model/picoSystem/UserRoleModel";
import {dateFormat} from "@/filters";
import {CommonModel} from "@/model/CommonModel";


export default {
  name: 'picoRole',
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  filters: {
    dateFormat: dateFormat
  },
  computed: {
    ...mapState({})
  },
  data() {
    // 校检体验时长
    const validateMaxUseMinute = (rule, value, callback) => {
      let reg = /^[0-9]+$/
      if (!reg.test(value)) {
        callback(new Error('请输入1-999范围内的整数'))
      }
      if (value <= 0 || value > 999) {
        callback(new Error('请输入1-999范围内的整数'))
      }
      callback()
    }
    return {
      // 外部方法
      find_obj_from_arr_by_id: find_obj_from_arr_by_id,
      // 枚举
      enums: enums,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        pages: {
          size: 20
        },
        searchFilter: {
          search: [],
          filter: [
            {
              type: 'select',
              label: '体验者角色',
              key: 'name',
              hidden: false,
              value: '',
              data: [],
              dataObject: [],
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {

              }
            }
          ]
        }
      },
      // 详情弹窗
      detail: {
        title: "添加体验者",
        $index: 0,
        dialog: false,
        type: 'edit',// 编辑模式、新增模式
        edit: {},
        // 输入检测
        formRules: {
          'name': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 20, "体验者身份", true),
            trigger: 'blur'
          },
          'maxUseMinute': {required: true, validator: validateMaxUseMinute},
        },
      }
    }
  },
  async mounted() {
    // 获取列表
    this.ListMethods().getList(0, this.lists.pages.size, this.lists.query)
    // 初始化筛选
    this.ListMethods().initFilter()
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          [$this.lists.list, $this.lists.pages] = await UserRoleModel.getPageList(page - 1, size, "", query)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter() {
          // 获取角色列表
          let roleList = await UserRoleModel.getList({})
          let generateListFilter0 = CommonModel.generateListFilterOptions('name', 'name', roleList, true)
          $this.$set($this.lists.searchFilter.filter[0], "data", generateListFilter0[0])
          $this.$set($this.lists.searchFilter.filter[0], "dataObject", generateListFilter0[1])
          $this.$set($this.lists.searchFilter.filter[0], "dataOrigin", roleList)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击列表的编辑按钮
        clickEditBtn(edit, $index) {
          $this.detail.edit = JSON.parse(JSON.stringify(edit))
          $this.detail.title = "修改体验者"
          $this.detail.type = 'edit'
          $this.detail.$index = $index
          $this.detail.dialog = true
          setTimeout(() => {
            $this.$refs['editForm'].clearValidate()
          }, 300)
        },
        // 点击删除按钮
        async clickDelBtn(userRoleId,name, $index) {
          if (userRoleId === "0") {
            msg_err("默认用户不可以删除！")
            return
          }
          if (await msg_confirm('删除之后，将立即在使用该身份的PICO上生效，是否删除？')) {
            if (await UserRoleModel.deleteOne({
              userRoleId,
              userRole:name
            })) {
              $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
              msg_success('删除成功!')
              // 初始化筛选
              $this.ListMethods().initFilter()
            }
          }
        },
        // 点击新增按钮
        async clickAdd() {
          $this.detail.type = 'new'
          $this.detail.title = "添加体验者"
          $this.detail.edit = {}
          $this.detail.dialog = true
          setTimeout(() => {
            $this.$refs['editForm'].clearValidate()
          }, 300)
        },
      }
    },
    // 详情Methods
    DetailMethods() {
      let $this = this
      return {
        // 点击确定修改按钮
        async clickEditBtn() {
          if (await msg_confirm("修改之后，将立即在使用该身份的PICO上立即生效，是否确认修改？")) {
            $this.$refs['editForm'].validate(async validate => {
              if (validate) {
                $this.detail.loadingEdit = true;
                if (await UserRoleModel.addOrEdit($this.detail.edit).catch(res => {

                })) {
                  msg_success('修改成功')
                  $this.detail.loadingEdit = false;
                  // 修改列表信息
                  $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
                  $this.detail.dialog = false
                  // 初始化筛选
                  $this.ListMethods().initFilter()
                }
              }
            })
          }
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.$refs['editForm'].validate(async validate => {
            if (validate) {
              $this.detail.loadingAdd = true;
              if (await UserRoleModel.addOrEdit($this.detail.edit).catch(res => {
                $this.detail.loadingAdd = false;
              })) {
                msg_success('新增成功')
                $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                $this.detail.dialog = false
                $this.detail.loadingAdd = false;
                // 初始化筛选
                $this.ListMethods().initFilter()
              }

            }
          })
        }
      }
    }
  }
}
</script>

<style scoped>

</style>
