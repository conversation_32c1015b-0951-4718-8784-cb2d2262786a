<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
      </list-search-filter>
      <!--  操作  -->
      <div class="right clearfix">
          <el-button class="fr" type="default" @click="calInfo.dialog=true" size="small">计算某个供应商某天的使用情况数据</el-button>
      </div>
    </div>

    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              highlight-current-row
              style="width: 100%;">
      <el-table-column label="经销商编号" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.dealerId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="经销商名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.dealerName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最近展出时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.startTime | dateFormat }} - {{ scope.row.endTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最近展出地点" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.address }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="150"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickViewBtn(scope.row,scope.$index)">详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--计算统计信息选择供应商和日期-->
    <el-dialog
      title="选择供应商和日期"
      :visible.sync="calInfo.dialog"
      :close-on-click-modal="false"
      width="30%"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="150px" ref="calInfoForm" :model="calInfo.edit" :rules="calInfo.formRules">
          <el-form-item label="体验者身份:" prop="dealerId">
            <el-select v-model="calInfo.edit.dealerId">
              <el-option v-for="item in lists.searchFilter.filter[0].data" :label="item.label" :value="item.value" :key="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="日期:" prop="day">
            <el-input v-model.trim="calInfo.edit.day" placeholder="2021-09-30 的格式"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">

        <el-button  type="primary"
                   @click="clickCalOneDealerOneDayUseInfoBtn">确 认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import enumsPicoSystem from '@/enums/picoSystem'
import elDragDialog from '@/directive/el-drag-dialog'
import {
  find_obj_from_arr_by_id,
  searchWordFiltration
} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {ScheduleModel} from "@/model/picoSystem/ScheduleModel";
import {dateFormat} from "@/filters";
import {AdminUserModel} from "@/model/erp/AdminUserModel";
import {PLATFORM_ID_PICO_SYSTEM} from "@/model/ConfigModel";
import {CommonModel} from "@/model/CommonModel";
import {validateMaxLength} from "@/utils/validate";
import {UserInfoModel} from "@/model/picoSystem/UserInfoModel";
import {msg_success} from "@/utils/ele_component";


export default {
  name: 'useInfoIndex',
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  filters: {
    dateFormat: dateFormat
  },
  computed: {
    ...mapState({})
  },
  data() {
    return {
      // 外部方法
      find_obj_from_arr_by_id: find_obj_from_arr_by_id,
      // 枚举
      enums: enums,
      enumsPicoSystem: enumsPicoSystem,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        pages: {
          size: 20
        },
        unBindPico: true,
        searchFilter: {
          search: [
            // {
            //   type: 'input',
            //   label: '经销商编号',
            //   placeholder: "请输入经销商编号",
            //   key: 'dealerId',
            //   value: '',
            //   format: (v) => {
            //     v = searchWordFiltration(v, 'mongo')
            //     return {'$regex': `.*${v}.*`}
            //   }
            // },
            {
              type: 'input',
              label: '最近展出地点',
              placeholder: "请输入地点",
              key: 'address',
              value: '',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            },
          ],
          filter: [
            {
              type: 'select',
              label: '经销商',
              key: 'dealerId',
              hidden: false,
              value: '',
              data: [],
              dataObject: [],
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {

              }
            },
            {
              type: 'timeHourRange',
              label: ['开始时间', '结束时间', '最近展出时间'],
              value: '',
              data: [],
              change: function (value) {
              },
              format: function (value) {
                return {
                  startTime: {
                    '$gte': value[0].getTime()
                  },
                  endTime: {
                    '$lte': value[1].getTime()
                  }
                }
              }
            }
          ]
        }
      },
      // 计算使用数据
      calInfo:{
        dialog:false,
        edit:{
          dealerId:"",
          day:"",
        },
        // 输入检测
        formRules: {
          'day': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 10, "日期", true),
            trigger: 'blur'
          },
          'dealerId': {required: true, message: "请选择经销商", trigger: ['change', 'blur']},
        },
      }
    }
  },
  async mounted() {
    // 获取列表
    this.ListMethods().getList(0, this.lists.pages.size, this.lists.query)
    // 初始化筛选
    this.ListMethods().initFilter()
  },
  methods: {
    // 计算某个供应商某一天的使用数据情况
    clickCalOneDealerOneDayUseInfoBtn(){
      this.$refs['calInfoForm'].validate(async validate => {
        if(validate){
          if(await UserInfoModel.calOneDayUseInfo({
            dealerId:this.calInfo.edit.dealerId,
            day:this.calInfo.edit.day
          })){
            msg_success("计算成功！")
            this.calInfo.dialog=false
          }
        }
      })
    },
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          [$this.lists.list, $this.lists.pages] = await ScheduleModel.getUseInfoIndexList(page - 1, size, "", query)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter() {
          // 获取供应商列表
          let dealerList = await AdminUserModel.getListByPlatformId({}, PLATFORM_ID_PICO_SYSTEM)
          let dealerFilterList = CommonModel.generateListFilterOptions("realName", "adminUserId", dealerList, true)
          $this.$set($this.lists.searchFilter.filter[0], "dataOrigin", dealerList)
          $this.$set($this.lists.searchFilter.filter[0], "data", dealerFilterList[0])
          $this.$set($this.lists.searchFilter.filter[0], "dataObject", dealerFilterList[1])
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击详情按钮
        clickViewBtn(entity) {
          $this.$router.push({
            name: 'useInfoDealer',
            query: {
              dealerId: entity.dealerId
            }
          })
        }
      }
    },
  }
}
</script>

<style scoped lang="scss">
.filter-bottom {
  margin-top: 20px;
}
</style>
