<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">

      </div>
    </div>

    <!--总计展示-->
    <div class="show-container">
      <div class="number-box flex flex-around">
        <div class="li flex flex-dr flex-center">
          <div class="title">IP数</div>
          <div class="number">{{ calTotalInfo.ipNumber }}</div>
        </div>
        <div class="li flex flex-dr flex-center">
          <div class="title">浏览量(PV)</div>
          <div class="number">{{ calTotalInfo.pvNumber }}</div>
        </div>
        <div class="li flex flex-dr flex-center">
          <div class="title">访客数(UV)</div>
          <div class="number">{{ calTotalInfo.uvNumber }}</div>
        </div>
        <div class="li flex flex-dr flex-center">
          <div class="title">会话数</div>
          <div class="number">{{ calTotalInfo.sessionNumber }}</div>
        </div>
        <div class="li flex flex-dr flex-center">
          <div class="title">平均访问时长</div>
          <div class="number">{{ calTotalInfo.avaTime | timeFormat }}</div>
        </div>
      </div>
    </div>

    <!--分类详细展示-->
    <el-tabs v-model="tableShow" class="tab">
      <el-tab-pane label="按日期统计（图表）" name="day-chart" type="card">
        <div class="flex flex-start">
          <el-select v-model="calDayChartType" @change="v=>ListMethods().showChart(v,'day')">
            <el-option label="IP数" value="ip"></el-option>
            <el-option label="浏览量(PV)" value="pv"></el-option>
            <el-option label="访客数(UV)" value="uv"></el-option>
            <el-option label="会话数" value="session"></el-option>
          </el-select>
        </div>
        <div class="flex flex-center">
          <div id="charts-day" style="width: 1200px;height: 500px;"></div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="按日期统计（列表）" name="day" type="card">
        <el-table class="url-list" :data="calDayArr" v-loading="calDayArrLoading" element-loading-text="加载中" border fit
                  style="width: 100%;">
          <el-table-column label="日期" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.day }}</span>
            </template>
          </el-table-column>
          <el-table-column label="IP数" align="center" width="90">
            <template slot-scope="scope">
              <span>{{ scope.row.ipNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="浏览量(PV)" align="center" width="90">
            <template slot-scope="scope">
              <span>{{ scope.row.pvNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="访客数(UV)" align="center" width="110">
            <template slot-scope="scope">
              <span>{{ scope.row.uvNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="会话数" align="center" width="90">
            <template slot-scope="scope">
              <span>{{ scope.row.sessionNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="平均访问时长" align="center" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.avaTime | timeFormat }}</span>
            </template>
          </el-table-column>

        </el-table>
      </el-tab-pane>
      <el-tab-pane label="按页面统计（列表）" name="url" type="card">
        <el-table class="url-list" :data="calUrlArr" v-loading="calUrlArrLoading" element-loading-text="加载中" border fit
                  style="width: 100%;">
          <el-table-column label="访问页面" align="center">
            <template slot-scope="scope">
              <a :href="scope.row.url" target="_blank">{{ scope.row.url }}</a>
            </template>
          </el-table-column>
          <el-table-column label="IP数" align="center" width="90">
            <template slot-scope="scope">
              <span>{{ scope.row.ipNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="浏览量(PV)" align="center" width="90">
            <template slot-scope="scope">
              <span>{{ scope.row.pvNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="访客数(UV)" align="center" width="110">
            <template slot-scope="scope">
              <span>{{ scope.row.uvNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="会话数" align="center" width="90">
            <template slot-scope="scope">
              <span>{{ scope.row.sessionNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="平均访问时长" align="center" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.avaTime | timeFormat }}</span>
            </template>
          </el-table-column>

        </el-table>
      </el-tab-pane>
      <el-tab-pane label="按城市统计（图表）" name="city-chart" type="card">
        <div class="flex flex-start">
          <el-select v-model="calCityChartType" @change="v=>ListMethods().showChart(v,'city')">
            <el-option label="IP数" value="ip"></el-option>
            <el-option label="浏览量(PV)" value="pv"></el-option>
            <el-option label="访客数(UV)" value="uv"></el-option>
            <el-option label="会话数" value="session"></el-option>
          </el-select>
        </div>
        <div class="flex flex-center">
          <div id="charts-city" style="width: 1200px;height: 500px;"></div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="按城市统计（列表）" name="city" type="card">
        <el-table class="url-list" :data="calCityArr" v-loading="calCityArrLoading" element-loading-text="加载中" border
                  fit
                  style="width: 100%;">
          <el-table-column label="城市" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.city === "0_0" ? "未知" : scope.row.city }}</span>
            </template>
          </el-table-column>
          <el-table-column label="IP数" align="center" width="90">
            <template slot-scope="scope">
              <span>{{ scope.row.ipNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="浏览量(PV)" align="center" width="90">
            <template slot-scope="scope">
              <span>{{ scope.row.pvNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="访客数(UV)" align="center" width="110">
            <template slot-scope="scope">
              <span>{{ scope.row.uvNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="会话数" align="center" width="90">
            <template slot-scope="scope">
              <span>{{ scope.row.sessionNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="平均访问时长" align="center" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.avaTime | timeFormat }}</span>
            </template>
          </el-table-column>

        </el-table>
      </el-tab-pane>
      <el-tab-pane label="按省份统计（地图）" name="province-map" type="card">
        <div class="flex flex-start">
          <el-select v-model="calProvinceChartType" @change="v=>ListMethods().showProvinceMap(v)">
            <el-option label="IP数" value="ip"></el-option>
            <el-option label="浏览量(PV)" value="pv"></el-option>
            <el-option label="访客数(UV)" value="uv"></el-option>
            <el-option label="会话数" value="session"></el-option>
          </el-select>
        </div>
        <div class="flex flex-center">
          <div id="charts-province-map" style="width: 1600px;height: 1000px;"></div>
        </div>
      </el-tab-pane>
    </el-tabs>

  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat, timeFormat} from "@/filters";
import {date_format, getQuery, getTodayDayStartAndEndTime, objectToLVArr} from "@/utils/common";
import {WebAnalyticsModel} from "@/model/erp/WebAnalyticsModel";
import * as echarts from 'echarts';
import webAnalyticsEnum from "@/enums/webAnalytics";
import chinaJson from './china.json'

export default {
  name: "overView",
  components: {
    ListSearchFilter
  },
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission,
      adminUserId: state => state.user.id
    })
  },
  filters: {dateFormat, timeFormat},
  data: function () {
    let $this = this;
    return {
      window: window,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      webAnalyticsEnum: webAnalyticsEnum,
      calTotalInfo: {
        avaTime: 0
      },
      tableShow: "day-chart",
      calUrlArr: [],
      calUrlArrLoading: false,
      calDayArr: [],
      calDayArrLoading: false,
      calDayChartType: "ip",
      calCityArr: [],
      calCityArrLoading: false,
      calCityChartType: "ip",
      calProvinceChartType: "ip",
      calProvinceArr: [],
      calProvinceObject: {},
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000000
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '访问页面',
              placeholder: "请输入访问页面",
              key: 'info.url',
              value: ''
            },
            {
              type: 'input',
              label: 'IP',
              placeholder: "请输入IP",
              key: 'info.ip',
              value: ''
            },
          ],
          filter: [
            {
              type: 'select',
              label: '统计网站',
              key: 'webSiteId',
              value: '',
              data: objectToLVArr(webAnalyticsEnum.webList),
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {

              }
            },
            {
              type: 'timeRange',
              label: ['开始时间', '结束时间', '访问时间'],
              value: '',
              data: [],
              change: function (value) {
              },
              format: function (value) {
                if (value.length === 2) {
                  const start = new Date(value[0]).getTime()
                  const end = new Date(value[1]).getTime()
                  const query = {
                    '$and': [
                      {"createTime": {'$gte': start}},
                      {"createTime": {'$lte': end}},
                    ]
                  }
                  return (query)
                }
              },
            },
          ]
        }
      },
    }
  },
  async mounted() {
    // 默认设置显示001的数据
    this.$set(this.lists.searchFilter.filter[0], "value", "001")
    // 默认设置显示当天的数据
    this.$set(this.lists.searchFilter.filter[1], "value", getTodayDayStartAndEndTime())
    //console.log(this.lists.query)

    this.ListMethods().showProvinceMap()
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 点击搜索重置按钮
        clickCleanBtn() {

        },
        // 初始化筛选列表
        async initFilter(type) {

        },
        // 获取列表
        async getList(query) {
          $this.lists.loading = true;
          let list = [];
          query = Object.assign({
            type: "enter",// 只查询进入网页
          }, query);
          // todo 修改列表，只返回需要的信息
          list = await WebAnalyticsModel.getList(query)
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },
        // 点击搜索按钮
        async clickSearchFilterBtn(query) {
          $this.lists.query = query
          await this.getList($this.lists.query)
          this.calPeriodicStat()
        },
        // 统计-计算某段时间的各项数据
        async calPeriodicStat() {
          let list = $this.lists.list
          // 统计总数据
          let pvNumber = list.length;
          let avaTime = 0;
          let ipSet = new Set()
          let uvSet = new Set()
          let sessionSet = new Set()
          let totalTime = 0;
          // 统计url数据
          let urlObject = {}
          // 分日期统计
          let dayObject = {}
          // 分城市统计
          let cityObject = {}
          // 分省份统计
          let provinceObject = {}
          list.forEach(li => {
            let info = li.info
            // 分url统计
            if (urlObject.hasOwnProperty(info.url)) {
              urlObject[info.url].pvNumber++
              urlObject[info.url].totalTime += info.stayTime
              urlObject[info.url].ipSet.add(info.ip)
              urlObject[info.url].sessionSet.add(info.sessionId)
              urlObject[info.url].uvSet.add(info.cookieId)
            } else {
              urlObject[info.url] = {
                ipSet: new Set().add(info.ip),
                uvSet: new Set().add(info.cookieId),
                sessionSet: new Set().add(info.sessionId),
                pvNumber: 1,
                totalTime: info.stayTime,
              }
            }
            // 分日期统计
            let day = li.day
            if (dayObject.hasOwnProperty(day)) {
              dayObject[day].pvNumber++
              dayObject[day].totalTime += info.stayTime
              dayObject[day].ipSet.add(info.ip)
              dayObject[day].sessionSet.add(info.sessionId)
              dayObject[day].uvSet.add(info.cookieId)
            } else {
              dayObject[day] = {
                ipSet: new Set().add(info.ip),
                uvSet: new Set().add(info.cookieId),
                sessionSet: new Set().add(info.sessionId),
                pvNumber: 1,
                totalTime: info.stayTime,
              }
            }
            // 分城市统计
            let city = (info.ipAddress.split("__"))[0]
            if (cityObject.hasOwnProperty(city)) {
              cityObject[city].pvNumber++
              cityObject[city].totalTime += info.stayTime
              cityObject[city].ipSet.add(info.ip)
              cityObject[city].sessionSet.add(info.sessionId)
              cityObject[city].uvSet.add(info.cookieId)
            } else {
              cityObject[city] = {
                ipSet: new Set().add(info.ip),
                uvSet: new Set().add(info.cookieId),
                sessionSet: new Set().add(info.sessionId),
                pvNumber: 1,
                totalTime: info.stayTime,
              }
            }
            // 分省份统计
            let province = (city.split("_"))[0]
            if (provinceObject.hasOwnProperty(province)) {
              provinceObject[province].pvNumber++
              provinceObject[province].totalTime += info.stayTime
              provinceObject[province].ipSet.add(info.ip)
              provinceObject[province].sessionSet.add(info.sessionId)
              provinceObject[province].uvSet.add(info.cookieId)
            } else {
              provinceObject[province] = {
                ipSet: new Set().add(info.ip),
                uvSet: new Set().add(info.cookieId),
                sessionSet: new Set().add(info.sessionId),
                pvNumber: 1,
                totalTime: info.stayTime,
              }
            }
            // 计算ip
            ipSet.add(info.ip)
            // 计算uv
            uvSet.add(info.cookieId)
            // 计算会话
            sessionSet.add(info.sessionId)
            // 累加浏览时间
            totalTime += info.stayTime
          })
          let ipNumber = ipSet.size
          let uvNumber = uvSet.size
          let sessionNumber = sessionSet.size;
          avaTime = totalTime / pvNumber
          if (!avaTime) {
            avaTime = 0
          }
          $this.calTotalInfo = {
            ipNumber, pvNumber, uvNumber, sessionNumber, avaTime
          }
          // 遍历urlObject生成信息
          let urlArr = []
          for (let url in urlObject) {
            if (urlObject.hasOwnProperty(url)) {
              let li = urlObject[url]
              let calInfo = {
                url: url,
                pvNumber: li.pvNumber,
                ipNumber: li.ipSet.size,
                uvNumber: li.uvSet.size,
                sessionNumber: li.sessionSet.size,
                avaTime: li.totalTime / li.pvNumber,
              }
              urlArr.push(calInfo)
            }
          }
          $this.$set($this, "calUrlArr", urlArr)
          // 遍历dayObject生成信息
          let dayArr = []
          for (let day in dayObject) {
            if (dayObject.hasOwnProperty(day)) {
              let li = dayObject[day]
              let calInfo = {
                day: day,
                pvNumber: li.pvNumber,
                ipNumber: li.ipSet.size,
                uvNumber: li.uvSet.size,
                sessionNumber: li.sessionSet.size,
                avaTime: li.totalTime / li.pvNumber,
              }
              dayArr.push(calInfo)
            }
          }
          $this.$set($this, "calDayArr", dayArr)
          // 显示日期统计图表
          this.showChart("ip", "day")
          // 遍历cityObject生成信息
          let cityArr = []
          // console.log(cityObject)
          for (let city in cityObject) {
            if (cityObject.hasOwnProperty(city)) {
              let li = cityObject[city]
              let calInfo = {
                city: city,
                pvNumber: li.pvNumber,
                ipNumber: li.ipSet.size,
                uvNumber: li.uvSet.size,
                sessionNumber: li.sessionSet.size,
                avaTime: li.totalTime / li.pvNumber,
              }
              cityArr.push(calInfo)
            }
          }
          $this.$set($this, "calCityArr", cityArr)
          // 显示城市统计图表
          this.showChart("ip", "city")
          // 遍历provinceObject生成信息
          let provinceArr = []
          console.log(provinceObject)
          for (let province in provinceObject) {
            if (provinceObject.hasOwnProperty(province)) {
              let li = provinceObject[province]
              let calInfo = {
                province: province,
                pvNumber: li.pvNumber,
                ipNumber: li.ipSet.size,
                uvNumber: li.uvSet.size,
                sessionNumber: li.sessionSet.size,
                avaTime: li.totalTime / li.pvNumber,
              }
              provinceArr.push(calInfo)
            }
          }
          $this.$set($this, "calProvinceArr", provinceArr)
          this.showProvinceMap("ip")
        },
        // 统计-图表
        showChart(type, cat) {
          // 获取最近30天实验人次数据
          let dataX = [];
          let dataY = [];
          let arr = []
          let title = ""
          switch (cat) {
            case "day":
              arr = $this.calDayArr
              break;
            case "city":
              arr = $this.calCityArr
              break;
            default:
              break
          }
          for (let i = 0; i < arr.length; i++) {
            let info = arr[i]
            dataX.push(info[cat])
            switch (type) {
              case "ip":
                dataY.push(info.ipNumber)
                title = "IP数"
                break;
              case "uv":
                dataY.push(info.uvNumber)
                title = "访客数（UV）"
                break;
              case "session":
                dataY.push(info.sessionNumber)
                title = "会话数"
                break;
              case "pv":
                dataY.push(info.pvNumber)
                title = "浏览量（PV）"
                break
              default:
                break
            }
          }
          let chartDom = document.getElementById('charts-' + cat);
          let myChart = echarts.init(chartDom);
          let option = {
            title: {
              text: title,
              x: "center",
            },
            xAxis: {
              type: 'category',
              data: dataX
            },
            yAxis: {
              type: 'value'
            },
            series: [
              {
                data: dataY,
                type: 'bar',
                itemStyle: {
                  normal: {
                    label: {
                      show: true, //开启显示数值
                      position: 'top', //数值在上方显示
                      textStyle: {  //数值样式
                        color: '#b34138',   //字体颜色
                        fontSize: 15  //字体大小
                      }
                    }
                  }
                }
              }
            ]
          };
          myChart.setOption(option);
        },
        // 统计-地图-省份显示
        showProvinceMap(type) {
          let arr = $this.calProvinceArr
          let data = []
          let title = ""
          for (let i = 0; i < arr.length; i++) {
            let info = arr[i]
            let object = {
              name: info["province"]
            }
            switch (type) {
              case "ip":
                object.value = info.ipNumber
                title = "IP数"
                break;
              case "uv":
                object.value = info.uvNumber
                title = "访客数（UV）"
                break;
              case "session":
                object.value = info.sessionNumber
                title = "会话数"
                break;
              case "pv":
                object.value = info.pvNumber
                title = "浏览量（PV）"
                break
              default:
                break
            }
            data.push(object)
          }
          let chartDom = document.getElementById('charts-province-map');
          echarts.registerMap('china', chinaJson);
          let myChart = echarts.init(chartDom);
          let barOption = {
            title: {
              show: true,
              text: title,
              z: 10,
              x: 'center',
              y: 'top',
              textAlign: 'left'
            },
            roam: false,
            visualMap: {
              min: 0,
              max: 1000,
              text: ['High', 'Low'],
              realtime: false,
              calculable: true,
              inRange: {
                color: ['green', 'orange', 'red']
              }
            },
            series: [
              {
                type: 'map',
                map: 'china',
                // label 在省份上显示的文本
                label: {
                  show: false,
                  color: 'Maroon',
                  position: 'top',
                  formatter: (params) => {
                    return `${params.name} ${params.value || 0}`
                  }
                },
                data: data
              }
            ]
          }
          myChart.setOption(barOption);
        }
      }
    },
  }
}
</script>
<style scoped lang="scss">
.show-container {
  .number-box {
    margin-top: 20px;

    .li {
      padding-top: 20px;

      .title {
        margin-bottom: 10px;
        color: #333;
      }

      .number {
        font-size: 30px;
        font-weight: bold;
        color: #333;
      }
    }
  }
}

.tab {
  margin-top: 20px;
}

.url-list {
  a {
    color: #004DCC;
  }

  a:hover {
    text-decoration: underline;
  }
}
</style>
