<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">

        </div>
      </div>
    </div>
    <!--列表-->
    <el-table class="lists" :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="执行时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column label="事件名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.info.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="事件信息" align="center">
        <template slot-scope="scope">
          <span>{{ JSON.stringify(scope.row.info.info) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="访问页面" align="center">
        <template slot-scope="scope">
          <a :href="scope.row.info.url" target="_blank">{{ scope.row.info.url }}</a>
        </template>
      </el-table-column>
      <el-table-column label="访客唯一标志码" align="center" width="100">
        <template slot-scope="scope">
          <span class="can-click userId" @click="ListMethods().clickUserId(scope.row.info.userId)">{{
              scope.row.info.userId
            }}</span>
        </template>
      </el-table-column>
      <el-table-column label="会话id" align="center">
        <template slot-scope="scope">
        <span class="can-click userId" @click="ListMethods().clickSessionId(scope.row.info.sessionId)">{{
            scope.row.info.sessionId
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="IP归属地" align="center" width="120">
        <template slot-scope="scope">
           <span class="flex flex-center">
            <span>{{ scope.row.info.ipAddress==="0_0"?"未知":scope.row.info.ipAddress }}</span>
            <el-button type="text" size="mini"
                       @click="ListMethods().clickIpView(scope.row,scope.$index)">查看</el-button>
         </span>
        </template>
      </el-table-column>
      <el-table-column label="IP" align="center" width="115">
        <template slot-scope="scope">
          <span class="can-click" @click="ListMethods().clickIP(scope.row.info.ip)">{{ scope.row.info.ip }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属网站" align="center" width="80">
        <template slot-scope="scope">
          <span>{{ webAnalyticsEnum.webList[scope.row.webSiteId] }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column align="center" label="操作" width="300"-->
      <!--                       class-name="small-padding fixed-width">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-button type="success" size="mini" round-->
      <!--                     style="margin-top: 10px;"-->
      <!--                     @click="ListMethods().clickViewBtn(scope.row,scope.$index)" :loading="scope.row.editLoading">详情-->
      <!--          </el-button>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat, timeFormat} from "@/filters";
import {date_format, getQuery, objectToLVArr} from "@/utils/common";
import {WebAnalyticsModel} from "@/model/erp/WebAnalyticsModel";
import webAnalyticsEnum from "@/enums/webAnalytics";
import {ToolsModel} from "@/model/erp/ToolsModel";

export default {
  name: "event",
  components: {
    ListSearchFilter
  },
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission,
      adminUserId: state => state.user.id
    })
  },
  filters: {dateFormat, timeFormat},
  data: function () {
    let $this = this;
    return {
      window: window,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      webAnalyticsEnum:webAnalyticsEnum,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '访问页面',
              placeholder: "请输入访问页面",
              key: 'info.url',
              value: ''
            },
            {
              type: 'input',
              label: '事件名称',
              placeholder: "请输入事件名称",
              key: 'info.name',
              value: '',
              format: (v) => {
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '事件信息',
              placeholder: "请输入事件信息",
              key: 'info.info',
              value: '',
              format: (v) => {
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: 'IP',
              placeholder: "请输入IP",
              key: 'info.ip',
              value: ''
            },
            {
              type: 'input',
              label: 'IP归属地',
              placeholder: "请输入IP归属地",
              key: 'info.ipAddress',
              value: '',
              format: (v) => {
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '访客唯一标志码',
              placeholder: "请输入访客唯一标志码",
              key: 'info.userId',
              value: ''
            },
            {
              type: 'input',
              label: '会话id',
              placeholder: "请输入会话id",
              key: 'info.sessionId',
              value: ''
            },
          ],
          filter: [
            {
              type: 'select',
              label: '统计网站',
              key: 'webSiteId',
              value: '',
              data: objectToLVArr(webAnalyticsEnum.webList),
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {

              }
            },
            {
              type: 'timeRange',
              label: ['开始时间', '结束时间', '访问时间'],
              value: '',
              data: [],
              change: function (value) {
              },
              format: function (value) {
                if (value.length === 2) {
                  const start = new Date(value[0]).getTime()
                  const end = new Date(value[1]).getTime()
                  const query = {
                    '$and': [
                      {"createTime": {'$gte': start}},
                      {"createTime": {'$lte': end}},
                    ]
                  }
                  return (query)
                }
              },
            },
          ]
        }
      },
      entityInfo: {
        addDialog: false,
        dialog: false,
        editLoading: false,
        showExperimentList: false,
        filter: {
          // 项目列表
          experimentList: {
            loading: false,
            list: []
          }
        },
        edit: {
          name: "",
        },
        // 输入检测
        formRules: {},
      },
    }
  },
  async mounted() {

    this.ListMethods().getList(0, 20, {})
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 点击了ip地址查看按钮
        clickIpView(row, index) {
          window.open("https://ip.900cha.com/" + row.info.ip + ".html")
        },
        // 点击了ip地址更新按钮
        async clickIpRefresh(row, index) {
          if (row.info.ipAddress.indexOf("内网") !== -1) {
            return
          }
          let address = await ToolsModel.getIpInfoOnline1(row.info.ip)
          $this.$set($this.lists.list[index].info, "ipAddress", address)
          //await WebAnalyticsModel.addOrEdit($this.lists.list[index])
        },
        // 点击了列表中的IP
        clickIP(ip) {
          $this.$set($this.lists.searchFilter.search[2], "value", ip)
          this.getList(0, $this.lists.pages.size, {
            "info.ip": ip
          })
        },
        // 点击了列表中的访客唯一标识码
        clickUserId(userId) {
          $this.$set($this.lists.searchFilter.search[5], "value", userId)
          this.getList(0, $this.lists.pages.size, {
            "info.userId": userId
          })
        },
        // 点击了列表中的会话id
        clickSessionId(sessionId) {
          $this.$set($this.lists.searchFilter.search[6], "value", sessionId)
          this.getList(0, $this.lists.pages.size, {
            "info.sessionId": sessionId
          })
        },
        // 点击搜索重置按钮
        clickCleanBtn() {
        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          query = Object.assign({
            type: "event",// 只查询进入网页
            webSiteId: $this.lists.searchFilter.filter[0].value ? $this.lists.searchFilter.filter[0].value : undefined,
          }, query);
          [list, $this.lists.pages] = await WebAnalyticsModel.getPageList(page - 1, size, "", query)
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter(type) {

        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
      }
    },
  }
}
</script>
<style scoped lang="scss">
.lists {
  a {
    color: #004DCC;
  }

  .can-click {
    color: #004DCC;
    cursor: pointer;
  }
}
</style>
