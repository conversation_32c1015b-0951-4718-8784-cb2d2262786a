<template>
  <div class="app-container">
    <div class="title-box" style="margin-bottom: 5px">
      {{clazzName}}
    </div>
    <!--筛选-->
    <div class="header-container clearfix">
      <div class="search-box">
        <el-input v-model="lists.searchFilter.search[0].value" :placeholder="lists.searchFilter.search[0].placeholder"
                  style="width: 300px">
          <el-button slot="append" icon="el-icon-search" @click="ListMethods().clickSearchFilterBtn()"></el-button>
        </el-input>
      </div>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="danger" style="background-color: #67C23A;border-color:#67C23A"
                     @click="ListMethods().clickAddBtn()">添加一个学生
          </el-button>
          <el-button class="el-button" type="success" v-permission="['administrator']"
                     @click="importStudent.dialog=true">批量导入学生
          </el-button>
          <el-button class="el-button" type="success" v-permission="['administrator']"
                     @click="ListMethods().clickExportBtn()">批量导出学生
          </el-button>
        </div>
      </div>
    </div>

    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;"
              @sort-change="sort=>ListMethods().sortChange(sort)">
      <el-table-column label="学号" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.account }}</span>
        </template>
      </el-table-column>
      <el-table-column label="姓名" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="性别" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sex }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="230"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="text" size="mini" round
                     @click="e=>ListMethods().clickDeleteBtn(scope.row,e)">移出本班
          </el-button>
          <el-button type="text" size="mini" round
                     @click="e=>ListMethods().clickEditBtn(scope.row,e)">修改
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--学生导入input-->
    <input
      id="importStudentFile"
      type="file"
      style="display: none"
      @change="(files)=>{ListMethods().importStudentFileChange(files)}"
    >
    <!--学生导入弹窗-->
    <el-dialog
      title="批量导入学生"
      :visible.sync="importStudent.dialog"
      width="500px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form>
          <el-form-item label="导入模板(Excel):">
            <span style="margin-right: 15px">学生导入列表.xls</span>
            <el-button type="default" size="mini" @click="ListMethods().clickDownloadBtn()">下载</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="importStudent.dialog=false">取 消
        </el-button>
        <el-button type="success" :loading="importStudent.doing"
                   @click="ListMethods().clickImportStudentBtn()">导入学生
        </el-button>
      </div>
    </el-dialog>

    <!--详情弹窗-学生详情-->
    <el-dialog
      :title="entityInfo.title"
      :visible.sync="entityInfo.dialog"
      width="500px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
          <el-form-item label="账号:" prop="account">
            <el-input v-model.trim="entityInfo.edit.account"
                      onKeypress="javascript:if(event.keyCode == 32 || event.keyCode == 43)event.returnValue = false;"
                      :disabled="entityInfo.type==='edit'">
              <div slot="suffix" v-if="entityInfo.edit.account&&entityInfo.type!=='edit'">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.account.length }} / 30
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="姓名:" prop="name">
            <el-input v-model.trim="entityInfo.edit.name"
                      onKeypress="javascript:if(event.keyCode == 32 || event.keyCode == 43)event.returnValue = false;">
              <div slot="suffix" v-if="entityInfo.edit.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.name.length }} / 30
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="性别:" prop="sex">
            <el-select v-model="entityInfo.edit.sex" style="width: 330px">
              <el-option value="男" label="男"></el-option>
              <el-option value="女" label="女"></el-option>
            </el-select>
          </el-form-item>
          <div style="padding-left: 65px" v-if="entityInfo.type==='edit'">该学生不存在，
            <el-button type="text" size="normal" @click="EntityInfoMethods().clickDeleteStudent(entityInfo.edit)">
              删除该学生档案
            </el-button>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="entityInfo.dialog=false">取 消</el-button>
        <el-button type="success" v-if="entityInfo.type==='add'" :loading="entityInfo.loading"
                   @click="EntityInfoMethods().clickAddBtn()">新 增</el-button>
        <el-button type="success" v-if="entityInfo.type==='edit'" :loading="entityInfo.loading"
                   @click="EntityInfoMethods().clickEditBtn()">确认修改</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import elDragDialog from '@/directive/el-drag-dialog'
import {msg_confirm, msg_confirm_choose, msg_err, msg_success} from '@/utils/ele_component'
import {
  arrToLVArr,
  checkPhone,
  date_format,
  downloadFile,
  find_obj_from_arr_by_id,
  getQuery,
  objectToLVArr
} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {SchoolModel} from "@/model/exp/SchoolModel";
import clip from '@/utils/clipboard';
import {DepartmentModel} from "@/model/exp/DepartmentModel";
import {ConfigModel} from "@/model/erp/ConfigModel";
import {dateFormat} from "@/filters";
import {CommonModel} from "@/model/CommonModel";
import {VEAModel} from "@/model/VEAModel";
import {validateMaxLength} from "@/utils/validate";
import {CONFIG_NAME_EXP} from "@/model/ConfigModel";
import {ExperimentModel} from "@/model/exp/ExperimentModel";
import {EnumDepartmentModel} from "@/model/exp/EnumDepartmentModel";
import {MajorModel} from "@/model/exp/MajorModel";
import {GradeModel} from "@/model/exp/GradeModel";
import {UserModel} from "@/model/exp/UserModel";
import {TeacherModel} from "@/model/exp/TeacherModel";
import {BaseUploadModel} from "@/model/BaseUploadModel";

export default {
  name: 'schoolStudentList',
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles
    }),
    administrationClazzId() {
      return this.$route.query["administrationClazzId"]
    },
    schoolId() {
      return this.$route.query["schoolId"]
    }
  },
  filters: {dateFormat},
  data() {
    // 校检账号
    const validateAccount = (rule, value, callback, maxLength, propName) => {
      if (!(value === 0 || value)) {
        callback(new Error('请输入' + propName))
        return
      }
      if (value.length > maxLength) {
        callback(new Error('最多输入' + maxLength + '个字，当前已输入' + value.length + "个字"))
      }
      // 特殊字符检测
      let regEn = /[`!#$%^&*()_+<>?:"{},.\/;'[\]]/im,
        regCn = /[·！#￥（——）：；“”‘、，|《。》？、【】[\]]/im;
      if (regEn.test(value) || regCn.test(value)) {
        callback(new Error('仅支持数字、字母、部分符号'));
      }
      // 中文检测
      regCn = /[\u4e00-\u9fa5]+/g
      if (regCn.test(value)) {
        callback(new Error('仅支持数字、字母、部分符号'));
      }
      callback()
    }
    // 校检姓名
    const validateName = (rule, value, callback, maxLength, propName) => {
      if (!(value === 0 || value)) {
        callback(new Error('请输入' + propName))
        return
      }
      if (value.length > maxLength) {
        callback(new Error('最多输入' + maxLength + '个字，当前已输入' + value.length + "个字"))
      }
      // 特殊字符检测
      let regEn = /[`!#$%^&*()@_+<>?:"{},.\/;'[\]]/im,
        regCn = /[·！#￥（——）：；“”‘、，|《。》？、【】[\]]/im;
      if (regEn.test(value) || regCn.test(value)) {
        callback(new Error('不支持特殊符号'));
      }
      // 仅支持中英文
      regEn = /[a-zA-Z]+/
      regCn = /[\u4e00-\u9fa5]+/g
      if (regEn.test(value) || regCn.test(value)) {
        // 判断是否有数字
        let regNumber = /\d/
        if (regNumber.test(value)) {
          callback(new Error('不能输入数字'));
        }
      } else {
        callback(new Error('仅支持中英文'));
      }
      callback()
    }
    return {
      clazzName:getQuery("clazzName"),
      // 外部方法
      date_format: date_format,
      // 枚举
      enums: enums,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '学号、姓名',
              placeholder: "请输入学号、姓名",
              key: 'search',
              value: '',
            },
          ],
          filter: []
        }
      },
      // 实体
      entityInfo: {
        dialog: false,
        loading: false,
        edit: {
          account: "",
          name: ""
        },
        // 输入检测
        formRules: {
          'account': {required: true, validator: (r, v, c) => validateAccount(r, v, c, 30, '账号'), trigger: 'blur'},
          'name': {required: true, validator: (r, v, c) => validateName(r, v, c, 20, "姓名"), trigger: 'blur'},
          'sex': {required: true, message: '请选择性别', trigger: 'change'},
        },
      },
      // 导入学生
      importStudent: {
        dialog: false,
        doing: false,
      }
    }
  },
  async mounted() {
    this.lists.queryBase = {
      administrationClazzId: this.administrationClazzId
    }
    // 获取列表
    this.ListMethods().getList(1, this.lists.pages.size, this.lists.query)
    // 初始化筛选
    this.ListMethods().initFilter()
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 点击下载导入列表按钮
        clickDownloadBtn() {
          // todo 做到配置文件里
          downloadFile("http://resouce.cdzyhd.com/**********/%E5%AD%A6%E7%94%9F%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E5%88%97%E8%A1%A8.xlsx", "学生导入列表.xls")
        },
        // 点击了学生导入按钮
        clickImportStudentBtn() {
          const uploader = document.getElementById('importStudentFile')
          uploader.click()
        },
        // 导入学生文件选择
        async importStudentFileChange(files) {
          const file = files.target.files[0]
          // 判断文件类型
          if (!BaseUploadModel.isImportExcelFile(file)) {
            return false
          }
          document.getElementById('importStudentFile').value = ''
          $this.importStudent.doing = true
          // todo
          if (await UserModel.importStudent(file, $this.administrationClazzId).catch(err => {
            $this.importStudent.dialog = false
            msg_err("批量导入学生失败")
          })) {
            $this.importStudent.dialog = false
            msg_success('批量导入学生成功')
            $this.ListMethods().getList(1, $this.lists.pages.size, $this.lists.query)
          }
          $this.importStudent.doing = false
        },
        // 点击导出按钮
        async clickExportBtn() {
          let search = $this.lists.query.search
          search = search ? search : "null"
          UserModel.exportStudentList($this.administrationClazzId, search)
        },
        // 获取列表
        async getList(page, size, query) {
          query = Object.assign(query, $this.lists.queryBase)
          $this.lists.loading = true;
          // 增加默认排序
          // if(!query.sort){
          //   query.sort="createtime,desc"
          // }
          [$this.lists.list, $this.lists.pages] = await UserModel.getList(page, size, query)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number, size, $this.lists.query)
        },
        // 排序被改变
        async sortChange(sort) {
          let querySort = $this.lists.query.sort
          querySort = CommonModel.elementTableSort(sort)
          $this.$set($this.lists.query, "sort", querySort)
          this.getList(1, $this.lists.pages.size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter(type, setNull) {

        },
        // 点击搜索按钮
        clickSearchFilterBtn() {
          let query = {}
          query.search = $this.lists.searchFilter.search[0].value
          $this.lists.query = query
          this.getList(1, $this.lists.pages.size, $this.lists.query)
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "新增学生"
          $this.entityInfo.edit = {
            account: "",
            name: "",
            sex: "女"
          }
          $this.entityInfo.dialog = true;
          setTimeout(() => {
            $this.$refs["entityInfoForm"].clearValidate()
          }, 300)
        },
        // 点击编辑按钮
        async clickEditBtn(entity) {
          $this.entityInfo.type = "edit"
          $this.entityInfo.title = "修改学生信息"
          $this.entityInfo.edit = JSON.parse(JSON.stringify(entity))
          $this.entityInfo.dialog = true;
          setTimeout(() => {
            $this.$refs["entityInfoForm"].clearValidate()
          }, 300)
        },
        // 点击删除按钮
        async clickDeleteBtn(entity) {
          if (await msg_confirm_choose("该学生移出本班后，不影响已有的实验。<br/> 确认移出？", "移出本班", "取消", "确认移出", true) === "right") {
            if (await UserModel.RemoveOne(entity)) {
              msg_success("移出成功")
              $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
            } else {
              msg_err("移出失败")
            }
          }
        }
      }
    },
    // 实体Methods
    EntityInfoMethods() {
      let $this = this;
      return {
        // 点击新增按钮
        async clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 构建参数
              $this.entityInfo.edit.administrationClazzId = $this.administrationClazzId
              $this.entityInfo.edit.schoolId = $this.schoolId
              $this.entityInfo.loading = true
              if (await UserModel.save($this.entityInfo.edit).catch(() => {
                $this.entityInfo.loading = false
              })) {
                msg_success('新增成功')
                $this.ListMethods().getList(1, $this.lists.pages.size, $this.lists.query)
                $this.entityInfo.dialog = false
              }
              $this.entityInfo.loading = false
            }
          })
        },
        // 点击修改按钮
        clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              $this.entityInfo.loading = true
              if (await msg_confirm('确认要修改该学生信息吗？').catch(err => {
                $this.entityInfo.loading = false
              })) {
                if (await UserModel.update($this.entityInfo.edit)) {
                  msg_success('修改成功')
                  $this.entityInfo.dialog = false
                  $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
                }
              }
              $this.entityInfo.loading = false
            }
          })
        },
        // 点击删除学生档案按钮
        async clickDeleteStudent(entity) {
          if (await msg_confirm_choose("已删除档案的学生不再保留原学籍学号，原学号可对新入学学生使用。<br>" +
            "档案删除后无法恢复，确认删除？", "删除学生档案", "取消", "确认删除", true) === "right") {
            if (await UserModel.deleteOne(entity)) {
              msg_success("删除学生档案成功")
              $this.entityInfo.dialog = false
              $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
            }
          }
        },
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
