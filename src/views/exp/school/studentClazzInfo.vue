<template>
  <div class="app-container">
    <div class="edit-container">
      <el-form label-width="120px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
        <el-form-item label="行政班级名称：" prop="name">
          <el-input onKeypress="javascript:if(event.keyCode == 32 || event.keyCode == 43)event.returnValue = false;"
                    v-model.trim="entityInfo.edit.name" placeholder="请输入行政班级名称，限30字以内">
            <div slot="suffix" v-if="entityInfo.edit.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.name.length }} / 30
                  </span>
                </span>
            </div>
          </el-input>
        </el-form-item>
        <el-form-item label="所属学校：" prop="schoolId">
<!--          <el-select v-model="entityInfo.edit.schoolId" style="width: 430px" :disabled="entityInfo.type==='edit'||adminRoles.includes('expTeacher')"-->
<!--                     @change="v=>EntityInfoMethods().onSchoolChange(v)">-->
<!--            <el-option v-for="item in lists.searchFilter.filter[0].data" :value="item.value" :label="item.label"-->
<!--                       :key="item.value">-->
<!--            </el-option>-->
<!--          </el-select>-->
          <el-select
            style="width: 430px"
            :disabled="entityInfo.type==='edit'||adminRoles.includes('expTeacher')"
            v-model="entityInfo.edit.schoolId"
            filterable
            remote
            reserve-keyword
            placeholder=""
            :remote-method="v=>EntityInfoMethods().querySchool(v)"
            :loading="entityInfo.filter.schoolList.loading"
            @change="v=>EntityInfoMethods().onSchoolChange(v)">
            <el-option
              v-for="item in entityInfo.filter.schoolList.list"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-button style="margin-left: 10px" type="text" size="mini" v-permission="['administrator']"
                     @click="EntityInfoMethods().clickAddSchoolBtn()">添加、修改学校
          </el-button>
        </el-form-item>
        <el-form-item label="所属院系：" prop="enumDepartmentId" v-if="entityInfo.edit.schoolId">
          <el-select v-model="entityInfo.edit.enumDepartmentId" style="width: 430px"
                     @change="v=>EntityInfoMethods().onDepartmentChange(v)">
            <el-option v-for="item in lists.searchFilter.filter[1].data" :value="item.value" :label="item.label"
                       :key="item.value">
            </el-option>
          </el-select>
          <el-button style="margin-left: 10px" type="text" size="mini"
                     @click="EntityInfoMethods().clickAddDepartmentBtn()">添加、修改院系
          </el-button>
        </el-form-item>
        <el-form-item label="所属专业：" prop="majorId" v-if="entityInfo.edit.enumDepartmentId">
          <el-select v-model="entityInfo.edit.majorId" style="width: 430px">
            <el-option v-for="item in lists.searchFilter.filter[2].data" :value="item.value" :label="item.label"
                       :key="item.value">
            </el-option>
          </el-select>
          <el-button style="margin-left: 10px" type="text" size="mini"
                     @click="EntityInfoMethods().clickAddMajorBtn()">添加、修改专业
          </el-button>
        </el-form-item>
        <el-form-item label="所属年级：" prop="gradeId" v-if="entityInfo.edit.enumDepartmentId">
          <el-select v-model="entityInfo.edit.gradeId" style="width: 470px">
            <el-option v-for="item in lists.searchFilter.filter[3].data" :value="item.value" :label="item.label"
                       :key="item.value">
              <div class="flex flex-between">
                <span>{{ item.label }}</span>
                <div>
                  <el-button type="text" size="mini"
                             @click="EntityInfoMethods().clickEditGradeBtn(item.value,item.label)">修改
                  </el-button>
                  <el-button type="text" size="mini"
                             @click="EntityInfoMethods().clickDeleteGradeBtn(item.value,item.label)">删除
                  </el-button>
                </div>
              </div>
            </el-option>
          </el-select>
          <el-button style="margin-left: 10px" type="text" size="mini"
                     @click="EntityInfoMethods().clickAddGradeBtn()">添加年级
          </el-button>
        </el-form-item>
      </el-form>
      <div class="buttons">
        <el-button type="default"
                   @click="EntityInfoMethods().clickCancelBtn()">取 消
        </el-button>
        <el-button type="success" v-if="entityInfo.type==='add'" :loading="entityInfo.loading"
                   @click="EntityInfoMethods().clickAddBtn()">提 交
        </el-button>
        <el-button type="success" v-if="entityInfo.type==='edit'" :loading="entityInfo.loading"
                   @click="EntityInfoMethods().clickEditBtn()">提 交
        </el-button>
      </div>
      <!--年级编辑框-->
      <el-dialog
        :title="grade.title"
        :visible.sync="grade.dialog"
        width="670px"
        center
        v-el-drag-dialog>
        <div class="dialog-container">
          <el-form label-width="120px" ref="gradeForm" :model="grade.edit" :rules="grade.formRules">
            <el-form-item label="年级名称:" prop="name">
              <el-input v-model.trim="grade.edit.name" placeholder="请输入年级名称，限20字以内"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="grade.dialog=false">取 消</el-button>
        <el-button v-if="grade.type==='add'" type="primary"
                   @click="EntityInfoMethods().sureAddGrade()">提 交</el-button>
        <el-button v-if="grade.type==='edit'" type="primary"
                   @click="EntityInfoMethods().sureEditGrade()">提 交</el-button>
      </span>
      </el-dialog>
    </div>
    <!--抽屉-学校管理页-->
    <el-drawer
      :visible.sync="drawer.schoolShow"
      direction="rtl"
      :destroy-on-close="true"
      :show-close="false"
      :modal-append-to-body="false"
      :append-to-body="true"
      @close="EntityInfoMethods().onSchoolDrawerClose()"
      size="50%">
      <div class="drawer-container">
        <school-page :as-component="true" @afterEdit="drawer.schoolEdited=true"></school-page>
      </div>
    </el-drawer>
    <!--抽屉-学院枚举管理页-->
    <el-drawer
      :visible.sync="drawer.departmentShow"
      :destroy-on-close="true"
      direction="rtl"
      :show-close="false"
      :modal-append-to-body="false"
      :append-to-body="true"
      @close="EntityInfoMethods().onDepartmentDrawerClose()"
      size="50%">
      <div class="drawer-container">
        <enum-department-page :as-component="true" @afterEdit="drawer.departmentEdited=true"
                              :school-info="{schoolId:this.entityInfo.edit.schoolId,schoolName:lists.searchFilter.filter[0]['dataObject'][this.entityInfo.edit.schoolId]}"></enum-department-page>
      </div>
    </el-drawer>
    <!--抽屉-专业管理页-->
    <el-drawer
      :visible.sync="drawer.majorShow"
      direction="rtl"
      :show-close="false"
      :destroy-on-close="true"
      :modal-append-to-body="false"
      :append-to-body="true"
      @close="EntityInfoMethods().onMajorDrawerClose()"
      size="50%">
      <div class="drawer-container">
        <major-page :as-component="true" @afterEdit="drawer.majorEdited=true"
                    :department-info="{departmentId:this.entityInfo.edit.enumDepartmentId,departmentName:lists.searchFilter.filter[1]['dataObject'][this.entityInfo.edit.enumDepartmentId]}"></major-page>
      </div>
    </el-drawer>
  </div>

</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import elDragDialog from '@/directive/el-drag-dialog'
import {msg_confirm, msg_err, msg_success} from '@/utils/ele_component'
import {arrToLVArr, checkPhone, date_format, find_obj_from_arr_by_id, objectToLVArr} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {SchoolModel} from "@/model/exp/SchoolModel";
import {dateFormat} from "@/filters";
import {CommonModel} from "@/model/CommonModel";
import {VEAModel} from "@/model/VEAModel";
import {validateMaxLength} from "@/utils/validate";
import {EnumDepartmentModel} from "@/model/exp/EnumDepartmentModel";
import {MajorModel} from "@/model/exp/MajorModel";
import {GradeModel} from "@/model/exp/GradeModel";
import {AdministrationClazzModel} from "@/model/exp/AdministrationClazzModel";
import SchoolPage from "./school"
import EnumDepartmentPage from "./enumDepartment"
import MajorPage from "./major"

export default {
  name: 'schoolStudentClazzInfo',
  components: {ListSearchFilter, SchoolPage, EnumDepartmentPage, MajorPage},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles
    })
  },
  filters: {dateFormat},
  data() {
    return {
      // 外部方法
      date_format: date_format,
      find_obj_from_arr_by_id: find_obj_from_arr_by_id,
      // 枚举
      enums: enums,
      // 列表
      lists: {
        searchFilter: {
          search: [],
          filter: [
            {
              type: 'select',
              label: '学校',
              key: 'schoolid',
              value: '',
              hidden: true,
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
            {
              type: 'select',
              label: '院系名称',
              key: 'departmentid',
              value: '',
              hidden: true,
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
            {
              type: 'select',
              label: '专业名称',
              key: 'majorid',
              value: '',
              hidden: true,
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
            {
              type: 'select',
              label: '年级名称',
              hidden: true,
              key: 'gradeid',
              value: '',
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
          ]
        }
      },
      // 新增
      entityInfo: {
        dialog: false,
        loading: false,
        edit: {
          schoolId: "",
          enumDepartmentId: "",
          gradeId: "",
          majorId: "",
          name: ""
        },
        filter:{
          schoolList:{
            list:[],
            loading:false
          }
        },
        // 输入检测
        formRules: {
          'name': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 30, '行政班名称'),
            trigger: 'blur'
          },
          'schoolId': {
            required: true,
            message: "请选择学校",
            trigger: 'change'
          },
          enumDepartmentId: {
            required: true,
            message: "请选择院系",
            trigger: 'change'
          },
          majorId: {
            required: true,
            message: "请选择专业",
            trigger: 'change'
          },
          gradeId: {
            required: true,
            message: "请选择年级",
            trigger: 'change'
          }
        },
      },
      // 年级
      grade: {
        dialog: false,
        type: "add",
        edit: {},
        formRules: {
          'name': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 10, "年级名称"), trigger: 'blur'},
        },
      },
      // 抽屉
      drawer: {
        schoolShow: false,
        schoolEdited: false,
        departmentShow: false,
        departmentEdited: false,
        majorShow: false,
        majorEdited: false,
      }
    }
  },
  async mounted() {
    // 如果是实验平台教师角色
    if(this.adminRoles.includes("expTeacher")){
      this.entityInfo.edit.schoolId=sessionStorage.getItem("expTeacher_schoolId")
      this.ListMethods().initFilter(1, false)
      this.ListMethods().initFilter(3, false)
    }

    this.ListMethods().initFilter()

    // 如果是编辑模式
    let type = this.$route.query["type"]
    this.entityInfo.type = type
    if (type === "edit") {// 编辑模式
      let id = this.$route.query["id"]
      let info = await AdministrationClazzModel.getOne(id)
      if (info) {
        this.entityInfo.edit = info
        // 获取各项select信息
        this.ListMethods().initFilter(1, false)
        this.ListMethods().initFilter(2, false)
        this.ListMethods().initFilter(3, false)
      } else {
        msg_err("未找到该班级信息！")
      }
    } else {
      //VEAModel.setBreadThirdTitle("添加班级")
    }
    // 初始化筛选

  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 初始化筛选列表
        async initFilter(type, setNull) {
          if (!type || type === 0) {
            // 获取学校列表
            let seriesList = await SchoolModel.getList(0, 0, {})
            let generateListFilter0 = CommonModel.generateListFilterOptions('name', 'id', seriesList, false)
            $this.$set($this.lists.searchFilter.filter[0], "data", generateListFilter0[0])
            $this.$set($this.lists.searchFilter.filter[0], "dataObject", generateListFilter0[1])
            $this.$set($this.lists.searchFilter.filter[0], "dataOrigin", seriesList)
            $this.$set($this.entityInfo.filter.schoolList, "list", generateListFilter0[0])
          }
          if (type === 1) {
            // 获取学校所属学院枚举列表
            let departmentList = await EnumDepartmentModel.getList(0, 0, {
              schoolId: $this.entityInfo.edit.schoolId,
            })
            let generateListFilter1 = CommonModel.generateListFilterOptions('name', 'id', departmentList)
            $this.$set($this.lists.searchFilter.filter[1], "data", generateListFilter1[0])
            $this.$set($this.lists.searchFilter.filter[1], "dataObject", generateListFilter1[1])
            $this.$set($this.lists.searchFilter.filter[1], "dataOrigin", departmentList)
            if (setNull) {
              $this.$set($this.entityInfo.edit, "enumDepartmentId", "")
            }
          }
          if (type === 2) {
            // 获取学院所属专业
            let majorList = await MajorModel.getList(0, 0, {
              departmentId: $this.entityInfo.edit.enumDepartmentId,
            })
            let generateListFilter2 = CommonModel.generateListFilterOptions('name', 'id', majorList)
            $this.$set($this.lists.searchFilter.filter[2], "data", generateListFilter2[0])
            $this.$set($this.lists.searchFilter.filter[2], "dataObject", generateListFilter2[1])
            $this.$set($this.lists.searchFilter.filter[2], "dataOrigin", majorList)
            if (setNull) {
              $this.$set($this.entityInfo.edit, "majorId", "")
            }
          }
          if (type === 3) {
            // 获取学校所属年级
            let gradeList = await GradeModel.getList(0, 0, {
              schoolId: $this.entityInfo.edit.schoolId,
            })
            let generateListFilter3 = CommonModel.generateListFilterOptions('name', 'id', gradeList)
            $this.$set($this.lists.searchFilter.filter[3], "data", generateListFilter3[0])
            $this.$set($this.lists.searchFilter.filter[3], "dataObject", generateListFilter3[1])
            $this.$set($this.lists.searchFilter.filter[3], "dataOrigin", gradeList)
            if (setNull) {
              $this.$set($this.entityInfo.edit, "gradeId", "")
            }
          }
        },


      }
    },
    // 试题Methods
    EntityInfoMethods() {
      let $this = this;
      return {
        // 学校搜索
        async querySchool(name) {
          $this.$set($this.entityInfo.filter.schoolList, "loading", true)
          let list = await SchoolModel.getSchoolListByName(name)
          let listResult = []
          list.forEach(li => {
            listResult.push({
              value: li.id,
              label: li.name
            })
          })
          $this.$set($this.entityInfo.filter.schoolList, "list", listResult)
          $this.$set($this.entityInfo.filter.schoolList, "loading", false)
        },
        // 学校被改变
        async onSchoolChange(v) {
          $this.ListMethods().initFilter(1, true)
          $this.ListMethods().initFilter(3, true)
          $this.$forceUpdate();
        },
        // 学院被改变
        async onDepartmentChange(v) {
          $this.ListMethods().initFilter(2, true)
          $this.$forceUpdate();
        },
        // 点击取消按钮
        clickCancelBtn() {
          $this.$router.go(-1);
        },
        // 点击新增学校按钮
        clickAddSchoolBtn() {
          $this.drawer.schoolShow = true
        },
        // 学校抽屉被关闭
        onSchoolDrawerClose() {
          if ($this.drawer.schoolEdited) {
            $this.ListMethods().initFilter(0, true)
            $this.$set($this.entityInfo.edit,"schoolId","")
            this.onSchoolChange()
          }
        },
        // 点击新增、修改学院按钮
        clickAddDepartmentBtn() {
          $this.drawer.departmentShow = true
        },
        // 学院枚举抽屉被关闭
        onDepartmentDrawerClose() {
          if ($this.drawer.departmentEdited) {
            $this.$set($this.entityInfo.edit,"enumDepartmentId","")
            this.onSchoolChange()
          }
        },
        // 点击新增、修改专业按钮
        clickAddMajorBtn() {
          $this.drawer.majorShow = true
        },
        // 专业抽屉被关闭
        onMajorDrawerClose() {
          if($this.drawer.majorEdited){
            $this.$set($this.entityInfo.edit,"majorId","")
            this.onDepartmentChange()
          }
        },
        // 点击新增年级按钮
        async clickAddGradeBtn() {
          $this.grade.dialog = true;
          $this.grade.type = "add"
          $this.grade.edit = {};
          $this.grade.title = "新增年级";
          setTimeout(() => {
            $this.$refs['gradeForm'].clearValidate()
          }, 300)
        },
        // 确认新增年级
        async sureAddGrade() {
          $this.$refs['gradeForm'].validate(async validate => {
            if (validate) {
              let result = await GradeModel.save({
                schoolId: $this.entityInfo.edit.schoolId,
                name: $this.grade.edit.name
              })
              if (result) {
                msg_success("新增年级成功")
                $this.ListMethods().initFilter(3)
                $this.grade.dialog = false;
              }
            }
          });
        },
        // 点击修改年级按钮
        async clickEditGradeBtn(id, name) {
          $this.grade.dialog = true;
          $this.grade.type = "edit"
          $this.grade.edit = {
            id: id,
            name: name
          };
          $this.grade.title = "修改年级";
          setTimeout(() => {
            $this.$refs['gradeForm'].clearValidate()
          }, 300)
        },
        // 确认修改年级
        async sureEditGrade() {
          $this.$refs['gradeForm'].validate(async validate => {
            if (validate) {
              if (await GradeModel.update({
                id: $this.grade.edit.id,
                name: $this.grade.edit.name
              })) {
                msg_success("修改年级成功")
                $this.ListMethods().initFilter(3)
                $this.grade.dialog = false;
              }
            }
          });
        },
        // 点击删除年级按钮
        async clickDeleteGradeBtn(id) {
          if (await msg_confirm("确定要删除这个年级吗?")) {
            if (await GradeModel.deleteOne([id])) {
              msg_success("删除成功")
              $this.$set($this.entityInfo.edit, "gradeId", "")
              $this.ListMethods().initFilter(3)
            } else {
              msg_err("删除失败")
            }
          }
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              $this.entityInfo.loading = true
              if (await AdministrationClazzModel.save({
                name: $this.entityInfo.edit.name,
                schoolId: $this.entityInfo.edit.schoolId,
                enumDepartmentId: $this.entityInfo.edit.enumDepartmentId,
                majorId: $this.entityInfo.edit.majorId,
                gradeId: $this.entityInfo.edit.gradeId,
              }).catch(err => {
                $this.entityInfo.loading = false
              })) {
                msg_success('新增成功')
                $this.entityInfo.loading = false
                $this.entityInfo.dialog = false
                this.clickCancelBtn()
              }
            }
          })
        },
        // 点击修改按钮
        async clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              $this.entityInfo.loading = true
              if (await AdministrationClazzModel.update($this.entityInfo.edit).catch(err => {
                $this.entityInfo.loading = false
              })) {
                msg_success('保存成功')
                $this.entityInfo.loading = false
                $this.entityInfo.dialog = false
                this.clickCancelBtn()
              }
            }
          })
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.edit-container {
  width: 650px;
}

.buttons {
  text-align: center;
}
</style>
