<template>
  <div class="app-container">
    <div class="edit-container">
      <el-form label-width="120px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
        <el-form-item label="账号:" prop="account">
          <el-input v-model.trim="entityInfo.edit.account"
                    onKeypress="javascript:if(event.keyCode == 32 || event.keyCode == 43)event.returnValue = false;"
                    :disabled="entityInfo.type==='edit'">
            <div slot="suffix" v-if="entityInfo.edit.account&&entityInfo.type!=='edit'">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.account.length }} / 30
                  </span>
                </span>
            </div>
          </el-input>
        </el-form-item>
        <el-form-item label="姓名:" prop="name">
          <el-input v-model.trim="entityInfo.edit.name"
                    onKeypress="javascript:if(event.keyCode == 32 || event.keyCode == 43)event.returnValue = false;">
            <div slot="suffix" v-if="entityInfo.edit.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.name.length }} / 30
                  </span>
                </span>
            </div>
          </el-input>
        </el-form-item>
        <el-form-item label="性别:" prop="sex">
          <el-select v-model="entityInfo.edit.sex" style="width: 530px">
            <el-option value="男" label="男"></el-option>
            <el-option value="女" label="女"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年龄:" prop="age">
          <el-input v-model.trim.number="entityInfo.edit.age" maxlength="2" type="number"
                    onKeypress="javascript:if(event.keyCode == 32 || event.keyCode == 43)event.returnValue = false;"></el-input>
        </el-form-item>
        <el-form-item label="联系方式:" prop="contact">
          <el-input v-model.trim="entityInfo.edit.contact">
            <div slot="suffix" v-if="entityInfo.edit.contact">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.contact.length }} / 30
                  </span>
                </span>
            </div>
          </el-input>
        </el-form-item>
        <el-form-item label="所属学校：" prop="schoolId">
          <!--          <el-select v-model="entityInfo.edit.schoolId" style="width: 430px"-->
          <!--                     :disabled="entityInfo.type==='edit'||adminRoles.includes('expTeacher')"-->
          <!--                     @change="v=>EntityInfoMethods().onSchoolChange(v)">-->
          <!--            <el-option v-for="item in lists.searchFilter.filter[0].data" :value="item.value" :label="item.label"-->
          <!--                       :key="item.value">-->
          <!--            </el-option>-->
          <!--          </el-select>-->
          <el-select
            style="width: 430px"
            :disabled="entityInfo.type==='edit'||adminRoles.includes('expTeacher')"
            v-model="entityInfo.edit.schoolId"
            filterable
            remote
            reserve-keyword
            placeholder=""
            :remote-method="v=>EntityInfoMethods().querySchool(v)"
            :loading="entityInfo.filter.schoolList.loading"
            @change="v=>EntityInfoMethods().onSchoolChange(v)">
            <el-option
              v-for="item in entityInfo.filter.schoolList.list"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-button style="margin-left: 10px" type="text" size="mini" v-permission="['administrator']"
                     @click="EntityInfoMethods().clickAddSchoolBtn()">添加、修改学校
          </el-button>
        </el-form-item>
        <el-form-item label="所属院系：" prop="enumDepartmentId" v-if="entityInfo.edit.schoolId">
          <el-select v-model="entityInfo.edit.enumDepartmentId" style="width: 430px"
                     :disabled="entityInfo.type==='edit'"
                     @change="v=>EntityInfoMethods().onDepartmentChange(v)">
            <el-option v-for="item in lists.searchFilter.filter[1].data" :value="item.value" :label="item.label"
                       :key="item.value">
            </el-option>
          </el-select>
          <el-button style="margin-left: 10px" type="text" size="mini"
                     @click="EntityInfoMethods().clickAddDepartmentBtn()">添加、修改院系
          </el-button>
        </el-form-item>
      </el-form>
      <div class="buttons">
        <el-button type="default"
                   @click="EntityInfoMethods().clickCancelBtn()">取 消
        </el-button>
        <el-button type="success" v-if="entityInfo.type==='add'" :loading="entityInfo.loading"
                   @click="EntityInfoMethods().clickAddBtn()">提 交
        </el-button>
        <el-button type="success" v-if="entityInfo.type==='edit'" :loading="entityInfo.loading"
                   @click="EntityInfoMethods().clickEditBtn()">提 交
        </el-button>
      </div>
    </div>
    <!--抽屉-学校管理页-->
    <el-drawer
      :visible.sync="drawer.schoolShow"
      direction="rtl"
      :destroy-on-close="true"
      :show-close="false"
      :modal-append-to-body="false"
      :append-to-body="true"
      @close="EntityInfoMethods().onSchoolDrawerClose()"
      size="50%">
      <div class="drawer-container">
        <school-page :as-component="true" @afterEdit="drawer.schoolEdited=true"></school-page>
      </div>
    </el-drawer>
    <!--抽屉-学院枚举管理页-->
    <el-drawer
      :visible.sync="drawer.departmentShow"
      :destroy-on-close="true"
      direction="rtl"
      :show-close="false"
      :modal-append-to-body="false"
      :append-to-body="true"
      @close="EntityInfoMethods().onDepartmentDrawerClose()"
      size="50%">
      <div class="drawer-container">
        <enum-department-page :as-component="true" @afterEdit="drawer.departmentEdited=true"
                              :school-info="{schoolId:this.entityInfo.edit.schoolId,schoolName:lists.searchFilter.filter[0]['dataObject'][this.entityInfo.edit.schoolId]}"></enum-department-page>
      </div>
    </el-drawer>
  </div>

</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import elDragDialog from '@/directive/el-drag-dialog'
import {msg_confirm, msg_err, msg_success} from '@/utils/ele_component'
import {arrToLVArr, checkPhone, date_format, find_obj_from_arr_by_id, objectToLVArr} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {SchoolModel} from "@/model/exp/SchoolModel";
import {dateFormat} from "@/filters";
import {CommonModel} from "@/model/CommonModel";
import {VEAModel} from "@/model/VEAModel";
import {validateMaxLength} from "@/utils/validate";
import {EnumDepartmentModel} from "@/model/exp/EnumDepartmentModel";
import {MajorModel} from "@/model/exp/MajorModel";
import {GradeModel} from "@/model/exp/GradeModel";
import {AdministrationClazzModel} from "@/model/exp/AdministrationClazzModel";
import SchoolPage from "./school"
import EnumDepartmentPage from "./enumDepartment"
import MajorPage from "./major"
import {TeacherModel} from "@/model/exp/TeacherModel";

export default {
  name: 'teacherInfo',
  components: {ListSearchFilter, SchoolPage, EnumDepartmentPage, MajorPage},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles
    }),
    schoolId() {
      return this.$route.query["schoolId"]
    },
    enumDepartmentId() {
      return this.$route.query["enumDepartmentId"]
    }
  },
  filters: {dateFormat},
  data() {
    // 校检年龄
    const validateAge = (rule, value, callback) => {
      let reg = /^[0-9]+$/
      if (!reg.test(value)) {
        callback(new Error('只能输入正整数'))
      }
      if (value <= 0) {
        callback(new Error('需大于0'))
      }
      if (value >= 100) {
        callback(new Error('需小于100'))
      }
      callback()
    }
    return {
      // 外部方法
      date_format: date_format,
      find_obj_from_arr_by_id: find_obj_from_arr_by_id,
      // 枚举
      enums: enums,
      // 列表
      lists: {
        searchFilter: {
          search: [],
          filter: [
            {
              type: 'select',
              label: '学校',
              key: 'schoolid',
              value: '',
              hidden: true,
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
            {
              type: 'select',
              label: '院系名称',
              key: 'departmentid',
              value: '',
              hidden: true,
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
          ]
        }
      },
      // 新增
      entityInfo: {
        dialog: false,
        loading: false,
        edit: {
          schoolId: "",
          enumDepartmentId: "",
          sex: "女"
        },
        filter: {
          schoolList: {
            list: [],
            loading: false
          }
        },
        // 输入检测
        formRules: {
          'account': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 30, "账号"), trigger: 'blur'},
          'name': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 30, "姓名"), trigger: 'blur'},
          'sex': {required: true, message: '请选择性别', trigger: 'change'},
          'age': {required: true, validator: validateAge, trigger: 'blur'},
          'contact': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 30, "联系方式"), trigger: 'blur'},
          'schoolId': {
            required: true,
            message: "请选择学校",
            trigger: 'change'
          },
          enumDepartmentId: {
            required: true,
            message: "请选择院系",
            trigger: 'change'
          },
        },
      },
      // 抽屉
      drawer: {
        schoolShow: false,
        schoolEdited: false,
        departmentShow: false,
        departmentEdited: false,
      }
    }
  },
  async mounted() {
    // 如果是实验平台教师角色
    if (this.adminRoles.includes("expTeacher")) {
      this.entityInfo.edit.schoolId = sessionStorage.getItem("expTeacher_schoolId")
      this.ListMethods().initFilter(1, false)
    }

    this.ListMethods().initFilter()

    // 如果是编辑模式
    let type = this.$route.query["type"]
    this.entityInfo.type = type
    if (type === "edit") {// 编辑模式
      let id = this.$route.query["id"]
      let info = await TeacherModel.getOne(id)
      if (info) {
        this.entityInfo.edit = info

      } else {
        msg_err("未找到该教师信息！")
      }
    } else {
      //VEAModel.setBreadThirdTitle("添加班级")
    }
    if (this.schoolId) {
      this.$set(this.entityInfo.edit, "schoolId", this.schoolId)
    }
    if (this.enumDepartmentId) {
      this.$set(this.entityInfo.edit, "enumDepartmentId", this.enumDepartmentId)
      this.ListMethods().initFilter(1, false)
    }
    // 初始化筛选

  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 初始化筛选列表
        async initFilter(type, setNull) {
          if (!type || type === 0) {
            // 获取学校列表
            let schoolList = await SchoolModel.getList(0, 0, {})
            let generateListFilter0 = CommonModel.generateListFilterOptions('name', 'id', schoolList, false)
            $this.$set($this.lists.searchFilter.filter[0], "data", generateListFilter0[0])
            $this.$set($this.lists.searchFilter.filter[0], "dataObject", generateListFilter0[1])
            $this.$set($this.lists.searchFilter.filter[0], "dataOrigin", schoolList)
            $this.$set($this.entityInfo.filter.schoolList, "list", generateListFilter0[0])
          }
          if (type === 1) {
            // 获取学校所属学院枚举列表
            let departmentList = await EnumDepartmentModel.getList(0, 0, {
              schoolId: $this.entityInfo.edit.schoolId,
            })
            let generateListFilter1 = CommonModel.generateListFilterOptions('name', 'id', departmentList)
            $this.$set($this.lists.searchFilter.filter[1], "data", generateListFilter1[0])
            $this.$set($this.lists.searchFilter.filter[1], "dataObject", generateListFilter1[1])
            $this.$set($this.lists.searchFilter.filter[1], "dataOrigin", departmentList)
            if (setNull) {
              $this.$set($this.entityInfo.edit, "enumDepartmentId", "")
            }
          }
        },


      }
    },
    // 试题Methods
    EntityInfoMethods() {
      let $this = this;
      return {
        // 学校搜索
        async querySchool(name) {
          $this.$set($this.entityInfo.filter.schoolList, "loading", true)
          let list = await SchoolModel.getSchoolListByName(name)
          let listResult = []
          list.forEach(li => {
            listResult.push({
              value: li.id,
              label: li.name
            })
          })
          $this.$set($this.entityInfo.filter.schoolList, "list", listResult)
          $this.$set($this.entityInfo.filter.schoolList, "loading", false)
        },
        // 学校被改变
        async onSchoolChange(v) {
          $this.ListMethods().initFilter(1, true)
          $this.$forceUpdate();
        },
        // 学院被改变
        async onDepartmentChange(v) {
          $this.$forceUpdate();
        },
        // 点击取消按钮
        clickCancelBtn() {
          $this.$router.go(-1);
        },
        // 点击新增学校按钮
        clickAddSchoolBtn() {
          $this.drawer.schoolShow = true
        },
        // 学校抽屉被关闭
        onSchoolDrawerClose() {
          if ($this.drawer.schoolEdited) {
            $this.ListMethods().initFilter(0, true)
            $this.$set($this.entityInfo.edit, "schoolId", "")
            this.onSchoolChange()
          }
        },
        // 点击新增、修改学院按钮
        clickAddDepartmentBtn() {
          $this.drawer.departmentShow = true
        },
        // 学院枚举抽屉被关闭
        onDepartmentDrawerClose() {
          if ($this.drawer.departmentEdited) {
            $this.$set($this.entityInfo.edit, "enumDepartmentId", "")
            this.onSchoolChange()
          }
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              $this.entityInfo.loading = true
              if (await TeacherModel.save($this.entityInfo.edit).catch(err => {
                $this.entityInfo.loading = false
              })) {
                msg_success('新增成功')
                this.clickCancelBtn()
              }
              $this.entityInfo.loading = false
            }
          })
        },
        // 点击修改按钮
        async clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              $this.entityInfo.loading = true
              if (await TeacherModel.update($this.entityInfo.edit).catch(err => {
                $this.entityInfo.loading = false
              })) {
                msg_success('保存成功')
                this.clickCancelBtn()
              }
              $this.entityInfo.loading = false
            }
          })
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.edit-container {
  width: 650px;
}

.buttons {
  text-align: center;
}
</style>
