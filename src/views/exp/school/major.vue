<template>
  <div class="app-container">
    <div class="page-title">{{ departmentName }}</div>
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="danger" style="background-color: #67C23A;border-color:#67C23A"
                     @click="ListMethods().clickAddBtn()">添加专业
          </el-button>
        </div>
      </div>
    </div>
    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;"
              @sort-change="sort=>ListMethods().sortChange(sort)">
      <el-table-column label="专业编号" align="center" width="250px">
        <template slot-scope="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="专业名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="230"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="text" size="mini" round
                     @click="e=>ListMethods().clickEditBtn(scope.row,scope.$index,e)">修改
          </el-button>
          <el-button type="text" size="mini" round
                     @click="e=>ListMethods().clickDeleteBtn(scope.row,scope.$index,e)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
    <!--详情弹窗-->
    <el-dialog
      :title="entityInfo.title"
      :visible.sync="entityInfo.dialog"
      width="500px"
      append-to-body
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
          <el-form-item label="专业名称:" prop="name">
            <el-input v-model.trim="entityInfo.edit.name"
                      onKeypress="javascript:if(event.keyCode == 32 || event.keyCode == 43)event.returnValue = false;">
              <div slot="suffix">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.name.length }} / 30
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="entityInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityInfoMethods().clickAddBtn()" :loading="entityInfo.loading"
                   v-if="entityInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityInfoMethods().clickEditBtn()" :loading="entityInfo.loading"
                   v-if="entityInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import {date_format, getQuery} from "@/utils/common";
import enums from "@/views/exp/school/studentClazz";
import {MajorModel} from "@/model/exp/MajorModel";
import {CommonModel} from "@/model/CommonModel";
import clip from "@/utils/clipboard";
import {msg_confirm, msg_success} from "@/utils/ele_component";
import {DepartmentModel} from "@/model/exp/DepartmentModel";
import {ConfigModel} from "@/model/erp/ConfigModel";
import {CONFIG_NAME_EXP} from "@/model/ConfigModel";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {dateFormat} from "@/filters";
import {validateMaxLength} from "@/utils/validate";
import {AdminUserModel} from "@/model/erp/AdminUserModel";

export default {
  name: "schoolMajor",
  components: {ListSearchFilter},
  filters: {
    dateFormat,
  },
  directives: {
    elDragDialog, permission
  },
  watch: {
    '$route'(to, from) { //监听路由是否变化
      if (to.query.departmentId !== from.query.departmentId) {
        this.departmentId = to.query.departmentId;
        this.init();//重新加载数据
      }
    }
  },
  props: {
    // 是否作为组件
    asComponent: {
      type: Boolean,
      default: false
    },
    // 传入学院信息
    departmentInfo: {
      type: Object,
      default() {
        return {
          departmentId: "",
          departmentName: ""
        }
      }
    }
  },
  computed: {
    departmentId() {
      return this.$route.query["departmentId"] || this.departmentInfo.departmentId
    },
    departmentName() {
      return this.$route.query["departmentName"] || this.departmentInfo.departmentName
    }
  },
  data() {
    let $this = this;
    return {
      // 外部方法
      date_format: date_format,
      // 枚举
      enums: enums,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'autocomplete',
              label: '专业名称',
              placeholder: "请输入专业名称",
              key: 'name',
              value: '',
              searchFunction: async function (query, callback) {
                let list = await MajorModel.conditionQueryList({
                  name: query,
                  departmentId: $this.departmentId
                })
                let listResult = []
                list.forEach(li => {
                  listResult.push({
                    value: li.name
                  })
                })
                callback(listResult)
              }
            },

          ],
          filter: []
        }
      },
      // 实体信息
      entityInfo: {
        dialog: false,
        title: "添加专业",
        type: "add",
        loading: false,
        edit: {
          departmentId: "",
          name: ""
        },
        // 输入检测
        formRules: {
          'name': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 30, '专业名称'),
            trigger: 'blur'
          }
        },
      },
    }
  },
  async mounted() {
    // 初始化
    this.init()
  },
  methods: {
    init() {
      // 设置学校信息
      this.lists.queryBase = {
        departmentId: this.departmentId
      }
      // 获取列表
      this.ListMethods().getList(1, this.lists.pages.size, this.lists.query)
      // 初始化筛选
      this.ListMethods().initFilter()
    },
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          query = Object.assign(query, $this.lists.queryBase)
          $this.lists.loading = true;
          [$this.lists.list, $this.lists.pages] = await MajorModel.getList(page, size, query)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number, size, $this.lists.query)
        },
        // 排序被改变
        async sortChange(sort) {
          let querySort = $this.lists.query.sort
          querySort = CommonModel.elementTableSort(sort)
          $this.$set($this.lists.query, "sort", querySort)
          this.getList(1, $this.lists.pages.size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter() {
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(1, $this.lists.pages.size, $this.lists.query)
        },
        // 点击添加按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "添加专业"
          $this.entityInfo.edit = {
            name: ""
          };
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
        // 点击列表的编辑按钮
        clickEditBtn(edit, $index) {
          $this.entityInfo.edit = JSON.parse(JSON.stringify(edit))
          $this.entityInfo.type = 'edit'
          $this.entityInfo.$index = $index
          $this.entityInfo.title = "修改专业"
          $this.entityInfo.dialog = true
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
        // 点击删除按钮
        async clickDeleteBtn(entity) {
          if (await msg_confirm("确认是否删除该专业？")) {
            await MajorModel.deleteOne(entity.id)
            $this.ListMethods().getList(1, $this.lists.pages.size, $this.lists.query)
            msg_success("删除此专业成功！")
            // 发出已修改回调
            $this.$emit("afterEdit",true)
          }
        },
      }
    },
    // 实体Methods
    EntityInfoMethods() {
      let $this = this;
      return {
        // 点击提交新增按钮
        clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              $this.entityInfo.loading = true
              if (await MajorModel.save({
                enumDepartmentId: $this.departmentId,
                name: $this.entityInfo.edit.name,
              }).catch(err => {
                $this.entityInfo.loading = false
              })) {
                msg_success('新增成功')
                $this.ListMethods().getList(1, $this.lists.pages.size, $this.lists.query)
                $this.entityInfo.loading = false
                $this.entityInfo.dialog = false
                // 发出已修改回调
                $this.$emit("afterEdit",true)
              }
            }
          })
        },
        // 点击修改按钮
        clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              $this.entityInfo.loading = true
              if (await MajorModel.update({
                id: $this.entityInfo.edit.id,
                name: $this.entityInfo.edit.name,
                enumDepartmentId: $this.departmentId,
              }).catch(err => {
                $this.entityInfo.loading = false
              })) {
                msg_success('保存成功')
                $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
                $this.entityInfo.dialog = false
                $this.entityInfo.loading = false
                // 发出已修改回调
                $this.$emit("afterEdit",true)
              }
            }
          })
        },
      }
    }
  }
}
</script>

<style scoped lang="scss">
.page-title {
  margin-bottom: 20px;
  font-size: 17px;
  font-weight: bold;
}
</style>
