<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="danger" style="background-color: #67C23A;border-color:#67C23A"
                     @click="ListMethods().clickAddBtn()">添加行政班级
          </el-button>
          <el-button class="el-button" type="success" v-permission="['administrator']"
                     @click="importStudent.dialog=true">批量导入学生
          </el-button>
          <el-button class="el-button" type="success" v-permission="['administrator']"
                     @click="ListMethods().clickExportBtn()">批量导出学生
          </el-button>
          <el-button class="el-button" type="danger" @click="ResetPasswordMethods().clickOpenBtn()">重置密码</el-button>
          <el-button class="el-button" type="danger" @click="MultipleResetMethods().clickOpenBtn()">批量重置密码
          </el-button>
        </div>
      </div>
    </div>

    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;"
              @sort-change="sort=>ListMethods().sortChange(sort)">
      <el-table-column label="学校" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.schoolName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="院系" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.enumDepartmentName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属专业" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.majorName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属年级" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gradeName }}</span>
        </template>
      </el-table-column>

      <el-table-column label="行政班级" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="学生人数" align="center" width="100px">
        <template slot-scope="scope">
          <span>{{ scope.row.studentCount }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="230"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="text" size="mini" round
                     @click="e=>ListMethods().clickViewListBtn(scope.row,scope.$index,e)">学生列表
          </el-button>
          <el-button type="text" size="mini" round
                     @click="e=>ListMethods().clickEditBtn(scope.row,e)">修改
          </el-button>
          <el-button type="text" size="mini" round
                     @click="e=>ListMethods().clickDeleteBtn(scope.row,e)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--学生导入input-->
    <input
      id="importStudentFile"
      type="file"
      style="display: none"
      @change="(files)=>{ListMethods().importStudentFileChange(files)}"
    >
    <!--学生导入弹窗-->
    <el-dialog
      title="批量导入学生"
      :visible.sync="importStudent.dialog"
      width="500px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form>
          <el-form-item label="导入模板(Excel):">
            <span style="margin-right: 15px">学生导入列表.xls</span>
            <el-button type="default" size="mini" @click="ListMethods().clickDownloadBtn()">下载</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="importStudent.dialog=false">取 消
        </el-button>
        <el-button type="success" :loading="importStudent.doing"
                   @click="ListMethods().clickImportStudentBtn()">导入学生
        </el-button>
      </div>
    </el-dialog>
    <!--重置学生密码弹窗-->
    <el-dialog
      title="重置学生密码"
      :visible.sync="resetPassword.dialog"
      width="900px"
      center
      :close-on-click-moda="false"
      v-el-drag-dialog>
      <!--搜索组件-->
      <div class="search-box flex flex-end" style="margin-bottom: 20px;width: 300px;">
        <el-input v-model="resetPassword.account">
          <el-button slot="append" @click="ResetPasswordMethods().clickSearchBtn()">搜索</el-button>
        </el-input>
      </div>
      <el-table :data="resetPassword.list" v-loading="resetPassword.loading" element-loading-text="加载中" border fit
                style="width: 100%;">
        <el-table-column label="账号" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.account }}</span>
          </template>
        </el-table-column>
        <el-table-column label="姓名" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属学校" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.schoolName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属教师" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.teacherName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button @click="ResetPasswordMethods().clickResetBtn(scope.row)">重置密码</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!--批量重置密码导入input-->
    <input
      id="multipleResetImportFile"
      type="file"
      style="display: none"
      @change="(files)=>{MultipleResetMethods().importFileChange(files)}"
    >
    <!--批量重置密码弹窗-->
    <el-dialog
      title="批量重置某个学校学生密码"
      :visible.sync="multipleReset.dialog"
      width="500px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form>
          <el-form-item label="导入模板(Excel):">
            <span style="margin-right: 15px">批量重置密码导入列表.xls</span>
            <el-button type="default" size="mini" @click="MultipleResetMethods().clickDownloadBtn()">下载</el-button>
          </el-form-item>
          <el-form-item label="选择学校">
            <el-select v-model="multipleReset.schoolId" style="width: 430px">
              <el-option v-for="item in multipleReset.schoolList" :value="item.value" :label="item.label"
                         :key="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="multipleReset.dialog=false">取 消
        </el-button>
        <el-button type="success" :loading="multipleReset.doing"
                   @click="MultipleResetMethods().clickImportBtn()">导入学生列表，开始重置
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import elDragDialog from '@/directive/el-drag-dialog'
import {msg_confirm, msg_err, msg_success} from '@/utils/ele_component'
import {
  arrToLVArr,
  checkPhone,
  date_format,
  downloadFile,
  find_obj_from_arr_by_id,
  objectArrStoreOneSameKeyObject,
  objectToLVArr
} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {SchoolModel} from "@/model/exp/SchoolModel";
import clip from '@/utils/clipboard';
import {DepartmentModel} from "@/model/exp/DepartmentModel";
import {ConfigModel} from "@/model/erp/ConfigModel";
import {dateFormat} from "@/filters";
import {CommonModel} from "@/model/CommonModel";
import {VEAModel} from "@/model/VEAModel";
import {validateMaxLength} from "@/utils/validate";
import {CONFIG_NAME_EXP} from "@/model/ConfigModel";
import {ExperimentModel} from "@/model/exp/ExperimentModel";
import {EnumDepartmentModel} from "@/model/exp/EnumDepartmentModel";
import {MajorModel} from "@/model/exp/MajorModel";
import {GradeModel} from "@/model/exp/GradeModel";
import {AdministrationClazzModel} from "@/model/exp/AdministrationClazzModel";
import {UserModel} from "@/model/exp/UserModel";
import {BaseUploadModel} from "@/model/BaseUploadModel";
import {getResetPasswordList, multipleResetSchoolPassword, resetUserPassword} from "@/api/exp/User";
import {read, utils} from 'xlsx'
import school from "@/views/exp/school/school";


export default {
  name: 'schoolStudentClazz',
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles
    })
  },
  filters: {dateFormat},
  data() {
    let $this = this;
    return {
      // 外部方法
      date_format: date_format,
      // 枚举
      enums: enums,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'autocomplete',
              label: '学校名称',
              placeholder: "请输入学校名称",
              key: 'schoolName',
              value: '',
              searchFunction: async function (query, callback) {
                // 如果是实验平台教师角色
                if ($this.adminRoles.includes("expTeacher")) {
                  callback([])
                  return
                }
                let list = await SchoolModel.getSchoolListByName(query)
                let listResult = []
                list.forEach(li => {
                  listResult.push({
                    value: li.name
                  })
                })
                callback(listResult)
              }
            },
            {
              type: 'autocomplete',
              label: '院系名称',
              placeholder: "请输入院系名称",
              key: 'enumDepartmentName',
              value: '',
              searchFunction: async function (query, callback) {
                // 如果是实验平台教师角色
                if ($this.adminRoles.includes("expTeacher")) {
                  callback([])
                  return
                }
                let list = await EnumDepartmentModel.conditionQueryList({
                  name: query
                })
                list = objectArrStoreOneSameKeyObject(list, "name")
                let listResult = []
                list.forEach(li => {
                  listResult.push({
                    value: li.name
                  })
                })
                callback(listResult)
              }
            },
            {
              type: 'autocomplete',
              label: '专业名称',
              placeholder: "请输入专业名称",
              key: 'majorName',
              value: '',
              searchFunction: async function (query, callback) {
                // 如果是实验平台教师角色
                if ($this.adminRoles.includes("expTeacher")) {
                  callback([])
                  return
                }
                let list = await MajorModel.conditionQueryList({
                  name: query
                })
                list = objectArrStoreOneSameKeyObject(list, "name")
                let listResult = []
                list.forEach(li => {
                  listResult.push({
                    value: li.name
                  })
                })
                callback(listResult)
              }
            },
            {
              type: 'autocomplete',
              label: '年级名称',
              placeholder: "请输入年级名称",
              key: 'gradeName',
              value: '',
              searchFunction: async function (query, callback) {
                // 如果是实验平台教师角色
                if ($this.adminRoles.includes("expTeacher")) {
                  callback([])
                  return
                }
                let list = await GradeModel.conditionQueryList(query)
                list = objectArrStoreOneSameKeyObject(list, "name")
                let listResult = []
                list.forEach(li => {
                  listResult.push({
                    value: li.name
                  })
                })
                callback(listResult)
              }
            },
            {
              type: 'autocomplete',
              label: '行政班级',
              placeholder: "请输入班级名称",
              key: 'name',
              value: '',
              searchFunction: async function (query, callback) {
                // 如果是实验平台教师角色
                if ($this.adminRoles.includes("expTeacher")) {
                  callback([])
                  return
                }
                let list = await AdministrationClazzModel.conditionQueryList(query)
                list = objectArrStoreOneSameKeyObject(list, "name")
                let listResult = []
                list.forEach(li => {
                  listResult.push({
                    value: li.name
                  })
                })
                callback(listResult)
              }
            },
          ],
          filter: [
            {
              type: 'select',
              label: '学校',
              key: 'schoolid',
              value: '',
              hidden: true,
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
            {
              type: 'select',
              label: '院系名称',
              key: 'departmentid',
              value: '',
              hidden: true,
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
            {
              type: 'select',
              label: '专业名称',
              key: 'majorid',
              value: '',
              hidden: true,
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
            {
              type: 'select',
              label: '年级名称',
              hidden: true,
              key: 'gradeid',
              value: '',
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
          ]
        }
      },
      // 导入学生
      importStudent: {
        dialog: false,
        doing: false,
      },
      // 重置学生密码弹窗
      resetPassword: {
        dialog: false,
        list: [],
        loading: false,
        account: ""
      },
      // 批量重置密码导入学生
      multipleReset: {
        dialog: false,
        doing: false,
        schoolId: "",
        schoolList: []
      },
    }
  },
  async mounted() {
    // 如果是实验平台教师角色
    if (this.adminRoles.includes("expTeacher")) {
      this.$set(this.lists.searchFilter.search[0], "hidden", true)
    }

    // 获取列表
    this.ListMethods().getList(1, this.lists.pages.size, this.lists.query)
    // 初始化筛选
    //this.ListMethods().initFilter()
    // 获取前台地址
    this.webUrl = await ConfigModel.getEdit(CONFIG_NAME_EXP, "webUrl")
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 点击下载导入列表按钮
        clickDownloadBtn() {
          // todo 做到配置文件里
          downloadFile("http://resouce.cdzyhd.com/**********/%E5%AD%A6%E7%94%9F%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E5%88%97%E8%A1%A8.xlsx", "学生导入列表.xls")
        },
        // 点击了学生导入按钮
        clickImportStudentBtn() {
          const uploader = document.getElementById('importStudentFile')
          uploader.click()
        },
        // 导入学生文件选择
        async importStudentFileChange(files) {
          const file = files.target.files[0]
          // 判断文件类型
          if (!BaseUploadModel.isImportExcelFile(file)) {
            return false
          }
          document.getElementById('importStudentFile').value = ''
          $this.importStudent.doing = true
          // todo
          if (await UserModel.importStudentIndex(file).catch(err => {
            $this.importClazz.dialog = false
            msg_err("批量导入学生失败")
          })) {
            $this.importStudent.dialog = false
            msg_success('批量导入学生成功')
            $this.ListMethods().getList(1, $this.lists.pages.size, $this.lists.query)
          }
          $this.importStudent.doing = false
        },
        // 点击导出按钮
        async clickExportBtn() {
          let schoolName = $this.lists.query.schoolName
          let departmentName = $this.lists.query.departmentName
          let majorName = $this.lists.query.majorName
          let gradeName = $this.lists.query.gradeName
          let clazzName = $this.lists.query.name
          UserModel.exportStudentListIndex(schoolName, departmentName, majorName, gradeName, clazzName)
        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          // 增加默认排序
          // if(!query.sort){
          //   query.sort="createtime,desc"
          // }

          // 如果是实验平台教师角色，就限制查询学校id 2021.9.22
          if ($this.adminRoles.includes("expTeacher")) {
            query.schoolId = sessionStorage.getItem("expTeacher_schoolId")
          }

          [$this.lists.list, $this.lists.pages] = await AdministrationClazzModel.getList(page, size, query)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number, size, $this.lists.query)
        },
        // 排序被改变
        async sortChange(sort) {
          let querySort = $this.lists.query.sort
          querySort = CommonModel.elementTableSort(sort)
          $this.$set($this.lists.query, "sort", querySort)
          this.getList(1, $this.lists.pages.size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter(type, setNull) {
          if (!type || type === 0) {
            // 获取学校列表
            let seriesList = await SchoolModel.getList(0, 0, {})
            let generateListFilter0 = CommonModel.generateListFilterOptions('name', 'id', seriesList, false)
            $this.$set($this.lists.searchFilter.filter[0], "data", generateListFilter0[0])
            $this.$set($this.lists.searchFilter.filter[0], "dataObject", generateListFilter0[1])
            $this.$set($this.lists.searchFilter.filter[0], "dataOrigin", seriesList)
          }
          if (type === 1) {
            // 获取学校所属学院枚举列表
            let departmentList = await EnumDepartmentModel.getList(0, 0, {
              schoolId: $this.entityInfo.edit.schoolId,
            })
            let generateListFilter1 = CommonModel.generateListFilterOptions('name', 'id', departmentList)
            $this.$set($this.lists.searchFilter.filter[1], "data", generateListFilter1[0])
            $this.$set($this.lists.searchFilter.filter[1], "dataObject", generateListFilter1[1])
            $this.$set($this.lists.searchFilter.filter[1], "dataOrigin", departmentList)
            if (setNull) {
              $this.$set($this.entityInfo.edit, "enumDepartmentId", "")
            }
          }
          if (type === 2) {
            // 获取学校所属专业
            let majorList = await MajorModel.getList(0, 0, {
              schoolId: $this.entityInfo.edit.schoolId,
            })
            let generateListFilter2 = CommonModel.generateListFilterOptions('name', 'id', majorList)
            $this.$set($this.lists.searchFilter.filter[2], "data", generateListFilter2[0])
            $this.$set($this.lists.searchFilter.filter[2], "dataObject", generateListFilter2[1])
            $this.$set($this.lists.searchFilter.filter[2], "dataOrigin", majorList)
            if (setNull) {
              $this.$set($this.entityInfo.edit, "majorId", "")
            }
          }
          if (type === 3) {
            // 获取学校所属年级
            let gradeList = await GradeModel.getList(0, 0, {
              schoolId: $this.entityInfo.edit.schoolId,
            })
            let generateListFilter3 = CommonModel.generateListFilterOptions('name', 'id', gradeList)
            $this.$set($this.lists.searchFilter.filter[3], "data", generateListFilter3[0])
            $this.$set($this.lists.searchFilter.filter[3], "dataObject", generateListFilter3[1])
            $this.$set($this.lists.searchFilter.filter[3], "dataOrigin", gradeList)
            if (setNull) {
              $this.$set($this.entityInfo.edit, "gradeId", "")
            }
          }
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(1, $this.lists.pages.size, $this.lists.query)
        },
        // 点击查看列表按钮
        clickViewListBtn(entity, $index) {
          // $this.$router.push(`/exp/school/studentList?administrationClazzId=${entity.id}&schoolId=${entity.schoolId}&clazzName=${entity.name}`);
          window.open(window.location.origin+ `/#/exp/school/studentList?administrationClazzId=${entity.id}&schoolId=${entity.schoolId}&clazzName=${entity.name}`);
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.$router.push(`/exp/school/studentClazzInfo?type=add`);
        },
        // 点击编辑按钮
        async clickEditBtn(entity) {
          $this.$router.push(`/exp/school/studentClazzInfo?type=edit&id=${entity.id}`);
        },
        // 点击删除按钮
        async clickDeleteBtn(entity) {
          if (await msg_confirm("确认是否删除该班级？")) {
            await AdministrationClazzModel.deleteOne(entity.id)
            $this.ListMethods().getList(1, $this.lists.pages.size, $this.lists.query)
            msg_success("删除此班级成功！")
          }
        }
      }
    },
    // 重置密码Methods
    ResetPasswordMethods() {
      let $this = this;
      return {
        // 点击按钮
        clickOpenBtn() {
          $this.resetPassword.dialog = true
        },
        // 获取列表
        async clickSearchBtn() {
          if ($this.resetPassword.account) {
            $this.resetPassword.loading = true;
            let [data,] = await getResetPasswordList({
              account: $this.resetPassword.account
            })
            if (!data.data) {
              data.data = []
            }
            $this.$set($this.resetPassword, "list", data.data)
            $this.resetPassword.loading = false;
          }
        },
        // 点击重置按钮
        async clickResetBtn(user) {
          if (await msg_confirm("确认要将" + user.name + "的密码重置为123456吗？")) {
            let [data] = await resetUserPassword({
              id: user.id
            })
            msg_success("重置密码成功")
          }
        }
      }
    },
    // 批量重置密码Methods
    MultipleResetMethods() {
      let $this = this;
      return {
        // 点击下载导入列表按钮
        clickDownloadBtn() {
          downloadFile("https://resouce.cdzyhd.com/**********/%E6%89%B9%E9%87%8F%E9%87%8D%E7%BD%AE%E5%AF%86%E7%A0%81%E5%AF%BC%E5%85%A5%E5%88%97%E8%A1%A81.xlsx", "批量重置密码导入列表.xls")
        },
        async clickOpenBtn() {
          $this.multipleReset.dialog = true
          // 获取学校列表
          let seriesList = await SchoolModel.getList(0, 0, {})
          let generateListFilter0 = CommonModel.generateListFilterOptions('name', 'id', seriesList, false)
          $this.$set($this.multipleReset, "schoolList", generateListFilter0[0])
        },
        // 点击了导入按钮
        clickImportBtn() {
          if (!$this.multipleReset.schoolId) {
            msg_err("请先选择要重置密码账号所属的学校！")
            return
          }
          const uploader = document.getElementById('multipleResetImportFile')
          uploader.click()
        },
        // 导入文件选择
        async importFileChange(files) {
          const file = files.target.files[0]
          // 判断文件类型
          if (!BaseUploadModel.isImportExcelFile(file)) {
            return false
          }
          const fileReader = new FileReader();
          fileReader.onload = (ev) => {
            try {
              const data = ev.target.result;
              // 切换为新的调用方式
              const workbook = read(data, {
                type: 'binary'
              });
              // 取第一张表
              const wsname = workbook.SheetNames[0];
              // 切换为新的调用方式 生成json表格内容
              const ws = utils.sheet_to_json(workbook.Sheets[wsname]);
              let accountList = []
              ws.forEach(li => {
                accountList.push(li["*学生学号"] + "")// 将账号转为字符串
              })

              //
              multipleResetSchoolPassword($this.multipleReset.schoolId, accountList)
            } catch (e) {
              return false;
            }
          };
          fileReader.readAsBinaryString(file);
        }
      }
    },
  }
}
</script>

<style scoped lang="scss">

</style>
