<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="danger" style="background-color: #67C23A;border-color:#67C23A"
                     @click="ListMethods().clickAddBtn()">添加学校
          </el-button>
          <el-button class="el-button" type="primary" style="background-color: #67C23A;border-color:#67C23A"
                     @click="semesterInfo.indexDialog=true">管理学期
          </el-button>

        </div>
      </div>
    </div>
    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;"
              @sort-change="sort=>ListMethods().sortChange(sort)">
      <el-table-column label="学校编号" align="center" width="250px">
        <template slot-scope="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="学校名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="320"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="text" size="mini" round
                     @click="e=>ListMethods().clickEditBtn(scope.row,scope.$index,e)">修改
          </el-button>
          <el-button type="text" size="mini" round
                     @click="e=>ListMethods().clickViewBtn(scope.row,scope.$index,e)" v-if="!asComponent">查看院系
          </el-button>
          <el-button type="text" size="mini" round
                     @click="e=>ListMethods().clickDeleteBtn(scope.row,scope.$index,e)">删除
          </el-button>
          <el-button type="text" size="mini" round
                     @click="e=>ListMethods().clickCopyBtn(scope.row,e)">复制链接
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
    <!--详情弹窗-->
    <el-dialog
      :title="entityInfo.title"
      :visible.sync="entityInfo.dialog"
      append-to-body
      width="500px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
          <el-form-item label="学校名称:" prop="name">
            <el-input v-model.trim="entityInfo.edit.name"
                      onKeypress="javascript:if(event.keyCode == 32 || event.keyCode == 43)event.returnValue = false;">
              <div slot="suffix">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.name.length }} / 30
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="entityInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityInfoMethods().clickAddBtn()" :loading="entityInfo.loading"
                   v-if="entityInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityInfoMethods().clickEditBtn()" :loading="entityInfo.loading"
                   v-if="entityInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
    <!--学期编辑弹窗-->
    <el-dialog
      title="编辑通用学期信息"
      :visible.sync="semesterInfo.indexDialog"
      width="600px"
      center
      append-to-body
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="130px" >
          <el-form-item label="选择要编辑的学期:" >
            <el-select  v-model="semesterInfo.value" style="width: 400px">
              <el-option v-for="item in lists.searchFilter.filter[0].data" :value="item.value" :label="item.label"
                         :key="item.value" v-if="item.value!==''">
                <div class="flex flex-between">
                  <span>{{ item.label }}</span>
                  <div>
                    <el-button type="text" size="mini"
                               @click="SemesterMethods().clickEditSemesterBtn(item.value,item.label)">修改
                    </el-button>
                    <el-button type="text" size="mini"
                               @click="SemesterMethods().clickDeleteSemesterBtn(item.value,item.label)">删除
                    </el-button>
                  </div>
                </div>
              </el-option>
            </el-select>
            <el-button style="margin-left: 10px" type="text" size="mini"
                       @click="SemesterMethods().clickAddSemesterBtn()">新增学期
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="semesterInfo.indexDialog=false">取 消
        </el-button>
      </div>
    </el-dialog>
    <!--弹窗-学期编辑和新增-->
    <el-dialog
      :title="semesterInfo.title"
      :visible.sync="semesterInfo.dialog"
      width="600px"
      center
      append-to-body
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="semesterInfoForm" :model="semesterInfo.edit" :rules="semesterInfo.formRules">
          <el-form-item label="学期:" prop="name">
            <el-input style="width: 350px" v-model.trim="semesterInfo.edit.name" placeholder="请输入学期名称"></el-input>
          </el-form-item>
          <el-form-item label="学期id:" v-if="semesterInfo.type==='edit'">
            <span>{{ semesterInfo.edit.id }}</span>
          </el-form-item>
          <el-form-item label="学期时间:" prop="date">
            <el-date-picker
              v-model="semesterInfo.edit.date"
              type="datetimerange"
              range-separator="至"
              :default-time="['00:00:00','23:59:59']"
              value-format="yyyy-MM-dd HH:mm:ss"
              start-placeholder="开学时间"
              end-placeholder="放假时间">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="semesterInfo.dialog=false">取 消
        </el-button>
        <el-button type="primary" v-if="semesterInfo.type==='add'"
                   @click="SemesterMethods().clickAddBtn()">新 增
        </el-button>
        <el-button type="primary" v-if="semesterInfo.type==='edit'"
                   @click="SemesterMethods().clickEditBtn()">修 改
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import {date_format, find_obj_from_arr_by_id} from "@/utils/common";
import enums from "@/views/exp/school/studentClazz";
import {SchoolModel} from "@/model/exp/SchoolModel";
import {CommonModel} from "@/model/CommonModel";
import clip from "@/utils/clipboard";
import {msg_confirm, msg_err, msg_success} from "@/utils/ele_component";
import {DepartmentModel} from "@/model/exp/DepartmentModel";
import {ConfigModel} from "@/model/erp/ConfigModel";
import {CONFIG_NAME_EXP} from "@/model/ConfigModel";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {dateFormat} from "@/filters";
import {validateMaxLength} from "@/utils/validate";
import {AdminUserModel} from "@/model/erp/AdminUserModel";
import {GradeModel} from "@/model/exp/GradeModel";
import {SemesterModel} from "@/model/exp/SemesterModel";
import {getResetPasswordList, resetUserPassword} from "@/api/exp/User";

export default {
  name: "schoolMain",
  components: {ListSearchFilter},
  filters: {
    dateFormat,
  },
  directives: {
    elDragDialog, permission
  },
  props: {
    // 是否作为组件
    asComponent: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 外部方法
      date_format: date_format,
      // 枚举
      enums: enums,
      webUrl: "",
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'autocomplete',
              label: '学校名称',
              placeholder: "请输入学校名称",
              key: 'schoolname',
              value: '',
              searchFunction: async function (query, callback) {
                let list = await SchoolModel.getSchoolListByName(query)
                let listResult = []
                list.forEach(li => {
                  listResult.push({
                    value: li.name
                  })
                })
                callback(listResult)
              }
            },

          ],
          filter: [
            {
              type: 'select',
              label: '学期',
              key: 'semesterid',
              value: '',
              data: [],
              dataObject: {},
              hidden: true,
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
          ]
        }
      },
      // 实体信息
      entityInfo: {
        dialog: false,
        title: "添加学校",
        type: "add",
        loading: false,
        edit: {
          schoolId: "",
          name: ""
        },
        // 输入检测
        formRules: {
          'name': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 30, '学校名称'),
            trigger: 'blur'
          }
        },
      },
      // 学期信息
      semesterInfo: {
        value:"",
        indexDialog:false,
        dialog: false,
        type: "add",
        edit: {
          name: "",
          date: []
        },
        // 输入检测
        formRules: {
          'name': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 20, "学期名称"), trigger: 'blur'},
          'date': {required: true, message: '请选择学期时间', trigger: 'blur'},
        },
      },

    }
  },
  async mounted() {
    // 获取列表
    this.ListMethods().getList(1, this.lists.pages.size, this.lists.query)
    // 初始化筛选
    this.ListMethods().initFilter()
    // 获取前台地址
    this.webUrl = await ConfigModel.getEdit(CONFIG_NAME_EXP, "webUrl")
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          [$this.lists.list, $this.lists.pages] = await SchoolModel.getList(page, size, query)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number, size, $this.lists.query)
        },
        // 排序被改变
        async sortChange(sort) {
          let querySort = $this.lists.query.sort
          querySort = CommonModel.elementTableSort(sort)
          $this.$set($this.lists.query, "sort", querySort)
          this.getList(1, $this.lists.pages.size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter(type) {
          // 获取学期列表
          if (!type || type === 0) {
            let semesterList = await SemesterModel.getList(0, 0, {})
            let generateListFilter0 = CommonModel.generateListFilterOptions('name', 'id', semesterList, false)
            // 新增时 默认选中当前学期功能，如果没有就选中最近学期

            let defaultSemesterId = ""
            for (let i = 0; i < semesterList.length; i++) {
              let li = semesterList[i]
              generateListFilter0[0][i].label = li.name + " (" + date_format(li.startTime, "yyyy/MM/dd HH:mm") + "-" + date_format(li.endTime, "yyyy/MM/dd HH:mm") + ")"
              if ($this.entityInfo.type === "add") {
                if (li.iscurrentsemester) {// 如果有当前时间的学期
                  defaultSemesterId = li.id
                }
                if (li.isdefaultselected && !defaultSemesterId) {// 如果有最近的学期
                  defaultSemesterId = li.id
                }
                if (!li.isdefaultselected && !defaultSemesterId) {// 如果没有最近的学期
                  defaultSemesterId = li.id
                }
              }
            }
            if ($this.entityInfo.type === "add") {
              $this.$set($this.entityInfo.edit, "semesterid", defaultSemesterId)
            }
            $this.$set($this.lists.searchFilter.filter[0], "data", generateListFilter0[0])
            $this.$set($this.lists.searchFilter.filter[0], "dataObject", generateListFilter0[1])
            $this.$set($this.lists.searchFilter.filter[0], "dataOrigin", semesterList)
          }

        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(1, $this.lists.pages.size, $this.lists.query)
        },
        // 点击添加按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "添加学校"
          $this.entityInfo.edit = {
            name: ""
          };
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
        // 点击复制学校链接按钮
        async clickCopyBtn(school, e) {
          let webUrl = $this.webUrl + "login?schoolId=" + school.id
          clip(webUrl, e);
        },
        // 点击列表的编辑按钮
        clickEditBtn(edit, $index) {
          $this.entityInfo.edit = JSON.parse(JSON.stringify(edit))
          $this.entityInfo.type = 'edit'
          $this.entityInfo.title = "修改学校"
          $this.entityInfo.$index = $index
          $this.entityInfo.dialog = true
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
        // 点击删除按钮
        async clickDeleteBtn(entity) {
          if (await msg_confirm("确认是否删除该学校？")) {
            await SchoolModel.deleteOne(entity.id)
            $this.ListMethods().getList(1, $this.lists.pages.size, $this.lists.query)
            msg_success("删除此学校成功！")
            // 发出已修改回调
            $this.$emit("afterEdit", true)
          }
        },
        // 点击查看详情按钮
        clickViewBtn(entity) {
          $this.$router.push(`/exp/school/enumDepartment?schoolId=${entity.id}&schoolName=${entity.name}`);
        },
      }
    },
    // 实体Methods
    EntityInfoMethods() {
      let $this = this;
      return {
        // 点击提交新增按钮
        clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              $this.entityInfo.loading = true
              if (await SchoolModel.save({
                name: $this.entityInfo.edit.name,
              }).catch(err => {
                $this.entityInfo.loading = false
              })) {
                msg_success('新增成功')
                $this.ListMethods().getList(1, $this.lists.pages.size, $this.lists.query)
                $this.entityInfo.loading = false
                $this.entityInfo.dialog = false
                // 发出已修改回调
                $this.$emit("afterEdit", true)
              }
            }
          })
        },
        // 点击修改按钮
        clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              $this.entityInfo.loading = true
              if (await SchoolModel.update({
                id: $this.entityInfo.edit.id,
                name: $this.entityInfo.edit.name,
              }).catch(err => {
                $this.entityInfo.loading = false
              })) {
                msg_success('保存成功')
                $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
                $this.entityInfo.dialog = false
                $this.entityInfo.loading = false
                // 发出已修改回调
                $this.$emit("afterEdit", true)
              }
            }
          })
        },
      }
    },
    // 学期Methods
    SemesterMethods() {
      let $this = this;
      return {
        // 点击新增学期按钮
        async clickAddSemesterBtn() {
          $this.semesterInfo.dialog = true
          $this.semesterInfo.type = "add"
          $this.semesterInfo.title = "新增学期"
          $this.semesterInfo.edit = {
            name: "",
            date: []
          }
        },
        // 点击删除学期按钮
        async clickDeleteSemesterBtn(id) {
          if (await msg_confirm("确定要删除这个学期吗?")) {
            if (await SemesterModel.remove([id])) {
              msg_success("删除成功")
              $this.ListMethods().initFilter(0)
              $this.semesterInfo.value=""
            } else {
              msg_err("删除失败")
            }
          }
        },
        // 点击修改学期按钮
        async clickEditSemesterBtn(id) {
          $this.semesterInfo.dialog = true
          $this.semesterInfo.type = "edit"
          $this.semesterInfo.title = "编辑学期信息"
          $this.semesterInfo.edit = {}
          $this.semesterInfo.edit = (find_obj_from_arr_by_id("id", id, $this.lists.searchFilter.filter[0].dataOrigin))[1]
          $this.$set($this.semesterInfo.edit, "date", [$this.semesterInfo.edit.startTime, $this.semesterInfo.edit.endTime])
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.$refs['semesterInfoForm'].validate(async validate => {
            if (validate) {
              $this.semesterInfo.edit.startTime = $this.semesterInfo.edit.date[0]
              $this.semesterInfo.edit.endTime = $this.semesterInfo.edit.date[1]
              if (await SemesterModel.save($this.semesterInfo.edit).catch(() => {

              })) {
                msg_success("新增成功")
                $this.semesterInfo.dialog = false
              }
              $this.ListMethods().initFilter(0)
            }
          });
        },
        // 点击修改按钮
        clickEditBtn() {
          $this.$refs['semesterInfoForm'].validate(async validate => {
            if (validate) {
              $this.semesterInfo.edit.startTime = $this.semesterInfo.edit.date[0]
              $this.semesterInfo.edit.endTime = $this.semesterInfo.edit.date[1]
              if (await SemesterModel.update($this.semesterInfo.edit).catch((res) => {

              })) {
                msg_success("修改成功")
                $this.semesterInfo.dialog = false
              }
              $this.ListMethods().initFilter(0)
            }
          });
        }
      }
    },

  }
}
</script>

<style scoped lang="scss">

</style>
