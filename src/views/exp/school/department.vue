<template>
  <div class="app-container">
    <div class="department-title">{{ schoolName + " " + departmentName }}</div>

    <!--筛选-->
    <div class="filter-container-ori flex flex-between">
      <div class="search-box">
        <el-input v-model="lists.searchFilter.search[0].value" :placeholder="lists.searchFilter.search[0].placeholder"
                  style="width: 300px">
          <el-button slot="append" icon="el-icon-search" @click="ListMethods().clickSearchFilterBtn()"></el-button>
        </el-input>
      </div>
      <div class="btn-box">
        <el-button class="el-button" type="danger" style="background-color: #67C23A;border-color:#67C23A"
                   @click="ListMethods().clickAddTeacherBtn()">添加一个教师
        </el-button>
        <el-button class="el-button" type="success" v-permission="['administrator']"
                   @click="importTeacher.dialog=true">批量导入教师
        </el-button>
        <el-button class="el-button" type="success" v-permission="['administrator']"
                   @click="ListMethods().clickExportBtn()">批量导出教师
        </el-button>
      </div>
    </div>

    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="账号" align="center" width="200px">
        <template slot-scope="scope">
          <span>{{ scope.row.account }}</span>
        </template>
      </el-table-column>
      <el-table-column label="姓名" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="性别" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sex }}</span>
        </template>
      </el-table-column>
      <el-table-column label="年龄" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.age }}</span>
        </template>
      </el-table-column>
      <el-table-column label="联系方式" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.contact|mobile }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <span>{{ enums.teacherForbiddenStatus[scope.row.isforbidden] }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="300"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="text" size="mini" round
                     @click="e=>ListMethods().clickDisableOrEnableBtn(scope.row,scope.$index)">
            {{ scope.row.isforbidden ? "启用" : "禁用" }}
          </el-button>
          <el-button type="text" size="mini" round
                     @click="e=>ListMethods().clickDeleteBtn(scope.row,scope.$index)">删除
          </el-button>
          <el-button type="text" size="mini" round
                     @click="e=>ListMethods().clickResetPasswordBtn(scope.row,scope.$index)">重置密码
          </el-button>
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickViewBtn(scope.row,scope.$index)">详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container clearfix">
      <div class="flex flex-start" style="float: right">
        <span style="font-size: 14px">教师人数：{{ lists.pages.totalElements }}人</span>
        <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                       :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                       layout="prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                       @size-change="(size)=>ListMethods().pageLimitChange(size)"
                       :page-count="lists.pages.totalPages">
        </el-pagination>
      </div>
    </div>
    <!--详情弹窗-教师详情-->
    <el-dialog
      :title="teacherInfo.title"
      :visible.sync="teacherInfo.dialog"
      width="500px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="teacherInfoForm" :model="teacherInfo.edit" :rules="teacherInfo.formRules">
          <el-form-item label="账号:" prop="account">
            <el-input v-model.trim="teacherInfo.edit.account"
                      onKeypress="javascript:if(event.keyCode == 32 || event.keyCode == 43)event.returnValue = false;"
                      :disabled="teacherInfo.type==='edit'">
              <div slot="suffix" v-if="teacherInfo.edit.account&&teacherInfo.type!=='edit'">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ teacherInfo.edit.account.length }} / 30
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="姓名:" prop="name">
            <el-input v-model.trim="teacherInfo.edit.name"
                      onKeypress="javascript:if(event.keyCode == 32 || event.keyCode == 43)event.returnValue = false;">
              <div slot="suffix" v-if="teacherInfo.edit.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ teacherInfo.edit.name.length }} / 30
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="性别:" prop="sex">
            <el-select v-model="teacherInfo.edit.sex" style="width: 330px">
              <el-option value="男" label="男"></el-option>
              <el-option value="女" label="女"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="年龄:" prop="age">
            <el-input v-model.trim.number="teacherInfo.edit.age" maxlength="2" type="number"
                      onKeypress="javascript:if(event.keyCode == 32 || event.keyCode == 43)event.returnValue = false;"></el-input>
          </el-form-item>
          <el-form-item label="联系方式:" prop="contact">
            <el-input v-model.trim="teacherInfo.edit.contact">
              <div slot="suffix" v-if="teacherInfo.edit.contact">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ teacherInfo.edit.contact.length }} / 30
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="teacherInfo.dialog=false">取 消</el-button>
        <el-button type="success" v-if="teacherInfo.type==='add'"
                   @click="TeacherInfoMethods().clickAddBtn()">新 增</el-button>
        <el-button type="success" v-if="teacherInfo.type==='edit'"
                   @click="TeacherInfoMethods().clickEditBtn()">确认修改</el-button>
      </span>
    </el-dialog>
    <!--教师导入input-->
    <input
      id="importFile"
      type="file"
      style="display: none"
      @change="(files)=>{ListMethods().importFileChange(files)}"
    >
    <!--教师导入弹窗-->
    <el-dialog
      title="批量导入教师"
      :visible.sync="importTeacher.dialog"
      width="500px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form>
          <el-form-item label="导入模板(Excel):">
            <span style="margin-right: 15px">教师导入列表.xls</span>
            <el-button type="default" size="mini" @click="TeacherInfoMethods().clickDownloadBtn()">下载</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="importTeacher.dialog=false">取 消
        </el-button>
        <el-button type="success" :loading="importTeacher.doing"
                   @click="ListMethods().clickImportBtn()">导入教师
        </el-button>
      </div>
    </el-dialog>
    <!--班级导入input-->
    <input
      id="importClazzFile"
      type="file"
      style="display: none"
      @change="(files)=>{ListMethods().importClazzFileChange(files)}"
    >
    <!--班级导入弹窗-->
    <el-dialog
      title="批量导入班级"
      :visible.sync="importClazz.dialog"
      width="500px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form>
          <el-form-item label="导入模板(Excel):">
            <span style="margin-right: 15px">班级批量导入列表.xls</span>
            <el-button type="default" size="mini" @click="TeacherInfoMethods().clickDownloadClazzBtn()">下载</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="importClazz.dialog=false">取 消
        </el-button>
        <el-button type="success" :loading="importClazz.doing"
                   @click="ListMethods().clickImportClazzBtn()">导入班级
        </el-button>
      </div>
    </el-dialog>
    <!--学生导入input-->
    <input
      id="importStudentFile"
      type="file"
      style="display: none"
      @change="(files)=>{ListMethods().importStudentFileChange(files)}"
    >
    <!--学生导入弹窗-->
    <el-dialog
      title="批量导入学生"
      :visible.sync="importStudent.dialog"
      width="500px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form>
          <el-form-item label="导入模板(Excel):">
            <span style="margin-right: 15px">学生批量导入列表.xls</span>
            <el-button type="default" size="mini" @click="TeacherInfoMethods().clickDownloadStudentBtn()">下载</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="importStudent.dialog=false">取 消
        </el-button>
        <el-button type="success" :loading="importStudent.doing"
                   @click="ListMethods().clickImportStudentBtn()">导入教师
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import elDragDialog from '@/directive/el-drag-dialog'
import {msg_confirm, msg_err, msg_success} from '@/utils/ele_component'
import {arrToLVArr, checkPhone, date_format, downloadFile, find_obj_from_arr_by_id, objectToLVArr} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {SchoolModel} from "@/model/exp/SchoolModel";
import clip from '@/utils/clipboard';
import {TeacherModel} from "@/model/exp/TeacherModel";
import {DepartmentModel} from "@/model/exp/DepartmentModel";
import {VEAModel} from "@/model/VEAModel";
import {validateMaxLength} from "@/utils/validate";
import {BaseUploadModel} from "@/model/BaseUploadModel";

export default {
  name: 'schoolDepartment',
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  // 过滤器
  filters: {
    mobile(value) {
      if (value) {
        if (value.length >= 7) {
          let start = value.slice(0, 3)
          let end = value.slice(-4)
          return `${start}****${end}`
        } else {
          return value
        }
      }
      return ""
    }
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles
    })
  },
  watch: {
    '$route'(to, from) { //监听路由是否变化
      if (to.query.departmentId !== from.query.departmentId) {
        this.departmentId = to.query.departmentId;
        this.init();//重新加载数据
      }
    }
  },
  data() {
    // 校检年龄
    const validateAge = (rule, value, callback) => {
      let reg = /^[0-9]+$/
      if (!reg.test(value)) {
        callback(new Error('只能输入正整数'))
      }
      if (value <= 0) {
        callback(new Error('需大于0'))
      }
      if (value >= 100) {
        callback(new Error('需小于100'))
      }
      callback()
    }
    return {
      departmentId: this.$route.query["departmentId"],
      departmentName: this.$route.query["departmentName"],
      schoolId: this.$route.query["schoolId"],
      schoolName: this.$route.query["schoolName"],
      // 外部方法
      date_format: date_format,
      // 枚举
      enums: enums,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '',
              placeholder: "请输入教师姓名、账号",
              key: 'search',
              value: ''
            },
          ],
          filter: []
        }
      },
      // 教师详情
      teacherInfo: {
        title: "新增教师",
        type: "add",
        dialog: false,
        edit: {},
        // 输入检测
        formRules: {
          'account': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 30, "账号"), trigger: 'blur'},
          'name': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 30, "姓名"), trigger: 'blur'},
          'sex': {required: true, message: '请选择性别', trigger: 'change'},
          'age': {required: true, validator: validateAge, trigger: 'blur'},
          'contact': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 30, "联系方式"), trigger: 'blur'},
        },
      },
      // 导入教师
      importTeacher: {
        dialog: false,
        doing: false,
      },
      // 导入班级
      importClazz: {
        dialog: false,
        doing: false,
      },
      // 导入学生
      importStudent: {
        dialog: false,
        doing: false,
      }
    }
  },
  async mounted() {
    this.init()
  },
  methods: {
    async init() {
      let departmentQueryList = await DepartmentModel.getList(0, 0, {departmentid: this.departmentId})
      if (departmentQueryList.length > 0) {
        let departmentInfo = departmentQueryList[0]
        this.departmentName = departmentInfo.name
        this.schoolName = (await SchoolModel.getList(0, 0, {schoolid: departmentInfo.schoolid}))[0].name
        this.enumDepartmentId=departmentInfo.enumDepartmentId;
      } else {
        if (this.departmentName === undefined) {
          this.$router.push("/")
        }
      }
      // 设置学院信息获取教师列表
      this.lists.queryBase = {
        departmentid: this.departmentId
      }
      // 获取列表
      this.ListMethods().getList(1, this.lists.pages.size, this.lists.query)
      // 初始化筛选
      this.ListMethods().initFilter()
    },
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          query = Object.assign(query, $this.lists.queryBase)
          // 增加默认排序
          if (!query.sort) {
            query.sort = "id,desc"
          }
          $this.lists.loading = true;
          [$this.lists.list, $this.lists.pages] = await TeacherModel.getList(page, size, query)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter() {
        },
        // 点击搜索按钮
        clickSearchFilterBtn() {
          let query = {}
          query.search = $this.lists.searchFilter.search[0].value
          $this.lists.query = query
          this.getList(1, $this.lists.pages.size, query)
        },
        // 点击禁用/启用按钮
        async clickDisableOrEnableBtn(teacher, index) {
          let choose = false;
          if (!teacher.isforbidden) {// 设置禁用
            if (await msg_confirm("禁用之后，该教师将无法登录实验管理后台，但其产生的所有数据将会继续保留，是否确认禁用?")) {
              choose = true
            }
          }
          if (teacher.isforbidden) {// 设置启用
            if (await msg_confirm("启用之后，该教师可继续登录使用实验管理后台，是否确认启用？")) {
              choose = true
            }
          }
          if (!choose) {
            return
          }
          let set = !teacher.isforbidden
          let entity = {
            id: teacher.id,
            isforbidden: set
          }
          if (await TeacherModel.update(entity)) {
            msg_success("修改状态成功")
            $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
          } else {

          }
        },
        // 点击删除按钮
        async clickDeleteBtn(teacher, index) {
          if (await msg_confirm("确认要删除该教师吗？")) {
            if (await TeacherModel.remove([teacher.id], $this.departmentId)) {
              msg_success("删除成功")
              $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
            } else {
              msg_err("删除失败")
            }
          }
        },
        // 点击重置密码按钮
        async clickResetPasswordBtn(teacher, index) {
          if (await msg_confirm("是否将该教师的登录密码重置为初始密码123456吗？")) {
            if (await TeacherModel.resetPwd(teacher.id)) {
              msg_success("重置密码成功!")
            }
          }
        },
        // 点击导出按钮
        async clickExportBtn() {
          let search = $this.lists.searchFilter.search[0].value
          search = search ? search : "null"
          TeacherModel.exportTeacherList($this.departmentId, search)
        },
        // 点击了导入按钮
        clickImportBtn() {
          const uploader = document.getElementById('importFile')
          uploader.click()
        },
        // 导入文件选择
        async importFileChange(files) {
          const file = files.target.files[0]
          // 判断文件类型
          if(!BaseUploadModel.isImportExcelFile(file)){
            return false
          }
          document.getElementById('importFile').value = ''
          $this.importTeacher.doing = true
          if (await TeacherModel.importTeacher(file, $this.departmentId).catch(err => {
            $this.importTeacher.dialog = false
            msg_err("批量导入教师失败")
          })) {
            $this.importTeacher.dialog = false
            msg_success('批量导入教师成功')
            $this.ListMethods().getList(1, $this.lists.pages.size, $this.lists.query)
          }
          $this.importTeacher.doing = false
        },
        // 点击了班级导入按钮
        clickImportClazzBtn() {
          const uploader = document.getElementById('importClazzFile')
          uploader.click()
        },
        // 班级导入文件选择
        async importClazzFileChange(files) {
          const file = files.target.files[0]
          document.getElementById('importClazzFile').value = ''
          $this.importClazz.doing = true
          // todo
          if (await TeacherModel.importClazz(file, $this.departmentId).catch(err => {
            $this.importClazz.dialog = false
            msg_err("批量导入班级失败")
          })) {
            $this.importClazz.dialog = false
            msg_success('批量导入班级成功')
          }
          $this.importClazz.doing = false
        },
        // 点击了学生导入按钮
        clickImportStudentBtn() {
          const uploader = document.getElementById('importStudentFile')
          uploader.click()
        },
        // 导入学生文件选择
        async importStudentFileChange(files) {
          const file = files.target.files[0]
          document.getElementById('importStudentFile').value = ''
          $this.importStudent.doing = true
          // todo
          if (await TeacherModel.importStudent(file, $this.departmentId).catch(err => {
            $this.importClazz.dialog = false
            msg_err("批量导入学生失败")
          })) {
            $this.importStudent.dialog = false
            msg_success('批量导入学生成功')
          }
          $this.importStudent.doing = false
        },
        // 点击新增教师按钮
        clickAddTeacherBtn() {
          // $this.teacherInfo.type = "add"
          // $this.teacherInfo.title = "新增教师"
          // $this.teacherInfo.edit = {}
          // $this.teacherInfo.dialog = true;
          // setTimeout(() => {
          //   $this.$refs['teacherInfoForm'].clearValidate()
          // }, 300)
          $this.$router.push(`/exp/school/teacherInfo?type=add&schoolId=${$this.schoolId}&enumDepartmentId=${$this.enumDepartmentId}`);
        },
        // 点击详情按钮
        clickViewBtn(entity) {
          // $this.teacherInfo.type = "edit"
          // $this.teacherInfo.title = "修改教师信息"
          // $this.teacherInfo.edit = JSON.parse(JSON.stringify(entity))
          // $this.teacherInfo.dialog = true;
          // setTimeout(() => {
          //   $this.$refs['teacherInfoForm'].clearValidate()
          // }, 300)
          $this.$router.push(`/exp/school/teacherInfo?type=edit&schoolId=${$this.schoolId}&enumDepartmentId=${$this.enumDepartmentId}&id=${entity.id}`);
        }
      }
    },
    // 教师Methods
    TeacherInfoMethods() {
      let $this = this
      return {
        // 点击新增按钮
        clickAddBtn() {
          $this.$refs['teacherInfoForm'].validate(async validate => {
            if (validate) {
              $this.teacherInfo.edit.departmentId = $this.departmentId;
              if (await TeacherModel.save($this.teacherInfo.edit)) {
                msg_success('新增成功')
                //$this.lists.list.push(newData)
                $this.ListMethods().getList(1, $this.lists.pages.size, $this.lists.query)
                $this.teacherInfo.dialog = false
              }
            }
          })
        },
        // 点击修改按钮
        clickEditBtn() {
          $this.$refs['teacherInfoForm'].validate(async validate => {
            if (validate) {
              if (await msg_confirm('确认要修改该教师信息吗？')) {
                // 删除无需修改字段
                delete $this.teacherInfo.edit.password
                delete $this.teacherInfo.edit.unionuserid
                delete $this.teacherInfo.edit.email
                if (await TeacherModel.update($this.teacherInfo.edit)) {
                  msg_success('修改成功')
                  $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
                  $this.teacherInfo.dialog = false
                }
              }
            }
          })
        },
        // 点击下载导入列表按钮
        clickDownloadBtn() {
          // todo 做到配置文件里
          downloadFile("http://resouce.cdzyhd.com/exp/20210326/%E6%95%99%E5%B8%88%E5%AF%BC%E5%85%A5%E5%88%97%E8%A1%A8.xls", "教师导入列表.xls")
        },
        // 点击下载班级导入列表按钮
        clickDownloadClazzBtn() {
          // todo 做到配置文件里
          downloadFile("http://resouce.cdzyhd.com/2021050801/%E7%8F%AD%E7%BA%A7%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E5%88%97%E8%A1%A8.xlsx", "班级批量导入列表.xls")
        },
        // 点击下载学生导入列表按钮
        clickDownloadStudentBtn() {
          // todo 做到配置文件里
          downloadFile("https://resouce.cdzyhd.com/2021050801/%E5%AD%A6%E7%94%9F%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E5%88%97%E8%A1%A8.xlsx", "学生批量导入列表.xls")
        },
      }
    }
  }
}
</script>

<style scoped lang="scss">
.department-title {
  margin-bottom: 20px;
  font-size: 17px;
  font-weight: bold;
}
</style>
