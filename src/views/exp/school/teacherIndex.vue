<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="danger" style="background-color: #67C23A;border-color:#67C23A"
                     @click="ListMethods().clickAddBtn()">添加一个教师
          </el-button>
          <el-button class="el-button" type="success" v-permission="['administrator']"
                     @click="ListMethods().clickExportBtn()">批量导出教师
          </el-button>
          <el-button class="el-button" type="success" v-permission="['administrator']"
                     @click="importTeacher.dialog=true">批量导入教师
          </el-button>
        </div>
      </div>
    </div>

    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;"
              @sort-change="sort=>ListMethods().sortChange(sort)">
      <el-table-column label="学校编号" align="center" width="160px">
        <template slot-scope="scope">
          <span>{{ scope.row.schoolId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="学校名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.schoolName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="院系名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="教师人数" align="center" width="100px">
        <template slot-scope="scope">
          <span>{{ scope.row.teacherNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createtime" :sortable="'custom'" width="160px">
        <template slot-scope="scope">
          <span>{{ scope.row.createtime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="230"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="text" size="mini" round
                     @click="e=>ListMethods().clickViewBtn(scope.row,e)">教师列表
          </el-button>
          <el-button type="text" size="mini" round
                     @click="e=>ListMethods().clickDeleteBtn(scope.row,e)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--教师导入input-->
    <input
      id="importFile"
      type="file"
      style="display: none"
      @change="(files)=>{ListMethods().importFileChange(files)}"
    >
    <!--教师导入弹窗-->
    <el-dialog
      title="批量导入教师"
      :visible.sync="importTeacher.dialog"
      width="500px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form>
          <el-form-item label="导入模板(Excel):">
            <span style="margin-right: 15px">教师导入列表.xls</span>
            <el-button type="default" size="mini" @click="ListMethods().clickDownloadImportExcelBtn()">下载</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="importTeacher.dialog=false">取 消
        </el-button>
        <el-button type="success" :loading="importTeacher.doing"
                   @click="ListMethods().clickImportBtn()">导入教师
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import elDragDialog from '@/directive/el-drag-dialog'
import {msg_confirm, msg_err, msg_success} from '@/utils/ele_component'
import {arrToLVArr, checkPhone, date_format, downloadFile, find_obj_from_arr_by_id, objectToLVArr} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {SchoolModel} from "@/model/exp/SchoolModel";
import clip from '@/utils/clipboard';
import {DepartmentModel} from "@/model/exp/DepartmentModel";
import {ConfigModel} from "@/model/erp/ConfigModel";
import {dateFormat} from "@/filters";
import {CommonModel} from "@/model/CommonModel";
import {VEAModel} from "@/model/VEAModel";
import {validateMaxLength} from "@/utils/validate";
import {CONFIG_NAME_EXP} from "@/model/ConfigModel";
import {TeacherModel} from "@/model/exp/TeacherModel";
import {BaseUploadModel} from "@/model/BaseUploadModel";

export default {
  name: 'teacherMain',
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles
    })
  },
  filters: {dateFormat},
  data() {
    return {
      // 外部方法
      date_format: date_format,
      // 枚举
      enums: enums,
      // 其他
      webUrl: "",
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '学校名称',
              placeholder: "请输入学校名称",
              key: 'schoolname',
              value: ''
            },
            {
              type: 'input',
              label: '院系名称',
              placeholder: "请输入院系名称",
              key: 'departmentname',
              value: ''
            },
          ],
          filter: []
        }
      },
      // 导入教师
      importTeacher: {
        dialog: false,
        doing: false,
      },
      // 导出教师
      exportTeacher: {
        doing: false,
      },
    }
  },
  async mounted() {
    // 如果是实验平台教师角色
    if (this.adminRoles.includes("expTeacher")) {
      // 隐藏学校名称搜索
      this.$set(this.lists.searchFilter.search[0], "hidden", true)
    }

    // 获取列表
    this.ListMethods().getList(1, this.lists.pages.size, this.lists.query)
    // 初始化筛选
    this.ListMethods().initFilter()
    // 获取前台地址
    this.webUrl = await ConfigModel.getEdit(CONFIG_NAME_EXP, "webUrl")
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 点击批量导出教师按钮
        async clickExportBtn() {
          let schoolName = $this.lists.query.schoolname
          let departmentName = $this.lists.query.departmentname
          TeacherModel.exportTeacherIndexList(schoolName, departmentName)
        },
        // 点击下载导入列表按钮
        clickDownloadImportExcelBtn() {
          // todo 做到配置文件里
          downloadFile("http://resouce.cdzyhd.com/2021052501/%E6%95%99%E5%B8%88%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF-%E6%95%99%E5%B8%88%E7%AE%A1%E7%90%86%E9%A6%96%E9%A1%B5%E7%94%A8.xls", "教师导入列表.xls")
        },
        // 点击了导入按钮
        clickImportBtn() {
          const uploader = document.getElementById('importFile')
          uploader.click()
        },
        // 教师批量导入文件选择
        async importFileChange(files) {
          const file = files.target.files[0]
          // 判断文件类型
          if(!BaseUploadModel.isImportExcelFile(file)){
            return false
          }
          document.getElementById('importFile').value = ''
          $this.importTeacher.doing = true
          if (await TeacherModel.importTeacherIndex(file).catch(err => {
            $this.importTeacher.dialog = false
            msg_err("批量导入教师失败")
          })) {
            $this.importTeacher.dialog = false
            msg_success('批量导入教师成功')
            $this.ListMethods().getList(1, $this.lists.pages.size, $this.lists.query)
          }
          $this.importTeacher.doing = false
        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          // 增加默认排序
          // if(!query.sort){
          //   query.sort="createtime,desc"
          // }
          // 如果是实验平台教师角色，就限制查询学校id 2021.9.22
          if ($this.adminRoles.includes("expTeacher")) {
            query.schoolId = sessionStorage.getItem("expTeacher_schoolId")
          }

          [$this.lists.list, $this.lists.pages] = await TeacherModel.getIndexList(page, size, query)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number, size, $this.lists.query)
        },
        // 排序被改变
        async sortChange(sort) {
          let querySort = $this.lists.query.sort
          querySort = CommonModel.elementTableSort(sort)
          $this.$set($this.lists.query, "sort", querySort)
          this.getList(1, $this.lists.pages.size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter() {
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(1, $this.lists.pages.size, $this.lists.query)
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.$router.push(`/exp/school/teacherInfo?type=add`);
        },
        // 点击查看学院详情按钮
        clickViewBtn(entity) {
          $this.$router.push(`/exp/school/department?departmentId=${entity.id}&schoolId=${entity.schoolId}`);
        },
        // 点击删除按钮
        async clickDeleteBtn(entity) {
          if (await msg_confirm("确认是否删除该授权学院？")) {
            await DepartmentModel.remove({
              id: entity.id,
              schoolid: entity.schoolId,
            })
            $this.ListMethods().getList(1, $this.lists.pages.size, $this.lists.query)
            msg_success("删除此授权学院成功！")
          }
        }
      }
    },
  }
}
</script>

<style scoped lang="scss">

</style>
