<template>
  <div class="app-container">
    <el-alert title="底图分辨率 1920px X 1080px,大小3M以内" style="margin-bottom: 5px"></el-alert>
    <el-alert title="Icon分辨率 200px X 200px,大小1M以内" style="margin-bottom: 10px"></el-alert>
    <el-card style="margin-bottom: 20px">
      <span slot="header">公共管理</span>
      <el-table :data="commonList" v-loading="lists.loading" element-loading-text="加载中" fit
                highlight-current-row
                style="width: 100%;">
        <el-table-column label="配置名称" align="center" width="300">
          <template slot-scope="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column label="标题" align="center" width="400">
          <template slot-scope="scope">
            <div>
              <el-input v-model.trim="scope.row.configs.title">
                <span slot="suffix" v-if="scope.row.configs.title">
              <span class="el-input__count">
                <span class="el-input__count-inner">
                  {{ scope.row.configs.title.length }} / 30
                </span>
              </span>
            </span>
              </el-input>
            </div>
            <div class="el-input-error" v-if="scope.row.configs.title.length>30">
              最多输入30个字
            </div>
          </template>
        </el-table-column>
        <el-table-column label="底图" align="center">
          <template slot-scope="scope">
            <div class="erp-uploader-one flex flex-dr flex-center upload-baseImg">
              <el-upload
                action="dev"
                :show-file-list="false"
                :on-success="file=>imgUploadSuccess(file,scope.$index,'baseImg',1)"
                :http-request="uploadRequest"
                :before-upload="file=>imgBeforeUpload(file,scope.$index,'baseImg',1)">
                <img v-if="scope.row.configs.baseImg&&!scope.row.configs.uploading1" :src="scope.row.configs.baseImg"
                     class="img-show" @click="" alt="">
                <i v-if="!scope.row.configs.baseImg&&!scope.row.configs.uploading1"
                   class="el-icon-plus uploader-icon"></i>
                <i v-if="!scope.row.configs.baseImg&&scope.row.configs.uploading1"
                   class="el-icon-loading uploader-icon"></i>
              </el-upload>
              <div class="buttons">
                <el-button type="text" v-if="scope.row.configs.baseImg"
                           @click="clickPreviewBtn(scope.row.configs.baseImg)">预览
                </el-button>
                <el-button type="text" v-if="scope.row.configs.baseImg" @click="scope.row.configs.baseImg=''">删除
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="icon" align="center">
          <template slot-scope="scope">
            <div class="erp-uploader-one flex flex-dr flex-center upload-icon">
              <el-upload
                action="dev"
                :show-file-list="false"
                :on-success="file=>imgUploadSuccess(file,scope.$index,'icon',1)"
                :http-request="uploadRequest"
                :before-upload="file=>imgBeforeUpload(file,scope.$index,'icon',1)">
                <img v-if="scope.row.configs.icon&&!scope.row.configs.uploading2" :src="scope.row.configs.icon"
                     class="img-show" @click="" alt="">
                <i v-if="!scope.row.configs.icon&&!scope.row.configs.uploading2"
                   class="el-icon-plus uploader-icon"></i>
                <i v-if="!scope.row.configs.icon&&scope.row.configs.uploading2"
                   class="el-icon-loading uploader-icon"></i>
              </el-upload>
              <div class="buttons">
                <el-button type="text" v-if="scope.row.configs.icon" @click="clickPreviewBtn(scope.row.configs.icon)">
                  预览
                </el-button>
                <el-button type="text" v-if="scope.row.configs.icon" @click="scope.row.configs.icon=''">删除</el-button>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="180"
                         class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" round
                       @click="ListMethods().clickEditBtn(scope.row,scope.$index,'common')">保存
            </el-button>
            <el-button type="text" size="mini" round
                       @click="ListMethods().clickRemoveBtn(scope.row,scope.$index,'common')">清空
            </el-button>
            <el-button type="text" size="mini" round v-if="scope.row.id"
                       @click="e=>ListMethods().clickCopyBtn(scope.row,e)">复制学校链接
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-card>
      <!--筛选-->
      <div class="clearfix logo-search">
        <list-search-filter :search-filter="lists.searchFilter"
                            @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
        </list-search-filter>
      </div>
      <span slot="header">学校管理</span>
      <!--列表-->
      <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" fit
                highlight-current-row
                style="width: 100%;margin-bottom: 20px">
        <el-table-column label="学校名称" align="center">
          <template slot-scope="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column label="标题" align="center">
          <template slot-scope="scope">
            <div>
              <el-input v-model.trim="scope.row.configs.title">
            <span slot="suffix" v-if="scope.row.configs.title">
              <span class="el-input__count">
                <span class="el-input__count-inner">
                  {{ scope.row.configs.title.length }} / 30
                </span>
              </span>
            </span>
              </el-input>
            </div>
            <div class="el-input-error" v-if="scope.row.configs.title.length>30">
              最多输入30个字
            </div>
          </template>
        </el-table-column>
        <el-table-column label="底图" align="center">
          <template slot-scope="scope">
            <div class="erp-uploader-one flex flex-dr flex-center upload-baseImg">
              <el-upload
                action="dev"
                :show-file-list="false"
                :on-success="file=>imgUploadSuccess(file,scope.$index,'baseImg',2)"
                :http-request="uploadRequest"
                :before-upload="file=>imgBeforeUpload(file,scope.$index,'baseImg',2)">
                <img v-if="scope.row.configs.baseImg&&!scope.row.configs.uploading1" :src="scope.row.configs.baseImg"
                     class="img-show" @click="" alt="" style="width: 200px;height: 120px">
                <i v-if="!scope.row.configs.baseImg&&!scope.row.configs.uploading1"
                   class="el-icon-plus uploader-icon"></i>
                <i v-if="!scope.row.configs.baseImg&&scope.row.configs.uploading1"
                   class="el-icon-loading uploader-icon"></i>
              </el-upload>
              <div class="buttons">
                <el-button type="text" v-if="scope.row.configs.baseImg"
                           @click="clickPreviewBtn(scope.row.configs.baseImg)">预览
                </el-button>
                <el-button type="text" v-if="scope.row.configs.baseImg" @click="scope.row.configs.baseImg=''">删除
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="icon" align="center">
          <template slot-scope="scope">
            <div class="erp-uploader-one flex flex-dr flex-center upload-icon">
              <el-upload
                action="dev"
                :show-file-list="false"
                :on-success="file=>imgUploadSuccess(file,scope.$index,'icon',2)"
                :http-request="uploadRequest"
                :before-upload="file=>imgBeforeUpload(file,scope.$index,'icon',2)">
                <img v-if="scope.row.configs.icon&&!scope.row.configs.uploading2" :src="scope.row.configs.icon"
                     class="img-show" @click="" alt="">
                <i v-if="!scope.row.configs.icon&&!scope.row.configs.uploading2"
                   class="el-icon-plus uploader-icon"></i>
                <i v-if="!scope.row.configs.icon&&scope.row.configs.uploading2"
                   class="el-icon-loading uploader-icon"></i>
              </el-upload>
              <div class="buttons">
                <el-button type="text" v-if="scope.row.configs.icon" @click="clickPreviewBtn(scope.row.configs.icon)">
                  预览
                </el-button>
                <el-button type="text" v-if="scope.row.configs.icon" @click="scope.row.configs.icon=''">删除</el-button>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="180"
                         class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" round
                       @click="ListMethods().clickEditBtn(scope.row,scope.$index,'school')">保存
            </el-button>
            <el-button type="text" size="mini" round
                       @click="ListMethods().clickRemoveBtn(scope.row,scope.$index,'school')">清空
            </el-button>
            <el-button type="text" size="mini" round v-if="scope.row.id"
                       @click="e=>ListMethods().clickCopyBtn(scope.row,e)">复制学校链接
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--列表分页-->
      <div class="pagination-container">
        <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                       :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                       layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                       :page-sizes="[10,20,50,100,200,500]"
                       @size-change="(size)=>ListMethods().pageLimitChange(size)"
                       :page-count="lists.pages.totalPages">
        </el-pagination>
      </div>

    </el-card>
    <!--图片预览-->
    <el-dialog center v-el-drag-dialog :visible.sync="previewDialog" width="1000px">
      <img :src="previewImg" alt="" style="cursor: pointer;max-width: 100%" @click="window.open(previewImg)">
    </el-dialog>
  </div>
</template>

<script>
/**
 * todo
 * 按原型修改
 */
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import elDragDialog from '@/directive/el-drag-dialog'
import {msg_confirm, msg_err, msg_success} from '@/utils/ele_component'
import {arrToLVArr, checkPhone, find_obj_from_arr_by_id, date_format} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {LoginConfigModel} from "@/model/exp/LoginConfigModel";
import {excel_export_from_json} from "@/utils/excel";
import {BaseUploadModel} from "@/model/BaseUploadModel";
import {ConfigModel} from "@/model/erp/ConfigModel";
import clip from "@/utils/clipboard";
import {CONFIG_NAME_EXP} from "@/model/ConfigModel";

export default {
  name: 'settingLogin',
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles
    })
  },
  data() {
    return {
      window: window,
      // 外部方法
      find_obj_from_arr_by_id: find_obj_from_arr_by_id,
      date_format: date_format,
      // 枚举
      enums: enums,
      commonList: [],
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '学校名称',
              key: 'schoolname',
              value: ''
            },
          ],
          filter: []
        }
      },
      previewDialog: false,
      previewImg: ""
    }
  },
  async mounted() {
    let a = document.querySelector(".logo-search .list-filter>div")
    a.classList.add("flex")
    a.classList.add("flex-start")
    // 增加公共配置
    let commonConfig = await LoginConfigModel.getCommonConfig()
    this.commonList = [commonConfig]
    // 获取列表
    this.ListMethods().getList(1, this.lists.pages.size, this.lists.query)
    // 初始化筛选
    this.ListMethods().initFilter()
    // 获取前台地址
    this.webUrl = await ConfigModel.getEdit(CONFIG_NAME_EXP, "webUrl")
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          [$this.lists.list, $this.lists.pages] = await LoginConfigModel.getList(page, size, query)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter() {

        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(1, $this.lists.pages.size, $this.lists.query)
        },
        // 点击列表的保存按钮
        async clickEditBtn(edit, $index, type) {
          let editObject = edit.configs
          // 检测
          if (type === 'common') {
            if (editObject.baseImg && editObject.icon && editObject.title) {
            } else {
              msg_err("请将此配置信息补充完整！")
              return
            }
          } else {
            // 要么填全，要么不填
            if (editObject.title) {
              if (!(editObject.baseImg && editObject.icon)) {
                msg_err("请将此学校的其余配置信息补充完整")
                return
              }
            }
            if (editObject.baseImg) {
              if (!(editObject.title && editObject.icon)) {
                msg_err("请将此学校的其余配置信息补充完整")
                return
              }
            }
            if (editObject.icon) {
              if (!(editObject.baseImg && editObject.title)) {
                msg_err("请将此学校的其余配置信息补充完整")
                return
              }
            }
          }
          if (editObject.title.length > 30) {
            msg_err("标题最多只能输入30个字")
            return
          }
          if (await LoginConfigModel.saveInfo(editObject)) {
            msg_success("保存成功")
          }
        },
        // 点击清空按钮
        async clickRemoveBtn(edit, $index, type) {
          console.log(edit)
          if (type === 'common') {
            $this.$set($this.commonList[$index], "configs", {
              baseImg: "",
              title: "",
              icon: "",
              schoolid: null,
            })
          } else {
            $this.$set($this.lists.list[$index], "configs", {
              baseImg: "",
              title: "",
              icon: "",
              schoolid: edit.id
            })
          }
        },
        // 点击复制学校链接按钮
        async clickCopyBtn(school, e) {
          let webUrl = $this.webUrl + "login?schoolId=" + school.id
          clip(webUrl, e);
        },
      }
    },
    /** 图片上传-开始 **/
    // 封面图上传成功后
    imgUploadSuccess(data, index, type, listIndex) {
      if (type === "baseImg") {
        if (listIndex === 1) {
          this.$set(this.commonList[index].configs, 'baseImg', data.data)
          this.$set(this.commonList[index].configs, 'uploading1', false)
        }
        if (listIndex === 2) {
          this.$set(this.lists.list[index].configs, 'baseImg', data.data)
          this.$set(this.lists.list[index].configs, 'uploading1', false)
        }
      }
      if (type === "icon") {
        if (listIndex === 1) {
          this.$set(this.commonList[index].configs, 'icon', data.data)
          this.$set(this.commonList[index].configs, 'uploading2', false)
        }
        if (listIndex === 2) {
          this.$set(this.lists.list[index].configs, 'icon', data.data)
          this.$set(this.lists.list[index].configs, 'uploading2', false)
        }
      }
      return true
    },
    // 封面图开始上传之前
    imgBeforeUpload(file, index, type, listIndex) {
      if (!BaseUploadModel.fileTypeLimit(file, "image")) {
        msg_err('只能上传图片文件!')
        return false
      }
      let maxM = 3
      if (type === "baseImg") {
        maxM = 3
      }
      if (type === "icon") {
        maxM = 1
      }
      const isLtM = file.size / 1024 / 1024 < maxM
      if (!isLtM) {
        msg_err('此类图片大小不能超过' + maxM + 'MB!')
        return isLtM
      }
      if (type === "baseImg") {
        if (listIndex === 1) {
          this.$set(this.commonList[index].configs, 'uploading1', true)
        }
        if (listIndex === 2) {
          this.$set(this.lists.list[index].configs, 'uploading1', true)
        }
      }
      if (type === "icon") {
        if (listIndex === 1) {
          this.$set(this.commonList[index].configs, 'uploading2', true)
        }
        if (listIndex === 2) {
          this.$set(this.lists.list[index].configs, 'uploading2', true)
        }
      }
      return true
    },
    // 自定义上传请求
    async uploadRequest(upload) {
      let $this = this
      let file = upload.file
      return await new Promise((resolve, reject) => {
        BaseUploadModel.qiNiuUpload(file, {
          next: (result) => {
          },
          error: (errResult) => {
            console.log(errResult)
            alert('上传失败')
          },
          complete: (result) => {
            let domain = BaseUploadModel.getBucketDomain(file)
            let url = domain + '/' + result.key + ''
            resolve({data: url})
          }
        })
      })
    },
    /** 图片上传-结束 **/
    //  点击预览按钮
    clickPreviewBtn(img) {
      if (img) {
        this.previewImg = img;
        this.previewDialog = true;
      }
    }
  }
}
</script>

<style>
.upload-baseImg .el-upload {
  width: 200px;
  height: 120px;
}

.upload-icon .el-upload {
  width: 80px;
  height: 80px;
}

.upload-baseImg .uploader-icon {
  height: 120px;
  width: 200px;
  line-height: 120px;
}

.upload-icon .uploader-icon {
  width: 80px;
  height: 80px;
  line-height: 80px;
}
</style>
<style scoped lang="scss">
.imgTool {
  img {
    width: 100px;
    height: 100px;
  }
}

.upload-baseImg .img-show {
  width: 200px;
  height: 120px;
}

.upload-baseImg .upload-icon {
  height: 120px;
  line-height: 120px;
}

.upload-icon .img-show {
  width: 80px;
  height: 80px;
}
</style>
