<template>
  <div class="app-container">
    <el-form label-width="120px" :model="info" ref="editForm" :rules="formRules">
      <el-form-item label="标题:" prop="title">
        <el-input v-model="info.title"></el-input>
      </el-form-item>
      <el-form-item label="类型:" prop="type">
        <el-input v-model="info.type" style="width: 200px"></el-input>
      </el-form-item>
      <el-form-item label="内容:" prop="content">
        <tinymce
          ref="tinymce_content"
          v-model="info.content"
          :height="500"
        />
      </el-form-item>
      <el-form-item label="发送给:">
        <el-cascader :props="chooseProps" v-model="chooseList" style="width: 900px"></el-cascader>
      </el-form-item>
      <el-form-item class="flex flex-around">
        <el-button type="primary" @click="clickSendBtn">发送消息</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>


<script>
/**
 * 选择列表有4层
 * 学校列表+全部
 * 学院列表+全部
 * 实验列表+全部
 * 老师+学生+全部
 */
import Tinymce from "@/components/Tinymce"
import {SchoolModel} from "@/model/exp/SchoolModel";
import {DepartmentModel} from "@/model/exp/DepartmentModel";
import {ExperimentScheduleModel} from "@/model/exp/ExperimentScheduleModel";
import {msg_err, msg_success} from "@/utils/ele_component";
import {MessageModel} from "@/model/erp/MessageModel";
import {checkPhone} from "@/utils/common";

export default {
  name: "settingMessage",
  components: {Tinymce},
  data() {
    // 获取发送列表
    async function getList(node, resolve) {
      const {level} = node;
      // 选择学校列表
      if (level === 0) {
        // 获取学校列表
        let schoolList = await SchoolModel.getList(1, 0, {})
        let schoolFormList = []
        schoolList.forEach((li) => {
          schoolFormList.push({
            value: li.id,
            label: li.name,
          })
        })
        schoolFormList.unshift({
          value: "0",
          label: "全部",
          leaf: 'leaf',
        })
        resolve(schoolFormList)
      }
      // 选择学院列表
      if (level === 1) {
        // 获取学院列表
        let departmentList = await DepartmentModel.getList(1, 0, {
          schoolid: node.value
        })
        let departmentFormList = []
        departmentList.forEach((li) => {
          departmentFormList.push({
            value: li.id,
            label: li.name,
          })
        })
        departmentFormList.unshift({
          value: "0",
          label: "全部",
          leaf: 'leaf',
        })
        resolve(departmentFormList)
      }
      // 选择实验列表
      if (level === 2) {
        // 获取实验列表
        let [experimentList] = await ExperimentScheduleModel.getExperimentScheduleList(1, 1000, {
          departmentid: node.value
        })
        // 整理实验列表
        let experimentFormList = []
        experimentList.forEach((li) => {
          li["departmentExperimentVoList"][0]["experimentVoList"].forEach(lis=>{
            experimentFormList.push({
              value: lis["id"],
              label: lis["name"],
            })
          })
        })
        experimentFormList.unshift({
          value: "0",
          label: "全部",
          leaf: 'leaf',
        })
        resolve(experimentFormList)
      }
      // 选择老师、学生列表
      if (level === 3) {
        let peopleList = [
          {
            value: "0",
            label: "全部",
            leaf: level >= 3
          },
          {
            value: "1",
            label: "老师",
            leaf: level >= 3
          },
          {
            value: "2",
            label: "学生",
            leaf: level >= 3
          }
        ]
        resolve(peopleList)
      }
    }

    // 校检发送对象
    const validateSendArr = (rule, value, callback) => {
      console.log(value)
      if (this.chooseList.length === 0) {
        callback(new Error('请选择消息接收对象'))
      }
      callback()
    }
    return {
      info: {
        title: "",// 消息标题
        type: "系统通知",
        content: "",// 消息内容
      },
      chooseList: [],// 选择的数组
      // 输入检测
      formRules: {
        'title': {required: true, message: '请输入标题', trigger: 'blur'},
        'type': {required: true, message: '请输入消息类型', trigger: 'blur'},
        'content': {required: true, message: "请输入消息内容", trigger: 'blur'},
      },
      chooseProps: { // 级联框选项
        lazy: true,

        lazyLoad(node, resolve) {
          getList(node, resolve)
        }
      }
    }
  },
  mounted() {

  },
  methods: {
    // 点击发送消息按钮
    clickSendBtn() {
      this.$refs['editForm'].validate(async validate => {
        if (validate) {
          if (this.chooseList.length === 0) {
            msg_err("请选择消息接收对象!")
            return
          }
          await MessageModel.sendExperimentMessage(this.info.title, this.info.type, this.info.content, this.chooseList)
          msg_success("批量发送消息请求已成功提交!")
          this.info = {
            title: "",// 消息标题
            type: "系统通知",
            content: "",// 消息内容
          }
          this.$refs["tinymce_content"].setContent("")
          this.chooseList=[]
        }
      });

    }
  }
}
</script>

<style scoped lang="scss">

</style>
