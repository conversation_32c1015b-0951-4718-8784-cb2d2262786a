<template>
  <div class="app-container">
    <div style="width: 400px">
      <el-form ref="editForm" :rules="formRules" :model="info">
        <el-form-item label-width="120" label="联系我们QQ号" prop="contact_qq">
          <el-input v-model="info.contact_qq"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="saveInfos">保存设置</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import {ConfigModel} from "@/model/erp/ConfigModel";
import {msg_success} from "@/utils/ele_component";
import {CONFIG_NAME_EXP} from "@/model/ConfigModel";

export default {
  name: "settingSetting",
  data() {
    return {
      info:{
        contact_qq: "",
      },
      // 输入检测
      formRules: {
        'contact_qq': {required: true, message: '请输入联系我们QQ号', trigger: 'blur'},
      },
    }
  },
  mounted() {
    this.getInfos()
  },
  methods: {
    // 获取配置信息
    async getInfos() {
      // 获取联系方式-QQ
      this.info.contact_qq = await ConfigModel.getEdit(CONFIG_NAME_EXP, "contact_qq")
    },
    // 保存信息
    async saveInfos() {
      this.$refs['editForm'].validate(async validate => {
        if (validate) {
          // 保存-联系方式-qq
          await ConfigModel.editEdit(CONFIG_NAME_EXP, "contact_qq", this.info.contact_qq)
          msg_success("保存成功")
        }
      });
    }
  }
}
</script>

<style scoped>

</style>
