<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success"
                     @click="ListMethods().exportExcel()">批量导出
          </el-button>
        </div>
      </div>
    </div>

    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              highlight-current-row
              style="width: 100%;">
      <el-table-column label="反馈时间" align="center">
        <template slot-scope="scope">
          <span>{{ date_format(scope.row.createTime, "yyyy-MM-dd HH:mm") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="角色" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.creatorInfo.roleName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="账号" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.creatorInfo.account }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="150"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditBtn(scope.row,scope.$index)">详情
          </el-button>
          <!--                    <el-button type="danger" size="mini" round-->
          <!--                               @click="ListMethods().clickDelBtn(scope.row.feedbackId,scope.$index)">-->
          <!--                      删除-->
          <!--                    </el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     :page-sizes="[10,20,50,100,200,500]"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
  </div>
</template>

<script>
/**
 * todo
 * 导出功能按原型修改
 */

import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import elDragDialog from '@/directive/el-drag-dialog'
import {msg_confirm, msg_err, msg_success} from '@/utils/ele_component'
import {arrToLVArr, checkPhone, find_obj_from_arr_by_id, date_format} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {FeedbackModel} from "@/model/erp/FeedbackModel";
import {excel_export_from_json} from "@/utils/excel";
import {PLATFORM_ID_EXP} from "@/model/ConfigModel";

export default {
  name: 'settingFeedback',
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles
    })
  },
  data() {
    return {
      // 外部方法
      find_obj_from_arr_by_id: find_obj_from_arr_by_id,
      date_format: date_format,
      PLATFORM_ID_EXP:PLATFORM_ID_EXP,
      // 枚举
      enums: enums,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '账号',
              key: 'creatorInfo.account',
              value: '',
              format: (v) => {
                return {'$regex': `.*${v}.*`}
              }
            },
          ],
          filter: [
            {
              type: 'select',
              label: '角色',
              key: 'creatorInfo.roleName',
              value: '',
              data: [
                {label: "全部", value: ""},
                {label: "学生", value: "学生"},
                {label: "老师", value: "老师"}
              ],
              dataObject: [],
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {

              },
            },
            {
              type: 'timeRange',
              key: "createTime",
              label: ['反馈开始时间', '反馈结束时间', '反馈时间'],
              value: '',
              data: [],
              change: function (value) {

              },
              format: function (value) {
                if (value.length === 2) {
                  const start = new Date(value[0]).getTime()
                  const end = new Date(value[1]).getTime()
                  const query = {
                    '$and': [
                      {"createTime": {'$gte': start}},
                      {"createTime": {'$lte': end}},
                    ]
                  }
                  return (query)
                }
              }
            },
          ]
        }
      },
    }
  },
  async mounted() {
    // 获取列表
    this.ListMethods().getList(1, this.lists.pages.size, this.lists.query)
    // 初始化筛选
    this.ListMethods().initFilter()
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          [$this.lists.list, $this.lists.pages] = await FeedbackModel.getPageList($this.PLATFORM_ID_EXP, page - 1, size, "", query)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.page, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter() {

        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击列表的编辑按钮
        clickEditBtn(edit, $index) {
          $this.$router.push({
            name: "settingFeedbackView",
            query: {
              id: edit.feedbackId
            }
          })
        },
        // 点击删除按钮
        async clickDelBtn(id, $index) {
          if (await msg_confirm('确认删除该反馈吗？删除后不可恢复！')) {
            if (await FeedbackModel.deleteOne(id)) {
              this.getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
              msg_success('删除成功!')
            }
          }
        },
        // 导出excel
        async exportExcel() {
          // 检测是否选择了时间
          let date = $this.lists.searchFilter.filter[1].value
          if (!date) {
            msg_err("请先在反馈时间筛选框处选择要导出的时间段！")
            return
          }
          // 生成查询参数
          let query = $this.lists.query
          if (!query.hasOwnProperty("$and")) {
            query = Object.assign(query, $this.lists.searchFilter.filter[1].format(date))
          }
          let list = (await FeedbackModel.getList($this.PLATFORM_ID_EXP, query)).data
          if (list.length > 0) {
            // map reduce生成arr
            function formatJson(filterVal, jsonData) {
              return jsonData.map(v => filterVal.map(j => {
                let value = '';
                switch (j) {
                  case "account":
                    value = v["creatorInfo"]["account"];
                    break;
                  case "roleName":
                    value = v["creatorInfo"]["roleName"];
                    break;
                  case "createTime":
                    value = date_format(v["createTime"], "yyyy-MM-dd HH:mm");
                    break;
                  default:
                    value = v[j]
                }
                return value
              }))
            }

            // todo excel中渲染富文本内容
            const header = ['角色', '账号', "反馈时间", "内容"];
            const filter = ["roleName", "account", "createTime", "content"];
            // 导出excel
            excel_export_from_json(list, header, filter, formatJson, "实验平台反馈列表")
          } else {
            msg_err('要导出的列表为空')
          }
        },
      }
    },
  }
}
</script>

<style lang="scss">
#vhtml img {
  max-width: 100% !important;
}
</style>
