<template>
  <div class="app-container">
    <div class="edit-container">
      <el-form label-width="120px" ref="editForm" :model="detail.edit" :rules="detail.formRules">
        <el-form-item label="账号:">
          <span>{{ detail.edit.creatorInfo["account"] }}</span>
        </el-form-item>
        <el-form-item label="角色:">
          <span>{{ detail.edit.creatorInfo["roleName"] }}</span>
        </el-form-item>
        <el-form-item label="反馈内容:">
          <div v-html="detail.edit.content" id="vhtml"></div>
        </el-form-item>
      </el-form>
      <div class="buttons" style="width: 100%;text-align: center">
        <el-button type="default"
                   @click="$router.go(-1)">返 回
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import {FeedbackModel} from "@/model/erp/FeedbackModel";
import {msg_confirm, msg_err, msg_success} from "@/utils/ele_component";
import enums from "@/views/exp/setting/feedback";
import {PLATFORM_ID_EXP} from "@/model/ConfigModel";

export default {
  name: "settingFeedbackView",
  data() {
    return {
      PLATFORM_ID_EXP: PLATFORM_ID_EXP,
      enums: enums,
      // 详情弹窗
      detail: {
        dialog: false,
        type: 'edit',// 编辑模式、新增模式
        edit: {
          creatorInfo: {}
        },
        // 输入检测
        formRules: {},
      }
    }
  },
  async mounted() {
    let info = await FeedbackModel.getList(PLATFORM_ID_EXP, {
      feedbackId: this.$route.query["id"]
    })
    info = info.data
    if (info.length === 1) {
      this.detail.edit = info[0]
    } else {
      msg_err("未找到此反馈信息")

    }
  },
  methods: {
    // 详情Methods
    DetailMethods() {
      let $this = this
      return {
        //
        adminRoleChange(value) {
          $this.$forceUpdate();
        },
        // 点击确定修改按钮
        async clickEditBtn() {
          $this.$refs['editForm'].validate(async validate => {
            if (validate) {
              $this.detail.loadingEdit = true;
              if (await FeedbackModel.addOrEdit($this.PLATFORM_ID_EXP, $this.detail.edit)) {
                msg_success('修改反馈信息成功')
              }
              $this.detail.loadingEdit = false;
            }
          })
        },
        // 点击删除按钮
        async clickDeleteBtn() {
          if (await msg_confirm('确认删除该反馈吗？删除后不可用恢复！')) {
            $this.detail.loadingDel = true;
            if (await FeedbackModel.deleteOne($this.detail.edit.feedbackId)) {
              $this.detail.dialog = false
              $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
              msg_success('删除成功')
            }
            $this.detail.loadingDel = false;
          }
        },
      }
    }
  }
}
</script>

<style scoped>

</style>
