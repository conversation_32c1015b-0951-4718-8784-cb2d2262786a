<template>
  <div class="app-container">
    <el-form>
      <el-form-item label="内容:">
        <tinymce
          ref="tinymce_content"
          v-model="content"
          :height="500"
        />
      </el-form-item>
      <el-form-item class="flex flex-around">
        <el-button type="primary" @click="clickSaveBtn">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import Tinymce from "@/components/Tinymce"
import {KnowConfigModel} from "@/model/exp/KnowConfigModel";
import {msg_err, msg_success} from "@/utils/ele_component";

export default {
  name: "settingKnow",
  components: {Tinymce},
  data() {
    return {
      contentOri: "",
      content: "",
    }
  },
  async mounted() {
    this.content = await KnowConfigModel.getInfo();
    this.contentOri = this.content;
  },
  methods: {
    async clickSaveBtn() {
      if(this.content===this.contentOri){
        msg_err("您尚未做任何修改，请修改内容后再保存!")
      }else{
        if (await KnowConfigModel.saveInfo(this.content)) {
          msg_success("修改实验须知成功")
        }
      }
    }
  }
}
</script>

<style scoped>

</style>
