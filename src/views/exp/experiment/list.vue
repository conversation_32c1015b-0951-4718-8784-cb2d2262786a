<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success"
                     @click="ListMethods().clickAddBtn()">新增实验
          </el-button>
        </div>
      </div>
    </div>

    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;"
              @sort-change="sort=>ListMethods().sortChange(sort)">
      <el-table-column label="实验编号" align="center" width="200px" prop="id" :sortable="true">
        <template slot-scope="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实验名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属系列" align="center">
        <template slot-scope="scope">
          <span>{{ lists.searchFilter.filter[0].dataObject[scope.row.seriesid] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否是Pico版本" align="center" width="150px">
        <template slot-scope="scope">
          <span>{{ {true: "是", false: "否"}[scope.row.picoVersion] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="添加时间" align="center" prop="createtime" :sortable="'custom'">
        <template slot-scope="scope">
          <span>{{ scope.row.createtime|dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="100"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickViewBtn(scope.row,scope.$index)">详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     :page-sizes="[10,20,50,100,200,500]"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
  </div>
</template>

<script>
/**
 * todo
 * 实验编号模糊搜索
 * 实验编号排序
 * 添加时间排序
 */
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import elDragDialog from '@/directive/el-drag-dialog'
import {msg_confirm, msg_err, msg_input, msg_success} from '@/utils/ele_component'
import {
  arrToLVArr,
  checkPhone,
  date_format,
  find_obj_from_arr_by_id,
  isObjArrHasSameIdValue, isObjArrHasSameIdValueAndOtherId,
  objectToLVArr
} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {ExperimentModel} from "@/model/exp/ExperimentModel";
import {CommonModel} from "@/model/CommonModel";
import {BaseUploadModel} from "@/model/BaseUploadModel";
import {VEAModel} from "@/model/VEAModel";

export default {
  name: 'experimentList',
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  // 过滤器
  filters: {
    dateFormat(value) {
      if (value) {
        return date_format(value, "yyyy-MM-dd HH:mm:ss")
      } else {
        return ""
      }
    }
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles
    })
  },
  data() {
    // 校检电话号码
    const validatePhoneNumber = (rule, value, callback) => {
      if (!checkPhone(this.detail.edit.phoneNumber)) {
        callback(new Error('手机号格式不正确'))
      }
      callback()
    }
    return {
      // 外部方法
      date_format: date_format,
      // 枚举
      enums: enums,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '实验编号',
              placeholder: "请输入实验编号",
              key: 'id',
              value: ''
            },
            {
              type: 'input',
              label: '实验名称',
              placeholder: "请输入实验名称",
              key: 'name',
              value: ''
            },
          ],
          filter: [
            {
              type: 'select',
              label: '所属系列',
              key: 'seriesid',
              value: '',
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
            {
              type: 'select',
              label: '是否是Pico版本',
              key: 'picoVersion',
              value: '',
              data: [
                {
                  label: "是",
                  value: 1
                },
                {
                  label: "否",
                  value: 0
                }
              ],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
            {
              type: 'timeRange',
              label: ['开始时间', '结束时间', '添加时间'],
              value: '',
              data: [],
              change: function (value) {
              },
              format: function (value) {
                return {
                  starttime: date_format(value[0], "yyyy-MM-dd HH:mm:ss"),
                  endtime: date_format(value[1], "yyyy-MM-dd HH:mm:ss")
                }
              },
            },
          ]
        }
      }
    }
  },
  async mounted() {
    // 获取列表
    this.ListMethods().getList(1, this.lists.pages.size, this.lists.query)
    // 初始化筛选
    this.ListMethods().initFilter()
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          query = Object.assign(query, $this.lists.queryBase)
          // 增加默认排序
          if (!query.sort) {
            query.sort = "createtime,desc"
          }
          $this.lists.loading = true;
          [$this.lists.list, $this.lists.pages] = await ExperimentModel.getList(page, size, query)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter() {
          // 获取实验系列列表
          let seriesList = await ExperimentModel.getSeriesList(0, 0, {})
          let generateListFilter0 = CommonModel.generateListFilterOptions('name', 'id', seriesList, true)
          $this.$set($this.lists.searchFilter.filter[0], "data", generateListFilter0[0])
          $this.$set($this.lists.searchFilter.filter[0], "dataObject", generateListFilter0[1])
          $this.$set($this.lists.searchFilter.filter[0], "dataOrigin", seriesList)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          this.getList(1, $this.lists.pages.size, query)
          $this.lists.query = query
        },
        // 排序被改变
        async sortChange(sort) {
          let querySort = $this.lists.query.sort
          querySort = CommonModel.elementTableSort(sort)
          $this.$set($this.lists.query, "sort", querySort)
          this.getList(1, $this.lists.pages.size, $this.lists.query)
        },
        // 点击新增按钮
        clickAddBtn() {
          $this.$router.push({
            path: "/exp/experiment/detail",
            query: {
              type: "add"
            }
          })
        },
        // 点击详情按钮
        clickViewBtn(entity, $index) {
          $this.$router.push({
            name: "experimentDetail",
            query: {
              type: "edit",
              id: entity.id
            }
          })
        }
      }
    },
  }
}
</script>

<style scoped lang="scss">
.img-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.img-uploader .el-upload:hover {
  border-color: #409EFF;
}

.img-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
</style>
