<template>
  <div class="app-container">
    <div class="edit-container">
      <div class="dialog-container">
        <el-alert title="使用状态不为生效中时可以修改信息" style="margin-bottom: 10px"
                  v-if="entityInfo.type==='edit'&&entityInfo.edit.usestatus===0"></el-alert>
        <el-form label-width="120px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
          <template v-if="entityInfo.type==='add'&&!departmentId">
            <el-form-item label="学校:" prop="schoolid">
<!--              <el-select v-model="entityInfo.edit.schoolid" placeholder="请选择学校" style="width: 535px"-->
<!--                         @change="v=>EntityInfoMethods().schoolChange(v)">-->
<!--                <el-option v-for="(item,index) in entityInfo.filter[0].data" :value="item.value" :key="index"-->
<!--                           :label="item.label"></el-option>-->
<!--              </el-select>-->
              <el-select
                style="width: 535px"
                v-model="entityInfo.edit.schoolid"
                filterable
                remote
                reserve-keyword
                placeholder="请选择学校"
                :remote-method="v=>EntityInfoMethods().querySchool(v)"
                :loading="entityInfo.filterObject.schoolList.loading"
                @change="v=>EntityInfoMethods().schoolChange(v)">
                <el-option
                  v-for="item in entityInfo.filterObject.schoolList.list"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="所属院系:" prop="departmentid" v-if="entityInfo.edit.schoolid">
              <el-select v-model="entityInfo.edit.departmentid" placeholder="请选择学院" style="width: 535px">
                <el-option v-for="(item,index) in entityInfo.filter[1].data" :value="item.value" :key="index"
                           :label="item.label"></el-option>
              </el-select>
            </el-form-item>
          </template>
          <template v-if="departmentId">
            <el-form-item label="学校:" prop="schoolid">
              <el-select v-model="entityInfo.edit.schoolid" placeholder="请选择学校" style="width: 535px"
                         disabled>
                <el-option v-for="(item,index) in entityInfo.filter[0].data" :value="item.value" :key="index"
                           :label="item.label"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="所属院系:" prop="departmentid">
              <el-select v-model="entityInfo.edit.departmentid" placeholder="请选择学院" style="width: 535px"
                         disabled>
                <el-option v-for="(item,index) in entityInfo.filter[1].data" :value="item.value" :key="index"
                           :label="item.label"></el-option>
              </el-select>
            </el-form-item>
          </template>
          <el-form-item label="所属系列:" prop="seriesid">
            <el-select v-model="entityInfo.edit.seriesid" placeholder="请选择系列" style="width: 535px"
                       :disabled="entityInfo.type==='edit'||entityInfo.edit.usestatus===0"
                       @change="value=>EntityInfoMethods().seriesChange(value,true)">
              <el-option v-for="(item,index) in entityInfo.filter[2].data" :value="item.value" :key="index"
                         :label="item.label"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="授权实验:" prop="experimentid" v-if="entityInfo.edit.seriesid">
            <el-select v-model="entityInfo.edit.experimentid" placeholder="请选择实验" style="width: 535px"
                       :disabled="entityInfo.type==='edit'||entityInfo.edit.usestatus===0"
            >
              <el-option v-for="(item,index) in entityInfo.filter[3].data" :value="item.value" :key="index"
                         :label="item.label"
                         v-if="entityInfo.edit.seriesid"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="授权使用时间:">
            <div class="flex flex-start">
              <el-radio style="min-height: 36px;line-height: 36px" v-model="entityInfo.edit.authorizedusetimestatus" :label="true"
                        :disabled="entityInfo.type==='edit'&&entityInfo.edit.usestatus===0">永久
              </el-radio>
              <el-radio style="min-height: 36px;line-height: 36px" v-model="entityInfo.edit.authorizedusetimestatus" :label="false"
                        :disabled="entityInfo.type==='edit'&&entityInfo.edit.usestatus===0">指定时间段
              </el-radio>
              <el-date-picker
                :disabled="entityInfo.type==='edit'&&entityInfo.edit.usestatus===0"
                @change="v=>EntityInfoMethods().dateChange(v)"
                @onPick="v=>EntityInfoMethods().dateChange(v)"
                v-if="!entityInfo.edit.authorizedusetimestatus"
                v-model="entityInfo.edit.useDate"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00','23:59:59']"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="buttons">
        <el-button type="default"
                   @click="$router.go(-1)">取 消
        </el-button>
        <el-button type="success" v-if="entityInfo.type==='add'"
                   @click="EntityInfoMethods().clickAddBtn()">新 增
        </el-button>
        <el-button type="success" v-if="entityInfo.type==='edit'&&entityInfo.edit.usestatus!==0"
                   @click="EntityInfoMethods().clickEditBtn()">确认修改
        </el-button>
      </div>
    </div>

  </div>
</template>

<script>
import {SchoolModel} from "@/model/exp/SchoolModel";
import {CommonModel} from "@/model/CommonModel";
import {DepartmentModel} from "@/model/exp/DepartmentModel";
import {ExperimentModel} from "@/model/exp/ExperimentModel";
import {msg_confirm, msg_err, msg_success} from "@/utils/ele_component";
import {ExperimentScheduleModel} from "@/model/exp/ExperimentScheduleModel";
import {VEAModel} from "@/model/VEAModel";

export default {
  name: "experimentSettingEdit",
  data() {
    return {
      // 路由参数
      departmentId: this.$route.query["departmentId"],
      // 新增/编辑信息
      entityInfo: {
        type: "add",
        title: "新增授权实验",
        dialog: false,
        edit: {
          authorizedusetimestatus: true,
          authstatus: true,
        },
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7;   //禁用以前的日期，今天不禁用
            // return date.getTime() <= Date.now();    //禁用今天以及以前的日期
          }
        },
        filter: [
          // 学校
          {
            data: [],
            dataObject: {},
            dataOrigin: [],
          },
          // 学院
          {
            data: [],
            dataObject: {},
            dataOrigin: [],
          },
          // 系列
          {
            data: [],
            dataObject: {},
            dataOrigin: [],
          },
          // 实验
          {
            data: [],
            dataObject: {},
            dataOrigin: [],
          },
        ],
        filterObject:{
          schoolList:{
            list:[],
            loading:false
          }
        },
        // 输入检测
        formRules: {
          'schoolid': {required: true, message: '请选择学校', trigger: 'change'},
          'departmentid': {required: true, message: '请选择学院', trigger: 'change'},
          'experimentid': {required: true, message: '请选择实验', trigger: 'change'},
          'seriesid': {required: true, message: '请选择实验系列', trigger: 'change'},
        },
      },
    }
  },
  async mounted() {
    if (this.departmentId) {// 如果是从学院直接新增
      let departmentQueryList = await DepartmentModel.getList(0, 0, {departmentid: this.departmentId})
      if (departmentQueryList.length > 0) {
        delete this.entityInfo.formRules.schoolid
        delete this.entityInfo.formRules.departmentid
        let departmentInfo = departmentQueryList[0]
        let schoolInfo = (await SchoolModel.getList(0, 0, {schoolid: departmentInfo.schoolid}))[0]
        this.entityInfo.edit.schoolid = schoolInfo.id
        this.entityInfo.edit.departmentid = this.departmentId
      }
    }
    let type = this.$route.query["type"]
    this.entityInfo.type = type
    if (type === "edit") {// 编辑模式
      let info = JSON.parse(sessionStorage.getItem("experimentDetailEntity"))
      await this.EntityInfoMethods().seriesChange(info.seriesid, true)
      this.entityInfo.edit = info
    }else{
      VEAModel.setBreadThirdTitle("新增授权实验")
    }
    this.EntityInfoMethods().initFilter()
  },
  methods: {
    // 新增、编辑Methods
    EntityInfoMethods() {
      let $this = this
      return {
        // 学校搜索
        async querySchool(name) {
          $this.$set($this.entityInfo.filterObject.schoolList, "loading", true)
          let list = await SchoolModel.getSchoolListByName(name)
          let listResult = []
          list.forEach(li => {
            listResult.push({
              value: li.id,
              label: li.name
            })
          })
          $this.$set($this.entityInfo.filterObject.schoolList, "list", listResult)
          $this.$set($this.entityInfo.filterObject.schoolList, "loading", false)
        },
        // 初始化筛选列表
        async initFilter() {
          // 获取学校列表
          let schoolList = await SchoolModel.getList(0, 0, {})
          let generateListFilter0 = CommonModel.generateListFilterOptions('name', 'id', schoolList)
          $this.$set($this.entityInfo.filter[0], "data", generateListFilter0[0])
          $this.$set($this.entityInfo.filter[0], "dataObject", generateListFilter0[1])
          $this.$set($this.entityInfo.filter[0], "dataOrigin", schoolList)
          $this.$set($this.entityInfo.filterObject.schoolList, "list", generateListFilter0[0])
          // 获取学院列表
          let departmentList = await DepartmentModel.getList(0, 0, {
            schoolid: $this.schoolId
          })
          let generateListFilter1 = CommonModel.generateListFilterOptions('name', 'id', departmentList)
          $this.$set($this.entityInfo.filter[1], "data", generateListFilter1[0])
          $this.$set($this.entityInfo.filter[1], "dataObject", generateListFilter1[1])
          $this.$set($this.entityInfo.filter[1], "dataOrigin", departmentList)
          // 获取实验系列列表
          let seriesList = await ExperimentModel.getSeriesList(0, 0, {})
          let generateListFilter2 = CommonModel.generateListFilterOptions('name', 'id', seriesList)
          $this.$set($this.entityInfo.filter[2], "data", generateListFilter2[0])
          $this.$set($this.entityInfo.filter[2], "dataObject", generateListFilter2[1])
          $this.$set($this.entityInfo.filter[2], "dataOrigin", seriesList)
        },
        // 所属学校被改变
        async schoolChange(v) {
          // 获取学院列表
          let departmentList = await DepartmentModel.getList(0, 0, {
            schoolid: v
          })
          let generateListFilter1 = CommonModel.generateListFilterOptions('name', 'id', departmentList)
          $this.$set($this.entityInfo.filter[1], "data", generateListFilter1[0])
          $this.$set($this.entityInfo.filter[1], "dataObject", generateListFilter1[1])
          $this.$set($this.entityInfo.filter[1], "dataOrigin", departmentList)
          $this.$set($this.entityInfo.edit, "departmentid", "");
        },
        // 所属系列被改变
        async seriesChange(v, setNull) {
          // 获取该系列实验列表
          let [experimentList] = await ExperimentModel.getList(1, 1000, {
            seriesid: v
          })
          let generateListFilter3 = CommonModel.generateListFilterOptions('name', 'id', experimentList)
          $this.$set($this.entityInfo.filter[3], "data", generateListFilter3[0])
          $this.$set($this.entityInfo.filter[3], "dataObject", generateListFilter3[1])
          $this.$set($this.entityInfo.filter[3], "dataOrigin", experimentList)
          if (setNull) {
            $this.$set($this.entityInfo.edit, "experimentid", "");
          }
        },
        // 授权时间改变
        dateChange(v) {
          console.log(v)
        },
        // 点击新增按钮
        clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 判断授权时间选择
              if (!$this.entityInfo.edit.authorizedusetimestatus) { // 非永久授权
                if (!$this.entityInfo.edit.useDate) {
                  msg_err("请选择授权时间段范围!")
                  return
                }
                let entity = JSON.parse(JSON.stringify($this.entityInfo.edit))
                if (new Date(entity.useDate[1]).getTime() <= new Date(entity.useDate[0]).getTime()) {
                  msg_err("开始时间和结束时间不能选择同一天")
                  return
                }
                $this.entityInfo.edit.authorizedusestime = $this.entityInfo.edit.useDate[0]
                $this.entityInfo.edit.authorizeduseetime = $this.entityInfo.edit.useDate[1]
              }
              // 构建参数
              console.log($this.entityInfo.edit)
              if(await ExperimentScheduleModel.save($this.entityInfo.edit)){
                msg_success('新增成功')
                if($this.departmentId){ // 从详情页跳转而来
                  $this.$router.go(-1)
                }else{// 从实验安排列表页跳转而来
                  $this.$router.push({
                    name:"experimentSetting"
                  })
                }
              }
            }
          })
        },
        // 点击修改按钮
        clickEditBtn($index) {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 判断授权时间选择
              if (!$this.entityInfo.edit.authorizedusetimestatus) { // 非永久授权
                if (!$this.entityInfo.edit.useDate) {
                  msg_err("请选择授权时间段范围!")
                  return
                }
                let entity = JSON.parse(JSON.stringify($this.entityInfo.edit))
                if (new Date(entity.useDate[1]).getTime() - new Date(entity.useDate[0]).getTime() < 86400000) {
                  msg_err("结束时间至少比开始时间多1天")
                  return
                }
                $this.entityInfo.edit.authorizedusestime = $this.entityInfo.edit.useDate[0]
                $this.entityInfo.edit.authorizeduseetime = $this.entityInfo.edit.useDate[1]
              }
              // 构建参数
              if (await msg_confirm('确认要修改授权实验吗？')) {
                if(await ExperimentScheduleModel.update($this.entityInfo.edit)){
                  msg_success('修改成功')
                  sessionStorage.setItem("experimentDetailEntity", JSON.stringify($this.entityInfo.edit))
                  $this.$router.go(-1)
                }
              }
            }
          })
        }
      }
    },
  }
}
</script>

<style scoped lang="scss">
.edit-container {
  width: 650px;
}

.buttons {
  text-align: center;
}
</style>
