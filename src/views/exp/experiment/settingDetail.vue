<template>
  <div class="app-container">
    <div class="department-title">{{ schoolName + " " + departmentName }}</div>
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success"
                     @click="ListMethods().clickAddEntityBtn()">新增授权实验
          </el-button>
        </div>
      </div>
    </div>

    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;" @sort-change="sort=>ListMethods().sortChange(sort)">
      <el-table-column label="实验名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.experimentname }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属系列" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.seriesname }}</span>
        </template>
      </el-table-column>
      <el-table-column label="授权时间" align="center" prop="dateSort" :sortable="'custom'">
        <template slot-scope="scope">
          <span v-if="!scope.row.authorizedusetimestatus">{{
              scope.row.authorizedusestime|dateFormat
            }} - {{ scope.row.authorizeduseetime|dateFormat }}</span>
          <span v-else>永久</span>
        </template>
      </el-table-column>
      <el-table-column label="使用状态" align="center" width="200">
        <template slot-scope="scope">
          <span v-if="scope.row.usestatus===0" class="status0"><i
            class="status_dot"></i>{{ enums.experimentUseStatus[scope.row.usestatus] }}</span>
          <span v-if="scope.row.usestatus===1" class="status1"><i
            class="status_dot"></i>{{ enums.experimentUseStatus[scope.row.usestatus] }}</span>
          <span v-if="scope.row.usestatus===2" class="status2"><i
            class="status_dot"></i>{{ enums.experimentUseStatus[scope.row.usestatus] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="授权" align="center" width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.authstatus"
            active-color="#13ce66"
            inactive-color="#ff4949"
          @change="v=>ListMethods().clickToggleAuthStatus(scope.row,scope.$index)">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="100"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditEntityBtn(scope.row,scope.$index)">详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import elDragDialog from '@/directive/el-drag-dialog'
import {msg_confirm, msg_err, msg_success} from '@/utils/ele_component'
import {arrToLVArr, checkPhone, date_format, find_obj_from_arr_by_id, objectToLVArr} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {SchoolModel} from "@/model/exp/SchoolModel";
import clip from '@/utils/clipboard';
import {DepartmentModel} from "@/model/exp/DepartmentModel";
import {ExperimentScheduleModel} from "@/model/exp/ExperimentScheduleModel";
import {ExperimentModel} from "@/model/exp/ExperimentModel";
import {CommonModel} from "@/model/CommonModel";
import {VEAModel} from "@/model/VEAModel";

export default {
  name: 'experimentSettingDetail',
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  filters: {
    dateFormat(value) {
      if (value) {
        return date_format(value, "yyyy-MM-dd HH:mm:ss")
      } else {
        return ""
      }
    },
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles
    })
  },
  watch: {
    '$route'(to, from) { //监听路由是否变化
      if (to.query.departmentId !== from.query.departmentId) {
        this.departmentId = to.query.departmentId;
        this.init();//重新加载数据
      }
    }
  },
  data() {
    // 校检电话号码
    const validatePhoneNumber = (rule, value, callback) => {
      if (!checkPhone(this.detail.edit.phoneNumber)) {
        callback(new Error('手机号格式不正确'))
      }
      callback()
    }
    return {
      // 路由参数
      schoolId: this.$route.query["schoolId"],
      departmentId: this.$route.query["departmentId"],
      schoolName: this.$route.query["schoolName"],
      departmentName: this.$route.query["departmentName"],
      // 外部方法
      date_format: date_format,
      find_obj_from_arr_by_id: find_obj_from_arr_by_id,
      // 枚举
      enums: enums,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '实验名称',
              key: 'name',
              value: ''
            },
          ],
          filter: [
            {
              type: 'select',
              label: '使用状态',
              key: 'usestatus',
              value: '',
              data: [
                {label: "全部", value: ""},
                {label: "生效中", value: 0},
                {label: "已过期", value: 1},
                {label: "已关闭", value: 2},
              ],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            }
          ]
        }
      },

    }
  },
  async mounted() {
    this.init()
  },
  methods: {
    async init() {
      let departmentQueryList = await DepartmentModel.getList(0, 0, {departmentid: this.departmentId})
      if (departmentQueryList.length > 0) {
        let departmentInfo = departmentQueryList[0]
        this.departmentName = departmentInfo.name
        let schoolInfo = (await SchoolModel.getList(0, 0, {schoolid: departmentInfo.schoolid}))[0]
        this.schoolName = schoolInfo.name
        this.schoolId = schoolInfo.id
      }
      // 设置学院信息获取列表
      this.lists.queryBase = {
        departmentid: this.departmentId
      }
      // 获取列表
      this.ListMethods().getList(1, this.lists.pages.size, this.lists.query)
      // 初始化筛选
      this.ListMethods().initFilter()
    },
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          query = Object.assign(query, $this.lists.queryBase)
          $this.lists.loading = true;
          // 增加默认排序
          if(!query.sort){
            query.sort="dateSort,asc"
          }
          [$this.lists.list, $this.lists.pages] = await ExperimentScheduleModel.getDetail(page, size, query)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter() {
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(1, $this.lists.pages.size, $this.lists.query)
        },
        // 排序被改变
        async sortChange(sort) {
          let querySort = $this.lists.query.sort
          querySort = CommonModel.elementTableSort(sort)
          // 时间按开始和结束排序
          // querySort = querySort.replace("authorizedusestime,desc", "authorizeduseetime,desc")
          // querySort = querySort.replace("authorizedusetime,asc", "authorizedusestime,asc")
          $this.$set($this.lists.query, "sort", querySort)
          this.getList(1, $this.lists.pages.size, $this.lists.query)
        },
        // 点击新增实体按钮
        clickAddEntityBtn() {
          $this.$router.push({
            name: "experimentSettingEdit",
            query: {
              type: "add",
              departmentId: $this.departmentId,
            }
          })
        },
        // 点击实体详情按钮
        clickEditEntityBtn(entity, $index) {
          entity.schoolid = $this.schoolId
          entity.departmentid = $this.departmentId
          // 如果授权时间不为永久，就设置时间
          if (!entity.authorizedusetimestatus) {
            let date = []
            if (entity.authorizedusestime) {
              date = [entity.authorizedusestime, entity.authorizeduseetime]
            }
            entity.useDate = date
          }
          sessionStorage.setItem("experimentDetailEntity", JSON.stringify(entity))
          $this.$router.push({
            name: "experimentSettingEdit",
            query: {
              type: "edit",
              departmentId: $this.departmentId,
            }
          })
        },
        // 点击关闭授权、开启授权按钮
        async clickToggleAuthStatus(entity, $index) {
          if (await ExperimentScheduleModel.update({
            id: entity.id,
            authstatus: entity.authstatus
          })) {
            msg_success(entity.authstatus ? "开启授权成功!" : "关闭授权成功!")
            // 获取列表
            $this.ListMethods().getList(1, $this.lists.pages.size, $this.lists.query)
          }
        }
      }
    },

  }
}
</script>


<style scoped lang="scss">
.department-title {
  text-align: left;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 20px;
}

.status0 {
  color: #00a854;
}

.status0 .status_dot {
  background-color: #00a854;
}

.status2 .status_dot {
  background-color: #ff4949;
}

.status2 {
  color: #ff4949;
}

.status1 {
  color: #999;
}

.status1 .status_dot {
  background-color: #bfbfbf;
}

.status_dot {
  display: inline-block;
  width: 5px;
  height: 5px;
  border-radius: 2.5px;
  margin-right: 5px;
  vertical-align: middle;
}
</style>
