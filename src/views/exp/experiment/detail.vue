<template>
  <div class="app-container">
    <div class="edit-container">
      <el-form label-width="180px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
        <el-form-item label="实验编号:" v-if="entityInfo.type==='edit'">
          <span>{{ entityInfo.edit.id }}</span>
        </el-form-item>
        <el-form-item label="实验名称:" prop="name">
          <el-input v-model.trim="entityInfo.edit.name">
            <span slot="suffix" v-if="entityInfo.edit.name">
              <span class="el-input__count">
                <span class="el-input__count-inner">
                  {{ entityInfo.edit.name.length }} / 30
                </span>
              </span>
            </span>
          </el-input>
        </el-form-item>
        <el-form-item label="所属系列:" prop="seriesid">
          <el-select v-model="entityInfo.edit.seriesid" @change="v=>EntityInfoMethods().onSeriesChange(v)"
                     style="width: 470px">
            <el-option v-for="item in lists.searchFilter.filter[0].data" :value="item.value" :label="item.label"
                       :key="item.value">
              <div class="flex flex-between">
                <span>{{ item.label }}</span>
                <el-button type="text" size="mini" v-if="item.label!=='无'"
                           @click="EntityInfoMethods().clickEditSeriesBtn(item.value,item.label)">修改
                </el-button>
              </div>
            </el-option>
          </el-select>
          <el-button style="margin-left: 10px" type="text" size="mini"
                     @click="EntityInfoMethods().clickAddSeriesBtn()">新增系列
          </el-button>
        </el-form-item>
        <el-form-item label="是否是Pico版本" prop="picoVersion">
          <el-select v-model="entityInfo.edit.picoVersion">
            <el-option :value="true" label="是" :key="true"></el-option>
            <el-option :value="false" label="否" :key="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Pico包名" prop="picoPackageName" v-if="entityInfo.edit.picoVersion">
          <el-input v-model.trim="entityInfo.edit.picoPackageName">
            <span slot="suffix" v-if="entityInfo.edit.picoPackageName">
              <span class="el-input__count">
                <span class="el-input__count-inner">
                  {{ entityInfo.edit.picoPackageName.length }} / 50
                </span>
              </span>
            </span>
          </el-input>
        </el-form-item>
        <el-form-item label="Pico包下载地址-外网" prop="picoPackageUrlOut" v-if="entityInfo.edit.picoVersion">
          <div><el-input v-model.trim="entityInfo.edit.picoPackageUrlOut">
          </el-input></div>
          <el-alert title="在版本管理-包管理中上传后复制" style="margin-top: 10px"></el-alert>
        </el-form-item>
        <el-form-item label="Pico包下载地址-内网" prop="picoPackageUrlIn" v-if="entityInfo.edit.picoVersion">
          <el-input v-model.trim="entityInfo.edit.picoPackageUrlIn">
          </el-input>
        </el-form-item>
        <el-form-item label="实验Icon:" prop="icon">
          <div class="erp-uploader-one">
            <el-upload
              action="dev"
              :show-file-list="false"
              :on-success="file=>EntityInfoMethods().iconUploadSuccess(file)"
              :http-request="uploadRequest"
              :before-upload="file=>EntityInfoMethods().iconBeforeUpload(file)">
              <img v-if="entityInfo.edit.icon&&!entityInfo.uploading" :src="entityInfo.edit.icon"
                   class="img-show" @click="" alt="">
              <i v-if="!entityInfo.edit.icon&&!entityInfo.uploading" class="el-icon-plus uploader-icon"></i>
              <i v-if="!entityInfo.edit.icon&&entityInfo.uploading" class="el-icon-loading uploader-icon"></i>
            </el-upload>
            <div class="buttons" style="width: 400px;margin-left: 75px">
              <el-button type="text" v-if="entityInfo.edit.icon"
                         @click="entityInfo.edit.icon?entityInfo.uploadPreviewShow=true:false">预览
              </el-button>
              <el-button type="text" v-if="entityInfo.edit.icon" @click="entityInfo.edit.icon=''">删除</el-button>
            </div>
            <div class="des">
              <div>图片比例：400px X 400px</div>
              <div>图片大小：1M以内</div>
            </div>
            <el-dialog center v-el-drag-dialog :visible.sync="entityInfo.uploadPreviewShow" width="600px">
              <img width="100%" :src="entityInfo.edit.icon" alt="" style="cursor: pointer"
                   @click="window.open(entityInfo.edit.icon)">
            </el-dialog>
          </div>
        </el-form-item>
      </el-form>
      <div class="buttons">
        <el-button type="default"
                   @click="EntityInfoMethods().clickCancelBtn()">取 消
        </el-button>
        <el-button type="success" v-if="entityInfo.type==='add'"
                   @click="EntityInfoMethods().clickAddBtn()">新 增
        </el-button>
        <el-button type="success" v-if="entityInfo.type==='edit'"
                   @click="EntityInfoMethods().clickEditBtn()">确认修改
        </el-button>
      </div>
    </div>
    <!--系列编辑框-->
    <el-dialog
      :title="series.title"
      :visible.sync="series.dialog"
      width="670px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="seriesForm" :model="series.edit" :rules="series.formRules">
          <el-form-item label="系列名称:" prop="name">
            <el-input v-model.trim="series.edit.name" placeholder="请输入系列名称，限30字以内">
              <span slot="suffix" v-if="series.edit.name">
              <span class="el-input__count">
                <span class="el-input__count-inner">
                  {{ series.edit.name.length }} / 30
                </span>
              </span>
            </span>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="series.dialog=false">取 消</el-button>
        <el-button v-if="series.type==='add'" type="primary"
                   @click="EntityInfoMethods().sureAddSeries()">提 交</el-button>
        <el-button v-if="series.type==='edit'" type="primary"
                   @click="EntityInfoMethods().sureEditSeries()">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {msg_confirm, msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {isObjArrHasSameIdValue, isObjArrHasSameIdValueAndOtherId} from "@/utils/common";
import {ExperimentModel} from "@/model/exp/ExperimentModel";
import {BaseUploadModel} from "@/model/BaseUploadModel";
import {CommonModel} from "@/model/CommonModel";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {VEAModel} from "@/model/VEAModel";
import {validateMaxLength} from "@/utils/validate";

export default {
  name: "experimentDetail",
  directives: {
    elDragDialog
  },
  data() {
    return {
      window: window,
      uploadRequest: BaseUploadModel.uploadRequest,
      lists: {
        searchFilter: {
          search: [],
          filter: [
            {
              type: 'select',
              label: '所属系列',
              key: 'seriesid',
              value: '',
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            },
          ]
        }
      },

      // 详情
      entityInfo: {
        $index: 0,
        title: "新增实验",
        type: "add",
        dialog: false,
        edit: {},
        uploading: false,
        uploadPreviewShow: false,
        // 输入检测
        formRules: {
          'seriesid': {required: true, message: '请选择所属系列', trigger: 'change'},
          'name': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 30, "实验名称"), trigger: 'blur'},
          'icon': {required: true, message: "请上传icon", trigger: ['change', 'blur']},
          'picoVersion': {required: true, message: "请选择是否是Pico版本", trigger: ['change', 'blur']},
          'picoPackageName': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 50, "Pico包名"), trigger: 'blur'},
          'picoPackageUrlOut': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 300, "Pico包下载地址-外网"), trigger: 'blur'},
          'picoPackageUrlIn': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 300, "Pico包下载地址-内网"), trigger: 'blur'},
        },
      },
      // 系列
      series: {
        dialog: false,
        type: "add",
        edit: {},
        formRules: {
          'name': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 30, "系列名称"), trigger: 'blur'},
        },
      }
    }
  },
  async mounted() {
    let type = this.$route.query["type"]
    this.entityInfo.type = type
    if (type === "edit") {// 编辑模式
      let id = this.$route.query["id"]
      let info = await ExperimentModel.getOne(id)
      if (info) {
        this.entityInfo.edit = info
      } else {
        msg_err("未找到该实验信息！")
      }
    } else {
      VEAModel.setBreadThirdTitle("新增实验")
    }
    this.EntityInfoMethods().initFilter()
  },
  methods: {
    // 实体信息Methods
    EntityInfoMethods() {
      let $this = this
      return {
        // 初始化筛选列表
        async initFilter() {
          // 获取实验系列列表
          let seriesList = await ExperimentModel.getSeriesList(0, 0, {})
          let generateListFilter0 = CommonModel.generateListFilterOptions('name', 'id', seriesList)
          $this.$set($this.lists.searchFilter.filter[0], "data", generateListFilter0[0])
          $this.$set($this.lists.searchFilter.filter[0], "dataObject", generateListFilter0[1])
          $this.$set($this.lists.searchFilter.filter[0], "dataOrigin", seriesList)
        },
        // 点击新增系列按钮
        async clickAddSeriesBtn() {
          $this.series.dialog = true;
          $this.series.type = "add"
          $this.series.edit = {};
          $this.series.title = "新增系列";
          setTimeout(() => {
            $this.$refs['seriesForm'].clearValidate()
          }, 300)
        },
        // 确认新增系列
        async sureAddSeries() {
          $this.$refs['seriesForm'].validate(async validate => {
            if (validate) {
              // 判断是否存在同名
              if (isObjArrHasSameIdValue($this.lists.searchFilter.filter[0].dataOrigin, "name", $this.series.edit)) {
                msg_err("已存在同样的系列名称")
                return
              }
              if (await ExperimentModel.saveSeries($this.series.edit)) {
                msg_success("新增系列成功")
                $this.series.dialog = false
                $this.EntityInfoMethods().initFilter()
              }
            }
          });
        },
        // 点击修改系列按钮
        async clickEditSeriesBtn(id, name) {
          $this.series.dialog = true;
          $this.series.type = "edit"
          $this.series.edit = {
            id: id,
            name: name
          };
          $this.series.title = "修改系列";
          setTimeout(() => {
            $this.$refs['seriesForm'].clearValidate()
          }, 300)
        },
        // 确认修改系列名称
        async sureEditSeries() {
          $this.$refs['seriesForm'].validate(async validate => {
            if (validate) {
              // 判断是否存在同名
              if (isObjArrHasSameIdValueAndOtherId($this.lists.searchFilter.filter[0].dataOrigin, "name", $this.series.edit.name, "id", $this.series.edit.id)) {
                msg_err("已存在相同的系列名称！")
                return
              }
              if (await ExperimentModel.updateSeries($this.series.edit)) {
                msg_success("修改系列成功")
                $this.series.dialog = false
                $this.EntityInfoMethods().initFilter()
              }
            }
          });
        },
        // 所属系列被改变
        onSeriesChange(v) {
          $this.$forceUpdate();
        },
        // icon图上传成功后
        iconUploadSuccess(data) {
          $this.$set($this.entityInfo.edit, 'icon', data.data)
          $this.entityInfo.uploading = false
          // let formRulesOld = JSON.parse(JSON.stringify($this.entityInfo.formRules))
          // $this.$set($this.entityInfo, 'formRules', {
          //   'icon': {required: true, message: "请", trigger: ['change', 'blur']},
          // })
          setTimeout(() => {
            $this.$refs['entityInfoForm'].validate();
            // $this.$set($this.entityInfo, 'formRules', formRulesOld)
            // $this.$refs['entityInfoForm'].validate();
          }, 100)

          return true
        },
        // icon图开始上传之前
        iconBeforeUpload(file) {
          if (!BaseUploadModel.fileTypeLimit(file, "image")) {
            $this.$message.error('只能上传图片文件!')
            return false
          }
          const isLt1M = file.size / 1024 / 1024 <= 1
          if (!isLt1M) {
            $this.$message.error('上传图片大小不能超过1MB!')
            return false
          }
          $this.entityInfo.uploading = true
          return true
        },
        // 点击新增按钮
        clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              if (await ExperimentModel.save($this.entityInfo.edit)) {
                msg_success('新增成功')
                $this.$router.go(-1);
                $this.entityInfo.dialog = false
              }
            }
          })
        },
        // 点击修改按钮
        clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              if (await msg_confirm('确认要修改该实验信息吗？')) {
                // 删除无需修改字段
                delete $this.entityInfo.edit.createtime
                delete $this.entityInfo.edit.seriesname
                if (await ExperimentModel.update($this.entityInfo.edit)) {
                  msg_success('修改成功')
                  $this.entityInfo.dialog = false
                  $this.$router.go(-1);
                }
              }
            }
          })
        },
        // 点击取消按钮
        clickCancelBtn() {
          $this.$router.go(-1);
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.edit-container {
  width: 650px;
}

.buttons {
  text-align: center;
}
</style>
