<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success"
                     @click="ListMethods().clickAddEntityBtn()">新增授权实验
          </el-button>
        </div>
      </div>
    </div>

    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="学校名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.schoolname }}</span>
        </template>
      </el-table-column>
      <el-table-column label="院系名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.departmentname }}</span>
        </template>
      </el-table-column>
      <el-table-column label="授权课程" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.experimentVoList |  experimentNames }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="150"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickViewBtn(scope.row)">详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import elDragDialog from '@/directive/el-drag-dialog'
import {msg_confirm, msg_err, msg_success} from '@/utils/ele_component'
import {arrToLVArr, checkPhone, date_format, find_obj_from_arr_by_id, objectToLVArr} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {SchoolModel} from "@/model/exp/SchoolModel";
import clip from '@/utils/clipboard';
import {DepartmentModel} from "@/model/exp/DepartmentModel";
import {ExperimentScheduleModel} from "@/model/exp/ExperimentScheduleModel";
import {CommonModel} from "@/model/CommonModel";
import {ExperimentModel} from "@/model/exp/ExperimentModel";
import {dateFormat} from "@/filters";
import {VEAModel} from "@/model/VEAModel";

export default {
  name: 'experimentSetting',
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  filters: {
    dateFormat,
    experimentNames(value) {
      if (value) {
        let names = [];
        value.forEach(li => {
          names.push(li.name)
        })
        if (names.length === 0) {
          return "--"
        } else {
          return names.join("、")
        }
      } else {
        return "--"
      }
    }
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles
    })
  },
  data() {
    return {
      // 外部方法
      date_format: date_format,
      // 枚举
      enums: enums,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '学校名称',
              key: 'schoolname',
              value: ''
            },
            {
              type: 'input',
              label: '院系名称',
              key: 'departmentname',
              value: ''
            },
          ],
          filter: [
            {
              type: 'select',
              label: '授权课程',
              key: 'experimentid',
              value: '',
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {
              }
            }
          ]
        }
      },
      // 新增/编辑信息
      entityInfo: {
        type: "add",
        title: "新增授权实验",
        dialog: false,
        edit: {
          name: "",
        },
        filter: [
          // 学校
          {
            data: [],
            dataObject: {},
            dataOrigin: [],
          },
          // 学院
          {
            data: [],
            dataObject: {},
            dataOrigin: [],
          },
          // 系列
          {
            data: [],
            dataObject: {},
            dataOrigin: [],
          },
          // 实验
          {
            data: [],
            dataObject: {},
            dataOrigin: [],
          },
        ],
        // 输入检测
        formRules: {
          'schoolid': {required: true, message: '请选择学校', trigger: 'blur'},
          'departmentid': {required: true, message: '请选择学院', trigger: 'blur'},
          'experimentid': {required: true, message: '请选择实验', trigger: 'blur'},
          'seriesid': {required: true, message: '请输入实验系列', trigger: 'blur'},
        },
      },
    }
  },
  async mounted() {
    // 获取列表
    this.ListMethods().getList(1, this.lists.pages.size, this.lists.query)
    // 初始化筛选
    this.ListMethods().initFilter()
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          [$this.lists.list, $this.lists.pages] = await ExperimentScheduleModel.getExperimentScheduleListNew(page, size, query)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter() {
          // 获取实验列表
          let [experimentList] = await ExperimentModel.getList(1, 10000, {})
          let generateListFilter3 = CommonModel.generateListFilterOptions('name', 'id', experimentList, true)
          $this.$set($this.lists.searchFilter.filter[0], "data", generateListFilter3[0])
          $this.$set($this.lists.searchFilter.filter[0], "dataObject", generateListFilter3[1])
          $this.$set($this.lists.searchFilter.filter[0], "dataOrigin", experimentList)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(1, $this.lists.pages.size, $this.lists.query)
        },
        // 点击查看学院详情按钮
        clickViewBtn(entity) {
          $this.$router.push(`/exp/experiment/settingDetail?departmentId=${entity.departmentid}&departmentName=${entity.departmentname}`)
        },
        // 点击新增实体按钮
        clickAddEntityBtn() {
          $this.$router.push({
            name: "experimentSettingEdit",
            query: {
              type: "add"
            }
          })
        },
      }
    },
  }
}
</script>

<style scoped lang="scss">

</style>
