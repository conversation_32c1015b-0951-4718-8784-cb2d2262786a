<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">

        </div>
      </div>
    </div>
    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="行为" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.action }}</span>
        </template>
      </el-table-column>
      <el-table-column label="注册码" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.expRegCodeId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="设备序列号" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.deviceSerial }}</span>
        </template>
      </el-table-column>
      <el-table-column label="设备名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.deviceName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实验名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.experimentName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实验id" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.experimentId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结果code" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.code }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结果msg" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.msg }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户ip" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.ip }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="155px">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat("yyyy-MM-dd HH:mm:ss") }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat, stringBrFilter} from "@/filters";
import {date_format, getQuery} from "@/utils/common";
import {msg_confirm, msg_input, msg_success} from "@/utils/ele_component";
import {ExpRegCodeLogModel} from "@/model/erp/ExpRegCodeLogModel";
import {ExperimentModel} from "@/model/exp/ExperimentModel";
import {CommonModel} from "@/model/CommonModel";

export default {
  name: "expRegCodeLogList",
  components: {
    ListSearchFilter,
  },
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: {dateFormat},
  data: function () {
    let $this = this;

    return {
      window: window,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      stringBrFilter: stringBrFilter,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '注册码',
              key: 'expRegCodeId',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '设备序列号',
              key: 'deviceSerial',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '设备名称',
              key: 'deviceName',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '实验名称',
              key: 'experimentName',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '实验id',
              key: 'experimentId',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            },
          ],
          filter: [],
          hideFilter: {
            experimentList: {},
            userList: {}
          }
        }
      },
      entityInfo: {
        dialog: false,
        editLoading: false,
        showExperimentList: false,
        title: "新增测试表单",
        type: "add",
        filter: {
          // 项目列表
          experimentList: {
            loading: false,
            list: []
          }
        },
        edit: {
          name: "",
        },
        // 输入检测
        formRules: {},
      },
    }
  },
  async mounted() {
    await this.ListMethods().initFilter()
    this.ListMethods().getList(0, 20, {})
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 点击搜索重置按钮
        clickCleanBtn() {

        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          [list, $this.lists.pages] = await ExpRegCodeLogModel.getPageList(page - 1, size, "", query)
          // 遍历list获取必要信息
          list.forEach(li => {
            // 获取项目名称
            if (li.experimentId) {
              li.experimentName = $this.lists.searchFilter.hideFilter.experimentList[li.experimentId]
            }
          })
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter(type) {
          $this.lists.loading = true
          // 获取所有实验列表
          let [allExperimentList,] = await ExperimentModel.getList(1, 500, {});
          let generateListFilter1 = CommonModel.generateListFilterOptions('name', 'id', allExperimentList)
          $this.$set($this.lists.searchFilter.hideFilter, "experimentList", generateListFilter1[1])
          $this.$set($this.lists.searchFilter.hideFilter, "experimentListArr", generateListFilter1[0])
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "新建注册码"
          $this.entityInfo.edit = {
            bindDevicesList: [],
            experimentList: [],
            needLogin: true,
            maxDevicesNumber: 1,
            limitRunNumber: false,
            limitRunNumberCount: 1,
            limitTime: false,
            active: true
          };
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 400)
        },
        // 点击编辑按钮
        async clickEditBtn(entity, index) {
          // 获取该表单的最新信息
          entity = await ExpRegCodeLogModel.getOne(entity.expRegCodeId)
          $this.$set($this.entityInfo, "edit", entity);
          $this.entityInfo.type = "edit"
          $this.entityInfo.title = "编辑注册码"
          $this.$set($this.lists.list[index], "editLoading", true);
          // 设置标签已选项
          $this.entityInfo.dialog = true;
          $this.$set($this.lists.list[index], "editLoading", false);
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 400)
        },
      }
    },
  }
}
</script>

<style scoped lang="scss">

</style>
