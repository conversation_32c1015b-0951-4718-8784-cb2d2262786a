<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success" @click="ListMethods().clickAddBtn()"
          >新建注册码
          </el-button>
        </div>
      </div>
    </div>
    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="注册码" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.expRegCodeId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="注册码名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属机构" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.organName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否启用" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.active ? "启用" : "禁用" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否限制绑定时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.limitBindTime ? "是" : "否" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否限制授权时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.limitTime ? "是" : "否" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否限制运行次数" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.limitRunNumber ? "是" : "否" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否限制IP" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.limitIp ? "是" : "否" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="155px">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat("yyyy-MM-dd HH:mm:ss") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center">
        <template slot-scope="scope">
          <el-tooltip placement="bottom" v-if="scope.row.remarks">
            <div slot="content" v-html="stringBrFilter(scope.row.remarks)">
            </div>
            <span v-if="scope.row.remarks.length>20">{{
                scope.row.remarks.slice(0, 20) + "  ..."
              }}</span>
            <span class="ellipsis" v-else>{{
                scope.row.remarks
              }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="160"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="success" size="mini" round
                     style="margin-top: 10px;"
                     @click="ListMethods().clickEditBtn(scope.row,scope.$index)">编辑
          </el-button>
          <el-button type="danger" size="mini" round style="margin-top: 10px;"
                     @click="EntityMethods().clickDeleteEntityBtn(scope.row)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
    <!--详情弹窗-新增和编辑-->
    <el-dialog
      :title="entityInfo.title"
      :visible.sync="entityInfo.dialog"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="1000px"
      center
      v-el-drag-dialog>
      <div class="dialog-container" style="max-height: 600px;overflow-y: scroll;padding:10px;">
        <el-form label-width="160px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
          <el-form-item label="名称:" prop="name">
            <el-input v-model.trim="entityInfo.edit.name">
              <div slot="suffix" v-if="entityInfo.edit.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.name.length }} / 30
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="所属机构:" prop="organName">
            <div class="flex flex-start">
              <el-input v-model.trim="entityInfo.edit.organName">
                <div slot="suffix" v-if="entityInfo.edit.organName">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.organName.length }} / 30
                  </span>
                </span>
                </div>
              </el-input>
            </div>
          </el-form-item>
          <el-form-item label="启用状态:" prop="active">
            <template>
              <el-radio v-model="entityInfo.edit.active" :label="true">启用</el-radio>
              <el-radio v-model="entityInfo.edit.active" :label="false">禁用</el-radio>
            </template>
          </el-form-item>
          <el-form-item label="是否限制绑定时间:" prop="limitBindTime">
            <template>
              <el-radio v-model="entityInfo.edit.limitBindTime" :label="true">限制</el-radio>
              <el-radio v-model="entityInfo.edit.limitBindTime" :label="false">不限制</el-radio>
            </template>
          </el-form-item>
          <el-form-item label="绑定时间设置:" prop="limitBindTimeDate" v-if="entityInfo.edit.limitBindTime">
            <el-date-picker
              v-model="entityInfo.edit.limitBindTimeDate"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00','23:59:59']"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="是否限制授权时间:" prop="limitTime">
            <template>
              <el-radio v-model="entityInfo.edit.limitTime" :label="true">限制</el-radio>
              <el-radio v-model="entityInfo.edit.limitTime" :label="false">不限制</el-radio>
            </template>
          </el-form-item>
          <el-form-item label="授权时间设置:" prop="limitTimeDate" v-if="entityInfo.edit.limitTime">
            <el-date-picker
              v-model="entityInfo.edit.limitTimeDate"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00','23:59:59']"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="是否限制运行次数:" prop="limitRunNumber">
            <template>
              <el-radio v-model="entityInfo.edit.limitRunNumber" :label="true">限制</el-radio>
              <el-radio v-model="entityInfo.edit.limitRunNumber" :label="false">不限制</el-radio>
            </template>
          </el-form-item>
          <el-form-item label="剩余运行次数:" prop="limitRunNumberCount" v-if="entityInfo.edit.limitRunNumber">
            <el-input v-model.trim="entityInfo.edit.limitRunNumberCount" type="number">
            </el-input>
          </el-form-item>
          <el-form-item label="是否需要登录:" prop="needLogin">
            <template>
              <el-radio v-model="entityInfo.edit.needLogin" :label="true">需要</el-radio>
              <el-radio v-model="entityInfo.edit.needLogin" :label="false">不需要</el-radio>
            </template>
          </el-form-item>
          <el-form-item label="是否需要限制IP:" prop="limitIp">
            <template>
              <el-radio v-model="entityInfo.edit.limitIp" :label="true">限制</el-radio>
              <el-radio v-model="entityInfo.edit.limitIp" :label="false">不限制</el-radio>
            </template>
          </el-form-item>
          <el-form-item label="允许登录IP列表:" prop="limitIpList" v-if="entityInfo.edit.limitIp">
            <el-button type="primary" size="small" style="margin-bottom: 10px;"
                       @click="EntityMethods().clickAddIpBtn()">新增
            </el-button>
            <el-table :data="entityInfo.edit.limitIpList" border fit
                      style="width: 100%;">
              <el-table-column label="IP" align="center">
                <template slot-scope="scope">
                  <span>{{ scope.row.ip }}</span>
                </template>
              </el-table-column>
              <el-table-column label="备注" align="center">
                <template slot-scope="scope">
                  <span>{{ scope.row.remark }}</span>
                </template>
              </el-table-column>
              <el-table-column label="添加时间" align="center">
                <template slot-scope="scope">
                  <span>{{ scope.row.createTime | dateFormat }}</span>
                </template>
              </el-table-column>
              <el-table-column label="编辑" align="center" width="100">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="EntityMethods().clickDelIpBtn(scope.$index)">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-form-item label="最多绑定实验设备数量:" prop="maxDevicesNumber">
            <el-input v-model.trim="entityInfo.edit.maxDevicesNumber" type="number">
            </el-input>
          </el-form-item>
          <el-form-item label="已绑定实验设备列表:" prop="bindDevicesList">
            <el-button type="primary" size="small" style="margin-bottom: 10px;"
                       @click="EntityMethods().clickAddDeviceBtn()">新增
            </el-button>
            <el-table :data="entityInfo.edit.bindDevicesListNew" border fit
                      style="width: 100%;">
              <el-table-column label="设备名称" align="center">
                <template slot-scope="scope">
                  <span>{{ scope.row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="设备序列号" align="center">
                <template slot-scope="scope">
                  <span>{{ scope.row.serial }}</span>
                </template>
              </el-table-column>
              <el-table-column label="编辑" align="center" width="100">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="EntityMethods().clickDelDeviceBtn(scope.$index)">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-form-item label="授权实验列表:" prop="bindDevicesList">
            <el-select
              style="width: 100%"
              v-model="entityInfo.edit.experimentId"
              @change="v=>EntityMethods().onChangeExperimentId(v)"
            >
              <el-option
                v-for="item in lists.searchFilter.hideFilter.experimentListArr"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <el-button type="primary" size="small" style="margin-bottom: 10px;"
                       @click="EntityMethods().clickAddNewExperiment()">新增
            </el-button>
            <el-table :data="entityInfo.edit.experimentList" border fit
                      style="width: 100%;">
              <el-table-column label="实验名称" align="center">
                <template slot-scope="scope">
                  <span>{{ scope.row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="实验ID" align="center">
                <template slot-scope="scope">
                  <span>{{ scope.row.id }}</span>
                </template>
              </el-table-column>
              <el-table-column label="编辑" align="center" width="100">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="EntityMethods().clickDelExperiment(scope.$index)">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-form-item label="备注:" prop="remarks">
            <el-input type="textarea" autosize v-model="entityInfo.edit.remarks"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="entityInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityMethods().clickAddBtn()"
                   v-if="entityInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityMethods().clickEditBtn()"
                   v-if="entityInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat, stringBrFilter} from "@/filters";
import {date_format, getQuery} from "@/utils/common";
import {msg_confirm, msg_input, msg_success} from "@/utils/ele_component";
import {ExpRegCodeModel} from "@/model/erp/ExpRegCodeModel";
import {ExperimentModel} from "@/model/exp/ExperimentModel";
import {CommonModel} from "@/model/CommonModel";

export default {
  name: "expRegCodeList",
  components: {
    ListSearchFilter,
  },
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: {dateFormat},
  data: function () {
    let $this = this;

    return {
      window: window,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      stringBrFilter: stringBrFilter,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '注册码',
              key: 'expRegCodeId',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '名称',
              key: 'name',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '机构',
              key: 'organName',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            },
          ],
          filter: [],
          hideFilter: {
            experimentList: {},
            userList: {}
          }
        }
      },
      entityInfo: {
        dialog: false,
        editLoading: false,
        showExperimentList: false,
        title: "新增测试表单",
        type: "add",
        filter: {
          // 项目列表
          experimentList: {
            loading: false,
            list: []
          }
        },
        edit: {
          name: "",
        },
        // 输入检测
        formRules: {},
      },
    }
  },
  async mounted() {
    await this.ListMethods().initFilter()
    this.ListMethods().getList(0, 20, {})
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 点击搜索重置按钮
        clickCleanBtn() {

        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          [list, $this.lists.pages] = await ExpRegCodeModel.getPageList(page - 1, size, "", query)
          // 遍历list获取必要信息
          list.forEach(li => {
            // 获取项目名称
            if (li.experimentId) {
              li.experimentName = $this.lists.searchFilter.hideFilter.experimentList[li.experimentId]
            }
          })
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter(type) {
          $this.lists.loading = true
          // 获取所有实验列表
          let [allExperimentList,] = await ExperimentModel.getList(1, 500, {});
          let generateListFilter1 = CommonModel.generateListFilterOptions('name', 'id', allExperimentList)
          $this.$set($this.lists.searchFilter.hideFilter, "experimentList", generateListFilter1[1])
          $this.$set($this.lists.searchFilter.hideFilter, "experimentListArr", generateListFilter1[0])
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "新建注册码"
          $this.entityInfo.edit = {
            bindDevicesList: [],
            experimentList: [],
            limitIpList: [],
            needLogin: true,
            maxDevicesNumber: 1,
            limitRunNumber: false,
            limitRunNumberCount: 1,
            limitBindTime: false,
            limitIp: false,
            limitTime: false,
            active: true
          };
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 400)
        },
        // 点击编辑按钮
        async clickEditBtn(entity, index) {
          // 获取该注册码的最新信息
          entity = await ExpRegCodeModel.getOne(entity.expRegCodeId)
          // 绑定设备对象转为数组
          JSON.stringify()
          entity.bindDevicesListNew = Object.keys(entity.bindDevicesList).map(serial => ({
            serial,
            name: entity.bindDevicesList[serial].name
          }));
          $this.$set($this.entityInfo, "edit", entity);
          $this.entityInfo.type = "edit"
          $this.entityInfo.title = "编辑注册码"
          $this.$set($this.lists.list[index], "editLoading", true);
          // 设置标签已选项
          $this.entityInfo.dialog = true;
          $this.$set($this.lists.list[index], "editLoading", false);
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 400)
        },
      }
    },
    // 实体方法集
    EntityMethods() {
      let $this = this;
      return {
        // 点击新增IP按钮
        async clickAddIpBtn() {
          let ip = await msg_input("请输入要允许使用的IP", "")
          if (!ip) {
            return
          }
          let remark = await msg_input("请输入该ip备注", "")
          if (!remark) {
            return
          }
          $this.entityInfo.edit.limitIpList.push({ip: ip, remark: remark, createTime: new Date().getTime()})
          msg_success("添加 " + ip + " 成功")
        },
        // 点击删除ip按钮
        async clickDelIpBtn(index) {
          if (await msg_confirm("确认要删除该IP吗？")) {
            $this.entityInfo.edit.limitIpList.splice(index, 1)
          }
        },
        // 点击新增设备按钮
        async clickAddDeviceBtn() {
          let name = await msg_input("请输入设备名称", "")
          if (!name) {
            return
          }
          let serial = await msg_input("请输入设备序列号", "")
          if (!serial) {
            return
          }
          $this.entityInfo.edit.bindDevicesListNew.push({name, serial})
          msg_success("添加 " + name + " 成功")
        },
        // 点击删除设备按钮
        async clickDelDeviceBtn(index) {
          if (await msg_confirm("确认要删除该设备吗？")) {
            $this.entityInfo.edit.bindDevicesListNew.splice(index, 1)
          }
        },
        // 当修改了项目id
        onChangeExperimentId(experimentId) {
          $this.entityInfo.edit.experimentName = $this.lists.searchFilter.hideFilter.experimentList[experimentId]
        },
        // 点击了新增实验按钮
        clickAddNewExperiment() {
          if ($this.entityInfo.edit.experimentId) {
            $this.entityInfo.edit.experimentList.push({
              name: $this.entityInfo.edit.experimentName,
              id: $this.entityInfo.edit.experimentId
            })
            msg_success("添加 " + $this.entityInfo.edit.experimentName + " 成功！")
          }
        },
        // 点击使用列表中的删除一个按钮
        async clickDelExperiment(index) {
          if (await msg_confirm("确认要删除该实验吗？")) {
            $this.entityInfo.edit.experimentList.splice(index, 1)
          }
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              let result = await ExpRegCodeModel.addOrEdit($this.entityInfo.edit)
              if (result.code === "000000") {
                msg_success("新增注册码成功！")
                $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                $this.entityInfo.dialog = false
              }
            }
          });
        },
        // 点击编辑按钮
        async clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (await msg_confirm("确定要保存吗？")) {
              if (validate) {
                // 恢复设备列表为对象
                const bindDevicesList = {};
                $this.entityInfo.edit.bindDevicesListNew.forEach(item => {
                  bindDevicesList[item["serial"]] = {
                    name: item.name
                  };
                });
                $this.entityInfo.edit.bindDevicesList = bindDevicesList
                let result = await ExpRegCodeModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("编辑注册码成功！")
                  $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
                  $this.entityInfo.dialog = false
                }
              }
            }
          });
        },
        // 点击删除按钮
        async clickDeleteEntityBtn(entity) {
          if (await msg_confirm("确定要删除该注册码吗？删除后不可恢复！")) {
            if (await ExpRegCodeModel.deleteOne({
              expRegCodeId: entity.expRegCodeId
            })) {
              msg_success("删除成功")
              $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
            }
          }
        },
        // 点击取消按钮
        clickCancelBtn() {
          $this.entityInfo.dialog = false
        },
      }
    },
  }
}
</script>

<style scoped lang="scss">

</style>
