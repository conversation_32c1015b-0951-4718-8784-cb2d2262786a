<template>
  <div class="app-container">
    <!-- 学校筛选框 -->
    <div class="school-filter flex flex-start">
      <el-select v-model="schoolId" style="margin-right: 15px" @change="changeSchool">
        <el-option v-for="item in schoolList" :label="item.name" :value="item.id" :key="item.id"></el-option>
      </el-select>
      <el-button type="success" @click="clickCalBtn" v-if="schoolId&&schoolId!==schoolInfo.id">统计</el-button>
    </div>
    <div v-if="this.schoolInfo.id">
      <div class="school-name">{{ this.schoolInfo.name }}
        <span
          v-show="experimentList.length!==experimentStatisticsList.length">{{
            (experimentStatisticsList.length / experimentList.length * 100).toFixed(0)
          }}%</span>
        <i v-show="experimentList.length!==experimentStatisticsList.length" class="loading el-icon-loading"></i>
      </div>
      <!-- 实验概览和项目列表 -->
      <div class="flex flex-between">
        <!--实验概览-->
        <div class="card-box overview">
          <div class="title flex flex-start">
            <img src="@/assets/exp/statistics/schoolComprehensiveStatistics/card-title-1.png" alt="">
            <span>实验概览</span>
          </div>
          <div class="content flex flex-between">
            <div class="left">
              <div class="title flex flex-start">
                <img src="@/assets/exp/statistics/schoolComprehensiveStatistics/school-title.png" alt="">
                <span>学校总人数</span>
              </div>
              <div class="num">{{ overviewInfo.userNum.totalNum }}<span>人</span></div>
            </div>
            <div class="right">
              <div class="num flex flex-between">
                <div class="left">
                  {{ overviewInfo.userNum.teacherNum }}<span>人</span>
                </div>
                <div class="right">
                  {{ overviewInfo.userNum.studentNum }}<span>人</span>
                </div>
              </div>
              <div class="list">
                <div class="li flex flex-start">
                  <img src="@/assets/exp/statistics/schoolComprehensiveStatistics/school-right-3.png" alt="">
                  <div class="text flex flex-between">
                    <span>完成实验总次数</span>
                    <span>{{ overviewInfo.totalScoreNum }}次</span>
                  </div>
                </div>
                <div class="li flex flex-start">
                  <img src="@/assets/exp/statistics/schoolComprehensiveStatistics/school-right-4.png" alt="">
                  <div class="text flex flex-between">
                    <span>安排实验总人次</span>
                    <span>{{ overviewInfo.totalScheduleNum }}人次</span>
                  </div>
                </div>
                <div class="li flex flex-start">
                  <img src="@/assets/exp/statistics/schoolComprehensiveStatistics/school-right-5.png" alt="">
                  <div class="text flex flex-between">
                    <span>完成实验总人次</span>
                    <span>{{ overviewInfo.totalCompleteNum }}人次</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!--实验项目-->
        <div class="card-box experiment">
          <div class="title flex flex-start">
            <img src="@/assets/exp/statistics/schoolComprehensiveStatistics/card-title-2.png" alt="">
            <span>实验项目</span>
          </div>
          <div class="content">
            <div class="list flex flex-start flex-wrap">
              <div class="li flex flex-center flex-dr" v-for="(item,index) in experimentList">
                <img :src="item.icon" alt="">
                <div class="name word-hr ellipsis">{{ item.name }}</div>
                <div class="series">{{ item.seriesname }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--实验单个列表-->
      <div class="experiment-list flex flex-between flex-wrap">
        <div class="li" v-for="(item,index) in experimentStatisticsList">
          <div class="title flex flex-between">
            <div class="left flex flex-start">
              <img :src="item.icon" alt="">
              <div class="names">
                <div class="name">{{ item.name }}</div>
                <div class="series">{{ item.seriesname }}</div>
              </div>
            </div>
            <div class="right">
              <el-button type="primary" size="small" @click="clickExportBtn(item)">导出</el-button>
            </div>
          </div>
          <div class="content flex flex-between">
            <div class="left">
              <div class="num">{{ item.statistics.scoreNum }}<span>次</span></div>
              <div class="text">完成实验总次数</div>
            </div>
            <div class="right">
              <div class="ls flex flex-start">
                <div class="name flex flex-start">
                  <img src="@/assets/exp/statistics/schoolComprehensiveStatistics/experiment-num-1.png" alt="">
                  <span>安排实验人次</span>
                </div>
                <div class="nums">
                  <div class="block"></div>
                  <div class="num">{{ item.statistics.scheduleNum }}<span>人次</span></div>
                </div>
              </div>
              <div class="ls flex flex-start">
                <div class="name flex flex-start">
                  <img src="@/assets/exp/statistics/schoolComprehensiveStatistics/experiment-num-2.png" alt="">
                  <span>完成实验人次</span>
                </div>
                <div class="nums">
                  <div class="block complete-block" :style="{ width: item.statistics.completeWidth }"></div>
                  <div class="num complete">{{ item.statistics.completeNum }}<span>人次</span></div>
                </div>
              </div>
              <div class="ls flex flex-start">
                <div class="name flex flex-start">
                  <img src="@/assets/exp/statistics/schoolComprehensiveStatistics/experiment-num-3.png" alt="">
                  <span>实验平均分数</span>
                </div>
                <div class="nums">
                  <div class="num avg">{{
                      item.statistics.avgScore != null ? item.statistics.avgScore.toFixed(2) : "无分数"
                    }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.app-container {
  padding: 50px 120px;
  background-color: #dde6ed;
  min-height: 100vh;
}

.school-filter {
  margin-bottom: 30px;
}

.school-name {
  text-align: center;
  font-size: 30px;
  color: #333;
  margin-bottom: 30px;

  .loading {
    margin-left: 0px;
    color: red;
    font-size: 23px;
  }

  span {
    margin-left: 10px;
    color: red;
    font-size: 23px;
  }
}

.card-box {
  padding: 10px 20px;

  > .title {
    padding: 10px 0px;
    border-bottom: 1px dashed #cecece;

    img {
      width: 32px;
      height: 32px;
      margin-right: 15px;
    }

    span {
      font-size: 20px;
    }
  }
}

// 实验概览
.card-box.overview {
  width: 991px;
  height: 432px;
  background-image: url("../../../assets/exp/statistics/schoolComprehensiveStatistics/school-bg.png");
  background-size: cover;
  background-position: 50% 50%;
  -webkit-backface-visibility: hidden;
  font-weight: 400;

  .content {
    padding: 55px 70px;

    > .left {
      width: 341px;
      height: 151px;
      background-image: url("../../../assets/exp/statistics/schoolComprehensiveStatistics/school-left-bg.png");
      background-size: cover;
      background-position: 50% 50%;
      -webkit-backface-visibility: hidden;
      color: #fff;
      margin-top: 0px;

      .title {
        padding: 10px 10px;

        img {
          width: 30px;
          height: 25px;
          margin-right: 8px;
        }

        span {
          font-size: 20px;
          font-weight: 400;
        }
      }

      .num {
        font-size: 70px;
        text-align: center;

        span {
          font-size: 40px;
        }
      }
    }

    > .right {
      > .num {
        color: #fff;
        margin-bottom: 20px;

        > .left {
          width: 173px;
          height: 81px;
          background-image: url("../../../assets/exp/statistics/schoolComprehensiveStatistics/school-right-1.png");
          background-size: cover;
          background-position: 50% 50%;
          text-align: center;
          line-height: 81px;
          font-size: 24px;
          padding-top: 15px;

          span {
            font-size: 20px;
          }
        }

        > .right {
          width: 176px;
          height: 81px;
          background-image: url("../../../assets/exp/statistics/schoolComprehensiveStatistics/school-right-2.png");
          background-size: cover;
          background-position: 50% 50%;
          text-align: center;
          line-height: 81px;
          font-size: 24px;
          padding-top: 15px;

          span {
            font-size: 20px;
          }
        }
      }

      > .list {
        .li {
          margin-bottom: 15px;

          img {
            width: 32px;
            height: 32px;
            margin-right: 20px;
          }

          .text {
            background: #EBF5F9;
            box-shadow: -2px 2px 5px 0px rgba(0, 139, 191, 0.1);
            border-radius: 4px;
            padding: 8px;
            width: 310px;

            span {
              font-size: 18px;
              color: #333;
            }
          }
        }
      }
    }
  }
}

// 实验项目列表
.card-box.experiment {
  width: 413px;
  height: 432px;
  background: #FFFFFF;
  border-radius: 4px;

  .content {
    height: 369px;
    overflow-y: scroll;
    overflow: -moz-scrollbars-none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      width: 0 !important
    }
  ;

  }

  .list {
    padding-top: 5px;

    .li {
      width: 124px;
      margin-bottom: 11px;

      > img {
        width: 75px;
        height: 75px;
        margin-bottom: 3px;
      }

      > .name {
        font-size: 14px;
        color: #333;
        margin-bottom: 5px;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        display: -webkit-box;
      }

      > .series {
        font-size: 12px;
        color: #848484;
      }
    }
  }
}

// 单个实验
.experiment-list {
  margin-top: 50px;

  > .li {
    width: 700px;
    height: 380px;
    background-image: url("../../../assets/exp/statistics/schoolComprehensiveStatistics/experiment_li_bg.png");
    background-size: cover;
    background-position: 50% 50%;
    -webkit-backface-visibility: hidden;
    border-radius: 4px;
    margin-bottom: 50px;

    > .title {
      padding: 20px;
      padding-bottom: 0px;

      .left {
        img {
          width: 75px;
          height: 75px;
          margin-right: 10px;
        }

        .names {
          .name {
            font-size: 20px;
            color: #333;
            margin-bottom: 5px;
          }

          .series {
            font-size: 14px;
            color: #666;
          }
        }
      }
    }

    > .content {
      padding: 62px 30px;
      padding-left: 80px;

      > .left {
        width: 156px;
        height: 156px;
        background-image: url("../../../assets/exp/statistics/schoolComprehensiveStatistics/experiment_li_circle.png");
        background-size: cover;
        background-position: 50% 50%;
        -webkit-backface-visibility: hidden;
        text-align: center;

        .num {
          padding-top: 56px;
          font-size: 26px;
          font-weight: bold;
          color: #FEB900;
          line-height: 27px;

          span {
            font-size: 15px;
          }
        }

        .text {
          font-size: 13px;
          font-weight: 400;
          color: #333333;
          line-height: 27px;
        }
      }

      > .right {
        width: 380px;
        padding-top: 10px;

        .ls {
          margin-bottom: 20px;

          .name {
            margin-right: 20px;

            img {
              width: 32px;
              height: 32px;
              margin-right: 7px;
            }

            span {
              font-size: 20px;
              font-weight: 400;
              color: #333333;
              line-height: 30px;
            }
          }

          .nums {
            position: relative;
            margin-top: -30px;

            .block {
              position: absolute;
              left: 0;
              top: 0px;
              width: 182px;
              height: 31px;
              background: #004DCC;
            }

            .num {
              position: absolute;
              left: 0;
              top: 0;
              width: 182px;
              height: 31px;
              line-height: 31px;
              color: #fff;
              padding-left: 10px;
              font-size: 20px;

              span {
                font-size: 14px;
                margin-left: 3px;
              }
            }

            .complete-block {
              background-color: #29C5FF;;
            }

            .complete {
              color: #333;
            }

            .avg {
              font-weight: 400;
              color: #003C9E;
              line-height: 30px;
            }
          }
        }
      }
    }
  }
}
</style>


<script>
import {SchoolModel} from "@/model/exp/SchoolModel";
import {find_obj_from_arr_by_id} from "@/utils/common";
import {UserModel} from "@/model/exp/UserModel";
import {ExperimentModel} from "@/model/exp/ExperimentModel";
import {StudentExperimentModel} from "@/model/exp/StudentExperimentModel";
import {msg_success} from "@/utils/ele_component";

export default {
  name: "schoolComprehensiveStatistics",
  data() {
    return {
      // 学校列表信息
      schoolList: [],
      // 学校信息
      schoolId: "",
      schoolInfo: {},
      // 概览信息
      overviewInfo: {
        // 用户数量
        userNum: {
          totalNum: 0,
          teacherNum: 0,
          studentNum: 0
        },
        totalScheduleNum: 0,
        totalCompleteNum: 0,
        totalScoreNum: 0,
      },
      // 实验项目列表
      experimentList: [],
      // 单个实验统计列表
      experimentStatisticsList: []
    }
  },
  mounted() {
    this.getSchoolList()
  },
  methods: {
    // 获取学校列表
    async getSchoolList() {
      this.schoolList = await SchoolModel.getListNew(0, 0, {})
    },
    // 改变选择的学校
    changeSchool(schoolId) {

    },
    // 点击统计按钮
    async clickCalBtn() {
      // 设置学校信息
      [, this.schoolInfo] = find_obj_from_arr_by_id("id", this.schoolId, this.schoolList);
      // 清除统计信息
      this.overviewInfo = {
        userNum: {
          totalNum: 0,
          teacherNum: 0,
          studentNum: 0
        },
        totalScheduleNum: 0,
        totalCompleteNum: 0,
        totalScoreNum: 0,
      }
      this.experimentList = []
      this.experimentStatisticsList = []
      msg_success("统计已开始，请耐心等待统计完成！")
      // 获取学校人数统计
      UserModel.getOneSchoolUserNumber(this.schoolId).then(res => {
        this.overviewInfo.userNum = res
      })
      // 获取安排的实验列表
      this.experimentList = await ExperimentModel.getOneSchoolExperimentInfoList(this.schoolId)
      // 遍历循环获取每个实验的统计情况
      let statisticsLength = 0
      for (let i = 0; i < this.experimentList.length; i++) {
        let experimentId = this.experimentList[i]["id"]
        StudentExperimentModel.getOneSchoolOneExperimentOverview(this.schoolId, experimentId).then(res => {
          // 设置完成实验人次框长度
          res.completeWidth = (res.completeNum / res.scheduleNum * 182) + "px";
          if (res.completeNum === 0) {
            res.completeWidth = "0px"
          }
          this.$set(this.experimentList[i], "statistics", res)
          // 设置统计情况
          this.experimentStatisticsList.push(this.experimentList[i])


          // 计算所有实验总数
          this.overviewInfo.totalScheduleNum += res.scheduleNum;
          this.overviewInfo.totalCompleteNum += res.completeNum;
          this.overviewInfo.totalScoreNum += res.scoreNum;
          // 判断是否计算完成
          if (this.experimentStatisticsList.length === this.experimentList.length) {
            msg_success("统计已完成！")
          }
          statisticsLength++;
        })
      }
    },
    // 点击某个实验的导出按钮
    clickExportBtn(experiment) {
      msg_success("导出时间较长，请耐心等待，不要关闭窗口！")
      StudentExperimentModel.exportOneSchoolAllStudentScore(this.schoolId, experiment.id)
    }
  }
}
</script>

