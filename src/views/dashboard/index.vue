<template>
  <div class="dashboard-container">
    <component :is="currentRole"/>
  </div>
</template>

<script>
import {mapGetters} from 'vuex'
import administratorDashboard from "@/views/dashboard/administratorDashboard";
import sellerDashboard from "@/views/dashboard/sellerDashboard";
import gameDashboard from "@/views/dashboard/gameDashboard";
import downloaderDashboard from "@/views/dashboard/downloaderDashboard";
import expTeacherDashboard from "@/views/dashboard/expTeacherDashboard";
import picoDashboard from "@/views/dashboard/picoDashboard";
export default {
  name: 'Dashboard',
  components: {administratorDashboard,sellerDashboard,gameDashboard,downloaderDashboard,picoDashboard},
  data() {
    return {
      currentRole: 'adminDashboard'
    }
  },
  computed: {
    ...mapGetters([
      'roles'
    ])
  },
  created() {
    if (this.roles.includes('administrator')) {
      this.currentRole = 'administratorDashboard'
    }
    if (this.roles.includes('seller')) {
      this.currentRole = 'sellerDashboard'
    }
    if (this.roles.includes('game')) {
      this.currentRole = 'gameDashboard'
    }
    if (this.roles.includes('downloader')) {
      this.currentRole = 'downloaderDashboard'
      if (this.roles.includes('expTeacher')) {
        this.currentRole = 'expTeacherDashboard'
      }
    }
    if (this.roles.includes('pico')) {
      this.currentRole = 'picoDashboard'
    }
  }
}
</script>
