<template>
  <div class="app-container">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="新闻列表" name="list">
        <!--筛选-->
        <div class="header-container clearfix">
          <list-search-filter :search-filter="lists.searchFilter"
                              @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
          </list-search-filter>
          <!--  操作  -->
          <div class="right">
            <div style="text-align: right">
              <el-button class="el-button" type="success" v-if="useType!=='selectNewsList'"
                         @click="ListMethods().clickAddNewsBtn()">发布新闻
              </el-button>
            </div>
          </div>
        </div>

        <!--列表-->
        <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
                  highlight-current-row
                  @selection-change="v=>ListMethods().onSelect(v)"
                  style="width: 100%;">
          <el-table-column
            type="selection"
            width="55">
          </el-table-column>
          <el-table-column label="发布时间" align="center" width="150">
            <template slot-scope="scope">
              <span>{{ date_format(scope.row.createTime, "yyyy-MM-dd HH:mm") }}</span>
            </template>
          </el-table-column>
          <el-table-column label="标题" align="center" width="600">
            <template slot-scope="scope">
              <span>{{ scope.row.title }}</span>
            </template>
          </el-table-column>
          <el-table-column label="描述简介" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.description }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="290" v-if="useType!=='selectNewsList'"
                           class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" round
                         @click="ListMethods().clickEditBtn(scope.row,scope.$index)">详情
              </el-button>
              <el-button type="primary" size="mini" round :disabled="scope.$index===0"
                         @click="ListMethods().clickUpBtn(scope.row,scope.$index)">上移
              </el-button>
              <el-button type="primary" size="mini" round :disabled="scope.$index===lists.listLength-1"
                         @click="ListMethods().clickDownBtn(scope.row,scope.$index)">下移
              </el-button>
              <el-button type="danger" size="mini" round
                         @click="ListMethods().clickDelBtn(scope.row.officialWebNewsId,scope.$index)">
                删除
              </el-button>
              <el-button type="success" size="mini" round
                         @click="ListMethods().clickPostUrl(scope.row.officialWebNewsId,scope.$index)">
                推送百度收录
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--列表分页-->
        <!--        <div class="pagination-container">-->
        <!--          <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"-->
        <!--                         :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"-->
        <!--                         layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"-->
        <!--                         :page-sizes="[10,20,50,100,200,500]"-->
        <!--                         @size-change="(size)=>ListMethods().pageLimitChange(size)"-->
        <!--                         :page-count="lists.pages.totalPages">-->
        <!--          </el-pagination>-->
        <!--        </div>-->
      </el-tab-pane>
      <el-tab-pane label="新闻配置" name="setting" v-if="useType!=='selectNewsList'">
        <el-form ref="bannerManage" :model=" newsManage.bannerManage" :rules="bannerManageFormRules"
                 label-width="120px">
          <el-form-item label="说明：">
            <span style="color:#666">H5移动端不能用视频做背景，固定显示图片。</span>
          </el-form-item>
          <el-form-item prop="type" label="显示模式：">
            <el-select v-model="newsManage.bannerManage.type">
              <el-option value="img" label="图片模式"></el-option>
              <el-option value="video" label="视频模式"></el-option>
              <el-option value="world" label="地球模式"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="PC顶部图片：" prop="banner_pc_img">
            <div class="flex flex-start">
              <erp-uploader-one-pic :img-in=" newsManage.bannerManage.banner_pc_img"
                                    uploader-id="banner_pc_img"
                                    :uploader-size="[200,100]" :pixel-limit="[1920,600]"
                                    :size-limit="3072"
                                    @uploadSuccess="data=>fileUpload(data, newsManage.bannerManage)"
                                    @afterDelete="data=>fileDelete(data, newsManage.bannerManage)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label="PC顶部视频：" prop="banner_pc_video">
            <div class="flex flex-start">
              <erp-uploader-one-video style="margin-right: 30px" :video-in=" newsManage.bannerManage.banner_pc_video"
                                      uploader-id="banner_pc_video"
                                      :uploader-size="[200,100]" :pixel-limit="[1920,600]"
                                      :size-limit="20480"
                                      @afterDelete="data=>fileDelete(data, newsManage.bannerManage)"
                                      @uploadSuccess="data=>fileUpload(data, newsManage.bannerManage)"></erp-uploader-one-video>
            </div>
          </el-form-item>
          <el-form-item label="H5顶部图片：" prop="banner_h5_img">
            <div class="flex flex-start">
              <erp-uploader-one-pic :img-in=" newsManage.bannerManage.banner_h5_img"
                                    uploader-id="banner_h5_img"
                                    :uploader-size="[200,100]" :pixel-limit="[750,600]"
                                    :size-limit="3072"
                                    @uploadSuccess="data=>fileUpload(data, newsManage.bannerManage)"
                                    @afterDelete="data=>fileDelete(data, newsManage.bannerManage)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
        </el-form>
        <div class="buttons">
          <el-button @click="clickSaveNewsManage">保存</el-button>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {msg_confirm, msg_err, msg_success} from "@/utils/ele_component";
import {date_format, find_obj_from_arr_by_id} from "@/utils/common";
import {OfficialWebNewsModel as OfficialWebNewsModel} from "@/model/erp/OfficialWebNewsModel";
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import erpUploaderOneVideo from "@/views/components/erpUploaderOneVideo";
import {ConfigModel} from "@/model/erp/ConfigModel";
import {CONFIG_NAME_OFFICIAL_WEB, CONFIG_NAME_OFFICIAL_WEB_XHYJ} from "@/model/ConfigModel";

export default {
  name: "newsManage",
  components: {ListSearchFilter, erpUploaderOnePic, erpUploaderOneVideo},
  directives: {
    elDragDialog, permission,
  },
  props: {
    "useType": {
      type: String,
      default() {
        return "edit"
      }
    }
  },
  data() {
    return {
      // 外部方法
      find_obj_from_arr_by_id: find_obj_from_arr_by_id,
      date_format: date_format,
      activeTab: "list",
      // 列表
      lists: {
        list: [],
        listLength: 0,
        loading: false,
        query: {},
        pages: {
          size: 1000
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '标题',
              key: 'title',
              value: '',
              format: (v) => {
                return {'$regex': `.*${v}.*`}
              }
            },
          ],
          filter: [
            {
              type: 'timeRange',
              key: "createTime",
              label: ['开始时间', '结束时间', '发布时间'],
              value: '',
              data: [],
              change: function (value) {

              },
              format: function (value) {
                if (value.length === 2) {
                  const start = new Date(value[0]).getTime()
                  const end = new Date(value[1]).getTime()
                  const query = {
                    '$and': [
                      {"createTime": {'$gte': start}},
                      {"createTime": {'$lte': end}},
                    ]
                  }
                  return (query)
                }
              }
            },
          ]
        }
      },
      // 新闻配置
      newsManage: {
        bannerManage: {}
      },
      bannerManageFormRules: {
        'banner_pc_img': {
          required: true,
          message: "请上传PC图片",
          trigger: 'change'
        },
        'banner_pc_video': {
          required: true,
          message: "请上传PC视频",
          trigger: "change",
        },
        'banner_h5_img': {
          required: true,
          message: "请上传H5图片",
          trigger: 'change'
        },
      }
    }
  },
  async mounted() {
    // 获取列表
    this.ListMethods().getList(1, this.lists.pages.size, this.lists.query)
    // 初始化筛选
    this.ListMethods().initFilter()
    // 获取新闻配置
    let newsManage = JSON.parse(await ConfigModel.getEdit(CONFIG_NAME_OFFICIAL_WEB_XHYJ, "newsManage"));
    this.$set(this, "newsManage", newsManage);
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 选择某项项目
        onSelect(v) {
          $this.$emit("onSelectedNewsList", v)
        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          query = Object.assign({
            owner: "xhyj"
          }, query);
          [$this.lists.list, $this.lists.pages] = await OfficialWebNewsModel.getPageList(page - 1, size, "sortOrder,desc", query)
          $this.lists.listLength = $this.lists.list.length;
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.page, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter() {

        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击列表的编辑按钮
        clickEditBtn(edit, $index) {
          $this.$router.push({
            name: "newsManageEditXhyj",
            query: {
              type: "edit",
              id: edit.officialWebNewsId
            }
          })
        },
        // 点击发布按钮
        clickAddNewsBtn() {
          $this.$router.push({
            name: "newsManageEditXhyj",
            query: {
              type: "add"
            }
          })
        },
        // 点击删除按钮
        async clickDelBtn(id, $index) {
          if (await msg_confirm('确认删除该新闻吗？删除后不可恢复！')) {
            if (await OfficialWebNewsModel.deleteOne(id)) {
              this.getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
              msg_success('删除成功!')
            }
          }
        },
        // 点击推送收录按钮
        async clickPostUrl(id) {
          if (await msg_confirm("确认要推送该新闻链接到百度吗？")) {
            let result = await OfficialWebNewsModel.postUrlToBaidu({
              url: "https://www.cdzyhd.com/newsView?id=" + id,
              newsId: id
            })
            if (result.code === "000000") {
              this.getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
              msg_success("推送成功")
            }
          }
        },
        // 点击上移按钮
        async clickUpBtn(edit, $index) {
          let one = $this.lists.list[$index - 1];
          let oneSortOrder = one.sortOrder
          one.sortOrder = edit.sortOrder
          edit.sortOrder = oneSortOrder
          await OfficialWebNewsModel.addOrEdit(edit)
          await OfficialWebNewsModel.addOrEdit(one)
          this.getList($this.lists.pages.page, $this.lists.pages.size, $this.lists.query)
        },
        // 点击下移按钮
        async clickDownBtn(edit, $index) {
          let one = $this.lists.list[$index + 1];
          let oneSortOrder = one.sortOrder
          one.sortOrder = edit.sortOrder
          edit.sortOrder = oneSortOrder
          await OfficialWebNewsModel.addOrEdit(edit)
          await OfficialWebNewsModel.addOrEdit(one)
          this.getList($this.lists.pages.page, $this.lists.pages.size, $this.lists.query)
        }
      }
    },
    // 通用-文件上传成功
    fileUpload(params, target) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
    },
    // 通用-文件删除
    fileDelete(params, target) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
    },
    // 点击保存新闻配置按钮
    async clickSaveNewsManage() {
      this.$refs['bannerManage'].validate(async validate => {
        if (validate) {
          if (await msg_confirm("确定要修改新闻配置吗？")) {
            if (await ConfigModel.editEdit(CONFIG_NAME_OFFICIAL_WEB_XHYJ, "newsManage", this.newsManage)) {
              msg_success("保存新闻配置成功")
            }
          }
        }
      });

    }
  }
}
</script>

<style scoped lang="scss">
.buttons {
  text-align: center;
}
</style>
