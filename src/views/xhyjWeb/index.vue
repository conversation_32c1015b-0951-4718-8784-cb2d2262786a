<template>
  <div class="app-container">
    <!--公司信息-->
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>公司信息</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="infoManage.cardShow=!infoManage.cardShow">
          <i class="el-icon-arrow-up" v-show="infoManage.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!infoManage.cardShow"></i>
          {{ infoManage.cardShow ? '收起' : '展开' }}
        </el-button>
        <el-button v-if="false" style="float:right" size="small" @click="clickSaveBtn(['infoManage'])">保存</el-button>
      </div>
      <div class="card-container" v-show="infoManage.cardShow">
        <a href="#" name="infoManage"></a>
        <el-form ref="infoManage" :model="infoManage" :rules="infoManage.formRules" label-width="120px">
          <el-form-item label="色彩风格：">
            <el-switch
              v-model="infoManage.colorType"
              active-color="#315397"
              inactive-color="#bb0f0c"
              active-text="蓝色"
              inactive-text="红色"
              active-value="blue"
              inactive-value="red">
            </el-switch>
          </el-form-item>
          <el-form-item label="公司logo：" prop="logos">
            <div class="flex flex-start flex-wrap">
              <!--pc-首页-->
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="infoManage.logos.pc_logo_index"
                                    uploader-id="pc_logo_index"
                                    uploader-title="PC-首页-顶部导航条" :uploader-size="[200,100]" :pixel-limit="[555,50]"
                                    :size-limit="200"
                                    @uploadSuccess="data=>fileUpload(data,infoManage.logos,'infoManage')"
                                    @afterDelete="data=>fileDelete(data,infoManage.logos,'infoManage')"></erp-uploader-one-pic>
              <!--pc-首页-->
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="infoManage.logos.pc_logo_index_banner"
                                    uploader-id="pc_logo_index_banner"
                                    uploader-title="PC-首页-banner" :uploader-size="[200,100]" :pixel-limit="[675,65]"
                                    :size-limit="200"
                                    @afterDelete="data=>fileDelete(data,infoManage.logos,'infoManage')"
                                    @uploadSuccess="data=>fileUpload(data,infoManage.logos,'infoManage')"></erp-uploader-one-pic>
              <!--pc-产品页-->
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="infoManage.logos.pc_logo_product"
                                    uploader-id="pc_logo_product"
                                    uploader-title="PC-产品页-顶部导航条" :uploader-size="[200,100]" :pixel-limit="[555,50]"
                                    :size-limit="200"
                                    @afterDelete="data=>fileDelete(data,infoManage.logos,'infoManage')"
                                    @uploadSuccess="data=>fileUpload(data,infoManage.logos,'infoManage')"></erp-uploader-one-pic>
              <!--pc-页底-->
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="infoManage.logos.pc_logo_bottom"
                                    uploader-id="pc_logo_bottom"
                                    uploader-title="底部导航条" :uploader-size="[200,100]" :pixel-limit="[278,26]"
                                    :size-limit="100"
                                    @afterDelete="data=>fileDelete(data,infoManage.logos,'infoManage')"
                                    @uploadSuccess="data=>fileUpload(data,infoManage.logos,'infoManage')"></erp-uploader-one-pic>
              <!--pc-底部背景-->
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="infoManage.logos.pc_bottom_bg"
                                    uploader-id="pc_bottom_bg"
                                    uploader-title="底部导航条背景PC" :uploader-size="[200,100]" :pixel-limit="[1920,340]"
                                    :size-limit="500"
                                    @afterDelete="data=>fileDelete(data,infoManage.logos,'infoManage')"
                                    @uploadSuccess="data=>fileUpload(data,infoManage.logos,'infoManage')"></erp-uploader-one-pic>
              <!--h5-底部背景-->
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="infoManage.logos.h5_bottom_bg"
                                    uploader-id="h5_bottom_bg"
                                    uploader-title="底部导航条背景H5" :uploader-size="[200,100]" :pixel-limit="[1920,340]"
                                    :size-limit="500"
                                    @afterDelete="data=>fileDelete(data,infoManage.logos,'infoManage')"
                                    @uploadSuccess="data=>fileUpload(data,infoManage.logos,'infoManage')"></erp-uploader-one-pic>
              <!--H5-透明-->
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="infoManage.logos.h5_logo_transparent"
                                    uploader-id="h5_logo_transparent"
                                    uploader-title="H5-透明" :uploader-size="[200,100]" :pixel-limit="[738,84]"
                                    :size-limit="200"
                                    @afterDelete="data=>fileDelete(data,infoManage.logos,'infoManage')"
                                    @uploadSuccess="data=>fileUpload(data,infoManage.logos,'infoManage')"></erp-uploader-one-pic>
              <!--H5-蓝色-->
              <erp-uploader-one-pic style="margin-right: 30px;margin-top: 30px" :img-in="infoManage.logos.h5_logo_blue"
                                    uploader-id="h5_logo_blue"
                                    uploader-title="H5-蓝色" :uploader-size="[200,100]" :pixel-limit="[738,84]"
                                    :size-limit="200"
                                    @afterDelete="data=>fileDelete(data,infoManage.logos,'infoManage')"
                                    @uploadSuccess="data=>fileUpload(data,infoManage.logos,'infoManage')"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label="公司名称：" prop="companyName">
            <el-input v-model="infoManage.companyName" placeholder="请输入公司名称" style="width: 500px">
              <div slot="suffix" v-if="infoManage.companyName">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ infoManage.companyName.length }} / 50
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="公司简称：" prop="companyNameShort">
            <el-input v-model="infoManage.companyNameShort" placeholder="请输入公司简称" style="width: 500px">
              <div slot="suffix" v-if="infoManage.companyNameShort">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ infoManage.companyNameShort.length }} / 6
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <!--          <el-form-item label="slogan-PC：" prop="sLogoPc">-->
          <el-form-item label="slogan-PC：">
            <el-input v-model="infoManage.sLogoPc" placeholder="请输入pc端slogan" style="width: 500px">
              <div slot="suffix" v-if="infoManage.sLogoPc">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ infoManage.sLogoPc.length }} / 20
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="slogan-H5：" prop="sLogoH5">
            <div class="flex flex-start">
              <el-input v-model="infoManage.sLogoH51" placeholder="请输入h5端slogan-上句" style="width: 165px" maxlength="10"
                        show-word-limit>
              </el-input>
              <el-input v-model="infoManage.sLogoH52" placeholder="请输入h5端slogan-中句" style="width: 165px" maxlength="10"
                        show-word-limit>
              </el-input>
              <el-input v-model="infoManage.sLogoH53" placeholder="请输入h5端slogan-下句" style="width: 165px" maxlength="10"
                        show-word-limit>
              </el-input>
            </div>
          </el-form-item>
          <el-form-item label="联系方式：" prop="contacts" class="form-item-contacts">
            <div class="flex flex-start" v-for="(contactItem,contactIndex) in infoManage.contactList"
                 style="margin-bottom: 10px">
              <el-select v-model="contactItem.type" style="width: 120px">
                <el-option label="客服热线" value="phone"></el-option>
                <el-option label="QQ" value="qq"></el-option>
              </el-select>
              <el-input v-model="contactItem.value" style="width: 380px" maxlength="15" show-word-limit></el-input>
              <el-button type="text" size="small" style="margin-left: 10px"
                         @click="infoMethods().clickDeleteOneContactBtn(contactItem,contactIndex)">删除
              </el-button>
            </div>
            <div>
              <el-button type="text" @click="infoMethods().clickAddOneContactBtn()">增加一个</el-button>
            </div>
          </el-form-item>
          <el-form-item label="公司地址：" prop="companyAddress">
            <el-input type="textarea" :rows="3" v-model="infoManage.companyAddress" placeholder="请输入公司地址"
                      style="width: 500px" maxlength="100" show-word-limit>
            </el-input>
          </el-form-item>
          <el-form-item label="备案号：" prop="beiAnNumber">
            <el-input v-model="infoManage.beiAnNumber" placeholder="请输入备案号" style="width: 500px">
              <div slot="suffix" v-if="infoManage.beiAnNumber">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ infoManage.beiAnNumber.length }} / 20
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="实验平台地址：" prop="experimentUrl">
            <el-input v-model="infoManage.experimentUrl" placeholder="请输入实验平台地址链接" style="width: 500px">
              <div slot="suffix" v-if="infoManage.experimentUrl">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ infoManage.experimentUrl.length }} / 40
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <!--banner设置-->
    <a href="#" name="bannerManage"></a>
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>Banner设置</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="bannerManage.cardShow=!bannerManage.cardShow">
          <i class="el-icon-arrow-up" v-show="bannerManage.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!bannerManage.cardShow"></i>
          {{ bannerManage.cardShow ? '收起' : '展开' }}
        </el-button>
        <el-button v-if="false" style="float:right" size="small" @click="clickSaveBtn(['bannerManage'])">保存</el-button>
      </div>
      <div class="card-container" v-show="bannerManage.cardShow">
        <el-form ref="bannerManage" :model="bannerManage" :rules="bannerManage.formRules" label-width="120px">
          <el-form-item label="说明：">
            <span style="color:#666">H5移动端不能用视频做背景，固定显示图片。</span>
          </el-form-item>
          <el-form-item prop="type" label="显示模式：">
            <el-switch
              v-model="bannerManage.type"
              active-value="img"
              inactive-value="video"
              active-text="图片模式"
              inactive-text="视频模式">
            </el-switch>
          </el-form-item>
          <el-form-item label="图片：" prop="banner_pc_img">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="bannerManage.banner_pc_img"
                                    uploader-id="banner_pc_img"
                                    :uploader-size="[200,100]" :pixel-limit="[1920,1080]"
                                    :size-limit="3072"
                                    @uploadSuccess="data=>fileUpload(data,bannerManage)"
                                    @afterDelete="data=>fileDelete(data,bannerManage)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label="视频：" prop="banner_pc_video">
            <div class="flex flex-start">
              <erp-uploader-one-video style="margin-right: 30px" :video-in="bannerManage.banner_pc_video"
                                      uploader-id="banner_pc_video"
                                      :uploader-size="[200,100]" :pixel-limit="[2560,1080]"
                                      :size-limit="20480"
                                      @afterDelete="data=>fileDelete(data,bannerManage)"
                                      @uploadSuccess="data=>fileUpload(data,bannerManage)"></erp-uploader-one-video>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <!--产品设置-->
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>实验展示楼层</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="experimentManage.cardShow=!experimentManage.cardShow">
          <i class="el-icon-arrow-up" v-show="experimentManage.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!experimentManage.cardShow"></i>
          {{ experimentManage.cardShow ? '收起' : '展开' }}
        </el-button>
        <el-button v-if="false" style="float:right" size="small" @click="clickSaveBtn(['experimentManage'])">保存
        </el-button>
      </div>
      <div class="card-container" v-show="experimentManage.cardShow">
        <el-alert title="最多3个" style="margin-bottom: 10px"></el-alert>
        <el-form ref="experimentManage" :model="experimentManage" :rules="experimentManage.formRules">
          <el-form-item label-width="100px" label="在官网显示：">
            <el-switch @change="v=>onFloorChangeShow(v,experimentManage)"
                       v-model="experimentManage.show">
            </el-switch>
          </el-form-item>
          <el-form-item label-width="100px" label="楼层名称：" prop="name">
            <el-input v-model.trim="experimentManage.name" placeholder="请输入楼层名称" style="width: 500px">
              <div slot="suffix" v-if="experimentManage.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ experimentManage.name.length }} / 20
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label-width="100px" label="副标题：" prop="subName">
            <el-input v-model.trim="experimentManage.subName" placeholder="请输入楼层副标题" style="width: 500px">
              <div slot="suffix" v-if="experimentManage.subName">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ experimentManage.subName.length }} / 20
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label-width="100px" label="产品设置：" prop="lists">
            <!--设置列表-->
            <div class="lists">
              <!--无分类，无系列-->
              <div class="list">
                <el-card class="box-card">
                  <a href="#" name="experimentManageError"></a>
                  <div class="container">
                    <!--设置表格-->
                    <product-experiment-table ref="experimentManageList"
                                              @onClickExperimentMoveUpBtn="v=>experimentMethods().clickExperimentMoveUpBtn(null,null,v)"
                                              @onClickExperimentMoveDownBtn="v=>experimentMethods().clickExperimentMoveDownBtn(null,null,v)"
                                              @onClickExperimentDeleteBtn="v=>experimentMethods().clickExperimentDeleteBtn(null,null,v)"
                                              @onImgUploadSuccess="v=>experimentMethods().onImgUploadSuccess(null,null,v)"
                                              @onImgDelete="v=>experimentMethods().onImgDelete(null,null,v)"
                                              :list="experimentManage.list"></product-experiment-table>
                    <el-divider></el-divider>
                    <div class="flex flex-start" style="margin-bottom: 15px">
                      <el-button
                        @click="experimentMethods().clickExperimentAddBtn()">新增实验
                      </el-button>
                    </div>
                  </div>
                </el-card>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>

    </el-card>
    <!--新闻列表-->
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>新闻列表</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="newsManage.cardShow=!newsManage.cardShow">
          <i class="el-icon-arrow-up" v-show="newsManage.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!newsManage.cardShow"></i>
          {{ newsManage.cardShow ? '收起' : '展开' }}
        </el-button>
        <el-button v-if="false" style="float:right" size="small" @click="clickSaveBtn(['newsManage'])">保存
        </el-button>
      </div>
      <div class="card-container" v-show="newsManage.cardShow">
        <el-form ref="newsManage" :model="newsManage" :rules="newsManage.formRules">
          <el-form-item label-width="100px" label="在官网显示：">
            <el-switch @change="v=>onFloorChangeShow(v,newsManage)"
                       v-model="newsManage.show">
            </el-switch>
          </el-form-item>
          <el-form-item label-width="100px" label="新闻列表：" prop="lists">
            <!--设置列表-->
            <div class="lists">
              <!--无分类，无系列-->
              <div class="list">
                <el-card class="box-card">
                  <a href="#" name="newsManageError"></a>
                  <div class="container">
                    <!--设置表格-->
                    <index-news-table ref="newsManageList"
                                      @onClickMoveUpBtn="v=>newsManageMethods().clickMoveUpBtn(v)"
                                      @onClickMoveDownBtn="v=>newsManageMethods().clickMoveDownBtn(v)"
                                      @onClickDeleteBtn="v=>newsManageMethods().clickDeleteBtn(v)"
                                      @onImgUploadSuccess="v=>newsManageMethods().onImgUploadSuccess(v)"
                                      @onImgDelete="v=>newsManageMethods().onImgDelete(v)"
                                      :list="newsManage.list"></index-news-table>
                    <el-divider></el-divider>
                    <div class="flex flex-start" style="margin-bottom: 15px">
                      <el-button v-if="newsManage.list.length<10"
                        @click="newsManageMethods().clickAddBtn()">选择新闻
                      </el-button>
                    </div>
                  </div>
                </el-card>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <el-dialog
        title="选择要显示的新闻"
        :visible.sync="chooseNews.dialog"
        width="1200px"
        center
        v-el-drag-dialog>
        <div class="dialog-container">
          <news-list useType="selectNewsList"
                     @onSelectedNewsList="v=>newsManageMethods().selectedNewsList(v)"></news-list>
        </div>
        <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="chooseNews.dialog=false">取 消</el-button>
        <el-button type="success" @click="newsManageMethods().clickChooseNewsBtn()">选 择</el-button>
      </span>
      </el-dialog>
    </el-card>
    <!--关于-->
    <a href="#" name="aboutManage"></a>
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>关于薪火印记</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="aboutManage.cardShow=!aboutManage.cardShow">
          <i class="el-icon-arrow-up" v-show="aboutManage.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!aboutManage.cardShow"></i>
          {{ aboutManage.cardShow ? '收起' : '展开' }}
        </el-button>
        <el-button v-if="false" style="float:right" size="small" @click="clickSaveBtn(['aboutManage'])">保存</el-button>
      </div>
      <div class="card-container" v-show="aboutManage.cardShow">
        <el-form ref="aboutManage" :model="aboutManage" :rules="aboutManage.formRules">
          <el-form-item label-width="100px" label="在官网显示：">
            <el-switch @change="v=>onFloorChangeShow(v,aboutManage)"
                       v-model="aboutManage.show">
            </el-switch>
          </el-form-item>
          <el-form-item label-width="100px" label="图片列表：" prop="pics">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="aboutManage.about_pc_bg"
                                    uploader-id="about_pc_bg"
                                    uploader-title="PC-大背景图"
                                    :uploader-size="[200,100]" :pixel-limit="[1920,670]"
                                    :size-limit="1024"
                                    @afterDelete="data=>fileDelete(data,aboutManage)"
                                    @uploadSuccess="data=>fileUpload(data,aboutManage)"></erp-uploader-one-pic>
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="aboutManage.about_pc_text_bg"
                                    uploader-id="about_pc_text_bg"
                                    uploader-title="PC-文字背景图"
                                    :uploader-size="[200,100]" :pixel-limit="[840,640]"
                                    :size-limit="1500"
                                    @afterDelete="data=>fileDelete(data,aboutManage)"
                                    @uploadSuccess="data=>fileUpload(data,aboutManage)"></erp-uploader-one-pic>
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="aboutManage.about_h5_text_bg"
                                    uploader-id="about_h5_text_bg"
                                    uploader-title="H5-文字背景图"
                                    :uploader-size="[200,100]" :pixel-limit="[750,1320]"
                                    :size-limit="1024"
                                    @afterDelete="data=>fileDelete(data,aboutManage)"
                                    @uploadSuccess="data=>fileUpload(data,aboutManage)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label-width="100px" label="关于文本：" prop="aboutText">
            <el-input :rows="10" type="textarea" v-model="aboutManage.aboutText" placeholder="请输入关于我们内容"
                      style="width: 660px" maxlength="680" show-word-limit>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <!--渠道合作-->
    <a href="#" name="copManage"></a>
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>渠道合作</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="copManage.cardShow=!copManage.cardShow">
          <i class="el-icon-arrow-up" v-show="copManage.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!copManage.cardShow"></i>
          {{ copManage.cardShow ? '收起' : '展开' }}
        </el-button>
        <el-button v-if="false" style="float:right" size="small" @click="clickSaveBtn(['copManage'])">保存</el-button>
      </div>
      <div class="card-container" v-show="copManage.cardShow">
        <el-form ref="copManage" :model="copManage" :rules="copManage.formRules">
          <el-form-item label-width="100px" label="在官网显示：">
            <el-switch @change="v=>onFloorChangeShow(v,copManage)"
                       v-model="copManage.show">
            </el-switch>
          </el-form-item>
          <el-form-item label-width="100px" label="大图：" prop="cop_big_img">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="copManage.cop_big_img" uploader-id="cop_big_img"
                                    uploader-title="大图"
                                    :uploader-size="[200,100]" :pixel-limit="[850,400]"
                                    :size-limit="400"
                                    @afterDelete="data=>fileDelete(data,copManage)"
                                    @uploadSuccess="data=>fileUpload(data,copManage)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label-width="100px" label="合作文本：" prop="copText">
            <el-input :rows="10" type="textarea" v-model="copManage.copText" placeholder="请输入内容"
                      style="width: 660px" :show-word-limit="true" maxlength="1000">

            </el-input>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <!--成功案例-->
    <a href="#" name="successManage"></a>
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <div>
          <span>成功案例</span>
          <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                     @click="successManage.cardShow=!successManage.cardShow">
            <i class="el-icon-arrow-up" v-show="successManage.cardShow"></i>
            <i class="el-icon-arrow-down" v-show="!successManage.cardShow"></i>
            {{ successManage.cardShow ? '收起' : '展开' }}
          </el-button>
          <el-button v-if="false" style="float:right" size="small" @click="clickSaveBtn(['successManage'])">保存
          </el-button>
        </div>
        <div style="font-size: 13px;color: #999;margin-top: 10px">
          图片大小200KB以内，图片分辨率290px X 140px
        </div>
      </div>
      <div class="card-container success-container" v-show="successManage.cardShow">
        <el-form ref="successManage" :model="successManage" :rules="successManage.formRules">
          <el-form-item label-width="100px" label="在官网显示：">
            <el-switch @change="v=>onFloorChangeShow(v,successManage)"
                       v-model="successManage.show">
            </el-switch>
          </el-form-item>
          <el-form-item prop="pics">

            <div class="flex flex-start">
              <ul class="success-list clearfix">
                <li v-for="item in successManage.list" style="list-style-type: none;float:left">
                  <erp-uploader-one-pic :key="item.id" v-if="item.img"
                                        style="margin-right: 30px;margin-bottom: 20px"
                                        :img-in="item.img"
                                        :uploader-id="'success_'+item.id"
                                        @afterDelete="data=>successMethods().afterDelete(data)"
                                        :show-des="false"
                                        :uploader-size="[188,100]" :pixel-limit="[300,160]"
                                        :size-limit="200"
                                        @uploadSuccess="data=>successMethods().fileUploadSuccess(data)"></erp-uploader-one-pic>
                </li>
                <erp-uploader-one-pic :key="successManage.list[successManage.list.length-1].id"
                                      style="margin-right: 30px;margin-bottom: 20px;float:left"
                                      :img-in="successManage.list[successManage.list.length-1].img"
                                      :uploader-id="'success_'+successManage.list[successManage.list.length-1].id"
                                      @afterDelete="data=>successMethods().afterDelete(data)"
                                      :show-des="false"
                                      :uploader-size="[188,100]" :pixel-limit="[300,160]"
                                      :size-limit="200"
                                      @uploadSuccess="data=>successMethods().fileUploadSuccess(data)"></erp-uploader-one-pic>
              </ul>

            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <!--自定义楼层-->
    <a href="#" name="floorManage"></a>
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>自定义楼层</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="floorManage.cardShow=!floorManage.cardShow">
          <i class="el-icon-arrow-up" v-show="floorManage.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!floorManage.cardShow"></i>
          {{ floorManage.cardShow ? '收起' : '展开' }}
        </el-button>
        <el-button v-if="false" style="float:right" size="small" @click="clickSaveBtn(['floorManage'])">保存</el-button>
      </div>
      <div class="card-container" v-show="floorManage.cardShow">
        <!--楼层列表-->
        <div class="floor-list">
          <el-form label-width="100px" class="floor-li" v-for="(item,index) in floorManage.list" ref="floors"
                   :model="item"
                   :rules="floorManage.formRules">
            <div class="floor-title flex flex-between">
              <span class="floor-name">{{ index + 1 }}</span>
              <div class="flex flex-start">
                <el-button type="text" @click="floorManageMethods().clickDeleteBtn(index)">删除</el-button>
                <el-button type="text" @click="floorManageMethods().clickMoveUpBtn(index)" v-if="index>0">上移</el-button>
                <el-button type="text" @click="floorManageMethods().clickMoveDownBtn(index)"
                           v-if="index<floorManage.list.length-1">下移
                </el-button>
              </div>
            </div>
            <el-form-item label="在官网显示：" label-width="100px">
              <el-switch @change="v=>floorManageMethods().onEnable(v,index)"
                         v-model="item.show">
              </el-switch>
            </el-form-item>
            <el-form-item label-width="100px" label="图片背景：" prop="floor_img_big">
              <div class="flex flex-start">
                <erp-uploader-one-pic style="margin-right: 30px" :img-in="item.floor_img_big"
                                      :uploader-id="'floor_img_big__'+index"
                                      uploader-title="大图"
                                      :uploader-size="[200,100]" :pixel-limit="[850,400]"
                                      :size-limit="1024"
                                      @afterDelete="data=>floorManageMethods().afterDelete(data,index)"
                                      @uploadSuccess="data=>floorManageMethods().fileUploadSuccess(data,index)"></erp-uploader-one-pic>
              </div>
            </el-form-item>
            <el-form-item label-width="100px" label="标题：" prop="title">
              <el-input v-model.trim="floorManage.list[index].title" placeholder="请输入标题" style="width: 500px">
                <div slot="suffix" v-if="floorManage.list[index].title">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ floorManage.list[index].title.length }} / 20
                  </span>
                </span>
                </div>
              </el-input>
            </el-form-item>
            <el-form-item label-width="100px" label="内容：" prop="content">
              <el-input :rows="5" type="textarea" v-model="floorManage.list[index].content" placeholder="请输入内容"
                        style="width: 660px" :show-word-limit="true" maxlength="350">
              </el-input>
            </el-form-item>
          </el-form>
          <div class="flex flex-center">
            <el-button type="default" @click="floorManageMethods().clickNewFloorBtn()">新建楼层
            </el-button>
          </div>
        </div>
      </div>
    </el-card>
    <!--按钮组-->
    <div class="bottom-button-container flex flex-center">
      <el-button type="primary" @click="clickSaveBtn()">保 存</el-button>
      <el-button @click="clickCancelBtn">取 消</el-button>
    </div>
  </div>
</template>

<script>
/**
 * 首页管理
 */
import {BaseUploadModel} from "@/model/BaseUploadModel";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import erpUploaderOneVideo from "@/views/components/erpUploaderOneVideo";
import {
  find_obj_from_arr_by_id,
  findObjectArrSameItemForArr,
  findObjectArrSomeObjFirstOne,
  randomNumber
} from "@/utils/common";
import Sortable from "sortablejs";
import {validateMaxLength} from "@/utils/validate";
import {msg_confirm, msg_confirm_choose, msg_err, msg_success} from "@/utils/ele_component";
import {ConfigModel} from "@/model/erp/ConfigModel";
import productExperimentTable from "@/views/officialWeb/components/productExperimentTable";
import {CONFIG_NAME_OFFICIAL_WEB_XHYJ} from "@/model/ConfigModel";
import {VEAModel} from "@/model/VEAModel";
import $ from "jquery"
import indexNewsTable from "@/views/officialWeb/components/indexNewsTable";
import newsList from "@/views/xhyjWeb/news";
import elDragDialog from "@/directive/el-drag-dialog";

export default {
  name: "indexManage",
  directives: {
    elDragDialog
  },
  components: {erpUploaderOnePic, erpUploaderOneVideo, productExperimentTable, indexNewsTable, newsList},
  data() {
    // 验证-公司信息-logos
    const validate_infoManage_logos = (rule, value, callback) => {
      if (!this.infoManage.logos.pc_logo_index) {
        callback(new Error("PC-首页-顶部导航条,未设置"))
      }
      if (!this.infoManage.logos.pc_logo_index_banner) {
        callback(new Error("PC-首页-banner,未设置"))
      }
      if (!this.infoManage.logos.pc_logo_product) {
        callback(new Error("PC-产品页-顶部导航条,未设置"))
      }
      if (!this.infoManage.logos.pc_logo_bottom) {
        callback(new Error("底部导航条,未设置"))
      }
      if (!this.infoManage.logos.h5_logo_transparent) {
        callback(new Error("H5-透明,未设置"))
      }
      if (!this.infoManage.logos.h5_logo_blue) {
        callback(new Error("H5-蓝色,未设置"))
      }
      callback()
    }
    // 验证-公司信息-联系方式
    const validate_infoManage_contacts = (rule, value, callback) => {
      for (let i = 0; i < this.infoManage.contactList.length; i++) {
        if (this.infoManage.contactList[i].value === "") {
          setTimeout(() => {
            $(".form-item-contacts").removeClass('is-error')
          }, 200)
          callback(new Error(`第${i + 1}个联系方式，内容未设置`))
        }
      }
      // 判断客服热线联系方式大于1个才能删除
      let findArr = findObjectArrSameItemForArr(this.infoManage.contactList, "type", 'phone')
      if (findArr.length < 1) {
        callback(new Error(`至少要有一个客服热线类型的联系方式`))
      }
      callback()
    }
    // 验证-公司信息-sLogo-H5
    const validate_infoManage_sLogoH5 = (rule, value, callback) => {
      if (this.infoManage.sLogoH51 === "") {
        callback(new Error(`slogan，上句未设置`))
      }
      if (this.infoManage.sLogoH52 === "") {
        callback(new Error(`slogan，中句未设置`))
      }
      if (this.infoManage.sLogoH53 === "") {
        callback(new Error(`slogan，下句未设置`))
      }
      callback()
    }
    const validate_bannerManage = (rule, value, callback, type) => {
      if (type === "img") {
        if (!this.bannerManage.banner_pc_img) {
          callback("请上传图片")
        }
      }
      if (type === "video") {
        if (!this.bannerManage.banner_pc_video) {
          callback("请上传视频")
        }
      }
      callback()
    }
    const validate_copManage = (rule, value, callback, type) => {
      if (type === "img") {
        if (!this.copManage.cop_big_img) {
          callback("请上传大图")
        }
      }
      callback()
    }
    // 验证-关于-图片列表方式
    const validate_aboutManage_pics = (rule, value, callback) => {
      if (!this.aboutManage.about_pc_bg) {
        callback(new Error("PC-大背景图，未设置"))
      }
      if (!this.aboutManage.about_pc_text_bg) {
        callback(new Error("PC-文字背景图，未设置"))
      }
      if (!this.aboutManage.about_h5_text_bg) {
        callback(new Error("H5-文字背景图，未设置"))
      }
      callback()
    }
    // 验证-成功案例-图片
    const validate_successManage_pics = (rule, value, callback) => {
      if (this.successManage.list.length < 2) {
        callback(new Error("至少上传一个成功案例"))
      }
    }
    // 验证-产品设置-列表
    const validate_experimentManage_lists = (rule, value, callback) => {
      // 父组件直接验证子组件表格数据
      // this.experimentManage.list.forEach((experiment, experimentIndex) => {
      //   this.experimentMethods().validateExperiment(experiment, experimentIndex, callback, null, null)
      // })

      // 直接调用子组件方法
      if (!this.$refs["experimentManageList"].validateForm()) {
        callback(new Error("请将缺失项或错误项修改正确"))
      }
      callback();
    }
    // 验证-新闻列表-列表
    const validate_newsManage_lists = (rule, value, callback) => {
      // 直接调用子组件方法
      if (!this.$refs["newsManageList"].validateForm()) {
        callback(new Error("请将缺失项或错误项修改正确"))
      }
      callback();
    }
    const validate_floorManage = (rule, value, callback) => {
      if (!value) {
        callback("请上传背景图片")
      }
      callback()
    }
    return {
      uploadRequest: BaseUploadModel.uploadRequest,
      indexManage: {},
      // 信息管理
      infoManage: {
        cardShow: true,
        companyName: "",// 公司名称
        companyNameShort: "",
        companyAddress: "",
        beiAnNumber: "",// 备案号
        contactList: [],
        contactObj: {
          "phone": "",
          "qq": ""
        },
        logos: {},
        sLogoPc: "",
        sLogoH51: "",
        sLogoH52: "",
        sLogoH53: "",
        experimentUrl: "",
        // 表单检测
        formRules: {
          'logos': {
            required: true,
            validator: validate_infoManage_logos,
            trigger: 'change'
          },
          'contacts': {
            required: true,
            validator: validate_infoManage_contacts,
          },
          'companyName': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 50, "公司名称"),
            trigger: 'blur'
          },
          'sLogoPc': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 20, "slogan-PC端"),
            trigger: 'blur'
          },
          'experimentUrl': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 40, "实验管理平台地址"),
            trigger: 'blur'
          },
          'sLogoH5': {
            required: true,
            validator: validate_infoManage_sLogoH5,
            trigger: 'blur'
          },
          'companyNameShort': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 6, "公司简称"),
            trigger: 'blur'
          },
          'companyAddress': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 100, "公司地址"),
            trigger: 'blur'
          },
          'beiAnNumber': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 20, "备案号"),
            trigger: 'blur'
          },
        },
      },
      // banner管理
      bannerManage: {
        cardShow: true,
        banner_pc_img: "",
        banner_pc_video: "",
        formRules: {
          'banner_pc_img': {
            required: true,
            validator: (r, v, c) => validate_bannerManage(r, v, c, 'img'),
            trigger: 'change'
          },
          'banner_pc_video': {
            required: true,
            validator: (r, v, c) => validate_bannerManage(r, v, c, 'video'),
            trigger: "change",
          },
        }
      },
      // 实验管理
      experimentManage: {
        cardShow: true,
        show: true,
        name: "",
        subName: "",
        list: [],
        formRules: {
          'name': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 20, "楼层名称"),
            trigger: 'blur'
          },
          'subName': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 20, "楼层副标题"),
            trigger: 'blur'
          },
          'lists': {
            required: true,
            validator: validate_experimentManage_lists,
            trigger: 'blur'
          }
        }
      },
      // 新闻列表管理
      newsManage: {
        cardShow: true,
        show: true,
        list: [],
        formRules: {
          'lists': {
            required: true,
            validator: validate_newsManage_lists,
            trigger: 'blur'
          }
        }
      },
      chooseNews: {
        dialog: false,
      },
      // 关于管理
      aboutManage: {
        show: true,
        cardShow: true,
        aboutText: "",
        about_pc_bg: "",
        about_pc_text_bg: "",
        about_h5_text_bg: "",
        formRules: {
          'aboutText': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 680, "关于文本"),
            trigger: "blur",
          },
          'pics': {
            required: true,
            validator: validate_aboutManage_pics,
            trigger: "change",
          },
        }
      },
      // 渠道合作
      copManage: {
        show: true,
        cardShow: true,
        copText: "",
        cop_big_img: "",
        formRules: {
          'copText': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 1000, "合作文本"),
            trigger: "blur",
          },
          'cop_big_img': {
            required: true,
            validator: (r, v, c) => validate_copManage(r, v, c, 'img'),
            trigger: "change",
          },
        }
      },
      // 成功案例
      successManage: {
        show: true,
        cardShow: true,
        list: [
          {
            id: new Date().getTime(),
          }
        ],
        formRules: {
          'pics': {
            required: true,
            validator: validate_successManage_pics,
            trigger: "change",
          },
        }
      },
      // 自定义楼层
      floorManage: {
        cardShow: true,
        list: [],
        formRules: {
          // 'floor_img_big': {
          //   required: true,
          //   validator: (r, v, c) => validate_floorManage(r, v, c),
          //   trigger: "change",
          // },
          'floor_img_big': {
            required: true,
            message: "请上传图片背景",
            trigger: "change",
          },
          'title': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 20, "标题"),
            trigger: "blur",
          },
          'content': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 350, "内容"),
            trigger: "blur",
          },
        }
      },
      // 错误跳转标记,用于跳转到最开始错误
      errAJumped: false,
    }
  },
  mounted() {
    // 读取配置信息
    this.getConfig()
    // 底部按钮组大小监听
    VEAModel.bottomButtonContainerWidthChange()
  },
  methods: {
    // 通用-楼层在前台显示和隐藏
    async onFloorChangeShow(v, target) {
      if (v) {
        if (!await msg_confirm("是否确认开启该楼层，开启该楼层后，该楼层将在官网上可见")) {
          this.$set(target, "show", !v)
          this.$forceUpdate()
        }
      } else {
        if (!await msg_confirm("是否确认隐藏该楼层，隐藏该楼层后，该楼层将在官网上不可见")) {
          this.$set(target, "show", !v)
          this.$forceUpdate()
        }
      }
    },
    // 读取配置
    async getConfig() {
      let data = await ConfigModel.getEdit(CONFIG_NAME_OFFICIAL_WEB_XHYJ, "indexManage")
      let indexManage = JSON.parse(data)
      this.indexManage = JSON.parse(data)
      // 设置各项配置
      this.infoManage = Object.assign(this.infoManage, indexManage.infoManage)
      this.bannerManage = Object.assign(this.bannerManage, indexManage.bannerManage)
      this.experimentManage = Object.assign(this.experimentManage, indexManage.experimentManage)
      this.aboutManage = Object.assign(this.aboutManage, indexManage.aboutManage)
      this.copManage = Object.assign(this.copManage, indexManage.copManage)
      this.successManage = Object.assign(this.successManage, indexManage.successManage)
      this.floorManage = Object.assign(this.floorManage, indexManage.floorManage)
      this.newsManage=Object.assign(this.newsManage,indexManage.newsManage)
      setTimeout(() => {
        Sortable.create(document.querySelector('.success-list'))
        // this.successMethods().setFloorSort()
      }, 500)
    },
    // 通用-文件上传成功
    fileUpload(params, target, formRef) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 通用-文件删除
    fileDelete(params, target, formRef) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 点击保存按钮
    async clickSaveBtn(floors) {
      if (!floors) {
        floors = ['infoManage', 'bannerManage', 'experimentManage', 'aboutManage', 'copManage', 'successManage', 'floorManage','newsManage']
      }
      // 每个项目的合法性检测
      this.errAJumped = false
      if (true) {
        // 公司信息检测
        if (floors.indexOf('infoManage') > -1) {
          this.$refs["infoManage"].validate(validate => {
            if (validate) {

            } else {
              if (!this.errAJumped) {
                this.infoManage.cardShow === false ? this.infoManage.cardShow = true : ""
                // location.hash = "infoManage";
                this.errAJumped = true
              }
            }
          })
        }
        if (floors.indexOf('bannerManage') > -1) {
          // banner设置检测
          this.$refs["bannerManage"].validate(validate => {
            if (validate) {

            } else {
              if (!this.errAJumped) {
                this.bannerManage.cardShow === false ? this.bannerManage.cardShow = true : ""
                // location.hash = "bannerManage";
                this.errAJumped = true
              }
            }
          })
        }
        if (floors.indexOf('experimentManage') > -1) {
          // 产品设置检测
          this.$refs["experimentManage"].validate(validate => {
            if (validate) {

            } else {
              if (!this.errAJumped) {
                this.experimentManage.cardShow === false ? this.experimentManage.cardShow = true : ""
                // location.hash = "experimentManageError";
                this.errAJumped = true
              }
            }
          })
        }
        if (floors.indexOf('aboutManage') > -1) {
          // 关于设置检测
          this.$refs["aboutManage"].validate(validate => {
            if (validate) {

            } else {
              if (!this.errAJumped) {
                this.aboutManage.cardShow === false ? this.aboutManage.cardShow = true : ""
                // location.hash = "aboutManage";
                this.errAJumped = true
              }
            }
          })
        }
        if (floors.indexOf('copManage') > -1) {
          // 渠道合作设置检测
          this.$refs["copManage"].validate(validate => {
            if (validate) {

            } else {
              if (!this.errAJumped) {
                this.copManage.cardShow === false ? this.copManage.cardShow = true : ""
                // location.hash = "copManage";
                this.errAJumped = true
              }
            }
          })
        }
        if (floors.indexOf('successManage') > -1) {
          // 成功案例设置检测
          this.$refs["successManage"].validate(validate => {
            if (validate) {

            } else {
              if (!this.errAJumped) {
                this.successManage.cardShow === false ? this.successManage.cardShow = true : ""
                // location.hash = "successManage";
                this.errAJumped = true
              }
            }
          })
        }
        if (floors.indexOf('floorManage') > -1) {
          // 楼层设置检测
          let floorManageListTemp = JSON.parse(JSON.stringify(this.floorManage.list))
          let floorDeleteCount = 0
          floorManageListTemp.forEach((li, index) => {
            // 如果一个楼层所有信息都未填就自动删除
            if (li.backgroundUrl === "" && li.title === "" && li.content === "") {
              this.floorManage.list.splice(index - floorDeleteCount, 1)
              floorDeleteCount += 1
            }
            this.$refs['floors'][index].validate(validate => {
              if (validate) {

              } else {
                if (!this.errAJumped) {
                  this.floorManage.cardShow === false ? this.floorManage.cardShow = true : ""
                  // location.hash = "floorManage";
                  this.errAJumped = true
                }
              }
            })
          })
        }
        if (floors.indexOf('newsManage') > -1) {
          // 产品设置检测
          this.$refs["newsManage"].validate(validate => {
            if (validate) {

            } else {
              if (!this.errAJumped) {
                this.newsManage.cardShow === false ? this.newsManage.cardShow = true : ""
                // location.hash = "newsManageError";
                this.errAJumped = true
              }
            }
          })
        }
        if (this.errAJumped) { // 表单验证有误
          setTimeout(() => {
            // 跳转到第一个错误处
            let firstErrEl = $("body .el-form-item__error")
            let top = firstErrEl.offset().top - 200
            // 跳转到指定位置
            document.body.scrollTop = top;
            document.documentElement.scrollTop = top;
          }, 600)
          return
        }
      }
      //  todo 判断是否已经更改
      let saveButtonChoose = await msg_confirm_choose("内容有更改，是否提交？", "确认保存", "我再想想", '确认提交')
      if (saveButtonChoose === "right") {
        let indexManage = this.indexManage
        // 保存前调用
        if (floors.indexOf('infoManage') > -1) {
          // 信息设置-联系方式-设置每种第一个联系方式
          let contactTypeList = ['phone', 'qq']
          contactTypeList.forEach(li => {
            let contact = findObjectArrSomeObjFirstOne(this.infoManage.contactList, "type", li)
            if (contact) {
              this.infoManage.contactObj[li] = contact.value
            }
          })
          indexManage.infoManage = JSON.parse(JSON.stringify(this.infoManage))
          // 删除不必要元素
          delete indexManage.infoManage.formRules
          delete indexManage.infoManage.cardShow
        }
        if (floors.indexOf('bannerManage') > -1) {
          indexManage.bannerManage = JSON.parse(JSON.stringify(this.bannerManage))
          // 删除不必要元素
          delete indexManage.bannerManage.formRules
          delete indexManage.bannerManage.cardShow
        }
        if (floors.indexOf('experimentManage') > -1) {
          indexManage.experimentManage = JSON.parse(JSON.stringify(this.experimentManage))
          // 删除不必要元素
          delete indexManage.experimentManage.formRules
          delete indexManage.experimentManage.cardShow
        }
        if (floors.indexOf('aboutManage') > -1) {
          indexManage.aboutManage = JSON.parse(JSON.stringify(this.aboutManage))
          // 删除不必要元素
          delete indexManage.aboutManage.formRules
          delete indexManage.aboutManage.cardShow
        }
        if (floors.indexOf('copManage') > -1) {
          indexManage.copManage = JSON.parse(JSON.stringify(this.copManage))
          // 删除不必要元素
          delete indexManage.copManage.formRules
          delete indexManage.copManage.cardShow
        }
        if (floors.indexOf('successManage') > -1) {
          indexManage.successManage = JSON.parse(JSON.stringify(this.successManage))
          indexManage.successManage.list = this.successMethods().beforeSave()
          // 删除不必要元素
          delete indexManage.successManage.formRules
          delete indexManage.successManage.cardShow
        }
        if (floors.indexOf('floorManage') > -1) {
          indexManage.floorManage = JSON.parse(JSON.stringify(this.floorManage))
          // 删除不必要元素
          delete indexManage.floorManage.formRules
          delete indexManage.floorManage.cardShow
        }
        if (floors.indexOf('newsManage') > -1) {
          indexManage.newsManage = JSON.parse(JSON.stringify(this.newsManage))
          // 删除不必要元素
          delete indexManage.newsManage.formRules
          delete indexManage.newsManage.cardShow
        }
        // 保存接口
        if (await ConfigModel.editEdit(CONFIG_NAME_OFFICIAL_WEB_XHYJ, "indexManage", indexManage)) {
          msg_success("保存成功")
          setTimeout(() => {
            window.location.href = '#/officialWeb_xhyj/index';
            window.location.reload()
          }, 1000)
        }
      } else {// 点击了我再想想或关闭按钮

      }
    },
    // 点击取消按钮
    async clickCancelBtn() {
      if (await msg_confirm_choose("是否取消更改？", "取消更改", "我再想想", '确认取消') === "right") {
        msg_success("取消成功")
        setTimeout(() => {
          window.location.href = '#/officialWeb/index';
          window.location.reload()
        }, 1000)
      }
    },
    // 公司信息相关
    infoMethods() {
      let $this = this;
      return {
        // 点击新增一个联系方式按钮
        clickAddOneContactBtn() {
          $this.infoManage.contactList.push({
            type: "phone",
            value: ""
          })
          $this.$refs["infoManage"].validate()
        },
        // 点击删除一个联系方式按钮
        clickDeleteOneContactBtn(item, index) {
          $this.infoManage.contactList.splice(index, 1)
          $this.$refs["infoManage"].validate()
        }
      }
    },
    // banner管理相关
    bannerMethods() {
      let $this = this;
      return {}
    },
    // 实验相关方法
    experimentMethods() {
      let $this = this;
      return {
        // 检测-实验填写合法性检测
        validateExperiment(experiment, experimentIndex, callback, categoryIndex, seriesIndex) {
          let rules = [
            {key: 'name', name: "产品名称"},
            {key: 'subName', name: "副标题"},
            {key: 'description', name: "产品简介"},
            {key: 'experimentDescription', name: "实验简介"},
            {key: 'product_bg', name: "产品封面"},
            {key: 'product_icon', name: "产品Icon"},
            // {key: 'product_iconLeft', name: "左上角Icon"},
            {key: 'product_info_bg', name: "产品详情-头图"},
            // {key: 'product_video', name: "产品详情-视频"},
            {key: 'product_info_img_1', name: "产品详情-图片介绍第1张"},
            {key: 'product_info_img_2', name: "产品详情-图片介绍第2张"},
            {key: 'product_info_img_3', name: "产品详情-图片介绍第3张"},
            {key: 'product_info_img_4', name: "产品详情-图片介绍第4张"},
          ]
          rules.forEach((rule, index) => {
            if (!experiment[rule.key]) {
              callback(new Error(`第${experimentIndex + 1}行未设置${rule.name}`))
            }
          })
        },
        // 工厂-新建实验
        factoryCreateNewExperiment() {
          return {
            id: new Date().getTime() // 随机id
          }
        },
        // 点击-实验-新建按钮
        clickExperimentAddBtn(seriesIndex, categoryIndex) {
          if ($this.experimentManage.list.length < 3) {// 最多3个
            $this.experimentManage.list.push(this.factoryCreateNewExperiment())
          }
        },
        // 点击-实验-删除按钮
        clickExperimentDeleteBtn(categoryIndex, seriesIndex, experimentParams) {
          let experimentIndex = experimentParams.index
          $this.experimentManage.list.splice(experimentIndex, 1)
        },
        // 点击-实验-上移按钮
        clickExperimentMoveUpBtn(categoryIndex, seriesIndex, experimentParams) {
          let experimentIndex = experimentParams.index
          let arr = []
          arr = $this.experimentManage.list
          arr[experimentIndex] = arr.splice(experimentIndex - 1, 1, arr[experimentIndex])[0]
          $this.$set($this.experimentManage, "list", arr)
        },
        // 点击-实验-下移按钮
        clickExperimentMoveDownBtn(categoryIndex, seriesIndex, experimentParams) {
          let experimentIndex = experimentParams.index
          let arr = []
          arr = $this.experimentManage.list
          arr[experimentIndex] = arr.splice(experimentIndex + 1, 1, arr[experimentIndex])[0]
          $this.experimentManage.list = arr
          //$this.$set($this.experimentManage, "list", arr)

        },
        // 监听-图片上传成功
        onImgUploadSuccess(categoryIndex, seriesIndex, experimentParams) {
          let experimentIndex = experimentParams.index // 实验编号
          let imgSrc = experimentParams.params[1] // 成功后图片地址
          // 判断上传的什么图片
          let uploaderId = experimentParams.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          $this.$set($this.experimentManage.list[experimentIndex], uploaderKey, imgSrc)
        },
        // 监听-图片删除
        onImgDelete(categoryIndex, seriesIndex, experimentParams) {
          let experimentIndex = experimentParams.index // 实验编号
          // 判断上传的什么图片
          let uploaderId = experimentParams.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          $this.$set($this.experimentManage.list[experimentIndex], uploaderKey, "")
        }
      }
    },
    // 成功案例相关
    successMethods() {
      let $this = this;
      return {
        // 设置拖动排序
        setFloorSort() {
          const el = document.querySelectorAll(
            ".success-list"
          )[0];
          Sortable.create(el, {
            ghostClass: "sortable-ghost", // Class name for the drop placeholder,
            setData: function (dataTransfer) {
              dataTransfer.setData("Text", "");
              // to avoid Firefox bug
              // Detail see : https://github.com/RubaXa/Sortable/issues/1012
            },
            onEnd: (evt) => {
              const targetRow = $this.successManage.list.splice(evt.oldIndex, 1)[0];// 拖动的对象
              $this.successManage.list.splice(evt.newIndex, 0, targetRow);// 插入
              $this.$set(this.successManage, "list", $this.successManage.list)
            },
          });
        },
        fileUploadSuccess(data) {
          let id = Number(data[0].replace("success_", ""))
          let index = find_obj_from_arr_by_id("id", id, $this.successManage.list)[0]
          if (!$this.successManage.list[index].hasOwnProperty('img')) {
            $this.successManage.list.push({
              id: new Date().getTime(),
            })
          }
          $this.$set($this.successManage.list[index], "img", data[1])
        },
        afterDelete(data) {
          let id = Number(data[0].replace("success_", ""))
          let index = find_obj_from_arr_by_id("id", id, $this.successManage.list)[0]
          $this.successManage.list.splice(index, 1)
        },
        // 保存时调用
        beforeSave() {
          let list = []
          $("ul.success-list img.img-show").each(function () {
            let url = $(this).attr("src")
            if (url) {
              list.push({
                id: new Date().getTime() + randomNumber(1, 1000),
                img: url
              })
            }
          })
          list.push({
            id: new Date().getTime() + randomNumber(1, 1000),
          })
          return list;
        }
      }
    },
    // 楼层管理相关
    floorManageMethods() {
      let $this = this;
      return {
        // 监听-启用，关闭
        async onEnable(v, index) {
          if (v) {
            if (!await msg_confirm("是否确认开启该楼层，开启该楼层后，该楼层将在官网上可见")) {
              $this.$set($this.floorManage.list[index], "show", !v)
            }
          } else {
            if (!await msg_confirm("是否确认隐藏该楼层，隐藏该楼层后，该楼层将在官网上不可见")) {
              $this.$set($this.floorManage.list[index], "show", !v)
            }
          }
        },
        // 点击新建楼层按钮
        clickNewFloorBtn() {
          $this.floorManage.list.push({
            show: true,
            title: "",
            backgroundUrl: "",
            content: ""
          })
        },
        // 点击删除楼层按钮
        clickDeleteBtn(index) {
          $this.floorManage.list.splice(index, 1)
        },
        fileUploadSuccess(data, index) {
          let id = data[0].split("__")[0]
          let src = data[1]
          $this.$set($this.floorManage.list[index], id, src)
        },
        afterDelete(data, index) {
          let id = data[0].split("__")[0]
          $this.$set($this.floorManage.list[index], id, "")
        },
        // 点击-上移按钮
        clickMoveUpBtn(index) {
          let arr = []
          arr = $this.floorManage.list
          arr[index] = arr.splice(index - 1, 1, arr[index])[0]
          $this.$set($this.floorManage, "list", arr)
        },
        // 点击-下移按钮
        clickMoveDownBtn(index) {
          let arr = []
          arr = $this.floorManage.list
          arr[index] = arr.splice(index + 1, 1, arr[index])[0]
          $this.$set($this.floorManage, "list", arr)
        },
      }
    },
    // 新闻管理相关
    newsManageMethods() {
      let $this = this;
      return {
        // 点击选择新闻弹窗的选择按钮
        clickChooseNewsBtn() {
          if ($this.chooseNews.list.length > 0) {
            if ($this.newsManage.list.length + $this.chooseNews.list.length <= 10) {
              $this.chooseNews.list.forEach(li => {
                let newObj = {
                  id: new Date().getTime() // 随机id
                }
                $this.newsManage.list.push(Object.assign(newObj, li))
              })
              $this.chooseNews.dialog=false;
            } else {
              msg_err("最多只能显示10个新闻,请先删除首页新闻或少选择一些要显示的新闻！");
            }
          } else {
            msg_err("请选择要显示的新闻!");
          }
        },
        // 弹窗选择的新闻列表改变
        selectedNewsList(v) {
          $this.$set($this.chooseNews, "list", v)
        },
        // 检测-实验填写合法性检测
        validate(tech, techIndex, callback) {
          let rules = [
            {key: 'title', name: "新闻标题"},
            {key: 'news_bg', name: "新闻背景"},
          ]
          rules.forEach((rule, index) => {
            if (!tech[rule.key]) {
              callback(new Error(`第${techIndex + 1}行未设置${rule.name}`))
              setTimeout(() => {
                $(".floorType2").removeClass('is-error')
              }, 300)
            }
          })
        },
        // 工厂-新建
        factoryCreateNew() {
          return {
            id: new Date().getTime() // 随机id
          }
        },
        // 点击-楼层-新建按钮
        clickAddBtn() {
          if ($this.newsManage.list.length < 10) {
            $this.chooseNews.dialog = true;
          } else {
            msg_err("最多只能显示10个新闻");
          }
        },
        // 点击-楼层-删除按钮
        clickDeleteBtn(params) {
          let index = params.index
          $this.newsManage.list.splice(index, 1)
        },
        // 点击-产品-上移按钮
        clickMoveUpBtn(params) {
          let index = params.index
          let arr = []
          arr = $this.newsManage.list
          arr[index] = arr.splice(index - 1, 1, arr[index])[0]
          $this.$set($this.newsManage, "list", arr)
        },
        // 点击-产品-下移按钮
        clickMoveDownBtn(params) {
          let index = params.index
          let arr = []
          arr = $this.newsManage.list
          arr[index] = arr.splice(index + 1, 1, arr[index])[0]
          $this.$set($this.newsManage, "list", arr)
        },
        // 监听-图片上传成功
        onImgUploadSuccess(params) {
          let index = params.index // 楼层编号
          let imgSrc = params.params[1] // 成功后图片地址
          // 判断上传的什么图片
          let uploaderId = params.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          $this.$set($this.newsManage.list[index], uploaderKey, imgSrc)
        },
        // 监听-图片删除
        onImgDelete(params) {
          let index = params.index // 楼层编号
          // 判断上传的什么图片
          let uploaderId = params.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          $this.$set($this.newsManage.list[index], uploaderKey, "")
        }
      }
    },
  }
}
</script>

<style>
floor-order tbody tr {
  float: left;
}
</style>
<style scoped lang="scss">
.floor-order {
  border-bottom: 1px solid #cecece;
  margin-bottom: 20px;

  .title {
    line-height: 40px;
    font-size: 16px;
  }

  .floor-name {
    margin-left: 15px;
    cursor: move;
    margin-bottom: 0;
  }
}

.floor-li {
  border-bottom: 1px dashed #cecece;
  margin-bottom: 20px;
}

.floor-name {
  width: 30px;
  height: 30px;
  line-height: 30px;
  border-radius: 15px;
  text-align: center;
  font-size: 20px;
  color: #fff;
  background-color: #ff4949;
  margin-bottom: 10px;
}

.app-container {
  padding-bottom: 150px;
}

// 解决新建后排版跳动问题 上传器大小改变后要改height值
.success-container .erp-uploader-one-pic {
  height: 140px;
}
</style>
