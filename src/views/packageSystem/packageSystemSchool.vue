<template>
  <div class="app-container">
    <!-- 全局加载指示器 -->
    <div class="global-loading-mask" v-if="globalLoading">
      <div class="global-loading-container">
        <i class="el-icon-loading"></i>
        <p>数据加载中，请稍候...</p>
      </div>
    </div>

    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success" @click="ListMethods().clickAddBtn()">新增学校用户
          </el-button>
        </div>
      </div>
    </div>
    <!--列表-->
    <el-table class="lists" :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="学校用户ID" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.packageSystemSchoolId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户名" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.username }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属学校" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.schoolName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="登录状态" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.hasLogin === 1 ? 'success' : 'info'">
            {{ scope.row.hasLogin === 1 ? '已登录过' : '未登录' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.disabled === 0 ? 'success' : 'danger'">
            {{ scope.row.disabled === 0 ? '正常' : '已禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="200"
                     class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                   @click="ListMethods().clickEditBtn(scope.row, scope.$index)">编辑
          </el-button>
          <!-- <el-button type="danger" size="mini" round
                   @click="ListMethods().clickDeleteBtn(scope.row, scope.$index)">删除
          </el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                   :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                   layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                   @size-change="(size)=>ListMethods().pageLimitChange(size)"
                   :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--学校用户编辑弹窗-->
    <el-dialog
      :title="schoolInfo.title"
      :visible.sync="schoolInfo.dialog"
      append-to-body
      width="600px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="schoolInfoForm" :model="schoolInfo.edit" :rules="schoolInfo.formRules">
          <el-form-item label="所属学校:" prop="schoolId">
            <el-select
              v-model="schoolInfo.edit.schoolId"
              filterable
              style="width: 100%;"
              @change="v=>SchoolInfoMethods().onSchoolChange(v)"
              placeholder="请选择学校"
              :loading="schools.loading"
              remote
              reserve-keyword
              :disabled="schoolInfo.edit.packageSystemSchoolId"
              :remote-method="SchoolInfoMethods().filterSchool"
            >
              <el-option
                v-for="(item, index) in schools.filteredList || schools.list"
                :key="index"
                :value="item.id"
                :label="item.name">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户名:" prop="username">
            <el-input v-model="schoolInfo.edit.username" placeholder="请输入用户名" :disabled="schoolInfo.edit.packageSystemSchoolId"></el-input>
          </el-form-item>
          <el-form-item label="登录密码:" prop="password" v-if="schoolInfo.type === 'add'">
            <el-input v-model="schoolInfo.edit.password" placeholder="请输入登录密码" show-password></el-input>
          </el-form-item>
          <el-form-item label="是否禁用:" prop="disabled">
            <el-switch
              v-model="schoolInfo.edit.disabled"
              :active-value="1"
              :inactive-value="0"
              active-color="#ff4949"
              inactive-color="#13ce66"
              active-text="已禁用"
              inactive-text="正常">
            </el-switch>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="schoolInfo.dialog = false">取 消</el-button>
        <el-button type="primary" @click="SchoolInfoMethods().submitForm()" :loading="schoolInfo.loading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat, timeFormat} from "@/filters";
import {date_format, searchWordFiltration} from "@/utils/common";
import {PackageSystemSchoolModel} from "@/model/packageSystem/PackageSystemSchoolModel";
import {SchoolModel} from "@/model/exp/SchoolModel";
import {validateMaxLength} from "@/utils/validate";
import {msg_success, msg_err} from "@/utils/ele_component";

export default {
  name: "packageSystemSchool",
  components: {
    ListSearchFilter
  },
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission,
      adminUserId: state => state.user.id
    })
  },
  filters: {dateFormat, timeFormat},
  data: function () {
    // 验证用户名
    const validateUsername = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入用户名'));
      }
      if (value.length > 50) {
        return callback(new Error('用户名不能超过50个字符'));
      }
      callback();
    };

    // 验证密码
    const validatePassword = (rule, value, callback) => {
      if (!value && this.schoolInfo.type === 'add') {
        return callback(new Error('请输入登录密码'));
      }
      if (value && value.length < 6) {
        return callback(new Error('密码长度不能少于6个字符'));
      }
      callback();
    };

    return {
      // 全局加载状态
      globalLoading: false,
      // 外部方法
      date_format: date_format,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '用户名',
              key: 'username',
              hidden: false,
              value: '',
              placeholder: '请输入用户名',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            }
          ],
          filter: [
            {
              type: 'select',
              label: '所属学校',
              key: 'schoolId',
              hidden: false,
              value: '',
              data: [],
              dataObject: {},
              dataOrigin: []
            },
            {
              type: 'select',
              label: '状态',
              key: 'disabled',
              hidden: false,
              value: '',
              data: [
                {label: '正常', value: 0},
                {label: '已禁用', value: 1}
              ],
              dataObject: {
                '0': '正常',
                '1': '已禁用'
              }
            },
            {
              type: 'select',
              label: '登录状态',
              key: 'hasLogin',
              hidden: false,
              value: '',
              data: [
                {label: '已登录', value: 1},
                {label: '未登录', value: 0}
              ],
              dataObject: {
                '1': '已登录',
                '0': '未登录'
              }
            }
          ]
        }
      },
      // 学校列表
      schools: {
        list: [],
        filteredList: [],
        loading: false
      },
      // 学校用户信息
      schoolInfo: {
        dialog: false,
        loading: false,
        title: '',
        type: 'add', // add 或 edit
        edit: {
          schoolId: '',
          username: '',
          password: '',
          disabled: 0,
          hasLogin: 0,
          extraInfo: {}
        },
        formRules: {
          schoolId: [
            { required: true, message: '请选择所属学校', trigger: 'change' }
          ],
          username: [
            { required: true, validator: validateUsername, trigger: 'blur' }
          ],
          password: [
            { validator: validatePassword, trigger: 'blur' }
          ]
        }
      }
    }
  },
  async mounted() {
    // 显示全局加载指示器
    this.globalLoading = true;
    try {
      // 获取学校列表
      await this.SchoolInfoMethods().getSchoolList();

      // 初始化学校筛选器
      await this.initSchoolFilter();

      // 获取列表数据
      await this.ListMethods().getList(0, 20, {});
    } catch (error) {
      console.error('页面初始化失败', error);
      this.$message.error('页面初始化失败，请刷新重试');
    } finally {
      // 隐藏全局加载指示器
      this.globalLoading = false;
    }
  },
  methods: {
    // 初始化学校筛选器
    async initSchoolFilter() {
      if (this.schools.list && this.schools.list.length > 0) {
        // 生成学校筛选器选项
        let schoolOptions = [];
        let schoolOptionsObject = {};

        this.schools.list.forEach(item => {
          schoolOptions.push({
            label: item.name,
            value: item.id
          });
          schoolOptionsObject[item.id] = item.name;
        });

        this.$set(this.lists.searchFilter.filter[0], "dataObject", schoolOptionsObject);
        this.$set(this.lists.searchFilter.filter[0], "data", schoolOptions);
        this.$set(this.lists.searchFilter.filter[0], "dataOrigin", this.schools.list);
      }
    },

    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 点击搜索重置按钮
        clickCleanBtn() {
          // 重置搜索条件
        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          try {
            [list, $this.lists.pages] = await PackageSystemSchoolModel.getPageList(page, size, "", query);

            // 处理列表数据，添加学校名称
            for (let i = 0; i < list.length; i++) {
              // 如果没有用户名，尝试从学校列表中获取
              if (!list[i].schoolName && list[i].schoolId) {
                const school = $this.schools.list.find(s => s.id === list[i].schoolId);
                if (school) {
                  list[i].schoolName = school.name;
                }
              }
            }

            $this.$set($this.lists, "list", list);
          } catch (error) {
            console.error('获取列表失败', error);
            $this.$message.error('获取列表失败');
          } finally {
            $this.lists.loading = false;
          }
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page - 1, $this.lists.pages.size, $this.lists.query);
        },
        // 分页-改变每页条数
        async pageLimitChange(size) {
          this.getList(0, size, $this.lists.query);
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query;
          this.getList(0, $this.lists.pages.size, $this.lists.query);
        },
        // 点击添加按钮
        clickAddBtn() {
          $this.schoolInfo.dialog = true;
          $this.schoolInfo.type = "add";
          $this.schoolInfo.title = "新增学校用户";
          $this.schoolInfo.edit = {
            schoolId: '',
            username: '',
            password: '',
            disabled: 0,
            hasLogin: 0,
            extraInfo: {}
          };

          setTimeout(() => {
            $this.$refs['schoolInfoForm'].clearValidate();
          }, 300);
        },
        // 点击编辑按钮
        clickEditBtn(row) {
          $this.schoolInfo.dialog = true;
          $this.schoolInfo.type = "edit";
          $this.schoolInfo.title = "编辑学校用户";
          $this.schoolInfo.edit = JSON.parse(JSON.stringify(row));

          setTimeout(() => {
            $this.$refs['schoolInfoForm'].clearValidate();
          }, 300);
        },
        // 点击删除按钮
        async clickDeleteBtn(row) {
          $this.$confirm('确定要删除该学校用户吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(async () => {
            try {
              const result = await PackageSystemSchoolModel.deleteOne({
                packageSystemSchoolId: row.packageSystemSchoolId
              });

              if (result) {
                msg_success('删除成功');
                this.getList($this.lists.pages.number - 1, $this.lists.pages.size, $this.lists.query);
              } else {
                msg_err('删除失败');
              }
            } catch (error) {
              console.error('删除失败', error);
              msg_err('删除失败');
            }
          }).catch(() => {
            // 取消删除
          });
        }
      }
    },
    // 学校信息Methods
    SchoolInfoMethods() {
      let $this = this;
      return {
        // 获取学校列表
        async getSchoolList() {
          $this.schools.loading = true;
          try {
            const data = await SchoolModel.getList(1, 1000, {});
            if (data && data.length > 0) {
              $this.schools.list = data[0];
              $this.schools.filteredList = data[0];
            }
          } catch (error) {
            console.error('获取学校列表失败', error);
            $this.$message.error('获取学校列表失败');
          } finally {
            $this.schools.loading = false;
          }
        },
        // 筛选学校
        filterSchool(query) {
          if (query) {
            $this.schools.filteredList = $this.schools.list.filter(item => {
              return item.name.toLowerCase().includes(query.toLowerCase());
            });
          } else {
            $this.schools.filteredList = $this.schools.list;
          }
        },
        // 学校选择变化
        onSchoolChange(schoolId) {
          const school = $this.schools.list.find(s => s.id === schoolId);
          if (school) {
            $this.schoolInfo.edit.username = school.name;
          }
        },
        // 提交表单
        submitForm() {
          $this.$refs['schoolInfoForm'].validate(async valid => {
            if (valid) {
              $this.schoolInfo.loading = true;
              try {
                // 确保实体结构正确
                const entity = {
                  packageSystemSchoolId: $this.schoolInfo.edit.packageSystemSchoolId,
                  schoolId: $this.schoolInfo.edit.schoolId,
                  username: $this.schoolInfo.edit.username,
                  disabled: $this.schoolInfo.edit.disabled,
                  extraInfo: $this.schoolInfo.edit.extraInfo || {}
                };

                // 如果是新增，添加密码
                if ($this.schoolInfo.type === 'add') {
                  entity.password = $this.schoolInfo.edit.password;
                }

                const result = await PackageSystemSchoolModel.addOrEdit(entity);

                if (result) {
                  msg_success($this.schoolInfo.type === 'add' ? '新增成功' : '修改成功');
                  $this.schoolInfo.dialog = false;
                  $this.ListMethods().getList($this.lists.pages.number - 1, $this.lists.pages.size, $this.lists.query);
                } else {
                  msg_err($this.schoolInfo.type === 'add' ? '新增失败' : '修改失败');
                }
              } catch (error) {
                console.error($this.schoolInfo.type === 'add' ? '新增失败' : '修改失败', error);
                msg_err($this.schoolInfo.type === 'add' ? '新增失败' : '修改失败');
              } finally {
                $this.schoolInfo.loading = false;
              }
            }
          });
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
// 全局加载指示器样式
.global-loading-mask {
  position: absolute;
  z-index: 1000;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;

  .global-loading-container {
    text-align: center;

    i {
      font-size: 32px;
      color: #409EFF;
      margin-bottom: 10px;
    }

    p {
      font-size: 14px;
      color: #606266;
      margin: 0;
    }
  }
}

.header-container {
  padding-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.right {
  margin-left: 20px;
}

.dialog-container {
  padding: 20px;
}

.lists {
  a {
    color: #004DCC;
  }

  .can-click {
    color: #004DCC;
    cursor: pointer;
  }
}
</style>
