<script setup>

</script>

<template>
  <div class="app-container">
    <!-- 全局加载指示器 -->
    <div class="global-loading-mask" v-if="globalLoading">
      <div class="global-loading-container">
        <i class="el-icon-loading"></i>
        <p>数据加载中，请稍候...</p>
      </div>
    </div>

    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success" @click="ListMethods().clickAddBtn()">新增实验包版本
          </el-button>
        </div>
      </div>
    </div>
    <!--列表-->
    <el-table class="lists" :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="包版本Id" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.packageVersionId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属实验" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.experimentName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属实验版本" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.experimentVersionName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="包版本号" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.version }}</span>
        </template>
      </el-table-column>
      <el-table-column label="包版本名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.versionName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="包版本说明" align="center">
        <template slot-scope="scope">
            <el-tooltip :content="scope.row.versionDesc" placement="top">
            <span>{{ scope.row.versionDesc.substring(0, 20) }}...</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column label="下载地址" align="center" width="200">
        <template slot-scope="scope">
          <el-link type="primary" :href="scope.row.downloadUrl" target="_blank" v-if="scope.row.downloadUrl">点击下载</el-link>
          <span v-else>暂无</span>
        </template>
      </el-table-column>
      <el-table-column label="实验版本名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.experimentVersionName }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="300"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="success" size="mini" round
                     style="margin-top: 10px;"
                     @click="ListMethods().clickEditBtn(scope.row,scope.$index)">详情
          </el-button>
          <el-button type="primary" size="mini" round
                    style="margin-top: 10px;"
                    @click="ListMethods().clickPushBtn(scope.row)">推送
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--详情弹窗-->
    <el-dialog
      :title="packageInfo.title"
      :visible.sync="packageInfo.dialog"
      append-to-body
      width="1000px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="150px" ref="packageInfoForm" :model="packageInfo.edit" :rules="packageInfo.formRules">
          <el-form-item label="所属实验:" prop="experimentId">
            <el-select
              v-model="packageInfo.edit.experimentId"
              filterable
              style="width: 100%;"
              @change="v=>PackageInfoMethods().onExperimentChange(v)"
              placeholder="请选择实验"
              :filter-method="PackageInfoMethods().filterExperiment"
              :remote-method="PackageInfoMethods().filterExperiment"
              :loading="experiments.loading"
              remote
              reserve-keyword
            >
              <el-option
                v-for="(item, index) in experiments.filteredList || experiments.list"
                :key="index"
                :value="item.id"
                :label="item.name"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="实验版本:" prop="experimentVersionId" v-if="packageInfo.edit.experimentId">
            <el-select
              v-model="packageInfo.edit.experimentVersionId"
              filterable
              style="width: 100%;"
              placeholder="请选择实验版本"
              @change="v=>PackageInfoMethods().onExperimentVersionChange(v)"
            >
              <el-option
                v-for="(version, idx) in packageInfo.versions"
                :key="idx"
                :value="version.experimentVersionId"
                :label="version.name">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="包版本号:" prop="version">
            <el-input v-model="packageInfo.edit.version"></el-input>
          </el-form-item>
          <el-form-item label="包版本名称:" prop="versionName">
            <el-input v-model="packageInfo.edit.versionName"></el-input>
          </el-form-item>
          <el-form-item label="包版本说明:" prop="versionDesc">
            <el-input type="textarea" :rows="3" v-model="packageInfo.edit.versionDesc"></el-input>
          </el-form-item>
          <el-form-item label="包文件:" prop="fileHash" v-if="packageInfo.type === 'add'&&packageInfo.edit.experimentVersionId">
            <!-- 上传说明 -->
            <div class="upload-notice-container">
              <div class="upload-notice-header">
                <i class="el-icon-info"></i>
                <span>上传要求说明</span>
              </div>
              <div class="upload-notice-content">
                <div class="upload-notice-item">
                  <i class="el-icon-document"></i>
                  <span><strong>文件格式：</strong>必须是 ZIP 压缩文件</span>
                </div>
                <div class="upload-notice-item">
                  <i class="el-icon-warning"></i>
                  <span><strong>程序入口：</strong>实验入口文件夹里只能有一个 exe 文件（UnityCrashHandler64.exe 除外）</span>
                </div>
                <div class="upload-notice-item">
                  <i class="el-icon-check"></i>
                  <span><strong>兼容性要求：</strong>为保障 Win11 兼容性，包中的文件夹、文件名最好全英文</span>
                </div>
              </div>
            </div>
            
            <el-upload
              class="upload-demo"
              action="dev"
              :show-file-list="false"
              :http-request="UploadMethods().uploadRequest"
              :on-success="UploadMethods().onSuccess"
              :on-error="UploadMethods().onError"
              :before-upload="UploadMethods().beforeUpload">
              <el-button size="small" type="primary">点击上传</el-button>
            </el-upload>
            <div v-if="packageInfo.uploadProgress > 0" class="progress-container">
              <el-progress
                :percentage="packageInfo.uploadProgress"
                :stroke-width="12"
                :color="customColorMethod">
              </el-progress>
            </div>
          </el-form-item>
          <el-form-item label="文件哈希值:" v-if="packageInfo.hashProgress > 0 || packageInfo.edit.fileHash">
            <div v-if="packageInfo.edit.fileHash">
              <span>{{ packageInfo.edit.fileHash }}</span>
            </div>
            <div v-else>
             <span>*正在计算中*</span>
            </div>
            <div v-if="packageInfo.hashProgress > 0 && packageInfo.hashProgress < 100" class="progress-container">
              <el-progress
                :percentage="packageInfo.hashProgress"
                :stroke-width="12"
                :color="customColorMethod">
              </el-progress>
            </div>
          </el-form-item>
          <el-form-item label="下载地址:" v-if="packageInfo.edit.downloadUrl">
            <el-input v-model="packageInfo.edit.downloadUrl" readonly>
              <el-button slot="append" type="primary" @click="window.open(packageInfo.edit.downloadUrl)">访问</el-button>
            </el-input>
          </el-form-item>
          <el-form-item label="文件信息:" v-if="packageInfo.edit.fileInfo && packageInfo.edit.fileInfo.originalName">
            <div class="file-info-container">
              <div class="file-info-item">
                <span class="file-info-label">原始文件名:</span>
                <span>{{ packageInfo.edit.fileInfo.originalName }}</span>
              </div>
              <div class="file-info-item">
                <span class="file-info-label">文件大小:</span>
                <span>{{ packageInfo.edit.fileInfo.sizeFormatted }}</span>
              </div>
              <div class="file-info-item">
                <span class="file-info-label">文件类型:</span>
                <span>{{ packageInfo.edit.fileInfo.type || '未知' }}</span>
              </div>
              <div class="file-info-item">
                <span class="file-info-label">最后修改:</span>
                <span>{{ packageInfo.edit.fileInfo.lastModifiedDate }}</span>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="packageInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="PackageInfoMethods().clickAddBtn()" 
                   :loading="packageInfo.loading"
                   :disabled="packageInfo.uploading || (packageInfo.hashProgress > 0 && packageInfo.hashProgress < 100)"
                   v-if="packageInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="PackageInfoMethods().clickEditBtn()" :loading="packageInfo.loading"
                   v-if="packageInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>

    <!--推送包对话框-->
    <el-dialog
      title="推送包版本"
      :visible.sync="pushPackage.dialog"
      append-to-body
      width="1000px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <div v-if="pushPackage.loading" class="loading-container">
          <el-skeleton :rows="5" animated></el-skeleton>
        </div>
        <div v-else>
          <!-- 包信息区域（标题和基本信息）-->
          <div class="push-header">
            <h2 class="push-title">包版本信息</h2>
          </div>

          <div class="push-info">
            <div class="push-info-section">
              <div class="push-info-item">
                <span class="push-info-label">包版本ID:</span>
                <span>{{ pushPackage.packageInfo.packageVersionId }}</span>
              </div>
              <div class="push-info-item">
                <span class="push-info-label">包版本号:</span>
                <span>{{ pushPackage.packageInfo.version }}</span>
              </div>
              <div class="push-info-item">
                <span class="push-info-label">包版本名称:</span>
                <span>{{ pushPackage.packageInfo.versionName }}</span>
              </div>
              <div class="push-info-item">
                <span class="push-info-label">所属实验:</span>
                <span>{{ pushPackage.packageInfo.experimentName }}</span>
              </div>
              <div class="push-info-item">
                <span class="push-info-label">所属实验版本:</span>
                <span>{{ pushPackage.packageInfo.experimentVersionName }}</span>
              </div>
            </div>

            <!-- 包说明内容 -->
            <div class="push-info-section" v-if="pushPackage.packageInfo.versionDesc">
              <div class="push-section-title">包版本说明</div>
              <div class="push-desc-content">
                <p v-for="(line, index) in formatDescLines(pushPackage.packageInfo.versionDesc)" :key="index">{{ line }}</p>
              </div>
            </div>

            <!-- 文件信息显示 -->
            <div class="push-info-section" v-if="pushPackage.packageInfo.fileInfo && pushPackage.packageInfo.fileInfo.originalName">
              <div class="push-section-title">文件信息</div>
              <div class="push-file-info-list">
                <div class="push-info-item">
                  <span class="push-info-label">文件名称:</span>
                  <span>{{ pushPackage.packageInfo.fileInfo.originalName }}</span>
                </div>
                <div class="push-info-item">
                  <span class="push-info-label">文件类型:</span>
                  <span>{{ pushPackage.packageInfo.fileInfo.type || '未知' }}</span>
                </div>
                <div class="push-info-item">
                  <span class="push-info-label">文件大小:</span>
                  <span>{{ pushPackage.packageInfo.fileInfo.sizeFormatted }}</span>
                </div>
                <div class="push-info-item">
                  <span class="push-info-label">修改时间:</span>
                  <span>{{ pushPackage.packageInfo.fileInfo.lastModifiedDate }}</span>
                </div>
              </div>
            </div>



          </div>

          <!-- 学校选择区域 -->
          <div class="push-header">
            <h2 class="push-title">推送实验包到学校</h2>
          </div>
          <!-- 添加已推送学校列表显示 -->
          <div class="pushed-school-box" v-if="pushPackage.packageInfo.pushedSchoolIds && pushPackage.packageInfo.pushedSchoolIds.length > 0">
              <h3>已推送过的学校</h3>
              <div class="pushed-schools-list flex flex-center">
                <el-tag
                  v-for="schoolId in pushPackage.packageInfo.pushedSchoolIds"
                  :key="schoolId"
                  effect="plain"
                  type="success"
                  class="pushed-school-tag">
                  {{ getPushedSchoolName(schoolId) }}
                </el-tag>
                <div class="push-info-item" v-if="!pushPackage.packageInfo.pushedSchoolIds.length">
                  <span>暂无推送记录</span>
                </div>
              </div>
            </div>
          <div class="push-school-select flex flex-center">
            <div>
              <h3>选择要推送的学校</h3>
              <el-transfer
                style="width: 100%;"
                v-model="pushPackage.selectedSchools"
                :data="pushPackage.schoolsData"
                :titles="['可选学校', '要推送的学校']"
                :props="{
                  key: 'id',
                  label: 'name',
                  disabled: 'disabled'
                }"
                filterable
                filter-placeholder="请输入学校名称"
                :filter-method="SchoolTransferMethods().filterSchoolItem"
                @change="SchoolTransferMethods().handleSelectionChange">
              </el-transfer>

              <div class="push-school-stats">
                本次已选择 <span class="selected-count">{{ pushPackage.selectedSchools.length }}</span> 所要推送的学校
              </div>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="pushPackage.dialog = false">取 消</el-button>
        <el-button type="primary" @click="SchoolTransferMethods().handlePushConfirm()">立即推送</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// todo
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat, timeFormat} from "@/filters";
import {date_format, getQuery, objectToLVArr, searchWordFiltration} from "@/utils/common";
import {PackageVersionModel} from "@/model/packageSystem/PackageVersionModel";
import {validateMaxLength} from "@/utils/validate";
import {msg_success} from "@/utils/ele_component";
import {ExperimentVersionModel} from "@/model/packageSystem/ExperimentVersionModel";
import {CommonModel} from "@/model/CommonModel";
import {BaseUploadModel} from "@/model/BaseUploadModel";
// 导入xxHash3 WASM
import xxhash from 'xxhash-wasm';
import { SchoolModel } from "@/model/exp/SchoolModel";
import { PackagePushModel } from "@/model/packageSystem/PackagePushModel";

export default {
  name: "packageVersion",
  components: {
    ListSearchFilter
  },
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission,
      adminUserId: state => state.user.id
    }),
    uploadHeaders() {
      return {
        Authorization: localStorage.getItem('token')
      }
    },
    uploadAction() {
      return process.env.VUE_APP_BASE_API + '/api/upload'
    },
    customColorMethod() {
      return function(percentage) {
        if (percentage < 30) {
          return '#909399';
        } else if (percentage < 70) {
          return '#e6a23c';
        } else {
          return '#67c23a';
        }
      }
    }
  },
  filters: {dateFormat, timeFormat},
  data: function () {
    let $this = this;
    return {
      // 全局加载状态
      globalLoading: false,
      window: window,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
          {
              type: 'input',
              label: '包版本Id',
              key: 'packageVersionId',
              value: '',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '包版本号',
              key: 'version',
              value: '',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '包版本名称',
              key: 'versionName',
              value: '',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '包版本说明',
              key: 'versionDesc',
              value: '',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            },
          ],
          filter: [
          {
              type: 'select',
              label: '实验名称',
              key: 'experimentId',
              hidden: false,
              value: '',
              data: [],
              dataObject: [],
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {

              }
            }
          ]
        }
      },
      // 实验列表
      experiments: {
        list: [],
        filteredList: null,
        loading: false
      },
      // 包信息
      packageInfo: {
        dialog: false,
        title: "新增实验包版本",
        type: "add",
        loading: false,
        uploading: false,
        uploadProgress: 0,
        hashProgress: 0,
        edit: {
          info: {},
          experimentVersionName: '',
          fileInfo: {
            originalName: '', // 原始文件名
            size: 0,          // 文件大小(字节)
            sizeFormatted: '', // 格式化后的文件大小
            lastModified: 0,  // 最后修改时间戳
            lastModifiedDate: '', // 最后修改日期
            type: '',         // 文件MIME类型
            extension: ''     // 文件扩展名
          }
        },
        versions: [], // 实验版本列表
        // 输入检测
        formRules: {
          'experimentId': {required: true, message: '请选择所属实验', trigger: 'change'},
          'experimentVersionId': {required: true, message: '请选择实验版本', trigger: 'change'},
          'version': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 50, "版本号"), trigger: 'blur'},
          'versionName': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 50, "版本名称"), trigger: 'blur'},
          'versionDesc': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 50, "版本说明"), trigger: 'blur'},
          'fileHash': {
            required: true,
            validator: (rule, value, callback) => {
              // 如果是编辑模式，则不需要验证文件上传
              if ($this.packageInfo.type === 'edit') {
                callback();
                return;
              }
              
              // 如果正在上传文件，不允许提交
              if ($this.packageInfo.uploading) {
                callback(new Error('文件正在上传中，请稍候...'));
                return;
              }
              
              // 如果正在计算哈希值但尚未完成，不允许提交
              if ($this.packageInfo.hashProgress > 0 && $this.packageInfo.hashProgress < 100) {
                callback(new Error('文件哈希值正在计算中，请稍候...'));
                return;
              }
              
              // 检查是否有下载地址（上传成功的标志）
              if (!$this.packageInfo.edit.downloadUrl) {
                callback(new Error('请上传包文件'));
                return;
              }
              
              // 检查是否有文件哈希值
              if (!value) {
                callback(new Error('文件哈希值计算中，请稍候...'));
                return;
              }
              
              callback();
            },
            trigger: 'change'
          },
        },
      },
      // 推送包信息
      pushPackage: {
        dialog: false,
        loading: false,
        packageInfo: {},
        allSchools: [], // 所有学校列表
        schoolsData: [], // 穿梭框使用的学校数据
        selectedSchools: [], // 已选择的学校ID
        searchKey: '', // 搜索关键字
        relatedSchoolIds: [], // 关联的学校ID列表
        duplicatedSchools: [] // 记录用户选择的重复学校ID
      },
    }
  },
  async mounted() {
    // 显示全局加载指示器
    this.globalLoading = true;
    try {
      // 初始化筛选器和缓存数据
      await this.ListMethods().initFilter()
      // 获取列表数据
      await this.ListMethods().getList(0, 20, {})
      // 预先获取学校列表，避免每次打开推送弹窗时才获取
      await this.SchoolTransferMethods().getSchoolList()
    } catch (error) {
      console.error('页面初始化失败', error);
      this.$message.error('页面初始化失败，请刷新重试');
    } finally {
      // 隐藏全局加载指示器
      this.globalLoading = false;
    }
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 点击搜索重置按钮
        clickCleanBtn() {

        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          [list, $this.lists.pages] = await PackageVersionModel.getPageList(page, size, "", query)
          // 获取实验名称
          for (let i = 0; i < list.length; i++) {
            // 查找实验名称
            const experiment = $this.experiments.list.find(exp => exp.id === list[i].experimentId);

            if (experiment) {
              list[i].experimentName = experiment.name;
            } else {
              list[i].experimentName = '未知';
            }
          }
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page - 1, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter() {
          // 获取实验列表
          $this.experiments.loading = true
          let experimentList = await ExperimentVersionModel.getAllExperimentList()
          // 保存原始数据，包含实验版本信息
          $this.experiments.list = experimentList

          // 生成筛选器选项
          let experimentOptions = [];
          let experimentOptionsObject = {};

          experimentList.forEach(item => {
            experimentOptions.push({
              label: item.name,
              value: item.id
            });
            experimentOptionsObject[item.id] = item.name;
          });

          $this.$set($this.lists.searchFilter.filter[0], "dataObject", experimentOptionsObject);
          $this.$set($this.lists.searchFilter.filter[0], "data", experimentOptions);
          $this.$set($this.lists.searchFilter.filter[0], "dataOrigin", experimentList);

          $this.experiments.loading = false
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击添加按钮
        async clickAddBtn() {
          $this.packageInfo.dialog = true;
          $this.packageInfo.type = "add"
          $this.packageInfo.title = "新增实验包版本"
          $this.packageInfo.edit = {
            extraInfo: {}
          };
          // 清空实验版本列表
          $this.packageInfo.versions = [];
          
          // 清空上传相关状态
          $this.packageInfo.uploading = false;
          $this.packageInfo.uploadProgress = 0;
          $this.packageInfo.hashProgress = 0;
          
          setTimeout(() => {
            $this.$refs['packageInfoForm'].clearValidate()
          }, 300)
        },
        // 点击列表的编辑按钮
        clickEditBtn(edit, $index) {
          $this.packageInfo.edit = JSON.parse(JSON.stringify(edit))
          $this.packageInfo.type = 'edit'
          $this.packageInfo.title = "修改实验包版本"
          $this.packageInfo.$index = $index

          // 如果已有实验ID，则获取实验版本列表
          if ($this.packageInfo.edit.experimentId) {
            const selectedExperiment = $this.experiments.list.find(item => item.id === $this.packageInfo.edit.experimentId);
            if (selectedExperiment && selectedExperiment.versionList) {
              // 确保数据结构一致性
              $this.packageInfo.versions = selectedExperiment.versionList.map(item => {
                return item;
              });

              // 设置实验版本名称
              if ($this.packageInfo.edit.experimentVersionId) {
                const selectedVersion = $this.packageInfo.versions.find(
                  v => v.experimentVersionId === $this.packageInfo.edit.experimentVersionId
                );
                if (selectedVersion) {
                  $this.packageInfo.edit.experimentVersionName = selectedVersion.name;
                }
              }
            } else {
              $this.packageInfo.versions = [];
            }
          } else {
            $this.packageInfo.versions = [];
          }

          $this.packageInfo.dialog = true
          setTimeout(() => {
            $this.$refs['packageInfoForm'].clearValidate()
          }, 300)
        },
        // 点击推送按钮
        async clickPushBtn(row) {
          $this.pushPackage.dialog = true;
          $this.pushPackage.loading = true;
          $this.pushPackage.packageInfo = JSON.parse(JSON.stringify(row));
          $this.pushPackage.selectedSchools = [];
          $this.pushPackage.searchKey = '';
          $this.pushPackage.duplicatedSchools = [];

          try {
            // 学校列表已在页面加载时获取，这里只需准备穿梭框数据
            if ($this.pushPackage.allSchools && $this.pushPackage.allSchools.length > 0) {
              $this.SchoolTransferMethods().prepareSchoolsData();
            } else {
              // 如果学校列表为空（可能是首次加载失败），尝试重新获取
              await $this.SchoolTransferMethods().getSchoolList();
            }

            // 确保pushedSchoolIds存在
            if (!$this.pushPackage.packageInfo.pushedSchoolIds) {
              $this.pushPackage.packageInfo.pushedSchoolIds = [];
            }

            // 获取实验版本信息，获取关联的学校
            if (row.experimentVersionId) {
              const experimentVersion = await ExperimentVersionModel.getOne(row.experimentVersionId);
              if (experimentVersion && experimentVersion.schoolIds) {
                $this.pushPackage.relatedSchoolIds = experimentVersion.schoolIds;

                // 仅选择未推送过的学校
                const unpushedSchools = experimentVersion.schoolIds.filter(
                  id => !$this.pushPackage.packageInfo.pushedSchoolIds.includes(id)
                );
                $this.pushPackage.selectedSchools = [...unpushedSchools];
              }
            }
          } catch (error) {
            console.error('获取推送信息失败', error);
            $this.$message.error('获取推送信息失败');
          } finally {
            $this.pushPackage.loading = false;
          }
        },
      }
    },
    // 实体Methods
    PackageInfoMethods() {
      let $this = this;
      return {
        // 点击提交新增按钮
        clickAddBtn() {
          // 检查上传状态
          if ($this.packageInfo.uploading) {
            $this.$message.warning('文件正在上传中，请稍候...');
            return;
          }
          
          if ($this.packageInfo.hashProgress > 0 && $this.packageInfo.hashProgress < 100) {
            $this.$message.warning('文件哈希值正在计算中，请稍候...');
            return;
          }
          
          $this.$refs['packageInfoForm'].validate(async validate => {
            if (validate) {
              $this.packageInfo.loading = true

              // 确保实验版本ID已选择
              if (!$this.packageInfo.edit.experimentVersionId) {
                $this.$message.error('请选择实验版本');
                $this.packageInfo.loading = false;
                return;
              }

              // 验证实验版本ID是否在选项中存在
              const validVersion = $this.packageInfo.versions.some(
                item => item.experimentVersionId === $this.packageInfo.edit.experimentVersionId
              );

              if (!validVersion) {
                $this.$message.error('所选实验版本无效，请重新选择');
                $this.packageInfo.loading = false;
                return;
              }

              if (await PackageVersionModel.addOrEdit($this.packageInfo.edit).catch(err => {
                $this.packageInfo.loading = false
              })) {
                msg_success('新增实验包版本成功')
                $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                $this.packageInfo.loading = false
                $this.packageInfo.dialog = false
              }
            }
          })
        },
        // 点击修改按钮
        clickEditBtn() {
          $this.$refs['packageInfoForm'].validate(async validate => {
            if (validate) {
              $this.packageInfo.loading = true

              // 确保实验版本ID已选择
              if (!$this.packageInfo.edit.experimentVersionId) {
                $this.$message.error('请选择实验版本');
                $this.packageInfo.loading = false;
                return;
              }

              if (await PackageVersionModel.addOrEdit($this.packageInfo.edit).catch(err => {
                $this.packageInfo.loading = false
              })) {
                msg_success('保存成功')
                $this.ListMethods().getList($this.lists.pages.number - 1, $this.lists.pages.size, $this.lists.query)
                $this.packageInfo.dialog = false
                $this.packageInfo.loading = false
              }
            }
          })
        },
        // 实验变化
        onExperimentChange(value) {
          // 获取当前选中的实验
          const selectedExperiment = $this.experiments.list.find(item => item.id === value);

          if (selectedExperiment && selectedExperiment.versionList) {
            // 设置实验版本列表
            $this.packageInfo.versions = selectedExperiment.versionList;

            // 确保数据结构一致性
            $this.packageInfo.versions = $this.packageInfo.versions.map(item => {
              // 如果没有experimentVersionId，则使用id
              if (!item.experimentVersionId && item.id) {
                return {
                  ...item,
                  experimentVersionId: item.id
                };
              }
              return item;
            });

            // 清空已选择的实验版本
            $this.packageInfo.edit.experimentVersionId = '';
          } else {
            $this.packageInfo.versions = [];
            $this.packageInfo.edit.experimentVersionId = '';
          }
        },
        // 实验实验版本变化
        onExperimentVersionChange(value) {
          // 获取选中的实验版本
          if (value && $this.packageInfo.versions) {
            const selectedVersion = $this.packageInfo.versions.find(v => v.experimentVersionId === value);
            if (selectedVersion) {
              // 设置实验版本名称
              $this.packageInfo.edit.experimentVersionName = selectedVersion.name;
            }
          }
          $this.$forceUpdate()
          setTimeout(() => {
            $this.$refs['packageInfoForm'].validate()
          }, 300)
        },
        // 过滤实验
        filterExperiment(query) {
          if (query) {
            const lowercaseQuery = query.toLowerCase();
            $this.experiments.filteredList = $this.experiments.list.filter(item => {
              return (item.name && item.name.toLowerCase().includes(lowercaseQuery)) ||
                     (item.id && item.id.toLowerCase().includes(lowercaseQuery));
            });
          } else {
            $this.experiments.filteredList = null;
          }
        }
      }
    },
    // 上传方法
    UploadMethods() {
      let $this = this
      return {
        // 上传前检查
        beforeUpload(file) {
          // 检查文件类型
          const isZip = file.type === 'application/zip' ||
                       file.type === 'application/x-zip-compressed' ||
                       file.name.toLowerCase().endsWith('.zip');

          if (!isZip) {
            $this.$message.error('只能上传 ZIP 格式的文件！');
            return false;
          }

          $this.packageInfo.uploading = true
          $this.packageInfo.uploadProgress = 0

          // 重置哈希相关状态
          $this.packageInfo.hashProgress = 0
          $this.packageInfo.edit.fileHash = ""

          // 收集文件元信息
          $this.UploadMethods().collectFileInfo(file)

          // 异步计算哈希值，不影响上传
          $this.calculateFileHashAsync(file)

          // 直接返回true允许上传
          return true
        },
        // 上传成功
        onSuccess(response) {
          if (response.code === 20000) {
            // 如果服务器返回了哈希值，则优先使用服务器的哈希值
            if (response.data.hash) {
              $this.$set($this.packageInfo.edit, 'fileHash', response.data.hash);
            }
            $this.packageInfo.edit.downloadUrl = response.data.url;
            msg_success('文件上传成功');
            
            // 触发表单验证
            setTimeout(() => {
              if ($this.$refs['packageInfoForm']) {
                $this.$refs['packageInfoForm'].validateField('fileHash');
              }
            }, 100);
          } else {
            $this.$message.error('文件上传失败');
          }
          $this.packageInfo.uploading = false;
          $this.packageInfo.uploadProgress = 0;
        },
        // 上传失败
        onError() {
          $this.$message.error('文件上传失败')
          $this.packageInfo.uploading = false
          $this.packageInfo.uploadProgress = 0
        },
        // 自定义上传请求
        async uploadRequest(upload) {
          let file = upload.file
          return await new Promise((resolve, reject) => {
            BaseUploadModel.qiNiuUploadForPackageSystem(file, $this.packageInfo.edit.experimentId, $this.packageInfo.edit.experimentVersionId, {
              next: (result) => {
                // 上传进度显示
                $this.packageInfo.uploadProgress = parseInt(result.total.percent);
              },
              error: (errResult) => {
                console.log(errResult)
                reject(errResult)
                $this.$message.error('上传失败')
                $this.packageInfo.uploading = false
                $this.packageInfo.uploadProgress = 0
              },
              complete: (result) => {
                let domain = BaseUploadModel.getBucketDomain("zyhd-package-system")
                let url = domain + '/' + result.key

                // 格式化返回结果，兼容原有逻辑
                let response = {
                  code: 20000,
                  data: {
                    hash: $this.packageInfo.edit.fileHash, // 使用计算的哈希值
                    url: url,
                    originalName: $this.packageInfo.edit.fileInfo.originalName,
                    fileSize: $this.packageInfo.edit.fileInfo.size,
                    fileType: $this.packageInfo.edit.fileInfo.type
                  }
                }

                $this.packageInfo.uploadProgress = 100
                setTimeout(() => {
                  $this.packageInfo.uploadProgress = 0
                  $this.packageInfo.uploading = false
                  
                  // 触发表单验证
                  if ($this.$refs['packageInfoForm']) {
                    $this.$refs['packageInfoForm'].validateField('fileHash');
                  }
                }, 500)

                resolve(response)
                upload.onSuccess(response)
              }
            })
          })
        },
        // 收集文件信息
        collectFileInfo(file) {
          // 获取文件扩展名
          const nameParts = file.name.split('.');
          const extension = nameParts.length > 1 ? nameParts[nameParts.length - 1].toLowerCase() : '';

          // 格式化文件大小
          const sizeFormatted = $this.formatSize(file.size);

          // 格式化最后修改日期
          const lastModifiedDate = file.lastModified ?
            date_format(new Date(file.lastModified), "yyyy-MM-dd HH:mm:ss") : '';

          // 保存文件信息
          $this.packageInfo.edit.fileInfo = {
            originalName: file.name,
            size: file.size,
            sizeFormatted: sizeFormatted,
            lastModified: file.lastModified || 0,
            lastModifiedDate: lastModifiedDate,
            type: file.type || $this.getMimeTypeFromExtension(extension),
            extension: extension
          };

          console.log('文件信息已收集:', $this.packageInfo.edit.fileInfo);
        },

        // 根据扩展名猜测MIME类型
        getMimeTypeFromExtension(extension) {
          const mimeTypes = {
            'zip': 'application/zip',
            'rar': 'application/x-rar-compressed',
            '7z': 'application/x-7z-compressed',
            'tar': 'application/x-tar',
            'gz': 'application/gzip',
            'pdf': 'application/pdf',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls': 'application/vnd.ms-excel',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'ppt': 'application/vnd.ms-powerpoint',
            'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'gif': 'image/gif',
            'bmp': 'image/bmp',
            'txt': 'text/plain',
            'html': 'text/html',
            'css': 'text/css',
            'js': 'text/javascript',
            'json': 'application/json',
            'xml': 'application/xml',
            'exe': 'application/octet-stream',
            'dll': 'application/octet-stream',
            'bin': 'application/octet-stream'
          };

          return mimeTypes[extension] || 'application/octet-stream';
        },
      }
    },

    // 异步计算文件哈希值（不阻塞上传）
    async calculateFileHashAsync(file) {
      try {
        // 初始化xxHash实例
        const hasher = await xxhash();

        // 创建64位哈希计算器（使用流式API）
        const hashStream = hasher.create64();

        const chunkSize = 4 * 1024 * 1024; // 4MB 分片大小
        let offset = 0;
        const fileSize = file.size;

        // 更新初始进度
        this.packageInfo.hashProgress = 1;

        while (offset < fileSize) {
          const end = Math.min(offset + chunkSize, fileSize);
          const chunk = await this.readFileChunk(file, offset, end);

          // 更新哈希计算
          hashStream.update(chunk);

          offset = end;
          const progress = Math.floor((offset / fileSize) * 100);

          // 更新计算进度
          this.packageInfo.hashProgress = progress;

          // 让出主线程，避免UI阻塞
          await new Promise(resolve => setTimeout(resolve, 0));
        }

        // 计算最终哈希值
        const hash = hashStream.digest();
        const hashHex = hash.toString(16).padStart(16, '0');
        this.$set(this.packageInfo.edit, 'fileHash', hashHex);
        this.packageInfo.hashProgress = 100;

        console.log('文件哈希计算完成:', hashHex);
        
        // 触发表单验证
        setTimeout(() => {
          if (this.$refs['packageInfoForm']) {
            this.$refs['packageInfoForm'].validateField('fileHash');
          }
        }, 100);

        return hashHex;
      } catch (err) {
        console.error('计算哈希值出错:', err);
        this.packageInfo.hashProgress = 0;
        return '';
      }
    },

    // 读取文件分片
    readFileChunk(file, start, end) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => resolve(new Uint8Array(e.target.result));
        reader.onerror = reject;
        reader.readAsArrayBuffer(file.slice(start, end));
      });
    },

    // 格式化文件大小
    formatSize(bytes) {
      if (bytes < 1024) return bytes + 'B';
      if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + 'KB';
      if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + 'MB';
      return (bytes / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
    },

    // 学校穿梭框方法
    SchoolTransferMethods() {
      let $this = this;
      return {
        // 获取学校列表
        async getSchoolList() {
          try {
            const data = await SchoolModel.getList(1, 1000, {});
            if (data && data.length > 0) {
              $this.pushPackage.allSchools = data[0];
              this.prepareSchoolsData();
            }
          } catch (error) {
            console.error("获取学校列表失败", error);
            $this.$message.error("获取学校列表失败");
          }
        },

        // 准备穿梭框数据
        prepareSchoolsData() {
          // 获取已推送学校ID列表
          const pushedSchoolIds = $this.pushPackage.packageInfo.pushedSchoolIds || [];

          // 构建穿梭框数据，标记已推送的学校为禁用状态
          $this.pushPackage.schoolsData = $this.pushPackage.allSchools.map(school => ({
            id: school.id,
            name: school.name,
            pinyin: school.pinyin || '',
            shortName: school.shortName || '',
            disabled: pushedSchoolIds.includes(school.id), // 如果已推送过则禁用
            isPushed: pushedSchoolIds.includes(school.id) // 标记是否已推送
          }));

          // 清空已选择列表
          $this.pushPackage.selectedSchools = [];
          $this.pushPackage.duplicatedSchools = [];
        },

        // 穿梭框内部过滤方法
        filterSchoolItem(query, item) {
          if (!query) return true;
          return item.name.toLowerCase().includes(query.toLowerCase());
        },

        // 处理穿梭框选择变化
        handleSelectionChange(value) {
          // 检查是否包含已推送的学校
          const pushedSchoolIds = $this.pushPackage.packageInfo.pushedSchoolIds || [];
          const duplicatedSchools = value.filter(id => pushedSchoolIds.includes(id));

          if (duplicatedSchools.length > 0) {
            // 存储重复学校ID
            $this.pushPackage.duplicatedSchools = duplicatedSchools;

            // 从选择列表中移除已推送的学校
            $this.pushPackage.selectedSchools = value.filter(id => !pushedSchoolIds.includes(id));

            // 显示警告
            const schoolNames = duplicatedSchools.map(id => {
              const school = $this.pushPackage.allSchools.find(s => s.id === id);
              return school ? school.name : id;
            }).join('、');

            $this.$message({
              message: `学校「${schoolNames}」已推送过此包版本，不能重复推送`,
              type: 'warning'
            });
          } else {
            $this.pushPackage.selectedSchools = value;
            $this.pushPackage.duplicatedSchools = [];
          }
        },

        // 推送确认
        async handlePushConfirm() {
          if ($this.pushPackage.selectedSchools.length === 0) {
            $this.$message.warning('请选择至少一所学校进行推送');
            return;
          }

          // 再次检查是否包含已推送的学校
          const pushedSchoolIds = $this.pushPackage.packageInfo.pushedSchoolIds || [];
          const duplicatedSchools = $this.pushPackage.selectedSchools.filter(id =>
            pushedSchoolIds.includes(id));

          if (duplicatedSchools.length > 0) {
            const schoolNames = duplicatedSchools.map(id => {
              const school = $this.pushPackage.allSchools.find(s => s.id === id);
              return school ? school.name : id;
            }).join('、');

            $this.$message.error(`不能重复推送学校：${schoolNames}`);
            return;
          }

          const pushInfo = {
            packageVersionId: $this.pushPackage.packageInfo.packageVersionId,
            // packageName: $this.pushPackage.packageInfo.versionName,
            // experimentVersionId: $this.pushPackage.packageInfo.experimentVersionId,
            // experimentVersionName: $this.pushPackage.packageInfo.experimentVersionName,
            schoolIds: $this.pushPackage.selectedSchools,
            selectedSchoolsInfo: $this.pushPackage.allSchools
              .filter(school => $this.pushPackage.selectedSchools.includes(school.id))
              .map(school => ({
                id: school.id,
                name: school.name
              }))
          };

          // 创建推送
          const pushResult = await PackagePushModel.createPush(pushInfo);
          if (pushResult) {
            $this.$message.success('推送生成成功');
            $this.pushPackage.dialog = false;
          } else {
            $this.$message.error('推送生成失败');
          }
        }
      };
    },

    // 格式化描述文本，处理换行
    formatDescLines(text) {
      if (!text) return [];
      return text.split('\n').filter(line => line.trim() !== '');
    },

    // 获取已推送学校名称
    getPushedSchoolName(schoolId) {
      const school = this.pushPackage.allSchools.find(s => s.id === schoolId);
      return school ? school.name : schoolId;
    },
  }
}
</script>
<style scoped lang="scss">
// 全局加载指示器样式
.global-loading-mask {
  position: absolute;
  z-index: 1000;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;

  .global-loading-container {
    text-align: center;

    i {
      font-size: 32px;
      color: #409EFF;
      margin-bottom: 10px;
    }

    p {
      font-size: 14px;
      color: #606266;
      margin: 0;
    }
  }
}

.lists {
  a {
    color: #004DCC;
  }

  .can-click {
    color: #004DCC;
    cursor: pointer;
  }
}

.progress-container {
  margin-top: 15px;
  padding: 10px 15px;
  background-color: #f5f7fa;
  border-radius: 4px;

  .progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 13px;
    color: #606266;
  }
}

.file-info-container {
  padding: 10px 15px;
  background-color: #f5f7fa;
  border-radius: 4px;

  .file-info-item {
    display: flex;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .file-info-label {
      width: 100px;
      color: #606266;
      font-weight: 500;
    }
  }
}

.push-header {
  margin: 20px 0 10px;
  border-bottom: 1px solid #EBEEF5;
  padding-bottom: 10px;

  .push-title {
    font-size: 18px;
    color: #303133;
    margin: 0;
    font-weight: 600;
  }
}

.push-info {
  margin-bottom: 20px;

  .push-info-section {
    background-color: #f5f7fa;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);


    &:last-child {
      margin-bottom: 0;
    }
  }


  .push-section-title {
    font-weight: 600;
    font-size: 14px;
    color: #606266;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px dashed #dcdfe6;
  }

  .push-info-item {
    display: flex;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }

    .push-info-label {
      width: 120px;
      font-weight: bold;
      color: #606266;
    }
  }

  .push-desc-content {
    color: #606266;
    font-size: 14px;
    line-height: 1.6;

    p {
      margin: 0 0 5px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .push-file-info-list {
    display: flex;
    flex-direction: column;

    .push-info-item {
      display: flex;
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }

      .push-info-label {
        width: 120px;
        font-weight: bold;
        color: #606266;
      }
    }
  }

  .push-hash-value {
    text-align: center;
    padding: 5px 0;

    .el-tag {
      font-family: 'Courier New', monospace;
      padding: 7px 10px;
      font-size: 13px;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.push-school-select {
  margin-top: 20px;

  h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    text-align: center;
  }

  .filter-container {
    margin-bottom: 15px;
  }

  .el-transfer {
    text-align: left;
  }

  .push-school-stats {
    text-align: center;
    margin-top: 15px;
    color: #606266;

    .selected-count {
      font-weight: bold;
      color: #409EFF;
    }
  }
}

.loading-container {
  padding: 20px;
}

.pushed-school-box {
  h3 {
    margin-bottom: 15px;
    font-size: 16px;
    text-align: center;
  }
}

.pushed-schools-list {
  display: flex;
  flex-wrap: wrap;
  margin-top: 5px;

  .pushed-school-tag {
    margin-right: 10px;
    margin-bottom: 10px;
  }
}

.upload-notice-container {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #dcdfe6;

  .upload-notice-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    i {
      margin-right: 5px;
    }

    span {
      font-weight: 600;
      font-size: 14px;
      color: #606266;
    }
  }

  .upload-notice-content {
    .upload-notice-item {
      display: flex;
      align-items: center;
      margin-bottom: 5px;

      i {
        margin-right: 5px;
      }

      span {
        font-size: 13px;
        color: #606266;
      }
    }
  }
}
</style>
