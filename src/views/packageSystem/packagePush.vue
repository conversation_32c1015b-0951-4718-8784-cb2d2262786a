<template>
  <div class="app-container">
    <!-- 全局加载指示器 -->
    <div class="global-loading-mask" v-if="globalLoading">
      <div class="global-loading-container">
        <i class="el-icon-loading"></i>
        <p>数据加载中，请稍候...</p>
      </div>
    </div>

    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                        @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                        @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
    </div>
    <!--列表-->
    <el-table class="lists" :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
            style="width: 100%;">
      <el-table-column label="推送ID" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.packagePushId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="包版本ID" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.packageVersionId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="包版本名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.packageVersionName || '未知' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属实验" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.experimentName || '未知' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属实验版本" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.experimentVersionName || '未知' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="推送学校" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.schoolName || '未知' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="推送时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column label="学校接收状态" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.schoolAccept ? 'success' : 'info'">
            {{ scope.row.schoolAccept ? '已接收' : '未接收' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="学校接收时间" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.schoolAcceptTime">{{ scope.row.schoolAcceptTime | dateFormat }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="学校下载状态" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.schoolDownload ? 'success' : 'info'">
            {{ scope.row.schoolDownload ? '已下载' : '未下载' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="学校下载时间" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.schoolDownloadTime">{{ scope.row.schoolDownloadTime | dateFormat }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="150"
                     class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="success" size="mini" round
                   style="margin-top: 10px;"
                   @click="ListMethods().clickViewBtn(scope.row,scope.$index)">详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                   :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                   layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                   @size-change="(size)=>ListMethods().pageLimitChange(size)"
                   :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--详情弹窗-->
    <el-dialog
      title="推送详情"
      :visible.sync="pushInfo.dialog"
      append-to-body
      width="800px"
      center
      v-el-drag-dialog>
      <div class="dialog-container" v-loading="pushInfo.loading">
        <div class="info-section">
          <h3 class="section-title">基本信息</h3>
          <div class="info-item">
            <span class="info-label">推送ID:</span>
            <span>{{ pushInfo.detail.packagePushId }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">创建时间:</span>
            <span>{{ pushInfo.detail.createTime | dateFormat }}</span>
          </div>
        </div>

        <div class="info-section">
          <h3 class="section-title">包版本信息</h3>
          <div class="info-item">
            <span class="info-label">包版本ID:</span>
            <span>{{ pushInfo.detail.packageVersionId }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">包版本名称:</span>
            <span>{{ pushInfo.detail.packageVersionName || '未知' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">所属实验:</span>
            <span>{{ pushInfo.detail.experimentName || '未知' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">所属实验版本:</span>
            <span>{{ pushInfo.detail.experimentVersionName || '未知' }}</span>
          </div>
        </div>

        <div class="info-section">
          <h3 class="section-title">学校信息</h3>
          <div class="info-item">
            <span class="info-label">学校ID:</span>
            <span>{{ pushInfo.detail.schoolId }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">学校名称:</span>
            <span>{{ pushInfo.detail.schoolName || '未知' }}</span>
          </div>
        </div>

        <div class="info-section">
          <h3 class="section-title">接收状态</h3>
          <div class="info-item">
            <span class="info-label">学校接收状态:</span>
            <el-tag :type="pushInfo.detail.schoolAccept ? 'success' : 'info'">
              {{ pushInfo.detail.schoolAccept ? '已接收' : '未接收' }}
            </el-tag>
          </div>
          <div class="info-item" v-if="pushInfo.detail.schoolAcceptTime">
            <span class="info-label">学校接收时间:</span>
            <span>{{ pushInfo.detail.schoolAcceptTime | dateFormat }}</span>
          </div>
        </div>

        <div class="info-section">
          <h3 class="section-title">下载状态</h3>
          <div class="info-item">
            <span class="info-label">学校下载状态:</span>
            <el-tag :type="pushInfo.detail.schoolDownload ? 'success' : 'info'">
              {{ pushInfo.detail.schoolDownload ? '已下载' : '未下载' }}
            </el-tag>
          </div>
          <div class="info-item" v-if="pushInfo.detail.schoolDownloadTime">
            <span class="info-label">学校下载时间:</span>
            <span>{{ pushInfo.detail.schoolDownloadTime | dateFormat }}</span>
          </div>
        </div>

        <div class="info-section" v-if="pushInfo.detail.extraInfo">
          <h3 class="section-title">额外信息</h3>
          <pre class="extra-info">{{ JSON.stringify(pushInfo.detail.extraInfo, null, 2) }}</pre>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="pushInfo.dialog = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat, timeFormat} from "@/filters";
import {date_format, searchWordFiltration} from "@/utils/common";
import {PackagePushModel} from "@/model/packageSystem/PackagePushModel";
import {ExperimentVersionModel} from "@/model/packageSystem/ExperimentVersionModel";
import {SchoolModel} from "@/model/exp/SchoolModel";

export default {
  name: "packagePush",
  components: {
    ListSearchFilter
  },
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission,
      adminUserId: state => state.user.id
    })
  },
  filters: {dateFormat, timeFormat},
  data: function () {
    return {
      // 全局加载状态
      globalLoading: false,
      // 外部方法
      date_format: date_format,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '推送ID',
              key: 'packagePushId',
              value: '',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '包版本ID',
              key: 'packageVersionId',
              value: '',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            }
          ],
          filter: [
            {
              type: 'select',
              label: '实验名称',
              key: 'experimentId',
              hidden: false,
              value: '',
              data: [],
              dataObject: [],
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function () {
                // 选择变化时的处理逻辑
              }
            },
            {
              type: 'select',
              label: '学校名称',
              key: 'schoolId',
              hidden: false,
              value: '',
              data: [],
              dataObject: [],
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function () {
                // 选择变化时的处理逻辑
              }
            },
            {
              type: 'select',
              label: '接收状态',
              key: 'schoolAccept',
              hidden: false,
              value: '',
              data: [
                {label: '已接收', value: true},
                {label: '未接收', value: false}
              ],
              dataObject: {
                'true': '已接收',
                'false': '未接收'
              },
              change: function () {
                // 选择变化时的处理逻辑
              }
            },
            {
              type: 'select',
              label: '下载状态',
              key: 'schoolDownload',
              hidden: false,
              value: '',
              data: [
                {label: '已下载', value: true},
                {label: '未下载', value: false}
              ],
              dataObject: {
                'true': '已下载',
                'false': '未下载'
              },
              change: function () {
                // 选择变化时的处理逻辑
              }
            }
          ]
        }
      },
      // 实验列表
      experiments: {
        list: [],
        loading: false
      },
      // 学校列表
      schools: {
        list: [],
        loading: false
      },
      // 不再需要单独的包版本列表，因为API已经返回了关联的包版本信息
      // 推送详情
      pushInfo: {
        dialog: false,
        loading: false,
        detail: {}
      }
    }
  },
  async mounted() {
    // 显示全局加载指示器
    this.globalLoading = true;
    try {
      // 初始化筛选器和缓存数据
      await this.ListMethods().initFilter()
      // 获取列表数据
      await this.ListMethods().getList(0, 20, {})
    } catch (error) {
      console.error('页面初始化失败', error);
      this.$message.error('页面初始化失败，请刷新重试');
    } finally {
      // 隐藏全局加载指示器
      this.globalLoading = false;
    }
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 点击搜索重置按钮
        clickCleanBtn() {
          // 重置搜索条件
        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          try {
            [list, $this.lists.pages] = await PackagePushModel.getPageListWithPackageVersion(page, size, "", query);

            // 处理列表数据，添加关联信息
            for (let i = 0; i < list.length; i++) {
              // 添加学校名称
              const school = $this.schools.list.find(s => s.id === list[i].schoolId);
              if (school) {
                list[i].schoolName = school.name;
              }

              // 使用API直接返回的包版本信息
              if (list[i].packageVersion && list[i].packageVersion.length > 0) {
                const packageVersion = list[i].packageVersion[0];
                list[i].packageVersionName = packageVersion.versionName;
                list[i].experimentId = packageVersion.experimentId;
                list[i].experimentVersionId = packageVersion.experimentVersionId;
                list[i].experimentVersionName = packageVersion.experimentVersionName;

                // 添加实验名称
                const experiment = $this.experiments.list.find(exp => exp.id === packageVersion.experimentId);
                if (experiment) {
                  list[i].experimentName = experiment.name;
                }
              }
            }

            $this.$set($this.lists, "list", list);
          } catch (error) {
            console.error('获取推送列表失败', error);
            $this.$message.error('获取推送列表失败');
          } finally {
            $this.lists.loading = false;
          }
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page - 1, $this.lists.pages.size, $this.lists.query);
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query);
        },
        // 初始化筛选列表
        async initFilter() {
          try {
            // 获取实验列表
            $this.experiments.loading = true;
            let experimentList = await ExperimentVersionModel.getAllExperimentList();
            $this.experiments.list = experimentList;

            // 生成实验筛选器选项
            let experimentOptions = [];
            let experimentOptionsObject = {};

            experimentList.forEach(item => {
              experimentOptions.push({
                label: item.name,
                value: item.id
              });
              experimentOptionsObject[item.id] = item.name;
            });

            $this.$set($this.lists.searchFilter.filter[0], "dataObject", experimentOptionsObject);
            $this.$set($this.lists.searchFilter.filter[0], "data", experimentOptions);
            $this.$set($this.lists.searchFilter.filter[0], "dataOrigin", experimentList);
            $this.experiments.loading = false;

            // 获取学校列表
            $this.schools.loading = true;
            const schoolData = await SchoolModel.getList(1, 1000, {});
            if (schoolData && schoolData.length > 0) {
              $this.schools.list = schoolData[0];

              // 生成学校筛选器选项
              let schoolOptions = [];
              let schoolOptionsObject = {};

              $this.schools.list.forEach(item => {
                schoolOptions.push({
                  label: item.name,
                  value: item.id
                });
                schoolOptionsObject[item.id] = item.name;
              });

              $this.$set($this.lists.searchFilter.filter[1], "dataObject", schoolOptionsObject);
              $this.$set($this.lists.searchFilter.filter[1], "data", schoolOptions);
              $this.$set($this.lists.searchFilter.filter[1], "dataOrigin", $this.schools.list);
            }
            $this.schools.loading = false;

            // 不再需要获取包版本列表，因为API已经返回了关联的包版本信息
          } catch (error) {
            console.error('初始化筛选器失败', error);
            $this.$message.error('初始化筛选器失败');
          }
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query;
          this.getList(0, $this.lists.pages.size, $this.lists.query);
        },
        // 点击查看详情按钮
        async clickViewBtn(row) {
          $this.pushInfo.loading = true;
          $this.pushInfo.dialog = true;

          try {
            // 深拷贝行数据
            $this.pushInfo.detail = JSON.parse(JSON.stringify(row));

            // 如果需要获取更详细的信息，可以从服务器获取
            if (!row.schoolName || !row.experimentName) {
              const pushDetail = await PackagePushModel.getOne(row.packagePushId);
              if (pushDetail) {
                // 合并详情数据
                $this.pushInfo.detail = { ...$this.pushInfo.detail, ...pushDetail };

                // 添加学校名称
                if (!$this.pushInfo.detail.schoolName) {
                  const school = $this.schools.list.find(s => s.id === $this.pushInfo.detail.schoolId);
                  if (school) {
                    $this.pushInfo.detail.schoolName = school.name;
                  }
                }

                // 添加包版本信息
                if (!$this.pushInfo.detail.packageVersionName && pushDetail.packageVersion && pushDetail.packageVersion.length > 0) {
                  const packageVersion = pushDetail.packageVersion[0];
                  $this.pushInfo.detail.packageVersionName = packageVersion.versionName;
                  $this.pushInfo.detail.experimentId = packageVersion.experimentId;
                  $this.pushInfo.detail.experimentVersionId = packageVersion.experimentVersionId;
                  $this.pushInfo.detail.experimentVersionName = packageVersion.experimentVersionName;

                  // 添加实验名称
                  if (!$this.pushInfo.detail.experimentName) {
                    const experiment = $this.experiments.list.find(exp => exp.id === packageVersion.experimentId);
                    if (experiment) {
                      $this.pushInfo.detail.experimentName = experiment.name;
                    }
                  }
                }
              }
            }
          } catch (error) {
            console.error('获取推送详情失败', error);
            $this.$message.error('获取推送详情失败');
          } finally {
            $this.pushInfo.loading = false;
          }
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
// 全局加载指示器样式
.global-loading-mask {
  position: absolute;
  z-index: 1000;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;

  .global-loading-container {
    text-align: center;

    i {
      font-size: 32px;
      color: #409EFF;
      margin-bottom: 10px;
    }

    p {
      font-size: 14px;
      color: #606266;
      margin: 0;
    }
  }
}

.lists {
  a {
    color: #004DCC;
  }

  .can-click {
    color: #004DCC;
    cursor: pointer;
  }
}

.dialog-container {
  .info-section {
    margin-bottom: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
    padding: 15px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-top: 0;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 1px solid #EBEEF5;
    }

    .info-item {
      display: flex;
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        width: 120px;
        font-weight: bold;
        color: #606266;
      }
    }

    .extra-info {
      background-color: #fff;
      padding: 10px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-size: 13px;
      color: #606266;
      max-height: 200px;
      overflow-y: auto;
      white-space: pre-wrap;
      margin: 0;
    }
  }
}
</style>
