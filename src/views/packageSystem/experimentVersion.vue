<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <div class="filter-container">
        <el-input
          v-model="search.keyword"
          placeholder="实验名称/实验ID"
          clearable
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="searchMethods().handleSearch()"
        />
        <el-button type="primary" icon="el-icon-search" @click="searchMethods().handleSearch()">
          搜索
        </el-button>
        <el-button type="default" icon="el-icon-refresh" @click="searchMethods().resetSearch()">
          重置
        </el-button>
      </div>
    </div>
    <!--列表-->
    <el-table
      class="lists"
      :data="experimentData.filteredList"
      v-loading="experimentData.loading"
      element-loading-text="加载中"
      border
      fit
      style="width: 100%;"
    >
      <el-table-column label="实验ID" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实验名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.createtime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column label="版本" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.versionList && scope.row.versionList.length > 0">
            <span>数量：{{ scope.row.versionList.length }}</span>
            <div class="version-list">
              <el-tag
                v-for="(version, index) in scope.row.versionList.slice(0, 2)"
                :key="index"
                type="success"
                size="mini"
                style="margin-right: 5px; margin-top: 5px;"
              >
                {{ version.name }}
              </el-tag>
              <el-tag v-if="scope.row.versionList.length > 2" type="info" size="mini">更多...</el-tag>
            </div>
          </div>
          <span v-else>暂无版本</span>
          <div style="margin-top: 10px;">
            <el-button
            type="primary"
            size="mini"
            round
            @click="versionMethods().showVersions(scope.row)"
          >
            版本管理
          </el-button>
          </div>
        </template>
      </el-table-column>
<!--      <el-table-column align="center" label="操作" width="220">-->
<!--        <template slot-scope="scope">-->

<!--        </template>-->
<!--      </el-table-column>-->
    </el-table>

    <!--版本详情弹窗-->
    <el-dialog
      title="版本详情"
      :visible.sync="versionData.dialog"
      append-to-body
      width="1000px"
      center
      v-el-drag-dialog
    >
      <div class="dialog-container">
        <div class="experiment-info">
          <h3>实验信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="实验ID">{{ versionData.currentExperiment.id }}</el-descriptions-item>
            <el-descriptions-item label="实验名称">{{ versionData.currentExperiment.name }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ versionData.currentExperiment.createtime | dateFormat }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="version-list" style="margin-top: 20px;">
          <div class="version-header">
            <h3>版本列表</h3>
            <el-button
              type="success"
              size="mini"
              @click="versionMethods().showAddVersionDialog(versionData.currentExperiment.name)"
            >
              新增版本
            </el-button>
          </div>
          <el-table
            :data="versionData.list"
            border
            style="width: 100%;"
          >
            <el-table-column label="版本ID" align="center" width="180">
              <template slot-scope="scope">
                <span>{{ scope.row.experimentVersionId }}</span>
              </template>
            </el-table-column>
            <el-table-column label="版本名称" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.createTime | dateFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" align="center" width="120">
              <template slot-scope="scope">
                <el-tag :type="scope.row.enable ? 'success' : 'danger'">
                  {{ scope.row.enable ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="描述" align="center">
              <template slot-scope="scope">
                <el-tooltip :content="scope.row.description" placement="top" :disabled="!scope.row.description">
                  <span>{{ scope.row.description ? scope.row.description.slice(0, 15) + (scope.row.description.length > 15 ? '...' : '') : '暂无描述' }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="适用学校" align="center" width="120">
              <template slot-scope="scope">
                <el-popover
                  placement="top"
                  width="300"
                  trigger="hover"
                  v-if="scope.row.schoolIds && scope.row.schoolIds.length > 0"
                >
                  <div>
                    <div v-for="(schoolId, index) in scope.row.schoolIds" :key="index" class="school-item">
                      {{ getSchoolNameById(schoolId) }}
                    </div>
                  </div>
                  <div slot="reference" class="school-count">
                    共{{ scope.row.schoolIds.length }}所学校
                  </div>
                </el-popover>
                <span v-else>无适用学校</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作" width="120">
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  size="mini"
                  @click="versionMethods().editVersion(scope.row)"
                >编辑</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!--新增版本弹窗-->
    <el-dialog
      title="新增版本"
      :visible.sync="addVersion.dialog"
      append-to-body
      width="1000px"
      center
      v-el-drag-dialog
    >
      <div class="dialog-container">
        <el-form
          :model="addVersion.form"
          :rules="addVersion.rules"
          ref="addVersionForm"
          label-width="100px"
        >
          <el-form-item label="版本名称" prop="name">
            <el-input v-model="addVersion.form.name" placeholder="请输入版本名称"></el-input>
          </el-form-item>
          <el-form-item label="版本介绍" prop="description">
            <el-input
              type="textarea"
              :rows="3"
              v-model="addVersion.form.description"
              placeholder="请输入版本介绍"
            ></el-input>
          </el-form-item>
          <el-form-item label="启用状态" prop="enable">
            <el-switch v-model="addVersion.form.enable"></el-switch>
          </el-form-item>
          <el-form-item label="适用学校" prop="schoolIds">
            <el-select
              v-model="addVersion.form.schoolIds"
              multiple
              filterable
              remote
              reserve-keyword
              placeholder="请选择适用学校"
              :remote-method="searchSchoolByName"
              :loading="schoolData.searchLoading"
              style="width: 100%;"
            >
              <el-option
                v-for="item in schoolData.searchResults.length > 0 ? schoolData.searchResults : schoolData.list"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addVersion.dialog = false">取 消</el-button>
        <el-button
          type="primary"
          @click="versionMethods().submitAddVersion()"
          :loading="addVersion.loading"
        >确 定</el-button>
      </div>
    </el-dialog>

    <!--编辑版本弹窗-->
    <el-dialog
      title="编辑版本"
      :visible.sync="editVersion.dialog"
      append-to-body
      width="1000px"
      center
      v-el-drag-dialog
    >
      <div class="dialog-container">
        <el-form
          :model="editVersion.form"
          :rules="editVersion.rules"
          ref="editVersionForm"
          label-width="100px"
        >
          <el-form-item label="版本名称" prop="name">
            <el-input v-model="editVersion.form.name" placeholder="请输入版本名称" :disabled="editVersion.form.asMainVersion"></el-input>
          </el-form-item>
          <el-form-item label="版本介绍" prop="description">
            <el-input
              :disabled="editVersion.form.asMainVersion"
              type="textarea"
              :rows="3"
              v-model="editVersion.form.description"
              placeholder="请输入版本介绍"
            ></el-input>
          </el-form-item>
          <el-form-item label="启用状态" prop="enable">
            <el-switch v-model="editVersion.form.enable" :disabled="editVersion.form.asMainVersion"></el-switch>
          </el-form-item>
          <el-form-item label="适用学校" prop="schoolIds">
            <el-select
              v-model="editVersion.form.schoolIds"
              multiple
              filterable
              remote
              reserve-keyword
              placeholder="请选择适用学校"
              :remote-method="searchSchoolByName"
              :loading="schoolData.searchLoading"
              style="width: 100%;"
            >
              <el-option
                v-for="item in schoolData.searchResults.length > 0 ? schoolData.searchResults : schoolData.list"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editVersion.dialog = false">取 消</el-button>
        <el-button
          type="primary"
          @click="versionMethods().submitEditVersion()"
          :loading="editVersion.loading"
        >确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// todos
// [] 学校数量超过500的适应
// [] 删除功能

import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import { dateFormat, timeFormat } from "@/filters";
import { date_format } from "@/utils/common";
import { ExperimentVersionModel } from "@/model/packageSystem/ExperimentVersionModel";
import { SchoolModel } from "@/model/exp/SchoolModel";

export default {
  name: "experimentVersion",
  directives: {
    elDragDialog, permission
  },
  filters: { dateFormat, timeFormat },
  data() {
    // 自定义验证规则：检查版本名称不能是"主版本"
    const validateVersionName = (rule, value, callback) => {
      if (value === '主版本') {
        callback(new Error('版本名称不能为"主版本"'));
      } else {
        callback();
      }
    };

    return {
      // 外部方法
      date_format: date_format,

      // 搜索相关
      search: {
        keyword: ""
      },

      // 实验数据
      experimentData: {
        list: [],
        filteredList: [],
        loading: false
      },

      // 版本数据
      versionData: {
        dialog: false,
        currentExperiment: {},
        list: []
      },

      // 学校数据
      schoolData: {
        list: [],
        loading: false,
        searchKeyword: '',
        searchLoading: false,
        searchResults: []
      },

      // 新增版本
      addVersion: {
        dialog: false,
        loading: false,
        form: {
          name: '',
          description: '',
          enable: true,
          schoolIds: [],
          experimentId: ''
        },
        rules: {
          name: [
            { required: true, message: '请输入版本名称', trigger: 'blur' },
            { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
            { validator: validateVersionName, trigger: 'blur' }
          ],
          description: [
            { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
          ]
        }
      },

      // 编辑版本
      editVersion: {
        dialog: false,
        loading: false,
        currentVersionId: '',
        form: {
          experimentVersionId: '',
          name: '',
          description: '',
          enable: true,
          schoolIds: [],
          experimentId: '',
          asMainVersion: false
        },
        rules: {
          name: [
            { required: true, message: '请输入版本名称', trigger: 'blur' },
            { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },

          ],
          description: [
            { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
          ]
        }
      }
    };
  },
  created() {
    this.getExperimentList();
    this.getSchoolList();
  },
  methods: {
    // 获取实验列表
    async getExperimentList() {
      try {
        this.experimentData.loading = true;
        const data = await ExperimentVersionModel.getAllExperimentList();
        if (data) {
          this.experimentData.list = data;
          this.experimentData.filteredList = [...this.experimentData.list];
        }
      } catch (error) {
        console.error("获取实验列表失败", error);
        this.$message.error("获取实验列表失败");
      } finally {
        this.experimentData.loading = false;
      }
    },

    // 获取学校列表
    async getSchoolList() {
      try {
        this.schoolData.loading = true;
        const data = await SchoolModel.getList(1, 1000, {});
        if (data && data.length > 0) {
          this.schoolData.list = data[0];
        }
      } catch (error) {
        console.error("获取学校列表失败", error);
        this.$message.error("获取学校列表失败");
      } finally {
        this.schoolData.loading = false;
      }
    },

    // 根据学校名称搜索学校
    async searchSchoolByName(query) {
      if (query) {
        this.schoolData.searchLoading = true;
        try {
          const data = await SchoolModel.getSchoolListByName(query);
          this.schoolData.searchResults = data || [];
        } catch (error) {
          console.error("搜索学校失败", error);
        } finally {
          this.schoolData.searchLoading = false;
        }
      } else {
        this.schoolData.searchResults = [];
      }
    },

    // 获取学校名称
    getSchoolNameById(id) {
      const school = this.schoolData.list.find(school => school.id === id);
      return school ? school.name : id;
    },

    // 搜索相关方法
    searchMethods() {
      const $this = this;
      return {
        handleSearch() {
          if (!$this.search.keyword) {
            $this.experimentData.filteredList = [...$this.experimentData.list];
            return;
          }

          const keyword = $this.search.keyword.toLowerCase().trim();
          $this.experimentData.filteredList = $this.experimentData.list.filter(item => {
            return (item.id && item.id.toLowerCase().includes(keyword)) ||
                   (item.name && item.name.toLowerCase().includes(keyword));
          });
        },

        resetSearch() {
          $this.search.keyword = "";
          $this.experimentData.filteredList = [...$this.experimentData.list];
        }
      };
    },

    // 版本相关方法
    versionMethods() {
      const $this = this;
      return {
        showVersions(experiment) {
          $this.versionData.currentExperiment = experiment;
          $this.versionData.list = experiment.versionList || [];
          $this.versionData.dialog = true;
        },

        showAddVersionDialog(experimentName) {
          $this.addVersion.form = {
            name: '',
            description: '',
            enable: true,
            schoolIds: [],
            experimentId: $this.versionData.currentExperiment.id,
            experimentName:experimentName,
          };

          $this.addVersion.dialog = true;

          if ($this.$refs.addVersionForm) {
            $this.$refs.addVersionForm.resetFields();
          }
        },

        submitAddVersion() {
          $this.$refs.addVersionForm.validate(async (valid) => {
            if (valid) {
              // 检查版本名称是否为"主版本"
              if ($this.addVersion.form.name === '主版本') {
                $this.$message.error('版本名称不能为"主版本"');
                return;
              }

              try {
                $this.addVersion.loading = true;

                const params = {
                  experimentId: $this.addVersion.form.experimentId,
                  name: $this.addVersion.form.name,
                  description: $this.addVersion.form.description,
                  enable: $this.addVersion.form.enable,
                  schoolIds: $this.addVersion.form.schoolIds,
                  experimentName:$this.addVersion.form.experimentName
                };

                const res = await ExperimentVersionModel.addOrEdit(params);

                if (res && res.code === "000000") {
                  $this.$message.success('新增版本成功');
                  $this.addVersion.dialog = false;

                  // 重新获取实验列表以更新数据
                  await $this.getExperimentList();

                  // 如果当前有打开的实验详情，重新获取该实验的数据
                  if ($this.versionData.currentExperiment.id) {
                    const updatedExperiment = $this.experimentData.list.find(
                      item => item.id === $this.versionData.currentExperiment.id
                    );

                    if (updatedExperiment) {
                      $this.versionData.currentExperiment = updatedExperiment;
                      $this.versionData.list = updatedExperiment.versionList || [];
                    }
                  }
                } else {
                  $this.$message.error(res?.msg || '新增版本失败');
                }
              } catch (error) {
                console.error('新增版本失败', error);
                $this.$message.error('新增版本失败');
              } finally {
                $this.addVersion.loading = false;
              }
            }
          });
        },

        editVersion(version) {
          $this.editVersion.currentVersionId = version.experimentVersionId;
          $this.editVersion.form = {
            experimentVersionId: version.experimentVersionId,
            name: version.name,
            description: version.description || '',
            enable: version.enable,
            schoolIds: version.schoolIds || [],
            experimentId: $this.versionData.currentExperiment.id,
            asMainVersion: version.asMainVersion,
            experimentName:version.experimentName,
          };

          $this.editVersion.dialog = true;

          // 重置表单验证
          if ($this.$refs.editVersionForm) {
            $this.$refs.editVersionForm.resetFields();
          }
        },

        submitEditVersion() {
          $this.$refs.editVersionForm.validate(async (valid) => {
            if (valid) {
              // 检查版本名称是否为"主版本"
              if ($this.editVersion.form.name === '主版本' && $this.addVersion.form.asMainVersion===false) {
                $this.$message.error('版本名称不能为"主版本1"');
                return;
              }

              try {
                $this.editVersion.loading = true;

                const params = {
                  experimentVersionId: $this.editVersion.form.experimentVersionId,
                  experimentId: $this.editVersion.form.experimentId,
                  name: $this.editVersion.form.name,
                  description: $this.editVersion.form.description,
                  enable: $this.editVersion.form.enable,
                  schoolIds: $this.editVersion.form.schoolIds
                };

                const res = await ExperimentVersionModel.addOrEdit(params);

                if (res && res.code === "000000") {
                  $this.$message.success('编辑版本成功');
                  $this.editVersion.dialog = false;

                  // 重新获取实验列表以更新数据
                  await $this.getExperimentList();

                  // 更新当前实验的版本列表
                  if ($this.versionData.currentExperiment.id) {
                    const updatedExperiment = $this.experimentData.list.find(
                      item => item.id === $this.versionData.currentExperiment.id
                    );

                    if (updatedExperiment) {
                      $this.versionData.currentExperiment = updatedExperiment;
                      $this.versionData.list = updatedExperiment.versionList || [];
                    }
                  }
                } else {
                  $this.$message.error(res?.msg || '编辑版本失败');
                }
              } catch (error) {
                console.error('编辑版本失败', error);
                $this.$message.error('编辑版本失败');
              } finally {
                $this.editVersion.loading = false;
              }
            }
          });
        }
      };
    }
  }
};
</script>

<style scoped>
.header-container {
  padding-bottom: 20px;
}

.filter-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.filter-item {
  margin-right: 10px;
}

.version-list {
  margin-top: 5px;
}

.experiment-info {
  margin-bottom: 20px;
}

.school-count {
  color: #409EFF;
  cursor: pointer;
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.school-item {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.school-item:last-child {
  border-bottom: none;
}
</style>
