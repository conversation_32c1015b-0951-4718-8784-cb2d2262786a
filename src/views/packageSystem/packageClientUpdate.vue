<script setup>

</script>

<template>
  <div class="app-container">
    <!-- 全局加载指示器 -->
    <div class="global-loading-mask" v-if="globalLoading">
      <div class="global-loading-container">
        <i class="el-icon-loading"></i>
        <p>数据加载中，请稍候...</p>
      </div>
    </div>

    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success" @click="ListMethods().clickAddBtn()">新增客户端更新版本
          </el-button>
        </div>
      </div>
    </div>
    <!--列表-->
    <el-table class="lists" :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="更新ID" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.packageClientUpdateId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="版本号" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.versionNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户端类型" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.type === 'school' ? 'success' : 'primary'">
            {{ scope.row.type === 'school' ? '学校端' : scope.row.type === 'teacher' ? '教师端' : scope.row.type }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="更新说明" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.type === 'school'">
            <div v-if="scope.row.updateConfig && scope.row.updateConfig.schoolUpdateDes">
              <el-tooltip :content="scope.row.updateConfig.schoolUpdateDes" placement="top">
                <span>学校端: {{ scope.row.updateConfig.schoolUpdateDes.length > 15 ? scope.row.updateConfig.schoolUpdateDes.substring(0, 15) + '...' : scope.row.updateConfig.schoolUpdateDes }}</span>
              </el-tooltip>
            </div>
            <div v-if="scope.row.updateConfig && scope.row.updateConfig.studentClientUpdateDes" style="margin-top: 5px;">
              <el-tooltip :content="scope.row.updateConfig.studentClientUpdateDes" placement="top">
                <span style="color: #67C23A;">学生端: {{ scope.row.updateConfig.studentClientUpdateDes.length > 15 ? scope.row.updateConfig.studentClientUpdateDes.substring(0, 15) + '...' : scope.row.updateConfig.studentClientUpdateDes }}</span>
              </el-tooltip>
            </div>
          </div>
          <div v-else-if="scope.row.type === 'teacher'">
            <div v-if="scope.row.updateConfig && scope.row.updateConfig.teacherUpdateDes">
              <el-tooltip :content="scope.row.updateConfig.teacherUpdateDes" placement="top">
                <span>{{ scope.row.updateConfig.teacherUpdateDes.length > 20 ? scope.row.updateConfig.teacherUpdateDes.substring(0, 20) + '...' : scope.row.updateConfig.teacherUpdateDes }}</span>
              </el-tooltip>
            </div>
          </div>
          <span v-if="!scope.row.updateConfig || Object.keys(scope.row.updateConfig).length === 0">暂无</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column label="文件数量" align="center" width="100">
        <template slot-scope="scope">
          <div v-if="scope.row.type === 'school'">
            <el-tag size="mini" type="success">{{ getSchoolFileCount(scope.row.updateConfig) }}个文件</el-tag>
          </div>
          <div v-else-if="scope.row.type === 'teacher'">
            <el-tag size="mini" type="info">{{ getTeacherFileCount(scope.row.updateConfig) }}个文件</el-tag>
          </div>
          <span v-else>0个文件</span>
        </template>
      </el-table-column>
      <el-table-column label="推送状态" align="center" width="120">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isPushed ? 'success' : 'warning'" size="small">
            {{ scope.row.isPushed ? '已推送' : '未推送' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="280"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="success" size="mini" round
                     style="margin: 5px 2px;"
                     @click="ListMethods().clickEditBtn(scope.row,scope.$index)">详情
          </el-button>
          <el-button v-if="!scope.row.isPushed" type="primary" size="mini" round
                     style="margin: 5px 2px;"
                     @click="ListMethods().clickConfirmPushBtn(scope.row, scope.$index)">
            确认推送
          </el-button>
          <el-tag v-else type="success" size="small" style="margin: 5px 2px;">
            已推送
          </el-tag>
          <el-button type="danger" size="mini" round
                     style="margin: 5px 2px;"
                     :disabled="scope.row.isPushed"
                     @click="ListMethods().clickDeleteBtn(scope.row)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--详情弹窗-->
    <el-dialog
      :title="clientUpdateInfo.title"
      :visible.sync="clientUpdateInfo.dialog"
      append-to-body
      width="1200px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="150px" ref="clientUpdateInfoForm" :model="clientUpdateInfo.edit" :rules="clientUpdateInfo.formRules">
          <el-form-item label="版本号:" prop="versionNumber">
            <el-input v-model="clientUpdateInfo.edit.versionNumber" placeholder="请输入版本号，格式如：25052901" maxlength="8">
              <template slot="prepend">版本号</template>
            </el-input>
            <div style="margin-top: 5px; font-size: 12px; color: #909399;">
              <i class="el-icon-info"></i>
              版本号格式：8位数字，如 25052901（年月日版本）
            </div>
          </el-form-item>
          <el-form-item label="客户端类型:" prop="type">
            <el-select v-model="clientUpdateInfo.edit.type" placeholder="请选择客户端类型" style="width: 100%;">
              <el-option label="学校端" value="school"></el-option>
              <el-option label="教师端" value="teacher"></el-option>
            </el-select>
          </el-form-item>

          <!-- 学校端特有的配置 -->
          <div v-if="clientUpdateInfo.edit.type === 'school'">
            <h3 style="margin: 20px 0 10px 0; color: #409EFF;">学校端配置</h3>

            <el-tabs v-model="clientUpdateInfo.activeTab" type="border-card">
              <!-- 学校端配置Tab -->
              <el-tab-pane label="学校端配置" name="school">
                <!-- 学校端更新说明 -->
                <el-form-item label="学校端更新说明:" prop="updateConfig.schoolUpdateDes">
                  <el-input type="textarea" :rows="3" v-model="clientUpdateInfo.edit.updateConfig.schoolUpdateDes" placeholder="请输入学校端更新说明"></el-input>
                </el-form-item>

                <!-- 学校端Web前端包上传 -->
            <el-form-item label="学校端Web前端包:" prop="webDistZipFile" v-if="clientUpdateInfo.type === 'add'">
              <div class="upload-notice-container">
                <div class="upload-notice-header">
                  <i class="el-icon-info"></i>
                  <span>学校端Web前端包上传要求（可选）</span>
                </div>
                <div class="upload-notice-content">
                  <div class="upload-notice-item">
                    <i class="el-icon-document"></i>
                    <span><strong>文件格式：</strong>必须是 ZIP 压缩文件</span>
                  </div>
                  <div class="upload-notice-item">
                    <i class="el-icon-warning"></i>
                    <span><strong>内容要求：</strong>包含学校端前端的完整构建文件</span>
                  </div>
                  <div class="upload-notice-item">
                    <i class="el-icon-success"></i>
                    <span><strong>可选项：</strong>如不上传，此次更新不包含前端更新</span>
                  </div>
                </div>
              </div>

              <el-upload
                class="upload-demo"
                action="dev"
                :show-file-list="false"
                :http-request="UploadMethods().uploadWebDistRequest"
                :on-success="UploadMethods().onWebDistSuccess"
                :on-error="UploadMethods().onWebDistError"
                :before-upload="UploadMethods().beforeWebDistUpload">
                <el-button size="small" type="primary">点击上传学校端Web前端包（可选）</el-button>
              </el-upload>
              <div v-if="clientUpdateInfo.webDistUploadProgress > 0" class="progress-container">
                <el-progress
                  :percentage="clientUpdateInfo.webDistUploadProgress"
                  :stroke-width="12"
                  :color="customColorMethod">
                </el-progress>
              </div>
            </el-form-item>

            <!-- 学校端Linux版本上传 -->
            <el-form-item label="学校端Linux版本:" prop="schoolClientLinuxFile" v-if="clientUpdateInfo.type === 'add'">
              <div class="upload-notice-container">
                <div class="upload-notice-header">
                  <i class="el-icon-info"></i>
                  <span>学校端Linux版本上传要求</span>
                </div>
                <div class="upload-notice-content">
                  <div class="upload-notice-item">
                    <i class="el-icon-document"></i>
                    <span><strong>文件格式：</strong>Linux x86可执行文件</span>
                  </div>
                  <div class="upload-notice-item">
                    <i class="el-icon-warning"></i>
                    <span><strong>内容要求：</strong>学校端Linux版本的完整可执行文件</span>
                  </div>
                </div>
              </div>

              <el-upload
                class="upload-demo"
                action="dev"
                :show-file-list="false"
                :http-request="UploadMethods().uploadSchoolLinuxRequest"
                :on-success="UploadMethods().onSchoolLinuxSuccess"
                :on-error="UploadMethods().onSchoolLinuxError"
                :before-upload="UploadMethods().beforeSchoolLinuxUpload">
                <el-button size="small" type="warning">点击上传学校端Linux版本</el-button>
              </el-upload>
              <div v-if="clientUpdateInfo.schoolLinuxUploadProgress > 0" class="progress-container">
                <el-progress
                  :percentage="clientUpdateInfo.schoolLinuxUploadProgress"
                  :stroke-width="12"
                  :color="customColorMethod">
                </el-progress>
              </div>
            </el-form-item>

            <!-- 学校端Windows版本上传 -->
            <el-form-item label="学校端Windows版本:" prop="schoolClientWindowsFile" v-if="clientUpdateInfo.type === 'add'">
              <div class="upload-notice-container">
                <div class="upload-notice-header">
                  <i class="el-icon-info"></i>
                  <span>学校端Windows版本上传要求</span>
                </div>
                <div class="upload-notice-content">
                  <div class="upload-notice-item">
                    <i class="el-icon-document"></i>
                    <span><strong>文件格式：</strong>Windows exe可执行文件</span>
                  </div>
                  <div class="upload-notice-item">
                    <i class="el-icon-warning"></i>
                    <span><strong>内容要求：</strong>学校端Windows版本的完整可执行文件</span>
                  </div>
                </div>
              </div>

              <el-upload
                class="upload-demo"
                action="dev"
                :show-file-list="false"
                :http-request="UploadMethods().uploadSchoolWindowsRequest"
                :on-success="UploadMethods().onSchoolWindowsSuccess"
                :on-error="UploadMethods().onSchoolWindowsError"
                :before-upload="UploadMethods().beforeSchoolWindowsUpload">
                <el-button size="small" type="danger">点击上传学校端Windows版本</el-button>
              </el-upload>
              <div v-if="clientUpdateInfo.schoolWindowsUploadProgress > 0" class="progress-container">
                <el-progress
                  :percentage="clientUpdateInfo.schoolWindowsUploadProgress"
                  :stroke-width="12"
                  :color="customColorMethod">
                </el-progress>
              </div>
            </el-form-item>
              </el-tab-pane>

              <!-- 学生端配置Tab -->
              <el-tab-pane label="学生端配置（可选）" name="student">
                <div class="optional-notice" style="margin-bottom: 15px; padding: 10px; background-color: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px;">
                  <i class="el-icon-info" style="color: #409EFF;"></i>
                  <span style="color: #409EFF; margin-left: 5px;"><strong>提示：</strong>学生端配置为可选项，如果不上传学生端文件，则此次更新仅针对学校端。</span>
                </div>

                <!-- 学生端更新说明 -->
                <el-form-item label="学生端更新说明:" prop="updateConfig.studentClientUpdateDes">
                  <el-input type="textarea" :rows="3" v-model="clientUpdateInfo.edit.updateConfig.studentClientUpdateDes" placeholder="请输入学生端更新说明（可选）"></el-input>
                </el-form-item>

                <!-- 学生端客户端包上传 -->
                <el-form-item label="学生端客户端包:" prop="studentClientFile" v-if="clientUpdateInfo.type === 'add'">
                  <div class="upload-notice-container">
                    <div class="upload-notice-header">
                      <i class="el-icon-info"></i>
                      <span>学生端客户端包上传要求（可选）</span>
                    </div>
                    <div class="upload-notice-content">
                      <div class="upload-notice-item">
                        <i class="el-icon-document"></i>
                        <span><strong>文件格式：</strong>必须是Windows exe可执行文件</span>
                      </div>
                      <div class="upload-notice-item">
                        <i class="el-icon-warning"></i>
                        <span><strong>内容要求：</strong>学生端客户端的完整exe安装包</span>
                      </div>
                      <div class="upload-notice-item">
                        <i class="el-icon-success"></i>
                        <span><strong>可选项：</strong>如不上传，此次更新仅针对学校端</span>
                      </div>
                    </div>
                  </div>

                  <el-upload
                    class="upload-demo"
                    action="dev"
                    :show-file-list="false"
                    :http-request="UploadMethods().uploadStudentClientRequest"
                    :on-success="UploadMethods().onStudentClientSuccess"
                    :on-error="UploadMethods().onStudentClientError"
                    :before-upload="UploadMethods().beforeStudentClientUpload">
                    <el-button size="small" type="success">点击上传学生端客户端包（可选）</el-button>
                  </el-upload>
                  <div v-if="clientUpdateInfo.studentClientUploadProgress > 0" class="progress-container">
                    <el-progress
                      :percentage="clientUpdateInfo.studentClientUploadProgress"
                      :stroke-width="12"
                      :color="customColorMethod">
                    </el-progress>
                  </div>
                </el-form-item>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 教师端特有的配置 -->
          <div v-if="clientUpdateInfo.edit.type === 'teacher'">
            <h3 style="margin: 20px 0 10px 0; color: #409EFF;">教师端配置</h3>

            <!-- 教师端更新说明 -->
            <el-form-item label="教师端更新说明:" prop="teacherUpdateDes">
              <el-input type="textarea" :rows="4" v-model="clientUpdateInfo.edit.updateConfig.teacherUpdateDes" placeholder="请输入教师端更新说明"></el-input>
            </el-form-item>

            <!-- 教师端客户端文件上传 -->
            <el-form-item label="教师端客户端文件:" prop="teacherClientFile" v-if="clientUpdateInfo.type === 'add'">
              <div class="upload-notice-container">
                <div class="upload-notice-header">
                  <i class="el-icon-info"></i>
                  <span>教师端客户端文件上传要求</span>
                </div>
                <div class="upload-notice-content">
                  <div class="upload-notice-item">
                    <i class="el-icon-document"></i>
                    <span><strong>文件格式：</strong>必须是可执行文件或安装包</span>
                  </div>
                  <div class="upload-notice-item">
                    <i class="el-icon-warning"></i>
                    <span><strong>内容要求：</strong>教师端客户端的完整安装包</span>
                  </div>
                </div>
              </div>

              <el-upload
                class="upload-demo"
                action="dev"
                :show-file-list="false"
                :http-request="UploadMethods().uploadTeacherClientRequest"
                :on-success="UploadMethods().onTeacherClientSuccess"
                :on-error="UploadMethods().onTeacherClientError"
                :before-upload="UploadMethods().beforeTeacherClientUpload">
                <el-button size="small" type="info">点击上传教师端客户端文件</el-button>
              </el-upload>
              <div v-if="clientUpdateInfo.teacherClientUploadProgress > 0" class="progress-container">
                <el-progress
                  :percentage="clientUpdateInfo.teacherClientUploadProgress"
                  :stroke-width="12"
                  :color="customColorMethod">
                </el-progress>
              </div>
            </el-form-item>
          </div>

          <!-- 显示更新配置信息 -->
          <el-form-item label="更新配置:" v-if="clientUpdateInfo.edit.updateConfig && Object.keys(clientUpdateInfo.edit.updateConfig).length > 0">
            <div class="config-info-container">
              <!-- 学校端配置信息 -->
              <div v-if="clientUpdateInfo.edit.type === 'school'">
                <div v-if="clientUpdateInfo.edit.updateConfig.schoolUpdateDes" class="config-info-item">
                  <span class="config-info-label">学校端更新说明:</span>
                  <span>{{ clientUpdateInfo.edit.updateConfig.schoolUpdateDes }}</span>
                </div>
                <div v-if="clientUpdateInfo.edit.updateConfig.webDistZipDownloadUrl" class="config-info-item">
                  <span class="config-info-label">学校端Web前端包:</span>
                  <el-link type="primary" :href="clientUpdateInfo.edit.updateConfig.webDistZipDownloadUrl" target="_blank">点击下载</el-link>
                </div>
                <div v-if="clientUpdateInfo.edit.updateConfig.webDistZipFileHash" class="config-info-item">
                  <span class="config-info-label">学校端Web前端包哈希:</span>
                  <span>{{ clientUpdateInfo.edit.updateConfig.webDistZipFileHash }}</span>
                </div>
                <div v-if="clientUpdateInfo.edit.updateConfig.studentClientDownloadUrl" class="config-info-item">
                  <span class="config-info-label">学生端客户端包:</span>
                  <el-link type="success" :href="clientUpdateInfo.edit.updateConfig.studentClientDownloadUrl" target="_blank">点击下载</el-link>
                </div>
                <div v-if="clientUpdateInfo.edit.updateConfig.studentClientUpdateDes" class="config-info-item">
                  <span class="config-info-label">学生端更新说明:</span>
                  <span>{{ clientUpdateInfo.edit.updateConfig.studentClientUpdateDes }}</span>
                </div>
                <div v-if="clientUpdateInfo.edit.updateConfig.studentClientFileHash" class="config-info-item">
                  <span class="config-info-label">学生端文件哈希:</span>
                  <span>{{ clientUpdateInfo.edit.updateConfig.studentClientFileHash }}</span>
                </div>
                <div v-if="clientUpdateInfo.edit.updateConfig.schoolClientLinuxDownloadUrl" class="config-info-item">
                  <span class="config-info-label">学校端Linux版本:</span>
                  <el-link type="warning" :href="clientUpdateInfo.edit.updateConfig.schoolClientLinuxDownloadUrl" target="_blank">点击下载</el-link>
                </div>
                <div v-if="clientUpdateInfo.edit.updateConfig.schoolClientLinuxFileHash" class="config-info-item">
                  <span class="config-info-label">学校端Linux哈希:</span>
                  <span>{{ clientUpdateInfo.edit.updateConfig.schoolClientLinuxFileHash }}</span>
                </div>
                <div v-if="clientUpdateInfo.edit.updateConfig.schoolClientWindowsDownloadUrl" class="config-info-item">
                  <span class="config-info-label">学校端Windows版本:</span>
                  <el-link type="danger" :href="clientUpdateInfo.edit.updateConfig.schoolClientWindowsDownloadUrl" target="_blank">点击下载</el-link>
                </div>
                <div v-if="clientUpdateInfo.edit.updateConfig.schoolClientWindowsFileHash" class="config-info-item">
                  <span class="config-info-label">学校端Windows哈希:</span>
                  <span>{{ clientUpdateInfo.edit.updateConfig.schoolClientWindowsFileHash }}</span>
                </div>
              </div>

              <!-- 教师端配置信息 -->
              <div v-if="clientUpdateInfo.edit.type === 'teacher'">
                <div v-if="clientUpdateInfo.edit.updateConfig.teacherUpdateDes" class="config-info-item">
                  <span class="config-info-label">教师端更新说明:</span>
                  <span>{{ clientUpdateInfo.edit.updateConfig.teacherUpdateDes }}</span>
                </div>
                <div v-if="clientUpdateInfo.edit.updateConfig.teacherClientDownloadUrl" class="config-info-item">
                  <span class="config-info-label">教师端客户端包:</span>
                  <el-link type="info" :href="clientUpdateInfo.edit.updateConfig.teacherClientDownloadUrl" target="_blank">点击下载</el-link>
                </div>
                <div v-if="clientUpdateInfo.edit.updateConfig.teacherClientFileHash" class="config-info-item">
                  <span class="config-info-label">教师端文件哈希:</span>
                  <span>{{ clientUpdateInfo.edit.updateConfig.teacherClientFileHash }}</span>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="clientUpdateInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="ClientUpdateInfoMethods().clickAddBtn()"
                   :loading="clientUpdateInfo.loading"
                   :disabled="clientUpdateInfo.uploading"
                   v-if="clientUpdateInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="ClientUpdateInfoMethods().clickEditBtn()" :loading="clientUpdateInfo.loading"
                   v-if="clientUpdateInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat, timeFormat} from "@/filters";
import {date_format, getQuery, objectToLVArr, searchWordFiltration} from "@/utils/common";
import {PackageClientUpdateModel} from "@/model/packageSystem/PackageClientUpdateModel";
import {validateMaxLength} from "@/utils/validate";
import {msg_success} from "@/utils/ele_component";
import {BaseUploadModel} from "@/model/BaseUploadModel";
// 导入xxHash3 WASM
import xxhash from 'xxhash-wasm';

export default {
  name: "packageClientUpdate",
  components: {
    ListSearchFilter
  },
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission,
      adminUserId: state => state.user.id
    }),
    uploadHeaders() {
      return {
        Authorization: localStorage.getItem('token')
      }
    },
    uploadAction() {
      return process.env.VUE_APP_BASE_API + '/api/upload'
    },
    customColorMethod() {
      return function(percentage) {
        if (percentage < 30) {
          return '#909399';
        } else if (percentage < 70) {
          return '#e6a23c';
        } else {
          return '#67c23a';
        }
      }
    }
  },
  filters: {dateFormat, timeFormat},
  data: function () {
    let $this = this;
    return {
      // 全局加载状态
      globalLoading: false,
      window: window,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '更新ID',
              key: 'packageClientUpdateId',
              value: '',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '版本号',
              key: 'versionNumber',
              value: '',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '更新内容',
              key: 'content',
              value: '',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            },
          ],
          filter: [
            {
              type: 'select',
              label: '客户端类型',
              key: 'type',
              hidden: false,
              value: '',
              data: [
                {label: '学校端', value: 'school'},
                {label: '教师端', value: 'teacher'}
              ],
              dataObject: {
                'school': '学校端',
                'teacher': '教师端'
              },
              dataOrigin: [],
              change: function (value) {

              }
            },
            {
              type: 'select',
              label: '推送状态',
              key: 'isPushed',
              hidden: false,
              value: '',
              data: [
                {label: '已推送', value: true},
                {label: '未推送', value: false}
              ],
              dataObject: {
                true: '已推送',
                false: '未推送'
              },
              dataOrigin: [],
              change: function (value) {

              }
            }
          ]
        }
      },
      // 客户端更新信息
      clientUpdateInfo: {
        dialog: false,
        title: "新增客户端更新版本",
        type: "add",
        loading: false,
        uploading: false,
        uploadProgress: 0,
        webDistUploading: false,
        webDistUploadProgress: 0,
        studentClientUploading: false,
        studentClientUploadProgress: 0,
        schoolLinuxUploading: false,
        schoolLinuxUploadProgress: 0,
        schoolWindowsUploading: false,
        schoolWindowsUploadProgress: 0,
        teacherClientUploading: false,
        teacherClientUploadProgress: 0,
        activeTab: 'school', // 默认显示学校端Tab
        edit: {
          extraInfo: {},
          updateConfig: {}
        },
        // 输入检测
        formRules: {
          'versionNumber': {
            required: true,
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('请输入版本号'));
              } else if (!/^\d{8}$/.test(value)) {
                callback(new Error('版本号必须是8位数字，格式如：25052901'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          },
          'type': {required: true, message: '请选择客户端类型', trigger: 'change'},
          'updateConfig.schoolUpdateDes': {validator: (r, v, c) => validateMaxLength(r, v, c, 500, "学校端更新说明"), trigger: 'blur'},
          'updateConfig.studentClientUpdateDes': {
            validator: (rule, value, callback) => {
              // 对于学校端类型，学生端更新说明是可选的
              if ($this.clientUpdateInfo.edit.type === 'school') {
                // 如果有值，则验证长度
                if (value && value.length > 500) {
                  callback(new Error('最多输入500个字，当前已输入' + value.length + "个字"));
                } else {
                  callback();
                }
              } else {
                // 对于其他类型，使用原有的验证逻辑
                validateMaxLength(rule, value, callback, 500, "学生端更新说明");
              }
            },
            trigger: 'blur'
          },
          'updateConfig.teacherUpdateDes': {validator: (r, v, c) => validateMaxLength(r, v, c, 500, "教师端更新说明"), trigger: 'blur'},
        },
      },
    }
  },
  async mounted() {
    // 显示全局加载指示器
    this.globalLoading = true;
    try {
      // 获取列表数据
      await this.ListMethods().getList(0, 20, {})
    } catch (error) {
      console.error('页面初始化失败', error);
      this.$message.error('页面初始化失败，请刷新重试');
    } finally {
      // 隐藏全局加载指示器
      this.globalLoading = false;
    }
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 点击搜索重置按钮
        clickCleanBtn() {

        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          [list, $this.lists.pages] = await PackageClientUpdateModel.getPageList(page, size, "", query)
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page - 1, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击添加按钮
        async clickAddBtn() {
          $this.clientUpdateInfo.dialog = true;
          $this.clientUpdateInfo.type = "add"
          $this.clientUpdateInfo.title = "新增客户端更新版本"
          $this.clientUpdateInfo.edit = {
            extraInfo: {},
            updateConfig: {}
          };

          // 清空上传相关状态
          $this.clientUpdateInfo.uploading = false;
          $this.clientUpdateInfo.uploadProgress = 0;
          $this.clientUpdateInfo.webDistUploading = false;
          $this.clientUpdateInfo.webDistUploadProgress = 0;
          $this.clientUpdateInfo.studentClientUploading = false;
          $this.clientUpdateInfo.studentClientUploadProgress = 0;
          $this.clientUpdateInfo.schoolLinuxUploading = false;
          $this.clientUpdateInfo.schoolLinuxUploadProgress = 0;
          $this.clientUpdateInfo.schoolWindowsUploading = false;
          $this.clientUpdateInfo.schoolWindowsUploadProgress = 0;
          $this.clientUpdateInfo.teacherClientUploading = false;
          $this.clientUpdateInfo.teacherClientUploadProgress = 0;
          $this.clientUpdateInfo.activeTab = 'school'; // 重置为学校端Tab

          setTimeout(() => {
            $this.$refs['clientUpdateInfoForm'].clearValidate()
          }, 300)
        },
        // 点击列表的编辑按钮
        clickEditBtn(edit, $index) {
          $this.clientUpdateInfo.edit = JSON.parse(JSON.stringify(edit))
          $this.clientUpdateInfo.type = 'edit'
          $this.clientUpdateInfo.title = "修改客户端更新版本"
          $this.clientUpdateInfo.$index = $index

          $this.clientUpdateInfo.dialog = true
          setTimeout(() => {
            $this.$refs['clientUpdateInfoForm'].clearValidate()
          }, 300)
        },
        // 点击确认推送按钮
        async clickConfirmPushBtn(row, index) {
          // 显示详细的确认对话框，强调不可撤回
          $this.$confirm(
            '确认推送后，下级客户端将能够接收到此更新版本。\n\n⚠️ 重要提醒：推送操作不可撤回，请确认版本已经过充分测试！',
            '确认推送版本',
            {
              confirmButtonText: '确认推送',
              cancelButtonText: '取消',
              type: 'warning',
              dangerouslyUseHTMLString: false,
              customClass: 'confirm-push-dialog'
            }
          ).then(async () => {
            // 更新推送状态为已推送
            const updateData = {
              ...row,
              isPushed: true
            };

            // 转换版本号为数值类型
            if (updateData.versionNumber) {
              updateData.versionNumber = parseInt(updateData.versionNumber);
            }

            if (await PackageClientUpdateModel.addOrEdit(updateData).catch(err => {
              console.error('确认推送失败', err);
            })) {
              msg_success('推送确认成功，下级客户端现在可以接收此更新');
              // 更新本地数据
              $this.$set($this.lists.list[index], 'isPushed', true);
            }
          }).catch(() => {
            $this.$message({
              type: 'info',
              message: '已取消推送操作'
            });
          });
        },
        // 点击删除按钮
        async clickDeleteBtn(row) {
          // 检查推送状态
          if (row.isPushed) {
            $this.$message.warning('已推送的版本不允许删除。推送操作不可撤回，因此已推送版本将永久保留。');
            return;
          }

          $this.$confirm('确定要删除这个客户端更新版本吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(async () => {
            if (await PackageClientUpdateModel.deleteOne(row.packageClientUpdateId)) {
              msg_success('删除成功')
              this.getList($this.lists.pages.number - 1, $this.lists.pages.size, $this.lists.query)
            }
          }).catch(() => {
            $this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
        },
      }
    },
    // 客户端更新信息Methods
    ClientUpdateInfoMethods() {
      let $this = this;
      return {
        // 点击提交新增按钮
        clickAddBtn() {
          // 检查上传状态
          if ($this.clientUpdateInfo.uploading || $this.clientUpdateInfo.webDistUploading || $this.clientUpdateInfo.studentClientUploading || $this.clientUpdateInfo.schoolLinuxUploading || $this.clientUpdateInfo.schoolWindowsUploading || $this.clientUpdateInfo.teacherClientUploading) {
            $this.$message.warning('文件正在上传中，请稍候...');
            return;
          }

          $this.$refs['clientUpdateInfoForm'].validate(async validate => {
            if (validate) {
              $this.clientUpdateInfo.loading = true

              // 对于学校端类型，需要验证必要的配置
              if ($this.clientUpdateInfo.edit.type === 'school') {
                // 检查是否至少上传了一个学校端文件
                const hasSchoolLinux = !!$this.clientUpdateInfo.edit.updateConfig.schoolClientLinuxDownloadUrl;
                const hasSchoolWindows = !!$this.clientUpdateInfo.edit.updateConfig.schoolClientWindowsDownloadUrl;
                const hasWebDist = !!$this.clientUpdateInfo.edit.updateConfig.webDistZipDownloadUrl;

                if (!hasSchoolLinux && !hasSchoolWindows && !hasWebDist) {
                  $this.$message.error('请至少上传一个学校端文件（Linux版本、Windows版本或Web前端包）');
                  $this.clientUpdateInfo.loading = false;
                  return;
                }

                // 学校端Web前端包现在是可选的
                // 学校端Linux版本现在是可选的
                // 学校端Windows版本现在是可选的
                // 学生端客户端包现在是可选的，不再强制验证
                // 只要求至少上传一个学校端相关文件即可
              } else if ($this.clientUpdateInfo.edit.type === 'teacher') {
                // 教师端需要验证教师端文件上传
                if (!$this.clientUpdateInfo.edit.updateConfig.teacherClientDownloadUrl) {
                  $this.$message.error('请上传教师端客户端文件');
                  $this.clientUpdateInfo.loading = false;
                  return;
                }
              }

              // 转换版本号为数值类型
              const submitData = JSON.parse(JSON.stringify($this.clientUpdateInfo.edit));
              if (submitData.versionNumber) {
                submitData.versionNumber = parseInt(submitData.versionNumber);
              }

              try {
                const result = await PackageClientUpdateModel.addOrEdit(submitData);
                if (result) {
                  msg_success('新增客户端更新版本成功')
                  $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                  $this.clientUpdateInfo.loading = false
                  $this.clientUpdateInfo.dialog = false
                }
              } catch (err) {
                $this.clientUpdateInfo.loading = false
                if (err.response && err.response.data && err.response.data.code === '400001') {
                  $this.$message.error(err.response.data.message || '版本号已存在，请使用其他版本号');
                } else {
                  $this.$message.error('操作失败，请重试');
                }
              }
            }
          })
        },
        // 点击修改按钮
        clickEditBtn() {
          $this.$refs['clientUpdateInfoForm'].validate(async validate => {
            if (validate) {
              $this.clientUpdateInfo.loading = true

              // 转换版本号为数值类型
              const submitData = JSON.parse(JSON.stringify($this.clientUpdateInfo.edit));
              if (submitData.versionNumber) {
                submitData.versionNumber = parseInt(submitData.versionNumber);
              }

              try {
                const result = await PackageClientUpdateModel.addOrEdit(submitData);
                if (result) {
                  msg_success('保存成功')
                  $this.ListMethods().getList($this.lists.pages.number - 1, $this.lists.pages.size, $this.lists.query)
                  $this.clientUpdateInfo.dialog = false
                  $this.clientUpdateInfo.loading = false
                }
              } catch (err) {
                $this.clientUpdateInfo.loading = false
                if (err.response && err.response.data && err.response.data.code === '400001') {
                  $this.$message.error(err.response.data.message || '版本号已存在，请使用其他版本号');
                } else {
                  $this.$message.error('操作失败，请重试');
                }
              }
            }
          })
        },
      }
    },
    // 上传方法
    UploadMethods() {
      let $this = this
      return {
        // 学校端Web前端包上传前检查
        beforeWebDistUpload(file) {
          const isZip = file.type === 'application/zip' ||
                       file.type === 'application/x-zip-compressed' ||
                       file.name.toLowerCase().endsWith('.zip');

          if (!isZip) {
            $this.$message.error('只能上传 ZIP 格式的文件！');
            return false;
          }

          $this.clientUpdateInfo.webDistUploading = true
          $this.clientUpdateInfo.webDistUploadProgress = 0

          // 异步计算哈希值
          $this.calculateFileHashAsync(file, 'webDist')

          return true
        },
        // 学校端Web前端包上传成功
        onWebDistSuccess(response) {
          if (response.code === 20000) {
            $this.$set($this.clientUpdateInfo.edit.updateConfig, 'webDistZipDownloadUrl', response.data.url);
            msg_success('学校端Web前端包上传成功');
          } else {
            $this.$message.error('学校端Web前端包上传失败');
          }
          $this.clientUpdateInfo.webDistUploading = false;
          $this.clientUpdateInfo.webDistUploadProgress = 0;
        },
        // 学校端Web前端包上传失败
        onWebDistError() {
          $this.$message.error('学校端Web前端包上传失败')
          $this.clientUpdateInfo.webDistUploading = false
          $this.clientUpdateInfo.webDistUploadProgress = 0
        },
        // 学校端Web前端包自定义上传请求
        async uploadWebDistRequest(upload) {
          let file = upload.file
          return await new Promise((resolve, reject) => {
            BaseUploadModel.qiNiuUploadForPackageSystem(file, 'school-web-dist', 'web-frontend', {
              next: (result) => {
                $this.clientUpdateInfo.webDistUploadProgress = parseInt(result.total.percent);
              },
              error: (errResult) => {
                console.log(errResult)
                reject(errResult)
                $this.$message.error('上传失败')
                $this.clientUpdateInfo.webDistUploading = false
                $this.clientUpdateInfo.webDistUploadProgress = 0
              },
              complete: (result) => {
                let domain = BaseUploadModel.getBucketDomain("zyhd-package-system")
                let url = domain + '/' + result.key

                let response = {
                  code: 20000,
                  data: {
                    url: url,
                    hash: $this.clientUpdateInfo.edit.updateConfig.webDistZipFileHash,
                    originalName: file.name,
                    fileSize: file.size,
                    fileType: file.type
                  }
                }

                $this.clientUpdateInfo.webDistUploadProgress = 100
                setTimeout(() => {
                  $this.clientUpdateInfo.webDistUploadProgress = 0
                  $this.clientUpdateInfo.webDistUploading = false
                }, 500)

                resolve(response)
                upload.onSuccess(response)
              }
            })
          })
        },
        // 学生端客户端包上传前检查
        beforeStudentClientUpload(file) {
          const isExe = file.name.toLowerCase().endsWith('.exe');

          if (!isExe) {
            $this.$message.error('学生端客户端包必须是 exe 格式的文件！');
            return false;
          }

          $this.clientUpdateInfo.studentClientUploading = true
          $this.clientUpdateInfo.studentClientUploadProgress = 0

          // 异步计算哈希值
          $this.calculateFileHashAsync(file, 'studentClient')

          return true
        },
        // 学生端客户端包上传成功
        onStudentClientSuccess(response) {
          if (response.code === 20000) {
            $this.$set($this.clientUpdateInfo.edit.updateConfig, 'studentClientDownloadUrl', response.data.url);
            msg_success('学生端客户端包上传成功');
          } else {
            $this.$message.error('学生端客户端包上传失败');
          }
          $this.clientUpdateInfo.studentClientUploading = false;
          $this.clientUpdateInfo.studentClientUploadProgress = 0;
        },
        // 学生端客户端包上传失败
        onStudentClientError() {
          $this.$message.error('学生端客户端包上传失败')
          $this.clientUpdateInfo.studentClientUploading = false
          $this.clientUpdateInfo.studentClientUploadProgress = 0
        },
        // 学生端客户端包自定义上传请求
        async uploadStudentClientRequest(upload) {
          let file = upload.file
          return await new Promise((resolve, reject) => {
            BaseUploadModel.qiNiuUploadForPackageSystem(file, 'student-client', 'client-app', {
              next: (result) => {
                $this.clientUpdateInfo.studentClientUploadProgress = parseInt(result.total.percent);
              },
              error: (errResult) => {
                console.log(errResult)
                reject(errResult)
                $this.$message.error('上传失败')
                $this.clientUpdateInfo.studentClientUploading = false
                $this.clientUpdateInfo.studentClientUploadProgress = 0
              },
              complete: (result) => {
                let domain = BaseUploadModel.getBucketDomain("zyhd-package-system")
                let url = domain + '/' + result.key

                let response = {
                  code: 20000,
                  data: {
                    url: url,
                    hash: $this.clientUpdateInfo.edit.updateConfig.studentClientFileHash,
                    originalName: file.name,
                    fileSize: file.size,
                    fileType: file.type
                  }
                }

                $this.clientUpdateInfo.studentClientUploadProgress = 100
                setTimeout(() => {
                  $this.clientUpdateInfo.studentClientUploadProgress = 0
                  $this.clientUpdateInfo.studentClientUploading = false
                }, 500)

                resolve(response)
                upload.onSuccess(response)
              }
            })
          })
        },
        // 学校端Linux版本上传前检查
        beforeSchoolLinuxUpload(file) {
          $this.clientUpdateInfo.schoolLinuxUploading = true
          $this.clientUpdateInfo.schoolLinuxUploadProgress = 0

          // 异步计算哈希值
          $this.calculateFileHashAsync(file, 'schoolLinux')

          return true
        },
        // 学校端Linux版本上传成功
        onSchoolLinuxSuccess(response) {
          if (response.code === 20000) {
            $this.$set($this.clientUpdateInfo.edit.updateConfig, 'schoolClientLinuxDownloadUrl', response.data.url);
            msg_success('学校端Linux版本上传成功');
          } else {
            $this.$message.error('学校端Linux版本上传失败');
          }
          $this.clientUpdateInfo.schoolLinuxUploading = false;
          $this.clientUpdateInfo.schoolLinuxUploadProgress = 0;
        },
        // 学校端Linux版本上传失败
        onSchoolLinuxError() {
          $this.$message.error('学校端Linux版本上传失败')
          $this.clientUpdateInfo.schoolLinuxUploading = false
          $this.clientUpdateInfo.schoolLinuxUploadProgress = 0
        },
        // 学校端Linux版本自定义上传请求
        async uploadSchoolLinuxRequest(upload) {
          let file = upload.file
          return await new Promise((resolve, reject) => {
            BaseUploadModel.qiNiuUploadForPackageSystem(file, 'school-client-linux', 'linux-app', {
              next: (result) => {
                $this.clientUpdateInfo.schoolLinuxUploadProgress = parseInt(result.total.percent);
              },
              error: (errResult) => {
                console.log(errResult)
                reject(errResult)
                $this.$message.error('上传失败')
                $this.clientUpdateInfo.schoolLinuxUploading = false
                $this.clientUpdateInfo.schoolLinuxUploadProgress = 0
              },
              complete: (result) => {
                let domain = BaseUploadModel.getBucketDomain("zyhd-package-system")
                let url = domain + '/' + result.key

                let response = {
                  code: 20000,
                  data: {
                    url: url,
                    hash: $this.clientUpdateInfo.edit.updateConfig.schoolClientLinuxFileHash,
                    originalName: file.name,
                    fileSize: file.size,
                    fileType: file.type
                  }
                }

                $this.clientUpdateInfo.schoolLinuxUploadProgress = 100
                setTimeout(() => {
                  $this.clientUpdateInfo.schoolLinuxUploadProgress = 0
                  $this.clientUpdateInfo.schoolLinuxUploading = false
                }, 500)

                resolve(response)
                upload.onSuccess(response)
              }
            })
          })
        },
        // 学校端Windows版本上传前检查
        beforeSchoolWindowsUpload(file) {
          const isExe = file.name.toLowerCase().endsWith('.exe');

          if (!isExe) {
            $this.$message.error('学校端Windows版本必须是 exe 格式的文件！');
            return false;
          }

          $this.clientUpdateInfo.schoolWindowsUploading = true
          $this.clientUpdateInfo.schoolWindowsUploadProgress = 0

          // 异步计算哈希值
          $this.calculateFileHashAsync(file, 'schoolWindows')

          return true
        },
        // 学校端Windows版本上传成功
        onSchoolWindowsSuccess(response) {
          if (response.code === 20000) {
            $this.$set($this.clientUpdateInfo.edit.updateConfig, 'schoolClientWindowsDownloadUrl', response.data.url);
            msg_success('学校端Windows版本上传成功');
          } else {
            $this.$message.error('学校端Windows版本上传失败');
          }
          $this.clientUpdateInfo.schoolWindowsUploading = false;
          $this.clientUpdateInfo.schoolWindowsUploadProgress = 0;
        },
        // 学校端Windows版本上传失败
        onSchoolWindowsError() {
          $this.$message.error('学校端Windows版本上传失败')
          $this.clientUpdateInfo.schoolWindowsUploading = false
          $this.clientUpdateInfo.schoolWindowsUploadProgress = 0
        },
        // 学校端Windows版本自定义上传请求
        async uploadSchoolWindowsRequest(upload) {
          let file = upload.file
          return await new Promise((resolve, reject) => {
            BaseUploadModel.qiNiuUploadForPackageSystem(file, 'school-client-windows', 'windows-app', {
              next: (result) => {
                $this.clientUpdateInfo.schoolWindowsUploadProgress = parseInt(result.total.percent);
              },
              error: (errResult) => {
                console.log(errResult)
                reject(errResult)
                $this.$message.error('上传失败')
                $this.clientUpdateInfo.schoolWindowsUploading = false
                $this.clientUpdateInfo.schoolWindowsUploadProgress = 0
              },
              complete: (result) => {
                let domain = BaseUploadModel.getBucketDomain("zyhd-package-system")
                let url = domain + '/' + result.key

                let response = {
                  code: 20000,
                  data: {
                    url: url,
                    hash: $this.clientUpdateInfo.edit.updateConfig.schoolClientWindowsFileHash,
                    originalName: file.name,
                    fileSize: file.size,
                    fileType: file.type
                  }
                }

                $this.clientUpdateInfo.schoolWindowsUploadProgress = 100
                setTimeout(() => {
                  $this.clientUpdateInfo.schoolWindowsUploadProgress = 0
                  $this.clientUpdateInfo.schoolWindowsUploading = false
                }, 500)

                resolve(response)
                upload.onSuccess(response)
              }
            })
          })
        },
        // 通用文件上传前检查
        beforeUpload(file) {
          $this.clientUpdateInfo.uploading = true
          $this.clientUpdateInfo.uploadProgress = 0

          // 异步计算哈希值
          $this.calculateFileHashAsync(file, 'general')

          return true
        },
        // 通用文件上传成功
        onSuccess(response) {
          if (response.code === 20000) {
            if (response.data.hash) {
              $this.$set($this.clientUpdateInfo.edit, 'fileHash', response.data.hash);
            }
            $this.clientUpdateInfo.edit.downloadUrl = response.data.url;
            msg_success('文件上传成功');
          } else {
            $this.$message.error('文件上传失败');
          }
          $this.clientUpdateInfo.uploading = false;
          $this.clientUpdateInfo.uploadProgress = 0;
        },
        // 通用文件上传失败
        onError() {
          $this.$message.error('文件上传失败')
          $this.clientUpdateInfo.uploading = false
          $this.clientUpdateInfo.uploadProgress = 0
        },
        // 教师端客户端文件上传前检查
        beforeTeacherClientUpload(file) {
          $this.clientUpdateInfo.teacherClientUploading = true
          $this.clientUpdateInfo.teacherClientUploadProgress = 0

          // 异步计算哈希值
          $this.calculateFileHashAsync(file, 'teacherClient')

          return true
        },
        // 教师端客户端文件上传成功
        onTeacherClientSuccess(response) {
          if (response.code === 20000) {
            $this.$set($this.clientUpdateInfo.edit.updateConfig, 'teacherClientDownloadUrl', response.data.url);
            msg_success('教师端客户端文件上传成功');
          } else {
            $this.$message.error('教师端客户端文件上传失败');
          }
          $this.clientUpdateInfo.teacherClientUploading = false;
          $this.clientUpdateInfo.teacherClientUploadProgress = 0;
        },
        // 教师端客户端文件上传失败
        onTeacherClientError() {
          $this.$message.error('教师端客户端文件上传失败')
          $this.clientUpdateInfo.teacherClientUploading = false
          $this.clientUpdateInfo.teacherClientUploadProgress = 0
        },
        // 教师端客户端文件自定义上传请求
        async uploadTeacherClientRequest(upload) {
          let file = upload.file
          return await new Promise((resolve, reject) => {
            BaseUploadModel.qiNiuUploadForPackageSystem(file, 'teacher-client', 'teacher-app', {
              next: (result) => {
                $this.clientUpdateInfo.teacherClientUploadProgress = parseInt(result.total.percent);
              },
              error: (errResult) => {
                console.log(errResult)
                reject(errResult)
                $this.$message.error('上传失败')
                $this.clientUpdateInfo.teacherClientUploading = false
                $this.clientUpdateInfo.teacherClientUploadProgress = 0
              },
              complete: (result) => {
                let domain = BaseUploadModel.getBucketDomain("zyhd-package-system")
                let url = domain + '/' + result.key

                let response = {
                  code: 20000,
                  data: {
                    url: url,
                    hash: $this.clientUpdateInfo.edit.updateConfig.teacherClientFileHash,
                    originalName: file.name,
                    fileSize: file.size,
                    fileType: file.type
                  }
                }

                $this.clientUpdateInfo.teacherClientUploadProgress = 100
                setTimeout(() => {
                  $this.clientUpdateInfo.teacherClientUploadProgress = 0
                  $this.clientUpdateInfo.teacherClientUploading = false
                }, 500)

                resolve(response)
                upload.onSuccess(response)
              }
            })
          })
        },
      }
    },

    // 异步计算文件哈希值（不阻塞上传）
    async calculateFileHashAsync(file, type) {
      try {
        // 初始化xxHash实例
        const hasher = await xxhash();

        // 创建64位哈希计算器（使用流式API）
        const hashStream = hasher.create64();

        const chunkSize = 4 * 1024 * 1024; // 4MB 分片大小
        let offset = 0;
        const fileSize = file.size;

        while (offset < fileSize) {
          const end = Math.min(offset + chunkSize, fileSize);
          const chunk = await this.readFileChunk(file, offset, end);

          // 更新哈希计算
          hashStream.update(chunk);

          offset = end;

          // 让出主线程，避免UI阻塞
          await new Promise(resolve => setTimeout(resolve, 0));
        }

        // 计算最终哈希值
        const hash = hashStream.digest();
        const hashHex = hash.toString(16).padStart(16, '0');

        // 根据类型设置哈希值
        if (type === 'webDist') {
          this.$set(this.clientUpdateInfo.edit.updateConfig, 'webDistZipFileHash', hashHex);
        } else if (type === 'studentClient') {
          this.$set(this.clientUpdateInfo.edit.updateConfig, 'studentClientFileHash', hashHex);
        } else if (type === 'schoolLinux') {
          this.$set(this.clientUpdateInfo.edit.updateConfig, 'schoolClientLinuxFileHash', hashHex);
        } else if (type === 'schoolWindows') {
          this.$set(this.clientUpdateInfo.edit.updateConfig, 'schoolClientWindowsFileHash', hashHex);
        } else if (type === 'teacherClient') {
          this.$set(this.clientUpdateInfo.edit.updateConfig, 'teacherClientFileHash', hashHex);
        } else if (type === 'general') {
          this.$set(this.clientUpdateInfo.edit, 'fileHash', hashHex);
        }

        console.log('文件哈希计算完成:', hashHex);

        return hashHex;
      } catch (err) {
        console.error('计算哈希值出错:', err);
        return '';
      }
    },

    // 读取文件分片
    readFileChunk(file, start, end) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => resolve(new Uint8Array(e.target.result));
        reader.onerror = reject;
        reader.readAsArrayBuffer(file.slice(start, end));
      });
    },

    // 计算学校端文件数量
    getSchoolFileCount(updateConfig) {
      if (!updateConfig) return 0;
      let count = 0;
      if (updateConfig.webDistZipDownloadUrl) count++;
      if (updateConfig.studentClientDownloadUrl) count++;
      if (updateConfig.schoolClientLinuxDownloadUrl) count++;
      if (updateConfig.schoolClientWindowsDownloadUrl) count++;
      return count;
    },

    // 计算教师端文件数量
    getTeacherFileCount(updateConfig) {
      if (!updateConfig) return 0;
      let count = 0;
      if (updateConfig.teacherClientDownloadUrl) count++;
      return count;
    },
  }
}
</script>

<style scoped lang="scss">
// 全局加载指示器样式
.global-loading-mask {
  position: absolute;
  z-index: 1000;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;

  .global-loading-container {
    text-align: center;

    i {
      font-size: 32px;
      color: #409EFF;
      margin-bottom: 10px;
    }

    p {
      font-size: 14px;
      color: #606266;
      margin: 0;
    }
  }
}

.lists {
  a {
    color: #004DCC;
  }

  .can-click {
    color: #004DCC;
    cursor: pointer;
  }
}

.progress-container {
  margin-top: 15px;
  padding: 10px 15px;
  background-color: #f5f7fa;
  border-radius: 4px;

  .progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 13px;
    color: #606266;
  }
}

.upload-notice-container {
  margin-bottom: 15px;
  padding: 15px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;

  .upload-notice-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-weight: 600;
    color: #409EFF;

    i {
      margin-right: 8px;
      font-size: 16px;
    }
  }

  .upload-notice-content {
    .upload-notice-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 14px;
      color: #606266;

      &:last-child {
        margin-bottom: 0;
      }

      i {
        margin-right: 8px;
        color: #409EFF;
        font-size: 14px;
      }

      strong {
        color: #303133;
      }
    }
  }
}

.config-info-container {
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #dcdfe6;

  .config-info-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .config-info-label {
      width: 150px;
      font-weight: 600;
      color: #606266;
      flex-shrink: 0;
    }

    .el-link {
      margin-left: 10px;
    }
  }
}

.dialog-container {
  max-height: 70vh;
  overflow-y: auto;

  h3 {
    border-bottom: 2px solid #409EFF;
    padding-bottom: 8px;
    margin-bottom: 20px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .config-info-item {
    flex-direction: column;
    align-items: flex-start !important;

    .config-info-label {
      width: 100% !important;
      margin-bottom: 5px;
    }
  }

  .upload-notice-item {
    flex-direction: column;
    align-items: flex-start !important;

    i {
      margin-bottom: 5px;
    }
  }
}

/* 确认推送对话框样式 */
.confirm-push-dialog .el-message-box__message {
  font-size: 14px;
  line-height: 1.6;
  color: #606266;
}

.confirm-push-dialog .el-message-box__title {
  font-weight: bold;
  color: #E6A23C;
}

.confirm-push-dialog .el-message-box__content {
  padding: 20px 24px;
}
</style>
