# XXHash64 实现指南

本文档提供了使用不同编程语言（Java、Go、Python、Electron）计算xxHash64哈希值的实现方法，可与前端Vue实现保持一致的计算结果。

## 1. Java 实现

使用 [lz4-java](https://github.com/lz4/lz4-java) 库，它包含官方的XXHash实现。

```java
import net.jpountz.xxhash.XXHash64;
import net.jpountz.xxhash.XXHashFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.ByteBuffer;

public class XXHash64Example {
    public static void main(String[] args) throws IOException {
        // 创建 XXHash64 实例，使用种子 0
        XXHashFactory factory = XXHashFactory.fastestInstance();
        XXHash64 xxHash64 = factory.hash64();
        
        // 文件路径
        String filePath = "/path/to/your/file.ext";
        File file = new File(filePath);
        
        // 对大文件进行分块读取并计算哈希
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] buffer = new byte[4 * 1024 * 1024]; // 4MB 缓冲区
            long hashValue = 0;
            
            // 创建流式哈希计算器
            net.jpountz.xxhash.StreamingXXHash64 streamingHash = factory.newStreamingHash64(0);
            
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                streamingHash.update(buffer, 0, bytesRead);
            }
            
            // 获取哈希值
            hashValue = streamingHash.getValue();
            
            // 转换为16位十六进制字符串
            String hashHex = Long.toHexString(hashValue);
            // 补齐前导零，确保长度为16
            while (hashHex.length() < 16) {
                hashHex = "0" + hashHex;
            }
            
            System.out.println("XXHash64: " + hashHex);
        }
    }
}
```

依赖配置 (Maven):

```xml
<dependency>
    <groupId>org.lz4</groupId>
    <artifactId>lz4-java</artifactId>
    <version>1.8.0</version>
</dependency>
```

## 2. Go 实现

使用 [github.com/cespare/xxhash](https://github.com/cespare/xxhash) 库，性能极高。

```go
package main

import (
    "bufio"
    "fmt"
    "io"
    "os"
    
    "github.com/cespare/xxhash/v2"
)

func main() {
    // 打开文件
    filePath := "/path/to/your/file.ext"
    file, err := os.Open(filePath)
    if err != nil {
        fmt.Printf("无法打开文件: %v\n", err)
        return
    }
    defer file.Close()
    
    // 创建xxHash64摘要器
    h := xxhash.New()
    
    // 分块读取文件并更新哈希
    buffer := make([]byte, 4*1024*1024) // 4MB 缓冲区
    reader := bufio.NewReader(file)
    
    for {
        n, err := reader.Read(buffer)
        if err != nil && err != io.EOF {
            fmt.Printf("读取文件错误: %v\n", err)
            return
        }
        
        if n == 0 {
            break
        }
        
        h.Write(buffer[:n])
    }
    
    // 获取哈希值
    hashValue := h.Sum64()
    
    // 转换为16位十六进制字符串
    hashHex := fmt.Sprintf("%016x", hashValue)
    
    fmt.Printf("XXHash64: %s\n", hashHex)
}
```

安装依赖:

```bash
go get github.com/cespare/xxhash/v2
```

## 3. Python 实现

使用 [xxhash](https://pypi.org/project/xxhash/) 库。

```python
import xxhash
import os

def calculate_file_xxhash64(file_path):
    # 创建xxHash64哈希对象，种子为0
    hasher = xxhash.xxh64()
    
    # 分块读取文件并更新哈希
    with open(file_path, 'rb') as f:
        chunk_size = 4 * 1024 * 1024  # 4MB 缓冲区
        
        for chunk in iter(lambda: f.read(chunk_size), b''):
            hasher.update(chunk)
    
    # 获取十六进制哈希值
    hash_hex = hasher.hexdigest()
    
    return hash_hex

if __name__ == "__main__":
    file_path = "/path/to/your/file.ext"
    hash_value = calculate_file_xxhash64(file_path)
    print(f"XXHash64: {hash_value}")
```

安装依赖:

```bash
pip install xxhash
```

## 4. Electron (Node.js) 实现

在Electron应用中，可以使用 [xxhash-addon](https://www.npmjs.com/package/xxhash-addon) 或 [xxhashjs](https://www.npmjs.com/package/xxhashjs) 库。

### 使用 xxhash-addon (推荐，性能更好)

```javascript
const fs = require('fs');
const XXHash = require('xxhash-addon');

async function calculateFileXXHash64(filePath) {
  return new Promise((resolve, reject) => {
    try {
      // 创建xxHash64哈希对象，种子为0
      const hasher = XXHash.create64(0);
      
      // 创建文件读取流
      const stream = fs.createReadStream(filePath, { 
        highWaterMark: 4 * 1024 * 1024 // 4MB 缓冲区
      });
      
      // 处理数据块
      stream.on('data', (chunk) => {
        hasher.update(chunk);
      });
      
      // 处理完成
      stream.on('end', () => {
        // 获取哈希值并转换为十六进制
        const hashValue = hasher.digest().toString('hex');
        resolve(hashValue);
      });
      
      // 错误处理
      stream.on('error', (err) => {
        reject(err);
      });
    } catch (err) {
      reject(err);
    }
  });
}

// 使用示例
async function main() {
  try {
    const filePath = '/path/to/your/file.ext';
    const hashValue = await calculateFileXXHash64(filePath);
    console.log(`XXHash64: ${hashValue}`);
  } catch (err) {
    console.error('计算哈希出错:', err);
  }
}

main();
```

安装依赖:

```bash
npm install xxhash-addon
```

### 使用 xxhash-wasm (与前端相同的库)

```javascript
const fs = require('fs');
const xxhash = require('xxhash-wasm');

async function calculateFileXXHash64(filePath) {
  try {
    // 初始化xxHash实例
    const hasher = await xxhash();
    
    // 创建流式哈希计算器
    const hashStream = hasher.create64();
    
    // 读取文件并分块计算哈希
    return new Promise((resolve, reject) => {
      const stream = fs.createReadStream(filePath, { 
        highWaterMark: 4 * 1024 * 1024 // 4MB 缓冲区
      });
      
      stream.on('data', (chunk) => {
        hashStream.update(chunk);
      });
      
      stream.on('end', () => {
        // 获取哈希值并转换为十六进制
        const hash = hashStream.digest();
        const hashHex = hash.toString(16).padStart(16, '0');
        resolve(hashHex);
      });
      
      stream.on('error', reject);
    });
  } catch (err) {
    throw err;
  }
}

// 使用示例
async function main() {
  try {
    const filePath = '/path/to/your/file.ext';
    const hashValue = await calculateFileXXHash64(filePath);
    console.log(`XXHash64: ${hashValue}`);
  } catch (err) {
    console.error('计算哈希出错:', err);
  }
}

main();
```

安装依赖:

```bash
npm install xxhash-wasm
```

## 哈希值兼容性说明

各个语言实现得到的xxHash64哈希值应当完全一致，因为它们都遵循相同的算法规范。默认情况下，各实现均使用种子值0。如需使用不同的种子值，请在各语言的相应API中指定。

注意事项：
1. 确保读取方式一致(二进制模式)
2. 确保使用相同的种子值
3. 对于流式哈希计算，确保按顺序处理完整的文件数据

这样可以保证在不同语言和平台之间获得一致的哈希结果，便于跨平台验证文件的完整性。 