<template>
  <div class="erp-uploader-one erp-uploader-one-video" :id="'erp-uploader-one-video-'+uploaderId">
    <el-upload
      action="dev"
      :show-file-list="false"
      :on-success="uploadSuccess"
      :http-request="upload=>uploadRequest(upload,uploadTarget)"
      :before-upload="beforeUpload">
      <video v-show="video&&!uploading" :src="video"
             class="video-show" @click="" preload="none"/>
      <i v-if="!video&&!uploading" class="el-icon-plus uploader-icon"></i>
      <i v-if="!video&&uploading" class="el-icon-loading uploader-icon"></i>
    </el-upload>
    <div class="buttons flex flex-center">
      <el-button type="text" v-if="video"
                 @click="video?uploadPreviewShow=true:false">预览
      </el-button>
      <el-button type="text" v-if="video" @click="clickDeleteImg">删除</el-button>
    </div>
    <div class="des flex flex-dr flex-center" v-if="showDes">
      <div class="title">{{ uploaderTitle }}</div>
      <div class="per" v-if="per>0">{{ per }}%</div>
      <div>视频比例：{{ pixelLimit[0] }}px X {{ pixelLimit[1] }}px</div>
      <div>视频大小：{{ sizeLimit | sizeLimitFilter }}以内</div>
    </div>
    <el-dialog center v-el-drag-dialog :visible.sync="uploadPreviewShow" width="900px" append-to-body>
      <video :src="video" controls style="width: 100%"></video>
    </el-dialog>
  </div>
</template>

<script>
import {BaseUploadModel} from "@/model/BaseUploadModel";
import {msg_err} from "@/utils/ele_component";
import elDragDialog from "@/directive/el-drag-dialog";
import axios from "axios";

/**
 * erp-单视频上传组件
 * <AUTHOR> 2021.3.18
 */
export default {
  name: "erpUploaderOneVideo",
  directives: {
    elDragDialog
  },
  filters: {
    // 视频大小过滤器
    sizeLimitFilter(v) {
      if (v >= 1024) {// 大于1M
        v = (v / 1024).toFixed(1)
        v = v.replace(/.0$/, '')
        return v + "MB"
      } else {
        return v + "KB"
      }
    },
  },
  props: {
    // 是否显示简介
    showDes: {
      type: Boolean,
      default: true
    },
    // 上传器 id
    uploaderId: {
      type: String,
      default: "",
    },
    // 上传器 显示分辨率
    uploaderSize: {
      type: Array,
      default: () => {
        return [100, 100]
      }
    },
    // 上传器 标题
    uploaderTitle: {
      type: String,
      default: ""
    },
    // 视频上传文件最大体积 KB
    sizeLimit: {
      type: Number,
      default: 1024
    },
    // 视频分辨率限制 PX
    pixelLimit: {
      type: Array,
      default: () => {
        return [200, 200]
      }
    },
    // 视频地址
    videoIn: {
      type: String,
      default: ""
    },
    // 上传bucket
    bucket: {
      type: String,
      default: "zyhd-resouce"
    },
    // 要上传到的服务器
    uploadTarget: {
      type: String,
      default: "qiniu"
    },
  },
  computed: {
    video: {
      get: function () {
        return this.videoIn
      },
      set: function (newValue) {
        return newValue
      }
    }
  },
  data() {
    return {
      window: window,
      uploading: false,
      uploadPreviewShow: false,
      per: "",// 进度显示
    }
  },
  mounted() {
    // 设置上传器宽高度和宽度
    document.querySelector("#erp-uploader-one-video-" + this.uploaderId + " .el-upload").style.width = this.uploaderSize[0] + "px"
    document.querySelector("#erp-uploader-one-video-" + this.uploaderId + " .el-upload").style.height = this.uploaderSize[1] + "px"
    if (!this.video) {
      document.querySelector("#erp-uploader-one-video-" + this.uploaderId + " .uploader-icon").style.width = this.uploaderSize[0] + "px"
      document.querySelector("#erp-uploader-one-video-" + this.uploaderId + " .uploader-icon").style.height = this.uploaderSize[1] + "px"
      document.querySelector("#erp-uploader-one-video-" + this.uploaderId + " .uploader-icon").style.lineHeight = this.uploaderSize[1] + "px"
    }
    document.querySelector("#erp-uploader-one-video-" + this.uploaderId + " .video-show").style.width = this.uploaderSize[0] + "px"
    document.querySelector("#erp-uploader-one-video-" + this.uploaderId + " .video-show").style.height = this.uploaderSize[1] + "px"
  },
  updated() {

  },
  methods: {
    // 上传前检测
    beforeUpload(file) {
      if (!BaseUploadModel.fileTypeLimit(file, "video")) {
        msg_err('只能上传视频文件!')
        return false
      }
      const isLess = file.size / 1024 <= this.sizeLimit
      if (!isLess) {
        let v = this.sizeLimit
        let text = ""
        if (v >= 1024) {// 大于1M
          v = (v / 1024).toFixed(1)
          text = v + "MB"
        } else {
          text = v + "KB"
        }
        msg_err('视频超过最大限制，最大' + text + "!")
        return false
      }
      this.uploading = true
      this.$emit("beforeUpload", "")
      return true
    },
    // 上传成功后
    uploadSuccess(data) {
      this.$set(this, 'video', data.data)
      this.uploading = false
      this.$emit("uploadSuccess", [this.uploaderId, data.data])
      return true
    },
    async uploadRequest(upload, target) {
      let file = upload.file
      return await new Promise((resolve, reject) => {
        if (target === 'zy0') {
          const formData = new FormData()
          formData.append('file', file)
          axios.create().request({
            url: `http://static.xhyjbj.com/api/uploadOneFile`,
            // url: `http://127.0.0.1:10810/api/uploadOneFile`,
            method: 'post',
            headers: {'Content-Type': 'multipart/form-data'},
            data: formData
          }).then(response => {
            if (response.status === 200) {
              resolve({data: "http://static.xhyjbj.com/files" + response.data.url})
            } else {
              msg_err(response.data)
              resolve(false)
            }
          })
        }
        if (!target || target === "qiniu") {
          BaseUploadModel.qiNiuUpload(file, {
            next: (result) => {
              // 上传进度显示
              this.per = result.total.percent.toFixed(0);
              if (this.per == 100) {
                this.per = 0;
              }
            },
            error: (errResult) => {
              console.log(errResult)
              msg_err('上传失败')
            },
            complete: (result) => {
              let domain = BaseUploadModel.getBucketDomain(this.bucket)
              let url = domain + '/' + result.key + ''
              resolve({data: url})
            }
          }, this.bucket)
        }
      })
    },
    // 点击删除视频
    clickDeleteImg() {
      this.video = ""
      // 检测是否有方法传入
      if (this.$listeners['afterDelete']) {
        this.$emit("afterDelete", [this.uploaderId])
      }
      // 设置上传加号图标css
      setTimeout(() => {
        document.querySelector("#erp-uploader-one-video-" + this.uploaderId + " .uploader-icon").style.width = this.uploaderSize[0] + "px"
        document.querySelector("#erp-uploader-one-video-" + this.uploaderId + " .uploader-icon").style.height = this.uploaderSize[1] + "px"
        document.querySelector("#erp-uploader-one-video-" + this.uploaderId + " .uploader-icon").style.lineHeight = this.uploaderSize[1] + "px"
      }, 75)
    }
  }
}
</script>

<style scoped lang="scss">
.buttons {
  text-align: center;
}

.des .title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}
</style>
