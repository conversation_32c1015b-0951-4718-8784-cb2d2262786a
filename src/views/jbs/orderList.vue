<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
        @clickSearchFilterBtn="query => ListMethods().clickSearchFilterBtn(query)"
        @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button type="primary" @click="ListMethods().clickOrganListBtn()">机构列表</el-button>
          <el-button type="primary" @click="ListMethods().clickAppListBtn()">应用列表</el-button>
          <el-button type="primary" @click="ListMethods().clickScriptListBtn()">剧本列表</el-button>
        </div>
      </div>
    </div>
    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit style="width: 100%;">
      <el-table-column label="行为" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.action }}</span>
        </template>
      </el-table-column>
      <el-table-column label="注册码" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.expRegCodeId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="设备序列号" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.deviceSerial }}</span>
        </template>
      </el-table-column>
      <el-table-column label="设备名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.deviceName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实验名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.experimentName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实验id" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.experimentId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结果code" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.code }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结果msg" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.msg }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户ip" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.ip }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="155px">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat("yyyy-MM-dd HH:mm:ss") }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number) => ListMethods().pageChange(number)"
        :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
        layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
        @size-change="(size) => ListMethods().pageLimitChange(size)" :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--弹窗-机构列表-->
    <el-dialog title="机构列表管理" :visible.sync="organ.dialogVisible" width="50%">
      <div class="clearfix">
        <el-button type="primary" @click="OrganMethods().addOrgan()"
          style="margin-bottom: 10px;float: right;">新增机构</el-button>
      </div>
      <el-table :data="organ.list" style="width: 100%" border fit>
        <el-table-column prop="id" label="机构ID" width="180" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.jbsOrganId }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="机构名称" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center">
          <template slot-scope="scope">
            <el-button size="mini" @click="OrganMethods().renameOrgan(scope.row)">改名</el-button>
            <el-button size="mini" type="danger" @click="OrganMethods().deleteOrgan(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!--弹窗-应用列表-->
    <el-dialog title="应用列表管理" :visible.sync="app.dialogVisible" width="50%">
      <div class="clearfix">
        <el-button type="primary" @click="AppMethods().addApp()"
          style="margin-bottom: 10px;float: right;">新增应用</el-button>
      </div>
      <el-table :data="app.list" style="width: 100%" border fit>
        <el-table-column prop="id" label="应用ID" width="180" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.jbsAppId }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="应用名称" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center">
          <template slot-scope="scope">
            <el-button size="mini" @click="AppMethods().renameApp(scope.row)">改名</el-button>
            <el-button size="mini" type="danger" @click="AppMethods().deleteApp(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!--弹窗-剧本列表-->
    <el-dialog title="剧本列表管理" :visible.sync="script.dialogVisible" width="50%">
      <div class="clearfix">
        <el-button type="primary" @click="ScriptMethods().addScript()"
          style="margin-bottom: 10px;float: right;">新增剧本</el-button>
      </div>
      <el-table :data="script.list" style="width: 100%" border fit>
        <el-table-column prop="id" label="剧本ID" width="180" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.jbsScriptId }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="剧本名称" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center">
          <template slot-scope="scope">
            <el-button size="mini" @click="ScriptMethods().renameScript(scope.row)">改名</el-button>
            <el-button size="mini" type="danger" @click="ScriptMethods().deleteScript(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import { mapState } from "vuex";
import { dateFormat, stringBrFilter } from "@/filters";
import { date_format, getQuery } from "@/utils/common";
import { msg_confirm, msg_input, msg_success, msg_err } from "@/utils/ele_component";
import { ExpRegCodeLogModel } from "@/model/erp/ExpRegCodeLogModel";
import { ExperimentModel } from "@/model/exp/ExperimentModel";
import { CommonModel } from "@/model/CommonModel";
import { JbsOrganModel } from "@/model/erp/JbsOrganModel";
import { JbsAppModel } from "@/model/erp/JbsAppModel";
import { JbsScriptModel } from "@/model/erp/JbsScriptModel";
export default {
  name: "expRegCodeLogList",
  components: {
    ListSearchFilter,
  },
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: { dateFormat },
  data: function () {
    let $this = this;

    return {
      window: window,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      stringBrFilter: stringBrFilter,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '注册码',
              key: 'expRegCodeId',
              value: '',
              format: function (v) {
                return { '$regex': `.*${v}.*` }
              }
            },
            {
              type: 'input',
              label: '设备序列号',
              key: 'deviceSerial',
              value: '',
              format: function (v) {
                return { '$regex': `.*${v}.*` }
              }
            },
            {
              type: 'input',
              label: '设备名称',
              key: 'deviceName',
              value: '',
              format: function (v) {
                return { '$regex': `.*${v}.*` }
              }
            },
            {
              type: 'input',
              label: '实验名称',
              key: 'experimentName',
              value: '',
              format: function (v) {
                return { '$regex': `.*${v}.*` }
              }
            },
            {
              type: 'input',
              label: '实验id',
              key: 'experimentId',
              value: '',
              format: function (v) {
                return { '$regex': `.*${v}.*` }
              }
            },
          ],
          filter: [],
          hideFilter: {
            experimentList: {},
            userList: {}
          }
        }
      },
      entityInfo: {
        dialog: false,
        editLoading: false,
        showExperimentList: false,
        title: "新增测试表单",
        type: "add",
        filter: {
          // 项目列表
          experimentList: {
            loading: false,
            list: []
          }
        },
        edit: {
          name: "",
        },
        // 输入检测
        formRules: {},
      },
      organ: {
        dialogVisible: false,
        list: [], // 机构列表数据
      },
      app: {
        dialogVisible: false,
        list: [], // 应用列表数据
      },
      script: {
        dialogVisible: false,
        list: [], // 剧本列表数据
      }
    }
  },
  async mounted() {
    await this.ListMethods().initFilter()
    this.ListMethods().getList(0, 20, {})
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 点击搜索重置按钮
        clickCleanBtn() {

        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          [list, $this.lists.pages] = await ExpRegCodeLogModel.getPageList(page - 1, size, "", query)
          // 遍历list获取必要信息
          list.forEach(li => {
            // 获取项目名称
            if (li.experimentId) {
              li.experimentName = $this.lists.searchFilter.hideFilter.experimentList[li.experimentId]
            }
          })
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter(type) {
          $this.lists.loading = true
          // 获取所有实验列表
          let [allExperimentList,] = await ExperimentModel.getList(1, 500, {});
          let generateListFilter1 = CommonModel.generateListFilterOptions('name', 'id', allExperimentList)
          $this.$set($this.lists.searchFilter.hideFilter, "experimentList", generateListFilter1[1])
          $this.$set($this.lists.searchFilter.hideFilter, "experimentListArr", generateListFilter1[0])
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "新建注册码"
          $this.entityInfo.edit = {
            bindDevicesList: [],
            experimentList: [],
            needLogin: true,
            maxDevicesNumber: 1,
            limitRunNumber: false,
            limitRunNumberCount: 1,
            limitTime: false,
            active: true
          };
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 400)
        },
        // 点击编辑按钮
        async clickEditBtn(entity, index) {
          // 获取该表单的最新信息
          entity = await ExpRegCodeLogModel.getOne(entity.expRegCodeId)
          $this.$set($this.entityInfo, "edit", entity);
          $this.entityInfo.type = "edit"
          $this.entityInfo.title = "编辑注册码"
          $this.$set($this.lists.list[index], "editLoading", true);
          // 设置标签已选项
          $this.entityInfo.dialog = true;
          $this.$set($this.lists.list[index], "editLoading", false);
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 400)
        },
        // 点击机构列表按钮
        clickOrganListBtn() {
          $this.organ.dialogVisible = true
          $this.OrganMethods().getList()
        },
        // 点击应用列表按钮
        clickAppListBtn() {
          $this.app.dialogVisible = true;
          $this.AppMethods().getList();
        },
        // 点击剧本列表按钮
        clickScriptListBtn() {
          $this.script.dialogVisible = true;
          $this.ScriptMethods().getList();
        }
      }
    },
    // 机构列表Methods
    OrganMethods() {
      let $this = this;
      return {
        // 新增机构 
        addOrgan() {
          msg_input('新增机构', '请输入机构名称').then(value => {
            if (!value.trim()) {
              msg_err('机构名称不能为空');
              return;
            }

            JbsOrganModel.getList({ name: value }).then(existingList => {
              if (existingList.length > 0) {
                // 检查是否为逻辑删除的记录
                if (existingList[0].deleted === 1) {
                  // 恢复已删除的记录
                  existingList[0].deleted = 0;
                  JbsOrganModel.addOrEdit(existingList[0]).then(() => {
                    msg_success('恢复机构成功');
                    // 刷新机构列表
                    $this.OrganMethods().getList();
                  })
                } else {
                  msg_err('该机构名称已存在');
                }
              } else {
                // 继续处理新增机构的逻辑
                JbsOrganModel.addOrEdit({ name: value }).then(() => {
                  msg_success('新增机构成功');
                  // 刷新机构列表
                  $this.OrganMethods().getList();
                })
              }
            }).catch(() => {
              msg_err('获取机构列表失败');
            });
          }).catch(() => {
            console.log('取消新增机构');
          });
        },
        // 改名
        renameOrgan(organ) {
          msg_input('改名', '请输入新的机构名称', organ.name).then(value => {
            if (!value.trim()) {
              msg_err('机构名称不能为空');
              return;
            }

            JbsOrganModel.getList({ name: value }).then(existingList => {
              if (existingList.length > 0) {
                msg_err('该机构名称已存在');
              } else {
                // 续处理修改构逻辑
                organ.name = value
                JbsOrganModel.addOrEdit(organ).then(() => {
                  msg_success('修改机构名称成功');
                  // 刷新机构列表
                  $this.OrganMethods().getList();
                })
              }
            });

          }).catch(() => {
            console.log('取消改名');
          });
        },
        deleteOrgan(organ) {
          msg_confirm('此操作将永久删除该机构, 是否继续?').then(() => {
            // 在这里处理删除机构的逻辑
            organ.deleted = 1
            JbsOrganModel.addOrEdit(organ).then(() => {
              msg_success('删除机构成功');
              // 刷新机构列表
              $this.OrganMethods().getList();
            })
          }).catch(() => {
            console.log('取消删除');
          });
        },
        async getList() {
          $this.organ.loading = true;
          try {
            let list = await JbsOrganModel.getList({
              deleted: 0
            });
            $this.$set($this.organ, "list", list);
          } catch (error) {
            msg_err('获取机构列表失败');
          } finally {
            $this.organ.loading = false;
          }
        }
      }
    },
    // 应用列表Methods
    AppMethods() {
      let $this = this;
      return {
        // 新增应用
        addApp() {
          msg_input('新增应用', '请输入应用名称').then(value => {
            if (!value.trim()) {
              msg_err('应用名称不能为空');
              return;
            }

            JbsAppModel.getList({ name: value }).then(existingList => {
              if (existingList.length > 0) {
                // 检查是否为逻辑删除的记录
                if (existingList[0].deleted === 1) {
                  // 恢复已删除的记录
                  existingList[0].deleted = 0;
                  JbsAppModel.addOrEdit(existingList[0]).then(() => {
                    msg_success('恢复应用成功');
                    // 刷新应用列表
                    $this.AppMethods().getList();
                  })
                } else {
                  msg_err('该应用名称已存在');
                }
              } else {
                // 继续处理新增应用的逻辑
                JbsAppModel.addOrEdit({ name: value }).then(() => {
                  msg_success('新增应用成功');
                  // 刷新应用列表
                  $this.AppMethods().getList();
                })
              }
            }).catch(() => {
              msg_err('获取应用列表失败');
            });
          }).catch(() => {
            console.log('取消新增应用');
          });
        },
        // 改名
        renameApp(app) {
          msg_input('改名', '请输入新的应用名称', app.name).then(value => {
            if (!value.trim()) {
              msg_err('应用名称不能为空');
              return;
            }

            JbsAppModel.getList({ name: value }).then(existingList => {
              if (existingList.length > 0) {
                msg_err('该应用名称已存在');
              } else {
                // 继续处理修改应用逻辑
                app.name = value
                JbsAppModel.addOrEdit(app).then(() => {
                  msg_success('修改应用名称成功');
                  // 刷新应用列表
                  $this.AppMethods().getList();
                })
              }
            });

          }).catch(() => {
            console.log('取消改名');
          });
        },
        deleteApp(app) {
          msg_confirm('此操作将永久删除该应用, 是否继续?').then(() => {
            app.deleted = 1;
            JbsAppModel.addOrEdit(app).then(() => {
              msg_success('删除应用成功');
              // 刷新应用列表
              this.getList();
            })
          }).catch(() => {
            console.log('取消删除');
          });
        },
        async getList() {
          $this.app.loading = true;
          try {
            let list = await JbsAppModel.getList({
              deleted: 0
            });
            $this.$set($this.app, "list", list);
          } catch (error) {
            msg_err('获取应用列表失败');
          } finally {
            $this.app.loading = false;
          }
        }
      }
    },
    // 剧本列表Methods
    ScriptMethods() {
      let $this = this;
      return {
        // 新增剧本
        addScript() {
          msg_input('新增剧本', '请输入剧本名称').then(value => {
            if (!value.trim()) {
              msg_err('剧本名称不能为空');
              return;
            }

            JbsScriptModel.getList({ name: value }).then(existingList => {
              if (existingList.length > 0) {
                // 检查是否为逻辑删除的记录
                if (existingList[0].deleted === 1) {
                  // 恢复已删除的记录
                  existingList[0].deleted = 0;
                  JbsScriptModel.addOrEdit(existingList[0]).then(() => {
                    msg_success('恢复剧本成功');
                    // 刷新剧本列表
                    $this.ScriptMethods().getList();
                  })
                } else {
                  msg_err('该剧本名称已存在');
                }
              } else {
                // 继续处理新增剧本的逻辑
                JbsScriptModel.addOrEdit({ name: value }).then(() => {
                  msg_success('新增剧本成功');
                  // 刷新剧本列表
                  $this.ScriptMethods().getList();
                })
              }
            }).catch(() => {
              msg_err('获取剧本列表失败');
            });
          }).catch(() => {
            console.log('取消新增剧本');
          });
        },
        // 改名
        renameScript(script) {
          msg_input('改名', '请输入新的剧本名称', script.name).then(value => {
            if (!value.trim()) {
              msg_err('剧本名称不能为空');
              return;
            }

            JbsScriptModel.getList({ name: value }).then(existingList => {
              if (existingList.length > 0) {
                msg_err('该剧本名称已存在');
              } else {
                // 继续处理修改剧本逻辑
                script.name = value
                JbsScriptModel.addOrEdit(script).then(() => {
                  msg_success('修改剧本名称成功');
                  // 刷新剧本列表
                  $this.ScriptMethods().getList();
                })
              }
            });

          }).catch(() => {
            console.log('取消改名');
          });
        },
        deleteScript(script) {
          msg_confirm('此操作将永久删除该剧本, 是否继续?').then(() => {
            script.deleted = 1;
            JbsScriptModel.addOrEdit(script).then(() => {
              msg_success('删除剧本成功');
              // 刷新剧本列表
              this.getList();
            })
          }).catch(() => {
            console.log('取消删除');
          });
        },
        async getList() {
          $this.script.loading = true;
          try {
            let list = await JbsScriptModel.getList({
              deleted: 0
            });
            $this.$set($this.script, "list", list);
          } catch (error) {
            msg_err('获取剧本列表失败');
          } finally {
            $this.script.loading = false;
          }
        }
      }
    }
  }
}
</script>

<style scoped lang="scss"></style>
