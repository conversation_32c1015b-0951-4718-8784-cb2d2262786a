<template>
  <div class="flex flex-dr flex-center">
    <div style="margin-bottom: 100px;margin-top: 100px;">上传文件到七牛云</div>
    <el-upload
      action="dev"
      :show-file-list="false"
      :on-success="uploadSuccess"
      :http-request="uploadRequest"
      :before-upload="beforeUpload">
      <el-button size="small" type="primary">点击上传</el-button>
    </el-upload>
    <div v-if="per>0" class="flex flex-dr flex-center" style="margin-top: 50px">
      <div style="margin-bottom: 10px"> 正在上传中，请稍后</div>
      <div class="per">{{ per }}%</div>
    </div>
    <div class="flex flex-dr flex-center" style="margin-top: 50px" v-if="list.length>0">
      <div style="margin-bottom: 10px">上传成功</div>
      <div v-for="item in list" style="margin-bottom: 10px" class="flex flex-start">
        <span style="margin-right: 30px">{{ item.name }}</span>
        <span>{{ item.url }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import {BaseUploadModel} from "@/model/BaseUploadModel";
import {msg_err} from "@/utils/ele_component";

/**
 * erp-上传文件到七牛云，无需登录
 */
export default {
  name: "uploadFile",
  filters: {},
  computed: {},
  data() {
    return {
      window: window,
      uploading: false,
      per: "",// 进度显示
      url: "",
      list: [],
    }
  },
  mounted() {
  },
  updated() {

  },
  methods: {
    // 上传前检测
    beforeUpload(file) {
      this.uploading = true
      this.url = ""
      this.list.push({
        name: file.name
      })
      return true
    },
    // 上传成功后
    uploadSuccess(data) {
      this.url = data.data
      this.$set(this.list[this.list.length - 1], "url", this.url)
      this.uploading = false
      return true
    },
    async uploadRequest(upload) {
      let file = upload.file
      return await new Promise((resolve, reject) => {
        BaseUploadModel.qiNiuUpload(file, {
          next: (result) => {
            // 上传进度显示
            this.per = result.total.percent.toFixed(0);
            if (this.per == 100) {
              this.per = 0;
            }
          },
          error: (errResult) => {
            console.log(errResult)
            msg_err('上传失败')
          },
          complete: (result) => {
            let domain = BaseUploadModel.getBucketDomain(file)
            let url = domain + '/' + result.key + ''
            resolve({data: url})
          }
        })
      })
    },
  }
}
</script>

<style scoped lang="scss">
</style>
