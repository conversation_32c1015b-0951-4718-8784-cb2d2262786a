<template>
  <div class="app-container">
    <el-form ref="aboutManage" :model="info">
      <el-form-item label="项目简介文本：">
        <el-input :rows="10" type="textarea" v-model="info.desText" placeholder="请输入"
                  style="width: 660px" maxlength="300" show-word-limit>
        </el-input>
      </el-form-item>
      <el-form-item label="申报书PDF下载地址：">
        <el-input v-model="info.pdfUrl" placeholder="地址" style="width: 500px">
        </el-input>
      </el-form-item>
      <el-form-item label="项目链接地址（正式）：">
        <el-input v-model="info.link1Url" placeholder="地址" style="width: 500px">
        </el-input>
      </el-form-item>
      <el-form-item label="项目链接地址（临时）：">
        <el-input v-model="info.link2Url" placeholder="地址" style="width: 500px">
        </el-input>
      </el-form-item>
      <el-form-item label="实验地址：">
        <el-input v-model="info.link3Url" placeholder="地址" style="width: 500px">
        </el-input>
      </el-form-item>
      <el-form-item label="简介视频：">
        <div class="flex flex-start">
          <erp-uploader-one-video style="margin-right: 30px" :video-in="info.video1Url"
                                  uploader-id="video1Url"
                                  :uploader-size="[200,100]" :pixel-limit="[2560,1080]"
                                  :size-limit="2048000"
                                  @afterDelete="data=>fileDelete(data,info)"
                                  @uploadSuccess="data=>fileUpload(data,info)"></erp-uploader-one-video>
        </div>
      </el-form-item>
      <el-form-item label="操作视频：">
        <div class="flex flex-start">
          <erp-uploader-one-video style="margin-right: 30px" :video-in="info.video2Url"
                                  uploader-id="video2Url"
                                  :uploader-size="[200,100]" :pixel-limit="[2560,1080]"
                                  :size-limit="2048000"
                                  @afterDelete="data=>fileDelete(data,info)"
                                  @uploadSuccess="data=>fileUpload(data,info)"></erp-uploader-one-video>
        </div>
      </el-form-item>
    </el-form>
    <!--按钮组-->
    <div class="bottom-button-container flex flex-center">
      <el-button type="primary" @click="clickSaveBtn()">保 存</el-button>
    </div>
  </div>
</template>

<script>
import erpUploaderOneVideo from "@/views/components/erpUploaderOneVideo";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import {ConfigModel} from "@/model/erp/ConfigModel";
import $ from "jquery"
import {msg_success} from "@/utils/ele_component";

let configName = "config_temp"
let configFieldName = "xicai0602"

export default {
  name: "xicai0602",
  components: {erpUploaderOnePic, erpUploaderOneVideo},
  data() {
    return {
      info: {
        desText: "",
        pdfUrl: "",
        video1Url: "",
        video2Url: "",
        link1Url: "",
        link2Url: "",
        link3Url: ""
      }
    }
  },
  mounted() {
    this.getConfig();
  },
  methods: {
    // 读取配置
    async getConfig() {
      let data = await ConfigModel.getEdit(configName, configFieldName)
      this.info = JSON.parse(data)
    },
    // 通用-文件上传成功
    fileUpload(params, target, formRef) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 通用-文件删除
    fileDelete(params, target, formRef) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    async clickSaveBtn() {
      // 保存接口
      if (await ConfigModel.editEdit(configName, configFieldName, this.info)) {
        msg_success("保存成功")
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
