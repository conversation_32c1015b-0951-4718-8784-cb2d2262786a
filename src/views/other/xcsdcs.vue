<template>
  <div class="app-container">
    <div class="content-wrapper">
      <div class="select-experiment-box">
        <el-select v-model="experimentId" @change="experimentIdChange">
          <el-option
            v-for="item in experimentList"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </div>
      <!-- 导航区 -->
      <div class="navigation-section">
        <el-button @click="clickPreBtn">上一个</el-button>
        <div class="page-control">
          <span class="page-jump">
            第<el-input v-model="offset" class="page-input"></el-input>个
            <el-button @click="clickJumpBtn">转到</el-button>
          </span>
          <el-select v-model="order" class="order-select" @change="orderChange">
            <el-option label="正序" :value="'1'"></el-option>
            <el-option label="倒序" :value="'2'"></el-option>
          </el-select>
        </div>
        <el-button @click="clickNextBtn">下一个</el-button>
      </div>

      <!-- 学生信息区 -->
      <div class="student-info">
        <div class="info-item">
          <span>姓名：{{ studentExperiment.name }}</span>
          <span>学号：{{ studentExperiment.account }}</span>
          <span>剩余：{{ studentExperiment.total }}</span>
        </div>
        <div class="submit-time">
          提交时间：{{ date_format(studentExperiment.labReportTime, "yyyy-MM-dd HH:mm:ss") }}
        </div>
      </div>

      <!-- 评分区 -->
      <div class="score-section">
        <div class="score-input">

          <el-button @click="clickSetNoScoreBtn">标记没分数</el-button>
          <el-input
            v-model="studentExperiment.experimentScore"
            placeholder="分数"
            @keyup.enter.native="clickSetBtn">
          </el-input>
          <el-button type="success" @click.native.prevent="clickSetBtn">提交分数</el-button>
        </div>

        <!-- 快速评分 -->
        <div class="quick-score">
          <span class="section-title">快速设置分数：</span>
          <div class="score-buttons">
            <el-button @click="clickSetQuickScore100()">快速100分</el-button>
            <el-button @click="clickSetQuickScore(100)">100分</el-button>
            <el-button @click="clickSetQuickScore(96)">96分</el-button>
            <el-button @click="clickSetQuickScore(92)">92分</el-button>
            <el-button @click="clickSetQuickScore(90)">90分</el-button>
            <el-button @click="clickSetQuickScore(88)">88分</el-button>
            <el-button @click="clickSetQuickScore(86)">86分</el-button>
          </div>
        </div>
      </div>
    </div>
    <div v-html="studentExperiment.labReportContent" class="sdcsLabContent"></div>
  </div>
</template>

<script>
import {getXcSykjSdcsReportList, SetXcSykjSdcsReport, SetXcSykjSdcsReportNoScore} from "@/api/exp/ExpGoApi";
import {msg_err, msg_success} from "@/utils/ele_component";
import {date_format} from "@/utils/common";
import {Message} from "element-ui";

export default {
  name: "xcsdcs",
  data() {
    return {
      date_format: date_format,
      offset: 0,
      order: "1",
      studentExperiment: {},
      experimentId:"439109176292147200",
      experimentList:[
        {label: "悬崖村实验空间", value: "439109176292147200"},
        {label: "飞夺泸定桥", value: "51099961055641600"},
        {label: "四渡赤水", value: "52081750515515392"},
        {label: "彝海结盟", value: "52081885093953536"},
        {label: "雪山草地", value: "52081968166338560"},
      ]
    }
  },
  mounted() {
    this.getList(0)
  },
  methods: {
    experimentIdChange(v){
      msg_success("更换实验，刷新列表，重新获取第一个数据")
      this.offset=0
      this.getList(this.offset);
    },
    // 获取列表
    async getList(offset) {
      // 悬崖村实验空间 439109176292147200
      // 飞夺泸定桥 51099961055641600
      // 四渡赤水 52081750515515392
      // 彝海结盟 52081885093953536
      // 雪山草地 52081968166338560
      let [data] = await getXcSykjSdcsReportList(offset, this.order, this.experimentId) // 439109176292147200 51099961055641600 52081885093953536
      this.studentExperiment = data.data
    },
    // 点击上一个
    clickPreBtn() {
      if (this.offset >= 1) {
        this.offset -= 1;
        this.getList(this.offset);
      } else {
        msg_err("已经是第一个了")
      }
    },
    // 点击下一个
    clickNextBtn() {
      this.offset += 1;
      this.getList(this.offset);
    },
    // 点击跳转按钮
    clickJumpBtn() {
      this.getList(this.offset)
    },
    // 排序方式改变
    orderChange(order) {
      this.getList(this.offset)
    },
    // 点击提交分数按钮
    async clickSetBtn() {
      if (this.studentExperiment.experimentScore > 0) {
        if (this.studentExperiment.experimentScore < 60 || this.studentExperiment.experimentScore > 100) {
          msg_err("请输入不小于60,不大于100的分数！")
          return
        }
        let [data] = await SetXcSykjSdcsReport(this.studentExperiment.id, this.studentExperiment.experimentScore)
        if (data.code === "000000") {
          msg_success("提交分数成功")
          this.getList(0)
        }
      } else {
        msg_err("请输入大于0的分数！")
      }
    },
    // 点击标记没有上传分数截图记录
    async clickSetNoScoreBtn() {
      if (this.studentExperiment.experimentScore >0) {
        msg_err("已有分数！")
        return
      }
      let [data] = await SetXcSykjSdcsReportNoScore(this.studentExperiment.id)
      if (data.code === "000000") {
        msg_success("设置成功")
        this.getList(0)
      }
    },
    // 点击快速设置分数
    clickSetQuickScore(score){
      this.$set(this.studentExperiment, "experimentScore", score);
    },
    // 点击快速设置分数
    async clickSetQuickScore100(score){
      let [data] = await SetXcSykjSdcsReport(this.studentExperiment.id, 100)
      if (data.code === "000000") {
        Message({
          message: "提交分数成功",
          type: 'success',
          duration: 1 * 1000
        });
        this.getList(0)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.content-wrapper {
  padding: 20px;
  width: 800px;
  margin: 0 auto;

  .navigation-section {
    width:600px;
  margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .page-control {
      display: flex;
      align-items: center;
      gap: 15px;

      .page-input {
        width: 80px;
        margin: 0 8px;
      }

      .order-select {
        width: 100px;
      }
    }
  }

  .student-info {
    width: 400px;
  margin: 0 auto;
    background-color: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;

    .info-item {
      display: flex;
      gap: 20px;
      margin-bottom: 10px;
    }

    .submit-time {
      color: #606266;
      font-size: 14px;
    }
  }

  .score-section {
    width: 500px;
  margin: 0 auto;
    .score-input {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-bottom: 20px;

      .el-input {
        width: 120px;
      }
    }

    .quick-score {
      .section-title {
        display: block;
        margin-bottom: 10px;
        font-weight: 500;
      }

      .score-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
      }
    }
  }
}

.sdcsLabContent{
  text-align: center;
}

.select-experiment-box{
  width: 300px;
  margin:0 auto;
  margin-bottom: 10px;
}
</style>
