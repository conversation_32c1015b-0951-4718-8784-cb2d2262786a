<template>
  <div class="app-container">
    <div style="margin-top:50px;margin-bottom: 30px;text-align: center;">
      <el-switch
        v-model="closeExtra"
        active-text="关闭外网访问"
        inactive-text="开启外网访问"
        active-color="#ff4949"
        inactive-color="#13ce66"
        @change="closeExtraChange">
      </el-switch>
    </div>
    <div style="text-align: center">
      <el-switch
        v-model="closeInner"
        active-text="关闭内网访问"
        inactive-text="开启内网访问"
        active-color="#ff4949"
        inactive-color="#13ce66"
        @change="closeInnerChange">
      </el-switch>
    </div>
  </div>
</template>

<script>
import {CONFIG_NAME_LIBRARY} from "@/model/ConfigModel";
import {ConfigModel} from "@/model/erp/ConfigModel";
import {msg_success} from "@/utils/ele_component";

export default {
  name: "librarySetting",
  data() {
    return {
      closeExtra: false,
      closeInner: false,
    }
  },
  async mounted() {
    this.closeExtra = (await ConfigModel.getEdit(CONFIG_NAME_LIBRARY, "closeExtra")) === "true";
    this.closeInner = (await ConfigModel.getEdit(CONFIG_NAME_LIBRARY, "closeInner")) === "true";
  },
  methods: {
    async closeExtraChange(v) {
      if (await ConfigModel.editEdit(CONFIG_NAME_LIBRARY, "closeExtra", v)) {
        msg_success("修改成功")
      }
    },
    async closeInnerChange(v) {
      if (await ConfigModel.editEdit(CONFIG_NAME_LIBRARY, "closeInner", v)) {
        msg_success("修改成功")
      }
    }
  }
}
</script>

<style scoped>

</style>
