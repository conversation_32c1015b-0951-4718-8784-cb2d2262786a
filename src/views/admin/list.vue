<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success"
                     @click="ListMethods().clickAdd()">新增用户
          </el-button>
        </div>
      </div>
    </div>

    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              highlight-current-row
              style="width: 100%;">
      <el-table-column label="账号" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.username }}</span>
        </template>
      </el-table-column>
      <el-table-column label="姓名" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.realName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="手机号" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.phoneNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="角色" align="center">
        <template slot-scope="scope">
          <span v-for="(item,index) in scope.row.roles[scope.row.platforms[0]]" :key="index">
            {{ enums.roles[item] }}
            <span v-if="index!==scope.row.roles[scope.row.platforms[0]].length-1">、</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="所属平台" align="center">
        <template slot-scope="scope">
          <span>{{ enums.platforms[scope.row.platforms[0]] }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="350"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round v-if="scope.row.platforms[0]==='picoSystem'"
                     @click="ListMethods().clickResetPasswordBtn(scope.row,scope.$index)">重置密码
          </el-button>
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditBtn(scope.row,scope.$index)">修改
          </el-button>
          <el-button type="danger" size="mini" round
                     v-if="scope.row.platforms[0]==='picoSystem'"
                     @click="ListMethods().clickDelBtn(scope.row.adminUserId,scope.$index,scope.row)">
            删除
          </el-button>
          <el-button type="primary" size="mini" round v-if="scope.row.platforms[0]==='picoSystem'"
                     @click="ListMethods().clickEnterAdminBtn(scope.row,scope.$index)">进入后台
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
    <!--详情弹窗-->
    <el-dialog
      title="用户详情"
      :visible.sync="detail.dialog"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="30%"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="editForm" :model="detail.edit" :rules="detail.formRules">
          <el-form-item label="账号:" prop="username">
            <el-input v-model.trim="detail.edit.username" :disabled="detail.type==='edit'"
                      onKeypress="javascript:if(event.keyCode == 32 || event.keyCode == 43)event.returnValue = false;"></el-input>
          </el-form-item>
          <el-form-item label="姓名:" prop="realName">
            <el-input v-model.trim="detail.edit.realName"></el-input>
          </el-form-item>
          <el-form-item label="手机号:" prop="phoneNumber">
            <el-input v-model.trim="detail.edit.phoneNumber"></el-input>
          </el-form-item>
          <el-form-item label="密码:" prop="password" v-if="!detail.edit.adminUserId">
            <el-input type="password" v-model.trim="detail.edit.password"></el-input>
          </el-form-item>
          <el-form-item label="角色:" prop="role">
            <el-select @change="v=>DetailMethods().adminRoleChange(v)" style="width: 100%"
                       :multiple="true"
                       :multiple-limit="1"
                       :disabled="detail.edit.platforms[0]==='picoSystem'"
                       v-model="detail.edit.roles[detail.edit.platforms[0]]">
              <el-option v-for="(item,index) in lists.searchFilter.filter[0].data" :value="item.value"
                         :label="item.label" :key="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="detail.dialog=false">取 消</el-button>
        <el-button v-if="detail.type==='edit'" type="primary"
                   @click="DetailMethods().clickEditBtn()">确认修改</el-button>
        <!--        <el-button type="danger"-->
        <!--                   v-if="detail.type==='edit'&&detail.edit.username!==adminUserInfo.userName&&detail.edit.username!=='admin'"-->
        <!--                   @click="DetailMethods().clickDeleteBtn()">删 除</el-button>-->
        <el-button v-if="detail.type==='new'" type="success"
                   @click="DetailMethods().clickAddBtn()">新 增</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {AdminUserModel} from '@/model/erp/AdminUserModel'
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import elDragDialog from '@/directive/el-drag-dialog'
import {msg_confirm, msg_err, msg_success} from '@/utils/ele_component'
import {arrToLVArr, checkPhone, find_obj_from_arr_by_id, objectToLVArr, searchWordFiltration} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {validateMaxLength} from "@/utils/validate";
import {PICO_SYSTEM_URL, PLATFORM_ID_ERP} from "@/model/ConfigModel";
import {EquipmentModel} from "@/model/picoSystem/EquipmentModel";

export default {
  name: 'adminList',
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminUserInfo: state => state.user,
      adminRoles: state => state.user.roles
    })
  },
  props: {
    // 是否作为组件
    asComponent: {
      type: Boolean,
      default: false
    },
    // 筛选平台
    platformId: {
      type: String,
      default: ""
    },
  },
  data() {
    // 校检电话号码
    const validatePhoneNumber = (rule, value, callback) => {
      if (!checkPhone(this.detail.edit.phoneNumber)) {
        callback(new Error('手机号格式不正确'))
      }
      callback()
    }
    // 校检用户角色
    const validateRole = (rule, value, callback) => {
      if (Object.keys(this.detail.edit.roles).length === 0) {
        callback(new Error('请选择用户角色'))
      }
      callback()
    }
    return {
      // 外部方法
      find_obj_from_arr_by_id: find_obj_from_arr_by_id,
      PLATFORM_ID_ERP: PLATFORM_ID_ERP,
      // 枚举
      enums: enums,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '姓名',
              key: 'realName',
              value: '',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '账号',
              key: 'username',
              value: '',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '手机号',
              key: 'phoneNumber',
              value: '',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            }
          ],
          filter: [
            {
              type: 'select',
              label: '权限角色',
              key: 'roles',
              hidden: true,
              value: '',
              data: [],
              dataObject: [],
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {

              }
            }
          ]
        }
      },
      // 详情弹窗
      detail: {
        $index: 0,
        dialog: false,
        type: 'edit',// 编辑模式、新增模式
        edit: {
          roles: {},
          platforms: ["erp"]
        },
        // 输入检测
        formRules: {
          'username': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 20, "账号"), trigger: 'blur'},
          'realName': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 20, "姓名"), trigger: 'blur'},
          'phoneNumber': {required: true, validator: validatePhoneNumber},
          'password': {required: true, pattern: /^\w{6,}$/, message: '密码至少6位'},
          'role': {required: true, message: '请选择角色权限', validator: validateRole},
        },
      }
    }
  },
  async mounted() {
    // 获取列表
    this.ListMethods().getList(0, this.lists.pages.size, this.lists.query)
    // 初始化筛选
    this.ListMethods().initFilter()
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          [$this.lists.list, $this.lists.pages] = await AdminUserModel.getPageList(page - 1, size, "", query, $this.platformId)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter() {
          let role = $this.enums.roles
          // 初始化权限角色列表
          if ($this.asComponent) {
            role = {
              'picoDealer': 'Pico经销商'
            }
          }
          $this.lists.searchFilter.filter[0]['data'] = objectToLVArr(role)
          $this.lists.searchFilter.filter[0]['dataObject'] = role
          $this.lists.searchFilter.filter[0]['dataOrigin'] = objectToLVArr(role)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击列表的编辑按钮
        clickEditBtn(edit, $index) {
          $this.detail.edit = JSON.parse(JSON.stringify(edit))
          $this.detail.type = 'edit'
          $this.detail.$index = $index
          $this.detail.dialog = true
          setTimeout(() => {
            $this.$refs['editForm'].clearValidate()
          }, 300)
        },
        // 点击删除按钮
        async clickDelBtn(id, $index, entity) {
          if (await msg_confirm('确认删除该用户吗？')) {
            // 删除前判断
            let role = entity["platforms"][0]
            switch (role) {
              // pico经销商下面不能绑定pico
              case "picoSystem":
                if (await msg_confirm("删除之后，该账号将无法继续登录，是否确认删除？")) {
                  // todo 逻辑删除
                  if (await AdminUserModel.picoLogicDeleteDealer(id)) {
                    msg_success("删除成功")
                  }
                }
                break
              default:
                if (await AdminUserModel.deleteOne(id)) {
                  msg_success('删除成功!')
                }
                break
            }
            await $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
            if ($this.asComponent) {
              // 发出已修改回调
              $this.$emit("afterEdit", true)
            }
          }
        },
        // 点击新增按钮
        async clickAdd() {
          $this.detail.type = 'new'
          $this.detail.edit = {
            roles: {},
            platforms: ["erp"]
          }
          $this.detail.dialog = true
          setTimeout(() => {
            $this.$refs['editForm'].clearValidate()
          }, 300)
        },
        // 点击重置密码按钮
        async clickResetPasswordBtn(user) {
          if (await msg_confirm(`确认重置密码，将初始该用户的登录密码，同时发送短信至${user.phoneNumber}通知用户，是否确认重置？`)) {
            let data = await AdminUserModel.resetPicoDealerPassword(user.adminUserId)
            if (data) {
              msg_success("重置密码成功")
            }
          }
        },
        // 点击进入pico管理系统后台按钮
        async clickEnterAdminBtn(user) {
          let platformId = user.platforms[0]
          let adminUserId = user.adminUserId
          AdminUserModel.generateAdminToken(platformId, adminUserId).then(res => {
            if (res.code === "000000") {
              window.open(PICO_SYSTEM_URL + "#/login?redirect=%2Fdashboard&directToken=" + res.data)
            }
          })
        }
      }
    },
    // 详情Methods
    DetailMethods() {
      let $this = this
      return {
        // 用户角色改变
        adminRoleChange(value) {
          $this.$forceUpdate();
        },
        // 点击确定修改按钮
        async clickEditBtn() {
          $this.$refs['editForm'].validate(async validate => {
            if (validate) {
              $this.detail.loadingEdit = true;
              if (await AdminUserModel.addOrEdit($this.detail.edit)) {
                msg_success('修改用户信息成功')
              }
              $this.detail.loadingEdit = false;
              // 修改列表信息
              $this.$set($this.lists.list, $this.detail.$index, $this.detail.edit)
              $this.detail.dialog = false
              if ($this.asComponent) {
                // 发出已修改回调
                $this.$emit("afterEdit", true)
              }
            }
          })
        },
        // 点击删除按钮
        async clickDeleteBtn() {
          if (await msg_confirm('确认删除该用户吗？删除后不可用恢复！')) {
            $this.detail.loadingDel = true;
            if (await AdminUserModel.deleteOne($this.detail.edit.adminUserId)) {
              $this.detail.dialog = false
              // 修改pageTool 数量-1
              $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
              msg_success('删除成功')
              if ($this.asComponent) {
                // 发出已修改回调
                $this.$emit("afterEdit", true)
              }
            }
            $this.detail.loadingDel = false;
          }
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.$refs['editForm'].validate(async validate => {
            if (validate) {
              $this.detail.loadingAdd = true;
              // 如果添加的是picoDealer角色，将其改为picoSystem系统下
              if ($this.detail.edit.roles["erp"][0] === "picoDealer") {
                $this.detail.edit.roles = {
                  "picoSystem": ["picoDealer"]
                }
                $this.detail.edit.platforms = ["picoSystem"]
              }
              if (await AdminUserModel.add($this.detail.edit).catch(res => {
                $this.detail.loadingAdd = false;
              })) {
                msg_success('新增成功')
                //$this.lists.list.push(newData)
                $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                if ($this.asComponent) {
                  // 发出已修改回调
                  $this.$emit("afterEdit", true)
                }
              } else {
                msg_err("新增失败")
              }
              $this.detail.dialog = false
              $this.detail.loadingAdd = false;
            }
          })
        }
      }
    }
  }
}
</script>

<style scoped>

</style>
