<template>
  <div class="app-container">
    <div class="info-container">
      <el-form>
        <el-form-item label="我的手机号：">
          {{ user.userName }}
        </el-form-item>
        <el-form-item label="我的姓名：">
          {{ user.name }}
        </el-form-item>
        <el-form-item label="我的角色：">
          {{enums.roles[user.roles[0]]}}
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import enums from '@/enums/index'

export default {
  name: 'myInfo',
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  data() {
    return {
      enums: enums
    }
  }
}
</script>

<style scoped>

</style>
