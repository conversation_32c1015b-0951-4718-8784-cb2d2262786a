<template>
  <div style="width: 900px;margin:0 auto;margin-bottom: 50px">
    <div v-for="item in list11" style="margin-top: 50px">
      <!--列表-->
      <div class="userInfo" style="margin-top: 30px;margin-bottom: 20px;">
        <div style="text-align: center"><span>学校：</span>西南财经大学</div>
      </div>
      <el-table :data="item" element-loading-text="加载中" border fit
                style="width: 100%;"
      >
        <el-table-column label="实验名称" align="center" width="250px">
          <template slot-scope="scope">
            <span>{{ scope.row.a }}</span>
          </template>
        </el-table-column>
        <el-table-column label="学生信息" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.c }}</span>
          </template>
        </el-table-column>
        <el-table-column label="开始时间" align="center" width="160px">
          <template slot-scope="scope">
            <span>{{ scope.row.b }}</span>
          </template>
        </el-table-column>
        <el-table-column label="分数" align="center" width="60px">
          <template slot-scope="scope">
            <span>{{ scope.row.d }}</span>
          </template>
        </el-table-column>
        <el-table-column label="实验用时" align="center" width="90px;">
          <template slot-scope="scope">
            <span>{{ scope.row.e }}</span>
          </template>
        </el-table-column>
        <el-table-column label="实验结果" align="center" width="90px;">
          <template slot-scope="scope">
            <span>完成</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import {API_URL_ERP} from '@/model/ConfigModel'

export default {
  name: 'TestIndex',
  data() {
    return {
      lists: {
        list1: [
          {
            a: "嘉陵江战役虚拟仿真实验",
            b: "2023-01-03 09:36:35",
            c: "管理科学与工程学院\n2022级管理科学与工程类\n42227107  尚佳颖",
            d: "95",
            e: "95分47秒"
          },
          {
            a: "嘉陵江战役虚拟仿真实验",
            b: "2023-01-03 09:38:04",
            c: "管理科学与工程学院\n2022级管理科学与工程类\n42227083  李孟临",
            d: "100",
            e: "98分33秒"
          },
          {
            a: "嘉陵江战役虚拟仿真实验",
            b: "2023-01-03 09:39:55",
            c: "管理科学与工程学院\n2022级管理科学与工程类\n42227104  张亚昕",
            d: "100",
            e: "94分25秒"
          },
          {
            a: "嘉陵江战役虚拟仿真实验",
            b: "2023-01-03 09:39:58",
            c: "管理科学与工程学院\n2022级管理科学与工程类\n42227080  张云翔",
            d: "92",
            e: "105分58秒"
          },
          {
            a: "嘉陵江战役虚拟仿真实验",
            b: "2023-01-03 09:40:15",
            c: "管理科学与工程学院\n2022级管理科学与工程类\n42227014  杨程茹",
            d: "95",
            e: "102分12秒"
          }
          ,
          {
            a: "嘉陵江战役虚拟仿真实验",
            b: "2023-01-03 09:40:23",
            c: "管理科学与工程学院\n2022级管理科学与工程类\n42222003  范妤菲",
            d: "100",
            e: "98分30秒"
          }
        ],
        list2: [
          {
            a: "红军长征之四渡赤水虚拟仿真实验",
            b: "2022-03-12 14:16:25",
            c: "法学院\n2021级法学（法学与金融学双学位）\n42122036  马羽昕",
            d: "100",
            e: "107分23秒"
          },
          {
            a: "红军长征之四渡赤水虚拟仿真实验",
            b: "2022-03-12 14:17:23",
            c: "法学院\n2021级法学（法学与金融学双学位）\n42122018  耿丽晶",
            d: "95",
            e: "110分11秒"
          },
          {
            a: "红军长征之四渡赤水虚拟仿真实验",
            b: "2022-03-12 14:17:45",
            c: "法学院\n2021级法学（法学与金融学双学位）\n42122048  罗婕",
            d: "100",
            e: "98分49秒"
          },
          {
            a: "红军长征之四渡赤水虚拟仿真实验",
            b: "2022-03-12 14:18:32",
            c: "法学院\n2021级法学（法学与金融学双学位）\n42122021  王沣田",
            d: "100",
            e: "94分27秒"
          },
          {
            a: "红军长征之四渡赤水虚拟仿真实验",
            b: "2022-03-12 14:18:48",
            c: "法学院\n2021级法学（法学与金融学双学位）\n42122050  陈维佳",
            d: "90",
            e: "108分32秒"
          }
        ],
        list3: [],
      },
      list11: []
    }
  },
  mounted() {
    this.list11 = this.genList()
  },
  methods: {
    genList() {
      let experimentName = [
        "伟大建党精神虚拟仿真实验",
        "苏区精神虚拟仿真实验",
        "西柏坡精神虚拟仿真实验",
        "南泥湾精神虚拟仿真实验",
        "沂蒙精神虚拟仿真实验",
        "井冈山精神虚拟仿真实验",
        "伟大长征精神虚拟仿真实验",
        "伟大抗战精神虚拟仿真实验",
        "'两弹一星'精神虚拟仿真实验",
        "大庆精神虚拟仿真实验",
        "塞罕坝精神虚拟仿真实验",
        "抗美援朝精神虚拟仿真实验",
        "焦裕禄精神虚拟仿真实验",
        "雷锋精神虚拟仿真实验",
        "红旗渠精神虚拟仿真实验",
        "特区精神虚拟仿真实验",
        "载入航天精神虚拟仿真实验",
        "伟大抗疫精神虚拟仿真实验",
        "嘉陵江战役虚拟仿真实验",
        "红军长征之四渡赤水虚拟仿真实验"
      ]
      let userList1 = [
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253006 丁弈洲",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253056 徐可",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253038 陈佳蕊",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253011 尚思奇",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253028 吴晓玥",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253041 赖璋洁",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253050 杨子川",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253012 何诗楠",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253053 杜昱彤",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253057 郑嘉晨",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253003 郑凯瑞",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253043 邹瑞琦",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253021 周楠",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253026 刘宸宇",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253033 邹瑷励",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253024 王晓溪",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253015 张鑫艺",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253034 张佳欣",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253017 罗金明",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253023 徐日琪",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253035 卜欣怡",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253049 林熙",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253009 周安恺",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253008 汪小雨",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253025 陆敏",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253037 肖瑞欣",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253002 赵珈仪",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253020 刘奕帆",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253060 付泽恺",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253058 刘明哲",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253027 蔡之怡",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253046 冯福帅",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253005 周子涵",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253045 孙明远",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253044 吴文昕",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253051 王佳崇",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253014 周星宇",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253040 尹华逸",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253007 朱津毅",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253055 符雨渟",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253048 王心意",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253013 郝琳青",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253036 张馨月",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253047 王心如",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253029 张卓文",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253052 何昫尧",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253019 刘乐怡",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253022 朱泓宇",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253004 丁芝兰",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253010 李欣悦",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253039 韩峪妃",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253032 胥雅晨",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253042 章茜墨",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253018 聂子航",
        "特拉华数据科学学院\n2022级金融数学（金融服务与量化分析）\n 42253030 潘煜航",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254036 冯林馨",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254047 刘杨",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254043 刘彦麟",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254022 赵相粤",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254025 萧竹熙",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254048 刘天翔",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254010 富子隽",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254030 梁修齐",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254049 张子琳",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254040 杨婉胤",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254013 张璇琦",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254052 周楷棋",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254033 廖宇嘉",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254012 郭玉玺",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254057 郑俊杰",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254029 唐诗睿",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254034 董一蒙",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254020 陈子赟",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254038 黄馨谊",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254016 梁晟彬",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254054 陈宇弢",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254001 魏丹莹",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254018 唐培杰",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254006 夏林希",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254056 林洋孜",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254024 陈泽昊",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254031 李光灏",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254004 胡心桐",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254002 闫瑾",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254003 许星晨",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254035 刘惠心",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254060 宋悦然",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254046 付婷煜",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254015 许嘉诚",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254026 王若璞",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254042 蔡涵",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254059 张弛",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254044 盖峻扬",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254058 张高稳",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254023 朱延",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254005 于昕玥",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254014 宋欣雨",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254027 丁一凡",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254021 郭瑭钰",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254039 张恒宜",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254045 傅泓程",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254011 周馨悦",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254050 李赫阳",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254028 叶子馨",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254053 叶炙轩",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254055 詹烨",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254041 马姣",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254019 刘天琦",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254007 吴高炅",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254037 尚千植",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254008 褚家杰",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254032 刘馨雯",
        "特拉华数据科学学院\n2022级信息管理与信息系统（信息系统与数据管理）\n 42254009 姜杉",
      ]
      let userList2=[

    ]
      let userList3=[
        "计算机与人工智能学院\n2022级计算机类\n42211178 杨和畅",
        "计算机与人工智能学院\n2022级计算机类\n42211132 胡妍",
        "计算机与人工智能学院\n2022级计算机类\n42211049 刘巧艺",
        "计算机与人工智能学院\n2022级计算机类\n42211170 罗靖成",
        "计算机与人工智能学院\n2022级计算机类\n42211054 吴素涵",
        "计算机与人工智能学院\n2022级计算机类\n42211157 周星彤",
      ]
      let userList4=[
        "数学学院\n2021级计算金融\n42142024 郑筱彤",
        "数学学院\n2021级计算金融\n42142038 夏琬翔",
        "数学学院\n2021级计算金融\n42142027 苏东",
        "数学学院\n2021级计算金融\n42142007 王一阳",
        "数学学院\n2021级计算金融\n42142011 吴嘉宇",
        "数学学院\n2021级计算金融\n42142036 张译之",
      ]
      let all_list = []
      let jt = 0;
      let m = 28;
      for (let i = 19; i < 20; i++) {
        let list = []
        for (let j = 0; j < 5; j++) {
          const randomIndex1 = Math.floor(Math.random() * 3);
          const randomIndex2 = Math.floor(Math.random() * 5);
          const randomIndex3 = Math.floor(Math.random() * 5);
          const randomSecond1 = Math.floor(Math.random() * 60) + 1;
          const randomSecond2 = Math.floor(Math.random() * 60) + 1;
          let li = {
            a: experimentName[i],
            b: "2021-11-07 14:" + (m + randomIndex3) + ":" + (randomSecond1 < 10 ? "0" + randomSecond1 : randomSecond1),
            c: userList4[jt],
            d: [88,92, 96, 100][randomIndex1],
            e: 90+[6, 7, 8, 9, 10][randomIndex2] + "分" + randomSecond2 + "秒"
          }
          list.push(li)
          jt++
        }
        all_list.push(list)
      }
      return all_list
    },
  }
}
</script>

<style scoped>

</style>
