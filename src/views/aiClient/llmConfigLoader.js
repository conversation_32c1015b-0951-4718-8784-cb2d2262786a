import {AiClientConfigModel} from "@/model/aiClient/AiClientConfigModel";

/**
 * 深拷贝函数，避免循环引用
 * @param {Object} obj 要拷贝的对象
 * @param {Map} cache 用于缓存已经拷贝过的对象，避免循环引用
 * @returns {Object} 拷贝后的对象
 */
const deepClone = (obj, cache = new Map()) => {
  // 处理非对象或null
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  // 处理日期
  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }

  // 处理正则表达式
  if (obj instanceof RegExp) {
    return new RegExp(obj)
  }

  // 检查循环引用
  if (cache.has(obj)) {
    return cache.get(obj)
  }

  // 创建新对象或数组
  const result = Array.isArray(obj) ? [] : {}

  // 缓存对象，处理循环引用
  cache.set(obj, result)

  // 递归复制所有属性
  Object.keys(obj).forEach(key => {
    // 跳过函数和内部属性
    if (typeof obj[key] === 'function' || key.startsWith('_')) {
      return
    }
    result[key] = deepClone(obj[key], cache)
  })

  return result
}

/**
 * 确保Agent配置的安全性，处理可能存在的无效数据
 * @param {Object} agent Agent配置对象
 * @returns {Object} 处理后的Agent配置
 */
const sanitizeAgentConfig = (agent) => {
  if (!agent || typeof agent !== 'object') {
    return {
      id: `agent_${Date.now()}`,
      name: '默认Agent',
      enabled: true,
      config: {
        providerId: '',
        modelId: '',
        provider: '',
        model: '',
        temperature: 0.7,
        max_tokens: 4096,
        prompt: [{ role: 'system', content: '你是一个AI助手。' }]
      }
    }
  }

  // 确保config对象存在
  if (!agent.config) {
    agent.config = {}
  }

  // 确保prompt数组存在且格式正确
  if (!Array.isArray(agent.config.prompt)) {
    agent.config.prompt = [{ role: 'system', content: '你是一个AI助手。' }]
  } else {
    // 确保每个prompt对象格式正确
    agent.config.prompt = agent.config.prompt.map(item => {
      if (typeof item !== 'object') {
        return { role: 'system', content: '' }
      }
      return {
        role: item.role || 'system',
        content: item.content || ''
      }
    })
  }

  return agent
}

// 加载LLM配置
export async function loadLlmConfig() {
  try {
    let publicConfig=await AiClientConfigModel.getConfig('config_ai_client', 'public_llm_config')
    publicConfig=JSON.parse(publicConfig)
    let configObject={success:true,config:publicConfig,message:''}
    const { success, config, message } = configObject
    if (success && config) {
      // 处理agents，确保格式正确
      if (Array.isArray(config.agents)) {
        config.agents = config.agents.map(sanitizeAgentConfig)
      }

      return config
    } else {
      console.error('读取配置失败:', message)
      // 返回默认配置
      return { providers: [], agents: [] }
    }
  } catch (error) {
    console.error('加载配置异常:', error)
    return { providers: [], agents: [] }
  }
}

// 保存LLM配置
export async function saveLlmConfig(config) {
  try {
    // 深度克隆配置，避免循环引用和不可序列化的值
    const clonedConfig = deepClone(config)

    // 确保每个Agent配置格式正确
    if (Array.isArray(clonedConfig.agents)) {
      clonedConfig.agents = clonedConfig.agents.map(sanitizeAgentConfig)
    }

    // 测试序列化
    try {
      JSON.stringify(clonedConfig)
    } catch (err) {
      console.error('配置对象无法序列化:', err)
      throw new Error(`配置对象无法序列化: ${err.message}`)
    }

    const success= await AiClientConfigModel.editConfig('config_ai_client', 'public_llm_config', clonedConfig)
    if (!success) {
      throw new Error('保存配置失败')
    }
    return success
  } catch (error) {
    console.error('保存配置失败:', error)
    throw error
  }
}
