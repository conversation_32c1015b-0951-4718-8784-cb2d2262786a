<template>
  <div class="system-settings-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="轮播图管理" name="carousel">
        <div class="carousel-management">
          <el-button type="primary" @click="addCarouselImage" class="mb-20">添加轮播图</el-button>
          
          <el-table :data="carouselData.carouselImages" border style="width: 100%">
            <el-table-column label="序号" type="index" width="80" align="center"></el-table-column>
            <el-table-column label="预览图" width="200" align="center">
              <template slot-scope="scope">
                <el-image 
                  style="width: 180px; height: 90px" 
                  :src="scope.row.url" 
                  fit="cover"
                  :preview-src-list="[scope.row.url]">
                </el-image>
              </template>
            </el-table-column>
            <el-table-column prop="alt" label="标题" width="180"></el-table-column>
            <el-table-column prop="caption" label="描述"></el-table-column>
            <el-table-column label="操作" width="180" align="center">
              <template slot-scope="scope">
                <el-button type="text" @click="editCarouselImage(scope.$index)">编辑</el-button>
                <el-button type="text" class="danger-text" @click="deleteCarouselImage(scope.$index)">删除</el-button>
                <el-button type="text" @click="moveUp(scope.$index)" :disabled="scope.$index === 0">上移</el-button>
                <el-button type="text" @click="moveDown(scope.$index)" :disabled="scope.$index === carouselData.carouselImages.length - 1">下移</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="save-btn-container">
            <el-button type="primary" @click="saveCarouselSettings">保存轮播图设置</el-button>
          </div>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="用户协议" name="userAgreement">
        <div class="editor-container">
          <tinymce v-model="userAgreementData.content" :height="500" />
          <div class="save-btn-container">
            <el-button type="primary" @click="saveUserAgreement">保存用户协议</el-button>
          </div>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="隐私政策" name="privacyPolicy">
        <div class="editor-container">
          <tinymce v-model="privacyPolicyData.content" :height="500" />
          <div class="save-btn-container">
            <el-button type="primary" @click="savePrivacyPolicy">保存隐私政策</el-button>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    
    <!-- 轮播图编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="currentCarouselImage" label-width="80px">
        <el-form-item label="图片">
          <el-upload
            class="avatar-uploader"
            action="#"
            :http-request="uploadImage"
            :show-file-list="false"
            :before-upload="beforeImageUpload">
            <img v-if="currentCarouselImage.url" :src="currentCarouselImage.url" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <div class="image-tip">建议尺寸: 800px × 400px</div>
        </el-form-item>
        <el-form-item label="标题">
          <el-input v-model="currentCarouselImage.alt"></el-input>
        </el-form-item>
        <el-form-item label="描述">
          <el-input type="textarea" v-model="currentCarouselImage.caption" :rows="3"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmCarouselImage">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Tinymce from '@/components/Tinymce'
import { AiClientConfigModel } from '@/model/aiClient/AiClientConfigModel'
import { BaseUploadModel } from '@/model/BaseUploadModel'

export default {
  name: 'SystemSettings',
  components: {
    Tinymce
  },
  data() {
    return {
      // 标签页激活名称
      activeName: 'carousel',
      
      // 轮播图相关数据
      carouselData: {
        carouselImages: []
      },
      
      // 用户协议相关数据
      userAgreementData: {
        content: ''
      },
      
      // 隐私政策相关数据
      privacyPolicyData: {
        content: ''
      },
      
      // 对话框相关
      dialogVisible: false,
      dialogTitle: '添加轮播图',
      currentCarouselImage: {
        url: '',
        alt: '',
        caption: ''
      },
      editingIndex: -1,
      
      // 上传相关
      uploadMethods: null
    }
  },
  created() {
    this.initData()
    this.uploadMethods = this.UploadMethods()
  },
  methods: {
    // 初始化数据
    async initData() {
      try {
        // 获取轮播图数据
        const carouselData = await AiClientConfigModel.getConfig('config_ai_client', 'carouselImages')
        if (carouselData) {
          this.carouselData.carouselImages = JSON.parse(carouselData)
        }
        
        // 获取用户协议数据
        const userAgreementData = await AiClientConfigModel.getConfig('config_ai_client', 'userAgreement')
        if (userAgreementData) {
          this.userAgreementData.content = userAgreementData
        }
        
        // 获取隐私政策数据
        const privacyPolicyData = await AiClientConfigModel.getConfig('config_ai_client', 'privacyPolicy')
        if (privacyPolicyData) {
          this.privacyPolicyData.content = privacyPolicyData
        }
      } catch (error) {
        this.$message.error('获取配置数据失败')
        console.error(error)
      }
    },
    
    // ===== 轮播图相关方法 =====
    addCarouselImage() {
      this.dialogTitle = '添加轮播图'
      this.currentCarouselImage = {
        url: '',
        alt: '',
        caption: ''
      }
      this.editingIndex = -1
      this.dialogVisible = true
    },
    
    editCarouselImage(index) {
      this.dialogTitle = '编辑轮播图'
      this.currentCarouselImage = JSON.parse(JSON.stringify(this.carouselData.carouselImages[index]))
      this.editingIndex = index
      this.dialogVisible = true
    },
    
    deleteCarouselImage(index) {
      this.$confirm('确认删除该轮播图?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.carouselData.carouselImages.splice(index, 1)
        this.$message.success('删除成功')
      }).catch(() => {})
    },
    
    moveUp(index) {
      if (index > 0) {
        const temp = this.carouselData.carouselImages[index]
        this.$set(this.carouselData.carouselImages, index, this.carouselData.carouselImages[index - 1])
        this.$set(this.carouselData.carouselImages, index - 1, temp)
      }
    },
    
    moveDown(index) {
      if (index < this.carouselData.carouselImages.length - 1) {
        const temp = this.carouselData.carouselImages[index]
        this.$set(this.carouselData.carouselImages, index, this.carouselData.carouselImages[index + 1])
        this.$set(this.carouselData.carouselImages, index + 1, temp)
      }
    },
    
    confirmCarouselImage() {
      if (!this.currentCarouselImage.url) {
        return this.$message.warning('请上传轮播图片')
      }
      
      if (!this.currentCarouselImage.alt) {
        return this.$message.warning('请输入轮播图标题')
      }
      
      if (!this.currentCarouselImage.caption) {
        return this.$message.warning('请输入轮播图描述')
      }
      
      if (this.editingIndex === -1) {
        // 添加
        this.carouselData.carouselImages.push(this.currentCarouselImage)
      } else {
        // 编辑
        this.$set(this.carouselData.carouselImages, this.editingIndex, this.currentCarouselImage)
      }
      
      this.dialogVisible = false
      this.$message.success('操作成功')
    },
    
    async saveCarouselSettings() {
      try {
        await AiClientConfigModel.editConfig(
          'config_ai_client', 
          'carouselImages', 
          JSON.stringify(this.carouselData.carouselImages)
        )
        this.$message.success('保存成功')
      } catch (error) {
        this.$message.error('保存失败')
        console.error(error)
      }
    },
    
    // ===== 用户协议相关方法 =====
    async saveUserAgreement() {
      try {
        await AiClientConfigModel.editConfig(
          'config_ai_client', 
          'userAgreement', 
          this.userAgreementData.content
        )
        this.$message.success('保存成功')
      } catch (error) {
        this.$message.error('保存失败')
        console.error(error)
      }
    },
    
    // ===== 隐私政策相关方法 =====
    async savePrivacyPolicy() {
      try {
        await AiClientConfigModel.editConfig(
          'config_ai_client', 
          'privacyPolicy', 
          this.privacyPolicyData.content
        )
        this.$message.success('保存成功')
      } catch (error) {
        this.$message.error('保存失败')
        console.error(error)
      }
    },
    
    // ===== 上传相关方法 =====
    UploadMethods() {
      let $this = this
      return {
        beforeImageUpload(file) {
          const isImage = file.type.indexOf('image/') === 0
          const isLt2M = file.size / 1024 / 1024 < 2
          
          if (!isImage) {
            $this.$message.error('上传文件只能是图片格式!')
            return false
          }
          
          if (!isLt2M) {
            $this.$message.error('上传图片大小不能超过 2MB!')
            return false
          }
          
          return true
        },
        
        uploadImage(options) {
          const file = options.file
          
          BaseUploadModel.qiNiuUpload(file, {
            next: (result) => {
              // 上传进度处理
            },
            error: (errResult) => {
              console.error(errResult)
              $this.$message.error('上传失败')
            },
            complete: (result) => {
              const domain = BaseUploadModel.getBucketDomain(file)
              const url = domain + '/' + result.key
              $this.currentCarouselImage.url = url
            }
          })
        }
      }
    },
    
    beforeImageUpload(file) {
      return this.uploadMethods.beforeImageUpload(file)
    },
    
    uploadImage(options) {
      this.uploadMethods.uploadImage(options)
    }
  }
}
</script>

<style lang="scss" scoped>
.system-settings-container {
  padding: 20px;
  
  .mb-20 {
    margin-bottom: 20px;
  }
  
  .carousel-management,
  .editor-container {
    padding: 20px 0;
  }
  
  .save-btn-container {
    margin-top: 20px;
    text-align: center;
  }
  
  .danger-text {
    color: #F56C6C;
  }
  
  .avatar-uploader {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      
      &:hover {
        border-color: #409EFF;
      }
    }
    
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 89px;
      line-height: 89px;
      text-align: center;
    }
    
    .avatar {
      width: 178px;
      height: 89px;
      display: block;
    }
  }
  
  .image-tip {
    color: #909399;
    font-size: 12px;
    margin-top: 5px;
  }
}
</style>
