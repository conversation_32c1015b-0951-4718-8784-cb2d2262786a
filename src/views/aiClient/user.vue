<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success" @click="ListMethods().clickAddBtn()">新增用户
          </el-button>
        </div>
      </div>
    </div>
    <!--列表-->
    <el-table class="lists" :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="用户名" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.username }}</span>
        </template>
      </el-table-column>
      <el-table-column label="真实姓名" align="center" width="150px">
        <template slot-scope="scope">
          <span>{{ scope.row.realName ? scope.row.realName : "/" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属机构" align="center">
        <template slot-scope="scope">
          <span>{{ lists.searchFilter.filter[0].dataObject[scope.row.organId] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="邮箱" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.email ? ListMethods().maskEmail(scope.row.email) : "/" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否启用" align="center" width="80px">
        <template slot-scope="scope">
          <span>{{ {0: "启用", 1: "禁用"}[scope.row.deleted] }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="300"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="success" size="mini" round
                     style="margin-top: 10px;"
                     @click="ListMethods().clickEditBtn(scope.row,scope.$index)">详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--详情弹窗-->
    <el-dialog
      :title="entityInfo.title"
      :visible.sync="entityInfo.dialog"
      append-to-body
      width="800px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
          <el-form-item label="所属机构:" prop="organId">
            <el-select :disabled="entityInfo.type==='edit'" value="entityInfo.edit.organId" placeholder="请选择机构" v-model="entityInfo.edit.organId">
              <el-option v-for="(item, index) in lists.searchFilter.filter[0].data" :key="index"
                         :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户名:" prop="username">
            <el-input v-model="entityInfo.edit.username" :disabled="entityInfo.type==='edit'"></el-input>
          </el-form-item>
          <el-form-item label="真实姓名:" prop="realName">
            <el-input v-model="entityInfo.edit.realName"></el-input>
          </el-form-item>
          <el-form-item label="newAPi的key:" prop="keyOfNewApi">
            <el-input v-model="entityInfo.edit.keyOfNewApi"></el-input>
          </el-form-item>
          <el-form-item label="邮箱:" prop="email">
            <el-input v-model="entityInfo.edit.email"></el-input>
          </el-form-item>
          <el-form-item label="是否启用:" prop="deleted">
            <el-select v-model="entityInfo.edit.deleted">
              <el-option label="启用" :value="0"></el-option>
              <el-option label="禁用" :value="1"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="entityInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityInfoMethods().clickAddBtn()" :loading="entityInfo.loading"
                   v-if="entityInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityInfoMethods().clickEditBtn()" :loading="entityInfo.loading"
                   v-if="entityInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat, timeFormat} from "@/filters";
import {date_format, getQuery, objectToLVArr, searchWordFiltration} from "@/utils/common";
import {WebAnalyticsModel} from "@/model/erp/WebAnalyticsModel";
import {ToolsModel} from "@/model/erp/ToolsModel";
import webAnalyticsEnum from "@/enums/webAnalytics";
import {AiClientUserModel} from "@/model/aiClient/AiClientUserModel";
import erpUploaderOneVideo from "@/views/components/erpUploaderOneVideo.vue";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic.vue";
import {validateMaxLength} from "@/utils/validate";
import {msg_success} from "@/utils/ele_component";
import {ExperimentModel} from "@/model/exp/ExperimentModel";
import {CommonModel} from "@/model/CommonModel";
import {AiClientOrganModel} from "@/model/aiClient/AiClientOrganModel";

export default {
  name: "user",
  components: {
    erpUploaderOnePic, erpUploaderOneVideo,
    ListSearchFilter
  },
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission,
      adminUserId: state => state.user.id
    })
  },
  filters: {dateFormat, timeFormat},
  data: function () {
    let $this = this;
    return {
      window: window,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      webAnalyticsEnum: webAnalyticsEnum,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '用户名',
              key: 'name',
              value: '',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '用户姓名',
              key: 'realName',
              value: '',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '邮箱',
              key: 'email',
              value: '',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              }
            },
          ],
          filter: [
            {
              type: 'select',
              label: '所属机构',
              key: 'organId',
              hidden: false,
              value: '',
              data: [],
              dataObject: [],
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {

              }
            }
          ]
        }
      },
      entityInfo: {
        dialog: false,
        title: "新增用户",
        type: "add",
        loading: false,
        uploading: false,
        edit: {
          info: {}
        },
        // 输入检测
        formRules: {
          'username': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 50, "用户名"),
            trigger: 'blur'
          },
          'realName': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 50, "真实姓名"),
            trigger: 'blur'
          },

          'organId': {required: true, message: "请选择所属机构", trigger: 'blur'},
          'email': {
            required: false,
            validator: (r, v, c) => validateMaxLength(r, v, c, 100, "邮箱"),
            trigger: 'blur'
          },
        },
      },
    }
  },
  async mounted() {
    this.ListMethods().initFilter()
    this.ListMethods().getList(0, 20, {})
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {

        // 点击搜索重置按钮
        clickCleanBtn() {

        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          [list, $this.lists.pages] = await AiClientUserModel.getPageList(page - 1, size, "", query)
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 初始化筛选列表
        async initFilter() {
          $this.lists.loading = true
          // 获取所有机构列表
          let allOrganList = await AiClientOrganModel.getList({});
          let generateListFilter1 = CommonModel.generateListFilterOptions('name', 'organId', allOrganList)
          $this.$set($this.lists.searchFilter.filter[0], "dataObject", generateListFilter1[1])
          $this.$set($this.lists.searchFilter.filter[0], "data", generateListFilter1[0])
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 邮箱脱敏处理
        maskEmail(email) {
          if (!email) return "/";
          const parts = email.split('@');
          if (parts.length !== 2) return email;
          
          const username = parts[0];
          const domain = parts[1];
          
          let maskedUsername;
          if (username.length <= 3) {
            maskedUsername = username.charAt(0) + '*'.repeat(username.length - 1);
          } else {
            maskedUsername = username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1);
          }
          
          return maskedUsername + '@' + domain;
        },
        // 点击添加按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "新增用户"
          $this.entityInfo.edit = {
            deleted: 0,
            email: ''
          };
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
        // 点击列表的编辑按钮
        clickEditBtn(edit, $index) {
          $this.entityInfo.edit = JSON.parse(JSON.stringify(edit))
          $this.entityInfo.type = 'edit'
          $this.entityInfo.title = "修改用户"
          $this.entityInfo.$index = $index
          $this.entityInfo.dialog = true
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
      }
    },
    // 实体Methods
    EntityInfoMethods() {
      let $this = this;
      return {
        // 点击提交新增按钮
        clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              $this.entityInfo.loading = true
              if (await AiClientUserModel.addOrEdit($this.entityInfo.edit).catch(err => {
                $this.entityInfo.loading = false
              })) {
                msg_success('新增机构成功')
                $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                $this.entityInfo.loading = false
                $this.entityInfo.dialog = false
              }
            }
          })
        },
        // 点击修改按钮
        clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              $this.entityInfo.loading = true
              if (await AiClientUserModel.addOrEdit($this.entityInfo.edit).catch(err => {
                $this.entityInfo.loading = false
              })) {
                msg_success('保存成功')
                $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
                $this.entityInfo.dialog = false
                $this.entityInfo.loading = false
              }
            }
          })
        },
      }
    },
  }
}
</script>
<style scoped lang="scss">
.lists {
  a {
    color: #004DCC;
  }

  .can-click {
    color: #004DCC;
    cursor: pointer;
  }
}
</style>
