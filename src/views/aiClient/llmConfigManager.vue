<template>
  <div class="llm-config-manager">
    <div class="config-container">
      <div class="config-header">
        <h2>模型配置管理</h2>
        <!-- <el-button type="primary" @click="handleClose">关闭</el-button> -->
      </div>

      <el-tabs v-model="activeTab" class="config-tabs">
        <el-tab-pane label="供应商" name="providers">
          <div class="tab-header">
            <h3>供应商配置</h3>
            <el-button type="primary" icon="el-icon-plus" round @click="addProvider">
              添加供应商
            </el-button>
          </div>

          <el-collapse v-model="activeProviders" accordion style="height: 100%; overflow: auto;">
            <el-collapse-item v-for="(provider, pIndex) in config.providers" :key="pIndex" :name="pIndex">
              <template slot="title">
                <div class="collapse-title">
                  <span>{{ provider.name }}</span>
                  <div>
                    <el-switch
                      v-model="provider.enabled"
                      active-text="启用"
                      inactive-text="禁用"
                      size="small"
                      style="margin-right: 10px;"
                    ></el-switch>
                    <el-button type="primary" size="small" icon="el-icon-edit" @click.stop="toggleProviderDetail(pIndex)" circle></el-button>
                    <el-button type="danger" size="small" icon="el-icon-delete" @click.stop="removeProvider(pIndex)" circle></el-button>
                    <el-button
                      type="info"
                      size="small"
                      icon="el-icon-arrow-up"
                      @click.stop="moveProvider(pIndex, 'up')"
                      :disabled="pIndex === 0"
                      circle></el-button>
                    <el-button
                      type="info"
                      size="small"
                      icon="el-icon-arrow-down"
                      @click.stop="moveProvider(pIndex, 'down')"
                      :disabled="pIndex === config.providers.length - 1"
                      circle></el-button>
                  </div>
                </div>
              </template>

              <div class="provider-form">
                <el-form label-position="top" :model="provider">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="显示名称">
                        <el-input v-model="provider.name" placeholder="请输入供应商名称"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="类型">
                        <el-select v-model="provider.type" placeholder="请选择供应商类型" style="width: 100%">
                          <el-option label="OpenAI" value="openai"></el-option>
                          <!-- <el-option label="自定义" value="custom"></el-option> -->
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <!-- <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="API URL">
                        <el-input v-model="provider.url" placeholder="请输入API URL"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="API Key">
                        <el-input v-model="provider.api_key" placeholder="请输入API Key" show-password></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row> -->

                  <el-form-item>
                    <el-switch
                      v-model="provider.enabled"
                      active-text="启用"
                      inactive-text="禁用"
                    ></el-switch>
                  </el-form-item>

                  <el-divider>模型列表</el-divider>

                  <div v-for="(model, mIndex) in provider.models" :key="mIndex" class="model-item">
                    <el-card shadow="hover">
                      <div class="model-header">
                        <h4>{{ model.name }}</h4>
                        <div class="model-actions">
                          <el-switch
                            v-model="model.enabled"
                            active-text="启用"
                            inactive-text="禁用"
                            size="small"
                            style="margin-right: 10px;"
                          ></el-switch>
                          <el-button
                            type="danger"
                            size="small"
                            icon="el-icon-delete"
                            @click="removeModel(provider, mIndex)"
                            circle></el-button>
                          <el-button
                            type="info"
                            size="small"
                            icon="el-icon-arrow-up"
                            @click="moveModel(provider, mIndex, 'up')"
                            :disabled="mIndex === 0"
                            circle></el-button>
                          <el-button
                            type="info"
                            size="small"
                            icon="el-icon-arrow-down"
                            @click="moveModel(provider, mIndex, 'down')"
                            :disabled="mIndex === provider.models.length - 1"
                            circle></el-button>
                        </div>
                      </div>

                      <el-row :gutter="20">
                        <el-col :span="8">
                          <el-form-item label="模型名称（对应供应商处模型的名称）">
                            <el-input v-model="model.config.name" placeholder="请输入配置名称"></el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="显示名称">
                            <el-input v-model="model.name" placeholder="请输入模型名称"></el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="描述">
                            <el-input v-model="model.description" placeholder="请输入模型描述"></el-input>
                          </el-form-item>
                        </el-col>
                      </el-row>

                      <el-collapse>
                        <el-collapse-item title="高级配置">
                          <el-row :gutter="20">
                            <el-col :span="12">
                              <el-form-item label="最大生成长度">
                                <el-input-number v-model="model.config.max_tokens" :step="1024" style="width: 100%" :disabled="!model.config.max_tokens_enabled"></el-input-number>
                                <div style="display: flex; align-items: center;">
                                  <el-checkbox v-model="model.config.max_tokens_enabled" @change="handleParamToggle(model.config, 'max_tokens', $event)">启用</el-checkbox>
                                </div>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="温度">
                                <el-slider v-model="model.config.temperature" :min="0" :max="2" :step="0.1" show-input :disabled="!model.config.temperature_enabled"></el-slider>
                                <div style="display: flex; align-items: center;">
                                  <el-checkbox v-model="model.config.temperature_enabled" @change="handleParamToggle(model.config, 'temperature', $event)">启用</el-checkbox>
                                </div>
                              </el-form-item>
                            </el-col>
                          </el-row>
                          <el-row :gutter="20">
                            <el-col :span="12">
                              <el-form-item label="Top P">
                                <el-slider v-model="model.config.top_p" :min="0" :max="1" :step="0.01" show-input :disabled="!model.config.top_p_enabled"></el-slider>
                                <div style="display: flex; align-items: center;">
                                  <el-checkbox v-model="model.config.top_p_enabled" @change="handleParamToggle(model.config, 'top_p', $event)">启用</el-checkbox>
                                </div>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="Top K">
                                <el-input-number v-model="model.config.top_k" :min="1" :step="1" style="width: 100%" :disabled="!model.config.top_k_enabled"></el-input-number>
                                <div style="display: flex; align-items: center;">
                                  <el-checkbox v-model="model.config.top_k_enabled" @change="handleParamToggle(model.config, 'top_k', $event)">启用</el-checkbox>
                                </div>
                              </el-form-item>
                            </el-col>
                          </el-row>
                          <el-row :gutter="20">
                            <el-col :span="12">
                              <el-form-item label="频率惩罚 (Frequency Penalty)">
                                <el-slider v-model="model.config.frequency_penalty" :min="-2" :max="2" :step="0.01" show-input :disabled="!model.config.frequency_penalty_enabled"></el-slider>
                                <div style="display: flex; align-items: center;">
                                  <el-checkbox v-model="model.config.frequency_penalty_enabled" @change="handleParamToggle(model.config, 'frequency_penalty', $event)">启用</el-checkbox>
                                </div>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="存在惩罚 (Presence Penalty)">
                                <el-slider v-model="model.config.presence_penalty" :min="-2" :max="2" :step="0.01" show-input :disabled="!model.config.presence_penalty_enabled"></el-slider>
                                <div style="display: flex; align-items: center;">
                                  <el-checkbox v-model="model.config.presence_penalty_enabled" @change="handleParamToggle(model.config, 'presence_penalty', $event)">启用</el-checkbox>
                                </div>
                              </el-form-item>
                            </el-col>
                          </el-row>
                          <el-form-item label="是否支持思考链">
                            <el-switch v-model="model.thinking_model"></el-switch>
                          </el-form-item>
                        </el-collapse-item>
                      </el-collapse>
                    </el-card>
                  </div>

                  <div class="add-model-btn">
                    <el-button type="primary" size="small" icon="el-icon-plus" @click="addModel(provider)">
                      添加模型
                    </el-button>
                  </div>
                </el-form>

                <div class="provider-actions">
                  <el-button type="danger" size="small" @click="removeProvider(pIndex)">
                    删除供应商
                  </el-button>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-tab-pane>

        <el-tab-pane label="Agent" name="agents">
          <div class="tab-header">
            <h3>Agent配置</h3>
            <el-button type="primary" icon="el-icon-plus" round @click="addAgent">
              添加Agent
            </el-button>
          </div>

          <div class="agent-table-container">
            <el-table :data="config.agents" border style="width: 100%" row-key="id">
              <el-table-column type="expand">
                <template slot-scope="props">
                  <el-form label-position="top" class="agent-detail-form" @submit.native.prevent>
                    <el-form-item label="配置" style="width: 100%;">
                      <el-card shadow="hover" style="width: 100%;">
                        <el-row :gutter="20">
                          <el-col :span="12">
                            <el-form-item label="选择供应商">
                              <el-select
                                v-model="props.row.config.providerId"
                                placeholder="请选择供应商"
                                style="width: 100%;"
                                @change="updateAgentProvider(props.row, $event)"
                                popper-class="agent-select-dropdown"
                              >
                                <el-option
                                  v-for="provider in config.providers.filter(p => p.enabled)"
                                  :key="provider.id"
                                  :label="provider.name"
                                  :value="provider.id"
                                ></el-option>
                              </el-select>
                            </el-form-item>
                          </el-col>
                        </el-row>

                        <el-row :gutter="20">
                          <el-col :span="12">
                            <el-form-item label="选择模型">
                              <el-select
                                v-model="props.row.config.modelId"
                                placeholder="请选择模型"
                                style="width: 100%;"
                                @change="updateAgentModel(props.row, $event)"
                                popper-class="agent-select-dropdown"
                              >
                                <el-option
                                  v-for="model in getProviderModels(props.row.config.providerId)"
                                  :key="model.id"
                                  :label="model.name"
                                  :value="model.id"
                                ></el-option>
                              </el-select>
                            </el-form-item>
                          </el-col>
                        </el-row>

                        <el-row :gutter="20">
                          <el-col :span="12">
                            <el-form-item label="最大生成长度">
                              <el-input-number v-model="props.row.config.max_tokens" :min="1" :step="1024" style="width: 100%" :disabled="!props.row.config.max_tokens_enabled"></el-input-number>
                              <div style="display: flex; align-items: center;">
                                <el-checkbox v-model="props.row.config.max_tokens_enabled" @change="handleParamToggle(props.row.config, 'max_tokens', $event)">启用</el-checkbox>
                              </div>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="温度">
                              <el-slider v-model="props.row.config.temperature" :min="0" :max="2" :step="0.1" show-input :disabled="!props.row.config.temperature_enabled"></el-slider>
                              <div style="display: flex; align-items: center;">
                                <el-checkbox v-model="props.row.config.temperature_enabled" @change="handleParamToggle(props.row.config, 'temperature', $event)">启用</el-checkbox>
                              </div>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row :gutter="20">
                          <el-col :span="12">
                            <el-form-item label="Top P">
                              <el-slider v-model="props.row.config.top_p" :min="0" :max="1" :step="0.01" show-input :disabled="!props.row.config.top_p_enabled"></el-slider>
                              <div style="display: flex; align-items: center;">
                                <el-checkbox v-model="props.row.config.top_p_enabled" @change="handleParamToggle(props.row.config, 'top_p', $event)">启用</el-checkbox>
                              </div>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="Top K">
                              <el-input-number v-model="props.row.config.top_k" :min="1" :step="1" style="width: 100%" :disabled="!props.row.config.top_k_enabled"></el-input-number>
                              <div style="display: flex; align-items: center;">
                                <el-checkbox v-model="props.row.config.top_k_enabled" @change="handleParamToggle(props.row.config, 'top_k', $event)">启用</el-checkbox>
                              </div>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row :gutter="20">
                          <el-col :span="12">
                            <el-form-item label="频率惩罚 (Frequency Penalty)">
                              <el-slider v-model="props.row.config.frequency_penalty" :min="-2" :max="2" :step="0.01" show-input :disabled="!props.row.config.frequency_penalty_enabled"></el-slider>
                              <div style="display: flex; align-items: center;">
                                <el-checkbox v-model="props.row.config.frequency_penalty_enabled" @change="handleParamToggle(props.row.config, 'frequency_penalty', $event)">启用</el-checkbox>
                              </div>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="存在惩罚 (Presence Penalty)">
                              <el-slider v-model="props.row.config.presence_penalty" :min="-2" :max="2" :step="0.01" show-input :disabled="!props.row.config.presence_penalty_enabled"></el-slider>
                              <div style="display: flex; align-items: center;">
                                <el-checkbox v-model="props.row.config.presence_penalty_enabled" @change="handleParamToggle(props.row.config, 'presence_penalty', $event)">启用</el-checkbox>
                              </div>
                            </el-form-item>
                          </el-col>
                        </el-row>

                        <el-form-item label="提示词">
                          <div v-for="(prompt, pIndex) in props.row.config.prompt" :key="pIndex" style="margin-bottom: 10px;">
                            <el-row :gutter="10" @click.native.stop>
                              <el-col :span="4">
                                <el-select v-model="prompt.role" style="width: 100%;" popper-class="agent-select-dropdown">
                                  <el-option label="系统" value="system"></el-option>
                                </el-select>
                              </el-col>
                              <el-col :span="18">
                                <el-input
                                  type="textarea"
                                  v-model="prompt.content"
                                  :rows="10"
                                  placeholder="请输入提示词内容"
                                  @click.native.stop
                                ></el-input>
                              </el-col>
                              <el-col :span="2" style="display: flex; align-items: center;">
                                <el-button
                                  type="danger"
                                  icon="el-icon-delete"
                                  circle
                                  @click.stop="removeAgentPrompt(props.row, pIndex)"
                                  :disabled="props.row.config.prompt.length <= 1"
                                ></el-button>
                              </el-col>
                            </el-row>
                          </div>
                          <el-button type="primary" size="small" icon="el-icon-plus" @click.stop="addAgentPrompt(props.row)">
                            添加提示词
                          </el-button>
                        </el-form-item>
                      </el-card>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
              <el-table-column label="名称" prop="name" width="180">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.name" placeholder="Agent名称"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="描述" prop="description">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.description" placeholder="Agent描述"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="状态" width="120">
                <template slot-scope="scope">
                  <el-switch
                    v-model="scope.row.enabled"
                    active-text="开启"
                    inactive-text="关闭"
                    :active-value="true"
                    :inactive-value="false"
                  ></el-switch>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="220">
                <template slot-scope="scope">
                  <el-button
                    type="danger"
                    size="small"
                    @click="removeAgent(scope.$index)"
                    icon="el-icon-delete"
                    circle
                  ></el-button>
                  <el-button
                    type="primary"
                    size="small"
                    @click="cloneAgent(scope.row)"
                    icon="el-icon-document-copy"
                    circle
                  ></el-button>
                  <el-button
                    type="info"
                    size="small"
                    @click="moveAgent(scope.$index, 'up')"
                    icon="el-icon-arrow-up"
                    :disabled="scope.$index === 0"
                    circle
                  ></el-button>
                  <el-button
                    type="info"
                    size="small"
                    @click="moveAgent(scope.$index, 'down')"
                    icon="el-icon-arrow-down"
                    :disabled="scope.$index === config.agents.length - 1"
                    circle
                  ></el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <el-tab-pane label="角色" name="roles">
          <div class="tab-header">
            <h3>角色配置</h3>
            <el-button type="primary" icon="el-icon-plus" round @click="addRole">
              添加角色
            </el-button>
          </div>

          <div class="agent-table-container">
            <el-table :data="config.roles" border style="width: 100%" row-key="id">
              <el-table-column type="expand">
                <template slot-scope="props">
                  <el-form label-position="top" class="agent-detail-form" @submit.native.prevent>
                    <el-form-item label="配置" style="width: 100%;">
                      <el-card shadow="hover" style="width: 100%;">
                        <el-row :gutter="20">
                          <el-col :span="12">
                            <el-form-item label="选择供应商">
                              <el-select
                                v-model="props.row.config.providerId"
                                placeholder="请选择供应商"
                                style="width: 100%;"
                                @change="updateRoleProvider(props.row, $event)"
                                popper-class="agent-select-dropdown"
                              >
                                <el-option
                                  v-for="provider in config.providers.filter(p => p.enabled)"
                                  :key="provider.id"
                                  :label="provider.name"
                                  :value="provider.id"
                                ></el-option>
                              </el-select>
                            </el-form-item>
                          </el-col>
                        </el-row>

                        <el-row :gutter="20">
                          <el-col :span="12">
                            <el-form-item label="选择模型">
                              <el-select
                                v-model="props.row.config.modelId"
                                placeholder="请选择模型"
                                style="width: 100%;"
                                @change="updateRoleModel(props.row, $event)"
                                popper-class="agent-select-dropdown"
                              >
                                <el-option
                                  v-for="model in getProviderModels(props.row.config.providerId)"
                                  :key="model.id"
                                  :label="model.name"
                                  :value="model.id"
                                ></el-option>
                              </el-select>
                            </el-form-item>
                          </el-col>
                        </el-row>

                        <el-row :gutter="20">
                          <el-col :span="12">
                            <el-form-item label="最大生成长度">
                              <el-input-number v-model="props.row.config.max_tokens" :min="1" :step="1024" style="width: 100%" :disabled="!props.row.config.max_tokens_enabled"></el-input-number>
                              <div style="display: flex; align-items: center;">
                                <el-checkbox v-model="props.row.config.max_tokens_enabled" @change="handleParamToggle(props.row.config, 'max_tokens', $event)">启用</el-checkbox>
                              </div>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="温度">
                              <el-slider v-model="props.row.config.temperature" :min="0" :max="2" :step="0.1" show-input :disabled="!props.row.config.temperature_enabled"></el-slider>
                              <div style="display: flex; align-items: center;">
                                <el-checkbox v-model="props.row.config.temperature_enabled" @change="handleParamToggle(props.row.config, 'temperature', $event)">启用</el-checkbox>
                              </div>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row :gutter="20">
                          <el-col :span="12">
                            <el-form-item label="Top P">
                              <el-slider v-model="props.row.config.top_p" :min="0" :max="1" :step="0.01" show-input :disabled="!props.row.config.top_p_enabled"></el-slider>
                              <div style="display: flex; align-items: center;">
                                <el-checkbox v-model="props.row.config.top_p_enabled" @change="handleParamToggle(props.row.config, 'top_p', $event)">启用</el-checkbox>
                              </div>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="Top K">
                              <el-input-number v-model="props.row.config.top_k" :min="1" :step="1" style="width: 100%" :disabled="!props.row.config.top_k_enabled"></el-input-number>
                              <div style="display: flex; align-items: center;">
                                <el-checkbox v-model="props.row.config.top_k_enabled" @change="handleParamToggle(props.row.config, 'top_k', $event)">启用</el-checkbox>
                              </div>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row :gutter="20">
                          <el-col :span="12">
                            <el-form-item label="频率惩罚 (Frequency Penalty)">
                              <el-slider v-model="props.row.config.frequency_penalty" :min="-2" :max="2" :step="0.01" show-input :disabled="!props.row.config.frequency_penalty_enabled"></el-slider>
                              <div style="display: flex; align-items: center;">
                                <el-checkbox v-model="props.row.config.frequency_penalty_enabled" @change="handleParamToggle(props.row.config, 'frequency_penalty', $event)">启用</el-checkbox>
                              </div>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="存在惩罚 (Presence Penalty)">
                              <el-slider v-model="props.row.config.presence_penalty" :min="-2" :max="2" :step="0.01" show-input :disabled="!props.row.config.presence_penalty_enabled"></el-slider>
                              <div style="display: flex; align-items: center;">
                                <el-checkbox v-model="props.row.config.presence_penalty_enabled" @change="handleParamToggle(props.row.config, 'presence_penalty', $event)">启用</el-checkbox>
                              </div>
                            </el-form-item>
                          </el-col>
                        </el-row>

                        <el-form-item label="提示词">
                          <el-input
                            type="textarea"
                            v-model="props.row.config.prompt"
                            :rows="10"
                            placeholder="请输入角色提示词"
                            @click.native.stop
                          ></el-input>
                        </el-form-item>
                      </el-card>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
              <el-table-column label="名称" prop="name" width="180">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.name" placeholder="角色名称"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="描述" prop="description">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.description" placeholder="角色描述"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="状态" width="120">
                <template slot-scope="scope">
                  <el-switch
                    v-model="scope.row.enabled"
                    active-text="开启"
                    inactive-text="关闭"
                    :active-value="true"
                    :inactive-value="false"
                  ></el-switch>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="220">
                <template slot-scope="scope">
                  <el-button
                    type="danger"
                    size="small"
                    @click="removeRole(scope.$index)"
                    icon="el-icon-delete"
                    circle
                  ></el-button>
                  <el-button
                    type="primary"
                    size="small"
                    @click="cloneRole(scope.row)"
                    icon="el-icon-document-copy"
                    circle
                  ></el-button>
                  <el-button
                    type="info"
                    size="small"
                    @click="moveRole(scope.$index, 'up')"
                    icon="el-icon-arrow-up"
                    :disabled="scope.$index === 0"
                    circle
                  ></el-button>
                  <el-button
                    type="info"
                    size="small"
                    @click="moveRole(scope.$index, 'down')"
                    icon="el-icon-arrow-down"
                    :disabled="scope.$index === config.roles.length - 1"
                    circle
                  ></el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <el-tab-pane label="助手" name="assistants">
          <div class="tab-header">
            <h3>助手配置</h3>
          </div>

          <el-form label-position="top">
            <el-card shadow="hover" style="margin-bottom: 20px;">
              <div slot="header" class="clearfix">
                <span>节点命名助手</span>
              </div>
<!--              <el-row :gutter="20">-->
<!--                <el-col :span="12">-->
<!--                  <el-form-item label="API URL">-->
<!--                    <el-input v-if="config.assistants.nameTool" v-model="config.assistants.nameTool.url" placeholder="请输入API URL"></el-input>-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
<!--                <el-col :span="12">-->
<!--                  <el-form-item label="API Key">-->
<!--                    <el-input v-if="config.assistants.nameTool" v-model="config.assistants.nameTool.api_key" placeholder="请输入API Key" show-password></el-input>-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
<!--              </el-row>-->
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="模型名称">
                    <el-input v-if="config.assistants.nameTool" v-model="config.assistants.nameTool.model" placeholder="请输入模型名称"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="提示词">
                    <el-input v-if="config.assistants.nameTool" type="textarea" v-model="config.assistants.nameTool.prompt" :rows="5" placeholder="请输入提示词"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>

            <el-card shadow="hover">
              <div slot="header" class="clearfix">
                <span>节点摘要助手</span>
              </div>
<!--              <el-row :gutter="20">-->
<!--                <el-col :span="12">-->
<!--                  <el-form-item label="API URL">-->
<!--                    <el-input v-if="config.assistants.summaryTool" v-model="config.assistants.summaryTool.url" placeholder="请输入API URL"></el-input>-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
<!--                <el-col :span="12">-->
<!--                  <el-form-item label="API Key">-->
<!--                    <el-input v-if="config.assistants.summaryTool" v-model="config.assistants.summaryTool.api_key" placeholder="请输入API Key" show-password></el-input>-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
<!--              </el-row>-->
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="模型名称">
                    <el-input v-if="config.assistants.summaryTool" v-model="config.assistants.summaryTool.model" placeholder="请输入模型名称"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="提示词">
                    <el-input v-if="config.assistants.summaryTool" type="textarea" v-model="config.assistants.summaryTool.prompt" :rows="5" placeholder="请输入提示词"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="配置文本查看" name="models">
          <div class="config-view-container">
            <el-input v-model="config_text" type="textarea" :rows="30"></el-input>
          </div>
        </el-tab-pane>
      </el-tabs>

      <div class="config-footer flex flex-center">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="saveConfig">保存</el-button>
      </div>
    </div>
  </div>
</template>

  <script>
  // Assuming llmConfigLoader is compatible with Vue 2 environment or adjusted accordingly
  import { loadLlmConfig, saveLlmConfig } from './llmConfigLoader';

  export default {
    name: 'LlmConfigManager',
    props: {
      // Define any props if needed
    },
    data() {
      return {
        // Configuration data
        config: {
          providers: [],
          agents: [],
          roles: [],
          assistants: {
            nameTool: null, // Initialize as null or {}
            summaryTool: null
          }
        },
        config_text: '',
        // Currently active tab
        activeTab: 'providers',
        // Currently expanded provider indices (using index as name)
        activeProviders: [],
      };
    },
    methods: {
      // Load configuration
      async loadConfig() {
        try {
          const loadedConfig = await loadLlmConfig();
          this.config_text = JSON.stringify(loadedConfig, null, 2);
          console.log('Loaded config:', loadedConfig);

          // Ensure defaults if properties are missing
          this.config.providers = loadedConfig.providers || [];
          this.config.agents = loadedConfig.agents || [];
          this.config.roles = loadedConfig.roles || [];
          this.config.assistants = loadedConfig.assistants || {
              nameTool: null, // Default structure
              summaryTool: null
          };

          // Ensure assistants objects exist if null
          if (!this.config.assistants.nameTool) {
              this.config.assistants.nameTool = { url: '', api_key: '', model: '', prompt: '' };
          }
          if (!this.config.assistants.summaryTool) {
              this.config.assistants.summaryTool = { url: '', api_key: '', model: '', prompt: '' };
          }


          // Ensure all agents have the correct config structure and defaults
          this.config.agents.forEach(agent => {
            if (!agent.config) {
              this.$set(agent, 'config', this.createDefaultAgentConfig());
            } else {
               // Ensure prompt exists
              if (!agent.config.prompt) {
                 this.$set(agent.config, 'prompt', this.createDefaultAgentConfig().prompt);
              }
              // Ensure default values for other params if missing
              const defaults = this.createDefaultAgentConfig();
              for (const key in defaults) {
                  if (!(key in agent.config)) {
                      this.$set(agent.config, key, defaults[key]);
                  }
              }
            }
          });

          // Ensure all roles have the correct config structure and defaults
          this.config.roles.forEach(role => {
            if (!role.config) {
               this.$set(role, 'config', this.createDefaultRoleConfig());
            } else {
              // Ensure prompt exists and is a string
              if (role.config.prompt === undefined || role.config.prompt === null) {
                 this.$set(role.config, 'prompt', this.createDefaultRoleConfig().prompt);
              }
               // Ensure default values for other params if missing
              const defaults = this.createDefaultRoleConfig();
              for (const key in defaults) {
                  if (!(key in role.config)) {
                      this.$set(role.config, key, defaults[key]);
                  }
              }
            }
          });

           // Ensure all provider models have the correct config structure and defaults
          this.config.providers.forEach(provider => {
              if(!provider.models) {
                  this.$set(provider, 'models', []);
              }
              provider.models.forEach(model => {
                  if(!model.config) {
                      this.$set(model, 'config', this.createDefaultModelConfig(model.name));
                  } else {
                      const defaults = this.createDefaultModelConfig(model.config.name || model.name);
                       for (const key in defaults) {
                          if (!(key in model.config)) {
                              this.$set(model.config, key, defaults[key]);
                          }
                      }
                  }
              })
          });


        } catch (error) {
          console.error('加载配置失败:', error);
          this.$message.error('加载配置失败');
        }
      },

      // Save configuration
      async saveConfig() {
        try {
          // Pre-save data cleaning and validation
          this.config.providers.forEach(provider => {
            if (!provider.id) {
              provider.id = 'provider_' + Date.now() + Math.floor(Math.random() * 1000);
            }
            if (!provider.models) provider.models = [];
            provider.models.forEach(model => {
              if (!model.id) {
                model.id = 'model_' + Date.now() + Math.floor(Math.random() * 1000);
              }
              if (!model.config) {
                model.config = this.createDefaultModelConfig(model.name);
              }
            });
          });

          this.config.agents.forEach(agent => {
            if (!agent.id) {
              agent.id = 'agent_' + Date.now() + Math.floor(Math.random() * 1000);
            }
            if (!agent.config) {
              agent.config = this.createDefaultAgentConfig();
            }
            if (!agent.config.prompt) {
              agent.config.prompt = this.createDefaultAgentConfig().prompt;
            }
          });

           this.config.roles.forEach(role => {
            if (!role.id) {
              role.id = 'role_' + Date.now() + Math.floor(Math.random() * 1000);
            }
            if (!role.config) {
              role.config = this.createDefaultRoleConfig();
            }
            if (role.config.prompt === undefined || role.config.prompt === null) {
               role.config.prompt = this.createDefaultRoleConfig().prompt;
            }
          });


          // Ensure assistant configuration correctness
          if (!this.config.assistants) {
            this.config.assistants = {
              nameTool: { /* default values */ },
              summaryTool: { /* default values */ }
            };
          }
          // Provide default assistant config if missing (adjust defaults as needed)
          if (!this.config.assistants.nameTool) {
              this.config.assistants.nameTool = {
                url: "https://api.siliconflow.cn/v1/chat/completions",
                api_key: "",
                model: "Qwen/Qwen2.5-14B-Instruct",
                prompt: "你是一个对话命名助手，根据用户的提问内容生成一个合适的对话名称，要突出用户的对话的意图，名称要简短，不要超过15个字。用户的问题在<USER_CONTENT>标签内。"
              };
          }
          if (!this.config.assistants.summaryTool) {
              this.config.assistants.summaryTool = {
                url: "https://api.siliconflow.cn/v1/chat/completions",
                api_key: "",
                model: "Qwen/Qwen2.5-14B-Instruct",
                prompt: "你是一个对话总结助手，请根据用户和AI的对话内容生成对话总结。\n用户的问题在<USER_CONTENT>标签内，AI的回答在<AI_CONTENT>标签内。\n要抓住对话的要点，但是也不要遗漏信息，总结要简短，不要超过300个字。\n输出结果要分别有对用户的总结和对AI回答内容的总结，并且不能是markdown格式。\n输出总结范文如下：\n用户：询问如何写好作文。\nAI：写好作文需要先确定主题，然后组织好文章结构，最后润色语言。"
              };
          }

          // Deep copy to avoid potential reactivity issues during save
          const configToSave = JSON.parse(JSON.stringify(this.config));
          await saveLlmConfig(configToSave);
          this.$message.success('配置已保存');
          this.$emit('updated'); // Emit update event
        } catch (error) {
          console.error('保存配置失败:', error);
          this.$message.error('保存配置失败: ' + error.message);
        }
      },

      // Close configuration manager
      handleClose() {
        this.$emit('close');
      },

      // Add provider
      addProvider() {
        const newProvider = {
          id: 'provider_' + Date.now() + Math.floor(Math.random() * 1000),
          name: '新供应商',
          type: 'openai',
          enabled: true,
          api_key: '',
          url: 'https://api.example.com/v1/chat/completions',
          models: []
        };
        this.config.providers.push(newProvider);
        // Automatically expand the newly added provider
        this.$nextTick(() => {
            this.activeProviders = [(this.config.providers.length - 1).toString()];
        })
      },

      // Remove provider
      removeProvider(index) {
        this.$confirm('确定要删除这个供应商吗？该操作不可恢复', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.config.providers.splice(index, 1);
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
        }).catch(() => {
          // Cancelled
        });
      },

      // Move provider
      moveProvider(index, direction) {
          const list = this.config.providers;
          if (direction === 'up' && index > 0) {
              // Swap with previous item
              const temp = list[index];
              this.$set(list, index, list[index - 1]);
              this.$set(list, index - 1, temp);
              // Update active collapse if needed
              if (this.activeProviders.includes(index.toString())) {
                  this.activeProviders = [(index - 1).toString()];
              }
          } else if (direction === 'down' && index < list.length - 1) {
              // Swap with next item
              const temp = list[index];
              this.$set(list, index, list[index + 1]);
              this.$set(list, index + 1, temp);
               // Update active collapse if needed
              if (this.activeProviders.includes(index.toString())) {
                  this.activeProviders = [(index + 1).toString()];
              }
          }
      },

       // --- Default Config Creators ---
      createDefaultModelConfig(name = 'model_name') {
        return {
          name: name,
          max_tokens: null,
          max_tokens_enabled: true,
          temperature: null,
          temperature_enabled: true,
          top_p: null,
          top_p_enabled: false,
          top_k: null,
          top_k_enabled: false,
          frequency_penalty: null,
          frequency_penalty_enabled: false,
          presence_penalty: null,
          presence_penalty_enabled: false
        };
      },

       createDefaultAgentConfig() {
          return {
            providerId: '',
            modelId: '',
            provider: '', // Derived, not stored directly? Consider removing if always looked up
            model: '', // Derived, not stored directly? Consider removing if always looked up
            temperature: 0.7,
            temperature_enabled: true,
            max_tokens: 4096,
            max_tokens_enabled: true,
            top_p: null,
            top_p_enabled: false,
            top_k: null,
            top_k_enabled: false,
            frequency_penalty: null,
            frequency_penalty_enabled: false,
            presence_penalty: null,
            presence_penalty_enabled: false,
            prompt: [
              {
                role: 'system',
                content: '你是一个智能助手，请帮助用户解决问题。'
              }
            ]
          };
      },

      createDefaultRoleConfig() {
          return {
            providerId: '',
            modelId: '',
            provider: '', // Derived?
            model: '',    // Derived?
            temperature: null,
            temperature_enabled: false,
            max_tokens: null,
            max_tokens_enabled: false,
            top_p: null,
            top_p_enabled: false,
            top_k: null,
            top_k_enabled: false,
            frequency_penalty: null,
            frequency_penalty_enabled: false,
            presence_penalty: null,
            presence_penalty_enabled: false,
            prompt: ''
          };
      },


      // --- Model Methods ---
      addModel(provider) {
          const newModel = {
              id: 'model_' + Date.now() + Math.floor(Math.random() * 1000),
              name: '新模型',
              description: '描述信息',
              enabled: true,
              config: this.createDefaultModelConfig('new_model_name'),
              thinking_model: false
          };
           // Ensure models array exists
          if (!provider.models) {
              this.$set(provider, 'models', []);
          }
          provider.models.push(newModel);
      },

      removeModel(provider, modelIndex) {
          if (provider.models) {
              provider.models.splice(modelIndex, 1);
          }
      },

      moveModel(provider, modelIndex, direction) {
        const list = provider.models;
        if (!list) return;

        if (direction === 'up' && modelIndex > 0) {
          const temp = list[modelIndex];
          this.$set(list, modelIndex, list[modelIndex - 1]);
          this.$set(list, modelIndex - 1, temp);
        } else if (direction === 'down' && modelIndex < list.length - 1) {
          const temp = list[modelIndex];
          this.$set(list, modelIndex, list[modelIndex + 1]);
          this.$set(list, modelIndex + 1, temp);
        }
      },

      // Parameter enable/disable toggle
      handleParamToggle(configObj, paramName, enabled) {
          console.log(`参数 ${paramName} 状态变化: ${enabled ? '启用' : '禁用'}`);
          let defaultValue = null;
           // Define default values when enabling
          switch (paramName) {
              case 'max_tokens': defaultValue = 4096; break;
              case 'temperature': defaultValue = 0.7; break;
              case 'top_p': defaultValue = 1.0; break;
              case 'top_k': defaultValue = 40; break; // Example default
              case 'frequency_penalty': defaultValue = 0.0; break;
              case 'presence_penalty': defaultValue = 0.0; break;
          }

          // Use this.$set to ensure reactivity if the property might not exist initially
          // Although the loadConfig ensures they exist, using $set is safer practice
          this.$set(configObj, paramName, enabled ? defaultValue : null);


          if(!enabled){// 如果是禁用，就删除
            delete configObj[paramName]
          }

          console.log(`参数 ${paramName} 已设置为 ${configObj[paramName]}`);
      },

      // --- Agent Methods ---
      getProviderModels(providerId) {
        const provider = this.config.providers.find(p => p.id === providerId);
        return provider && provider.models ? provider.models.filter(m => m.enabled) : [];
      },

      addAgent() {
        const newAgent = {
          id: 'agent_' + Date.now() + Math.floor(Math.random() * 1000),
          name: '新Agent',
          description: '描述信息',
          enabled: true,
          config: this.createDefaultAgentConfig()
        };
        this.config.agents.push(newAgent);
      },

      removeAgent(index) {
        this.config.agents.splice(index, 1);
      },

      updateAgentProvider(agent, providerId) {
        if (!agent || !agent.config) return;
        const provider = this.config.providers.find(p => p.id === providerId);
        if (provider) {
          this.$set(agent.config, 'providerId', providerId);
          this.$set(agent.config, 'provider', provider.name); // Update derived name
          // Reset model selection
          this.$set(agent.config, 'modelId', '');
          this.$set(agent.config, 'model', '');
          console.log('更新 Agent 供应商信息:', providerId, provider.name);
        }
      },

      updateAgentModel(agent, modelId) {
          if (!agent || !agent.config || !agent.config.providerId) return;
          const provider = this.config.providers.find(p => p.id === agent.config.providerId);
          if (provider && provider.models) {
              const model = provider.models.find(m => m.id === modelId);
              if (model && model.config) {
                  this.$set(agent.config, 'modelId', modelId);
                  this.$set(agent.config, 'model', model.config.name); // Update derived name

                  console.log('更新 Agent 模型信息:', modelId, model.config.name);

                  // Sync parameters from selected model config to agent config
                  const modelConfig = model.config;
                  const agentConfig = agent.config;

                  // Sync only if the param is enabled in the base model config
                  this.$set(agentConfig, 'max_tokens', modelConfig.max_tokens_enabled ? modelConfig.max_tokens : null);
                  this.$set(agentConfig, 'max_tokens_enabled', modelConfig.max_tokens_enabled);

                  this.$set(agentConfig, 'temperature', modelConfig.temperature_enabled ? modelConfig.temperature : null);
                  this.$set(agentConfig, 'temperature_enabled', modelConfig.temperature_enabled);

                  this.$set(agentConfig, 'top_p', modelConfig.top_p_enabled ? modelConfig.top_p : null);
                  this.$set(agentConfig, 'top_p_enabled', modelConfig.top_p_enabled);

                  this.$set(agentConfig, 'top_k', modelConfig.top_k_enabled ? modelConfig.top_k : null);
                  this.$set(agentConfig, 'top_k_enabled', modelConfig.top_k_enabled);

                  this.$set(agentConfig, 'frequency_penalty', modelConfig.frequency_penalty_enabled ? modelConfig.frequency_penalty : null);
                  this.$set(agentConfig, 'frequency_penalty_enabled', modelConfig.frequency_penalty_enabled);

                  this.$set(agentConfig, 'presence_penalty', modelConfig.presence_penalty_enabled ? modelConfig.presence_penalty : null);
                  this.$set(agentConfig, 'presence_penalty_enabled', modelConfig.presence_penalty_enabled);

              } else {
                   console.warn("Selected model or model config not found for ID:", modelId);
                   // Optionally reset fields if model not found
                   this.$set(agent.config, 'modelId', '');
                   this.$set(agent.config, 'model', '');
              }
          } else {
               console.warn("Provider not found for ID:", agent.config.providerId);
          }
      },

      addAgentPrompt(agent) {
        if (!agent || !agent.config) return;
        if (!agent.config.prompt) {
          this.$set(agent.config, 'prompt', []); // Initialize if it doesn't exist
        }
        agent.config.prompt.push({ role: 'system', content: '' });
      },

      removeAgentPrompt(agent, index) {
        if (!agent || !agent.config || !agent.config.prompt || agent.config.prompt.length <= 1) return;
        agent.config.prompt.splice(index, 1);
      },

      cloneAgent(agent) {
        try {
          const newAgent = JSON.parse(JSON.stringify(agent)); // Deep clone
          newAgent.id = 'agent_' + Date.now() + Math.floor(Math.random() * 1000);
          newAgent.name = '复制-' + newAgent.name;
          this.config.agents.push(newAgent);
        } catch (e) {
          console.error("Failed to clone agent:", e);
          this.$message.error("复制Agent失败");
        }
      },

      moveAgent(index, direction) {
        this.moveItem(this.config.agents, index, direction);
      },

       // --- Role Methods ---
      addRole() {
        const newRole = {
          id: 'role_' + Date.now() + Math.floor(Math.random() * 1000),
          name: '新角色',
          description: '描述信息',
          enabled: true,
          config: this.createDefaultRoleConfig()
        };
        this.config.roles.push(newRole);
      },

      removeRole(index) {
        this.config.roles.splice(index, 1);
      },

      updateRoleProvider(role, providerId) {
         if (!role || !role.config) return;
        const provider = this.config.providers.find(p => p.id === providerId);
        if (provider) {
          this.$set(role.config, 'providerId', providerId);
          this.$set(role.config, 'provider', provider.name); // Update derived name
          // Reset model selection
          this.$set(role.config, 'modelId', '');
          this.$set(role.config, 'model', '');
          console.log('更新 Role 供应商信息:', providerId, provider.name);
        }
      },

      updateRoleModel(role, modelId) {
         if (!role || !role.config || !role.config.providerId) return;
          const provider = this.config.providers.find(p => p.id === role.config.providerId);
          if (provider && provider.models) {
              const model = provider.models.find(m => m.id === modelId);
               if (model && model.config) {
                  this.$set(role.config, 'modelId', modelId);
                  this.$set(role.config, 'model', model.config.name); // Update derived name

                  console.log('更新 Role 模型信息:', modelId, model.config.name);

                  // Sync parameters from selected model config to role config
                  const modelConfig = model.config;
                  const roleConfig = role.config;

                  this.$set(roleConfig, 'max_tokens', modelConfig.max_tokens_enabled ? modelConfig.max_tokens : null);
                  this.$set(roleConfig, 'max_tokens_enabled', modelConfig.max_tokens_enabled);

                  this.$set(roleConfig, 'temperature', modelConfig.temperature_enabled ? modelConfig.temperature : null);
                  this.$set(roleConfig, 'temperature_enabled', modelConfig.temperature_enabled);

                  this.$set(roleConfig, 'top_p', modelConfig.top_p_enabled ? modelConfig.top_p : null);
                  this.$set(roleConfig, 'top_p_enabled', modelConfig.top_p_enabled);

                  this.$set(roleConfig, 'top_k', modelConfig.top_k_enabled ? modelConfig.top_k : null);
                  this.$set(roleConfig, 'top_k_enabled', modelConfig.top_k_enabled);

                  this.$set(roleConfig, 'frequency_penalty', modelConfig.frequency_penalty_enabled ? modelConfig.frequency_penalty : null);
                  this.$set(roleConfig, 'frequency_penalty_enabled', modelConfig.frequency_penalty_enabled);

                  this.$set(roleConfig, 'presence_penalty', modelConfig.presence_penalty_enabled ? modelConfig.presence_penalty : null);
                  this.$set(roleConfig, 'presence_penalty_enabled', modelConfig.presence_penalty_enabled);

              } else {
                   console.warn("Selected model or model config not found for ID:", modelId);
                   this.$set(role.config, 'modelId', '');
                   this.$set(role.config, 'model', '');
              }
          } else {
               console.warn("Provider not found for ID:", role.config.providerId);
          }
      },

       cloneRole(role) {
        try {
          const newRole = JSON.parse(JSON.stringify(role)); // Deep clone
          newRole.id = 'role_' + Date.now() + Math.floor(Math.random() * 1000);
          newRole.name = '复制-' + newRole.name;
          this.config.roles.push(newRole);
        } catch (e) {
          console.error("Failed to clone role:", e);
          this.$message.error("复制角色失败");
        }
      },

      moveRole(index, direction) {
        this.moveItem(this.config.roles, index, direction);
      },

      // Generic move helper
      moveItem(list, index, direction) {
          if (!list) return;
          if (direction === 'up' && index > 0) {
              const temp = list[index];
              this.$set(list, index, list[index - 1]);
              this.$set(list, index - 1, temp);
          } else if (direction === 'down' && index < list.length - 1) {
              const temp = list[index];
              this.$set(list, index, list[index + 1]);
              this.$set(list, index + 1, temp);
          }
      }
    },
    mounted() {
      this.loadConfig();
    }
  };
  </script>

  <style scoped>
  /* Basic Layout Styles - Should work similarly */
  .llm-config-manager {
    background-color: rgba(245, 247, 250, 0.98);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    padding: 0px;
    padding-top: 20px;
    overflow: hidden; /* Prevent body scroll */
  }

  .config-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: white;
    border-radius: 5px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden; /* Container handles inner scroll */
    margin: 0 20px 20px 20px;
  }

  .config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-shrink: 0; /* Header should not shrink */
  }

  .config-header h2 {
    margin: 0;
  }

  .config-tabs {
    flex: 1; /* Allow tabs to take remaining space */
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Tabs handle their content overflow */
  }

  /* Deep selectors for Element UI Tab Content */
  .config-tabs >>> .el-tabs__content {
    flex: 1;
    overflow: hidden; /* Let tab-pane handle scroll */
    height: 100%; /* Ensure content takes full height */
  }

  .config-tabs >>> .el-tab-pane {
    height: 100%; /* Make tab pane fill content area */
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Tab pane handles its content scroll */
  }

  .tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 0 10px; /* Add some padding */
    flex-shrink: 0;
  }

  .tab-header h3 {
    margin: 0;
  }

  .config-footer {
    margin-top: 20px;
    flex-shrink: 0; /* Footer should not shrink */
    text-align: center; /* Center buttons */
  }
  .config-footer.flex-center { /* Replicate flex centering if needed */
      display: flex;
      justify-content: center;
      align-items: center;
  }


  /* Provider Styles */
  .collapse-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%; /* Ensure title takes full width */
    padding-right: 10px; /* Add some spacing */
  }

  .collapse-title > div {
    display: flex;
    align-items: center;
  }

  .collapse-title .el-button {
    margin-left: 5px;
  }

  .provider-form {
    padding: 10px 15px; /* Add padding */
    background-color: #fdfdfd; /* Slightly different background */
  }

  .provider-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
  }

  .model-item {
    margin-bottom: 20px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
  }

  .model-item .el-card__header { /* Target Element UI card header */
    padding: 10px 15px; /* Adjust padding */
  }
  .model-item .el-card__body { /* Target Element UI card body */
    padding: 15px; /* Adjust padding */
  }


  .model-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* Padding is handled by el-card__header now */
  }

  .model-header h4 {
    margin: 0;
    font-size: 16px;
    color: #303133;
  }

  .model-actions {
    display: flex;
    gap: 5px; /* Gap might not be supported, use margin */
    justify-content: flex-end;
    align-items: center;
  }
  .model-actions > * {
      margin-left: 5px;
  }
  .model-actions > *:first-child {
      margin-left: 0;
  }


  .model-actions .el-switch {
    margin-right: 10px;
  }

  /* Agent/Role Table Styles */
  .agent-table-container {
    flex: 1; /* Allow table container to grow */
    overflow: auto; /* Add scroll to table container */
    padding: 0 10px 10px 10px;
  }

  .agent-detail-form {
    padding: 10px;
    width: 100%;
    background-color: #f9f9f9; /* Background for expanded form */
    border-radius: 4px;
  }

  /* Expanded cell padding */
  .agent-table-container >>> .el-table__expanded-cell {
    padding: 15px !important; /* Override default padding */
    background-color: #f9f9f9;
  }

  /* Prevent expand/collapse on input/select clicks (might require JS intervention if CSS fails) */
  .agent-table-container >>> .el-table__row input,
  .agent-table-container >>> .el-table__row select,
  .agent-table-container >>> .el-table__row textarea {
      pointer-events: auto; /* Ensure form elements are clickable */
  }

  /* Adjust form item margin */
  .agent-detail-form >>> .el-form-item {
    margin-bottom: 18px;
  }

  /* Ensure selects/inputs take width */
  .agent-detail-form >>> .el-select,
  .agent-detail-form >>> .el-input-number,
  .agent-detail-form >>> .el-slider {
    width: 100%;
  }

  /* Fix dropdown z-index if needed */
  .agent-select-dropdown {
    z-index: 2050 !important; /* Higher than default modal z-index in Element UI */
  }


  /* Table cell input height */
  .agent-table-container >>> .el-table .el-input--small .el-input__inner {
      height: 28px;
      line-height: 28px;
  }
  .agent-table-container >>> .el-table .el-input__inner {
      height: 32px; /* Default height might be slightly different */
      line-height: 32px;
  }

  /* Table action buttons */
  .agent-table-container >>> .el-table .el-button--mini {
      padding: 5px; /* Adjust padding for mini buttons */
      margin: 0 3px; /* Add spacing */
  }

  /* Ensure Checkbox click doesn't propagate */
  .el-checkbox {
      pointer-events: auto;
  }

  /* Assistant Tab Scrolling */
  .el-tab-pane[name="assistants"] > div:nth-of-type(2) { /* Target the div containing the form */
      overflow: auto;
      flex: 1;
      padding-right: 5px; /* Space for scrollbar */
  }

  </style>
