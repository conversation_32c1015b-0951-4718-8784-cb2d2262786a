<template>
  <div class="app-container">
    <div class="page-title flex flex-dr flex-center">
      <div>{{ experimentInfo.name }}</div>
      <div>{{ branchType }}-{{ versionType }}</div>
    </div>

    <!--分支类型-可变-->
    <el-tabs v-model="branchType" @tab-click="v=>TabMethods().clickBranchType(v)" type="card">
      <el-tab-pane v-for="item in branchTypes" :label="item.label" :name="item.value">
      </el-tab-pane>
    </el-tabs>

    <div class="clearfix">
      <!--版本类型-固定-->
      <el-tabs v-model="versionType" @tab-click="v=>TabMethods().clickVersionType(v)" tab-position="left"
               style="float:left;width: 10%;">
        <el-tab-pane v-for="item in versionTypes" :label="item.label" :name="item.value">
        </el-tab-pane>
      </el-tabs>
      <div class="list-container" style="width: 90%;float:right;">
        <!--筛选-->
        <div class="header-container clearfix">
          <list-search-filter :search-filter="lists.searchFilter"
                              @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
          </list-search-filter>
          <!--  操作  -->
          <div class="right">
            <div style="text-align: right">

            </div>
          </div>
        </div>
        <!--列表-->
        <div class="lists">
          <el-card class="li" v-for="(item,itemIndex) in lists.list">
            <el-form>
              <el-form-item label="版本号：" style="margin-bottom: 0px">{{ item.versionNumber }}</el-form-item>
              <el-form-item label="版本名称：" style="margin-bottom: 0px">{{ item.name }}</el-form-item>
              <el-form-item label="更新时间：" style="margin-bottom: 0px">
                {{ date_format(item.createTime, "yyyy-MM-dd HH:mm:ss") }}
              </el-form-item>
              <el-form-item label="更新说明：" style="margin-bottom: 0px">
              </el-form-item>
              <div v-html="item.description" style="width: 100%"></div>
              <el-form-item label="下载链接：" style="margin-bottom: 0px">
              </el-form-item>
              <el-alert
                v-if="!item.allowDownload&&allowDownload"
                title="此版本包不允许下载，如有需要请联系管理员!"
                type="error">
              </el-alert>
              <el-alert
                v-if="!allowDownload"
                title="已关闭所有包下载权限，如有需要请联系管理员!"
                type="error">
              </el-alert>
              <el-table :data="item.fileList" fit v-if="item.allowDownload&&allowDownload"
                        style="width:100%;">
                <el-table-column label="存储位置" align="center" width="150">
                  <template slot-scope="scope">
                    <span>{{ scope.row.location }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="下载密码" align="center" width="200">
                  <template slot-scope="scope">
                    <span>{{ scope.row.password }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="获取下载链接" align="center" v-if="!item.showFileUrl">
                  <template slot-scope="scope">
                    <el-button @click="EntityMethods().clickGetDownloadUrlBtn(item,itemIndex,scope.row)">获取</el-button>
                  </template>
                </el-table-column>
                <el-table-column label="下载链接" align="center" v-if="item.showFileUrl">
                  <template slot-scope="scope">
                    <div v-if="scope.row.password==='无密码'">{{ scope.row.url }}</div>
                    <div v-if="scope.row.password!=='无密码'">
                      {{ scope.row.url + "&experimentPackageId=" + item.experimentPackageId }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="复制链接" align="center" v-if="item.showFileUrl">
                  <template slot-scope="scope">
                    <el-button @click="e=>EntityMethods().clickCopyUrlBtn(e,scope.row,item)">复制链接</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form>
          </el-card>
        </div>
        <!--列表分页-->
        <div class="pagination-container">
          <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                         :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                         layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                         @size-change="(size)=>ListMethods().pageLimitChange(size)"
                         :page-count="lists.pages.totalPages">
          </el-pagination>
        </div>
      </div>
    </div>


  </div>
</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import elDragDialog from '@/directive/el-drag-dialog'
import {msg_confirm, msg_err, msg_success} from '@/utils/ele_component'
import {arrToLVArr, date_format, objectToLVArr, searchWordFiltration} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {ExperimentPackageModel} from "@/model/versionManager/ExperimentPackageModel";
import {DepartmentModel} from "@/model/exp/DepartmentModel";
import {dateFormat} from "@/filters";
import enumsVersionManager from "@/enums/versionManager";
import Tinymce from "@/components/Tinymce"
import {BaseUploadModel} from "@/model/BaseUploadModel";
import {ExperimentModel} from "@/model/versionManager/ExperimentModel";
import clip from "@/utils/clipboard";
import {ConfigModel} from "@/model/erp/ConfigModel";
import {CONFIG_NAME_OFFICIAL_WEB, CONFIG_NAME_VERSION_MANAGER} from "@/model/ConfigModel";

export default {
  name: 'versionManagerGamePackageExperiment',
  components: {ListSearchFilter, Tinymce},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      userInfo: state => state.user
    })
  },
  filters: {dateFormat},
  data() {
    return {
      // 路由参数
      experimentId: this.$route.query["experimentId"],
      // 外部方法
      date_format: date_format,
      // 枚举
      enums: enums,
      enumsVersionManager: enumsVersionManager,
      versionTypes: objectToLVArr(enumsVersionManager.versionType),
      branchTypes: [],
      // 配置
      allowDownload: true,
      // 分支tab
      branchType: "PC",
      // 版本tab
      versionType: "lts",
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '版本号',
              placeholder: "请输入版本号",
              key: 'versionNumber',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              },
              value: ''
            },
            {
              type: 'input',
              label: '版本名称',
              placeholder: "请输入版本名称",
              key: 'name',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              },
              value: ''
            }
          ],
          filter: []
        }
      },
      experimentInfo: {},
    }
  },
  async created() {

  },
  async mounted() {
    // 获取总体配置
    this.allowDownload = await ConfigModel.getEdit(CONFIG_NAME_VERSION_MANAGER, "allowDownload") === 'true'
    // 获取实验详情
    this.experimentInfo = await ExperimentModel.getOne({
      experimentId: this.experimentId
    })
    // 生成分支筛选
    this.branchTypes = arrToLVArr(this.experimentInfo.branchTypes)
    this.branchType = this.experimentInfo.branchTypes[0]
    // 获取列表
    this.ListMethods().getList(0, this.lists.pages.size, this.lists.query)
  },
  methods: {
    // Tab方法集
    TabMethods() {
      let $this = this;
      return {
        // 点击分支类型tab
        clickBranchType(tab) {
          $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击版本tab
        clickVersionType(tab) {
          $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
        }
      }
    },
    // 列表方法集
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let queryBase = {
            branchType: $this.branchType,
            versionType: $this.versionType,
            experimentId: $this.experimentId
          }
          query = Object.assign(query, queryBase)
          let listResult = await ExperimentPackageModel.getPageList(page - 1, size, "", query)
          $this.lists.pages = listResult[1]
          let list = listResult[0]
          $this.lists.list = list;
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "新增版本"
          $this.entityInfo.edit = {
            allowDownload: true
          };
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
            $this.$refs["tinymce_description"].setContent("")
          }, 300)
        },
        // 点击编辑按钮
        async clickEditBtn(entity) {
          entity = JSON.parse(JSON.stringify(entity))
          $this.entityInfo.dialog = true;
          // 分割大小版本号
          let dotIndex = entity.versionNumber.indexOf(".")
          entity.bigVersionNumber = entity.versionNumber.substring(0, dotIndex)
          entity.littleVersionNumber = entity.versionNumber.substring(dotIndex + 1)
          $this.$set($this.entityInfo, "edit", entity);
          $this.entityInfo.type = "edit"
          $this.entityInfo.title = "编辑版本"
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
        // 点击删除按钮
        async clickDeleteBtn(entity) {
          if (await msg_confirm("确认是否删除该版本？删除后不可恢复，请谨慎操作！")) {
            await ExperimentPackageModel.deleteOne({
              experimentPackageId: entity.experimentPackageId,
              experimentId: entity.experimentId,
            })
            $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
            msg_success("删除此版本成功！")
          }
        }
      }
    },
    // Entity方法集
    EntityMethods() {
      let $this = this;
      return {
        // 点击获取下载链接地址
        async clickGetDownloadUrlBtn(entity, entityIndex, file) {
          // 请求记录下载日志接口
          let result = await ExperimentPackageModel.addDownloadLog({
            experimentPackageId: entity.experimentPackageId,
            username: $this.userInfo.userName
          })
          if (result) {
            $this.$set($this.lists.list[entityIndex], "showFileUrl", true)
          } else {
            msg_err("获取下载链接失败")
          }
        },
        // 点击复制按钮
        clickCopyUrlBtn(e, file, entity) {
          let url = file.url
          if (file.password !== '无密码') {
            url += "&experimentPackageId=" + entity.experimentPackageId
          }
          clip(url, e);
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.page-title {
  text-align: center;
  font-weight: bold;

  > div:nth-child(1) {
    font-size: 26px;
    padding-bottom: 5px;
    margin-bottom: 5px;
    border-bottom: 1px dashed #cecece;
  }

  > div:nth-child(1) {

  }
}

.lists {
  .li {
    margin-bottom: 20px;
  }
}
</style>
