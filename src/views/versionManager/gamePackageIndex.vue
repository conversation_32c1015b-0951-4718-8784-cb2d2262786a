<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
      </list-search-filter>
      <!--  操作  -->
      <div class="right clearfix">
        <div class="fr">
          <div class="flex flex-start">
            <el-button class="el-button" type="success"
                       v-permission="['administrator','game']"
                       @click="ListMethods().clickAddBtn()">添加实验
            </el-button>
            <el-switch style="margin-left: 30px"
                       v-permission="['administrator','game']"
                       v-model="allowDownload"
                       @change="changeAllowDownload"
                       active-text="允许下载"
                       inactive-text="禁用下载">
            </el-switch>
          </div>
        </div>
      </div>
    </div>

    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;"
              @sort-change="sort=>ListMethods().sortChange(sort)">
      <el-table-column label="实验名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="包含分支" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.branchTypes.join("、") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="总包数量" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.packageNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="350"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="text" round v-permission="['administrator','game']"
                     @click="e=>ListMethods().clickViewBtn(scope.row,e)">管理包
          </el-button>
          <el-button type="text" round
                     @click="e=>ListMethods().clickViewAllBtn(scope.row,e)">查看包
          </el-button>
          <el-button type="text" round v-permission="['administrator','game']"
                     @click="e=>ListMethods().clickEditBtn(scope.row,e)">编辑
          </el-button>
          <el-button type="text" round v-permission="['administrator','game']"
                     @click="e=>ListMethods().clickDeleteBtn(scope.row,e)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
    <!--详情弹窗-新增和编辑-->
    <el-dialog
      :title="entityInfo.title"
      :visible.sync="entityInfo.dialog"
      width="670px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
          <el-form-item label="实验名称:" prop="name">
            <el-input v-model.trim="entityInfo.edit.name">
            <span slot="suffix" v-if="entityInfo.edit.name">
              <span class="el-input__count">
                <span class="el-input__count-inner">
                  {{ entityInfo.edit.name.length }} / 30
                </span>
              </span>
            </span>
            </el-input>
          </el-form-item>
          <el-form-item label="包含分支:" prop="branchTypes">
            <el-select v-model="entityInfo.edit.branchTypes" multiple placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in entityInfo.filter.branchTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="entityInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityMethods().clickAddBtn()" v-if="entityInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityMethods().clickEditBtn()"
                   v-if="entityInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import enumsVersionManager from '@/enums/versionManager'
import elDragDialog from '@/directive/el-drag-dialog'
import {msg_confirm, msg_err, msg_success} from '@/utils/ele_component'
import {date_format, generateSomeNumber, objectToLVArr, searchWordFiltration} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {DepartmentModel} from "@/model/exp/DepartmentModel";
import {dateFormat} from "@/filters";
import {validateMaxLength} from "@/utils/validate";
import {ExperimentModel} from "@/model/versionManager/ExperimentModel";
import {ExperimentPackageModel} from "@/model/versionManager/ExperimentPackageModel";
import {ConfigModel} from "@/model/erp/ConfigModel";
import {CONFIG_NAME_VERSION_MANAGER} from "@/model/ConfigModel";

export default {
  name: 'schoolMain',
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles
    })
  },
  filters: {dateFormat},
  data() {
    return {
      // 外部方法
      date_format: date_format,
      // 枚举
      enums: enums,
      enumsVersionManager: enumsVersionManager,
      allowDownload: true,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '实验名称',
              placeholder: "请输入实验名称",
              key: 'name',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              },
              value: ''
            }
          ],
          filter: []
        }
      },
      entityInfo: {
        dialog: false,
        title: "新增实验",
        type: "add",
        filter: {
          branchTypes: []
        },
        edit: {
          name: "",
          branchTypes: []
        },
        // 输入检测
        formRules: {
          'name': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 30, "实验名称"), trigger: 'blur'},
          'branchTypes': {required: true, message: "请选择", trigger: ['change', 'blur']},
        },
      }
    }
  },
  created() {

  },
  async mounted() {
    // 获取总体配置
    this.allowDownload = await ConfigModel.getEdit(CONFIG_NAME_VERSION_MANAGER, "allowDownload") === 'true'
    // 获取列表
    this.ListMethods().getList(0, this.lists.pages.size, this.lists.query)
    // 初始化筛选
    this.EntityMethods().initFilter()
  },
  methods: {
    // 切换是否全部允许下载
    async changeAllowDownload(e) {
      if (await ConfigModel.editEdit(CONFIG_NAME_VERSION_MANAGER, "allowDownload", e)){
        msg_success('切换成功')
      }
    },
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let listResult = await ExperimentModel.getPageList(page - 1, size, "", query)
          $this.lists.pages = listResult[1]
          $this.lists.list = listResult[0];
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击查看按钮
        clickViewBtn(entity) {
          $this.$router.push(`/versionManager/gamePackageExperiment?experimentId=${entity.experimentId}`)
        },
        clickViewAllBtn(entity) {
          $this.$router.push(`/versionManager/gamePackageAllView?experimentId=${entity.experimentId}`)
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.edit = {};
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
        // 点击编辑按钮
        async clickEditBtn(entity) {
          entity = JSON.parse(JSON.stringify(entity))
          $this.entityInfo.dialog = true;
          $this.entityInfo.edit = entity;
          $this.entityInfo.type = "edit"
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
        // 点击删除按钮
        async clickDeleteBtn(entity) {
          // 判断此实验是否有包
          let listResult = await ExperimentPackageModel.getList({
            experimentId: entity.experimentId
          })
          if (listResult.length > 0) {
            msg_err(`该实验还存在${listResult.length}个关联的包，请先删除所有包后再试！`)
            return
          }
          if (await msg_confirm("确认是否删除该实验？")) {
            await ExperimentModel.deleteOne({
              experimentId: entity.experimentId,
            })
            $this.ListMethods().getList(1, $this.lists.pages.size, $this.lists.query)
            msg_success("删除此实验成功！")
          }
        }
      }
    },
    // 实体Methods
    EntityMethods() {
      let $this = this;
      return {
        // 初始化筛选
        initFilter() {
          $this.$set($this.entityInfo.filter, "branchTypes", objectToLVArr($this.enumsVersionManager.branchType))
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 判断是否有重名实验
              let listResult = await ExperimentModel.getList({
                name: $this.entityInfo.edit.name
              })
              if (listResult.length > 0) {
                msg_err("已存在同名的实验，请修改后再试！")
                return
              }
              if (await msg_confirm('确认要新增该实验吗？')) {
                let result = await ExperimentModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("新增成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },
        // 点击编辑按钮
        async clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 判断是否有重名实验`
              let listResult = await ExperimentModel.getList({
                name: $this.entityInfo.edit.name,
                experimentId: {"$ne": $this.entityInfo.edit.experimentId}
              })
              if (listResult.length > 0) {
                msg_err("已存在同名的实验，请修改后再试！")
                return
              }
              if (await msg_confirm('确认要编辑该实验吗？')) {
                let result = await ExperimentModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("修改成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },
        // 点击取消按钮
        clickCancelBtn() {
          $this.entityInfo.dialog = false
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
