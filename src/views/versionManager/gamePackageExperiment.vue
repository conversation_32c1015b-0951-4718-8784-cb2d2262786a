<template>
  <div class="app-container">
    <div class="page-title flex flex-dr flex-center">
      <div>{{ experimentInfo.name }}</div>
      <div>{{ branchType }}-{{ versionType }}</div>
    </div>

    <!--分支类型-可变-->
    <el-tabs v-model="branchType" @tab-click="v=>TabMethods().clickBranchType(v)" type="card">
      <el-tab-pane v-for="item in branchTypes" :label="item.label" :name="item.value">
      </el-tab-pane>
    </el-tabs>

    <div class="clearfix">
      <!--版本类型-固定-->
      <el-tabs v-model="versionType" @tab-click="v=>TabMethods().clickVersionType(v)" tab-position="left"
               style="float:left;width: 10%;">
        <el-tab-pane v-for="item in versionTypes" :label="item.label" :name="item.value">
        </el-tab-pane>
      </el-tabs>
      <div class="list-container" style="width: 90%;float:right;">
        <!--筛选-->
        <div class="header-container clearfix">
          <list-search-filter :search-filter="lists.searchFilter"
                              @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
          </list-search-filter>
          <!--  操作  -->
          <div class="right">
            <div style="text-align: right">
              <el-button class="el-button" type="success"
                         @click="ListMethods().clickAddBtn()">新增{{ branchType }}版本
              </el-button>
            </div>
          </div>
        </div>
        <!--列表-->
        <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
                  style="width: 100%;"
                  @sort-change="sort=>ListMethods().sortChange(sort)">
          <el-table-column label="版本号" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.versionNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="版本名称" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center">
            <template slot-scope="scope">
              <span>{{ date_format(scope.row.createTime, "yyyy-MM-dd HH:mm:ss") }}</span>
            </template>
          </el-table-column>
          <el-table-column label="是否允许下载" align="center" width="150">
            <template slot-scope="scope">
              <span>{{ {true: '是', false: '否'}[scope.row.allowDownload] }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="230"
                           class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button type="text" size="mini" round
                         @click="e=>ListMethods().clickEditBtn(scope.row,e)">编辑
              </el-button>
              <el-button type="text" size="mini" round
                         @click="e=>ListMethods().clickDeleteBtn(scope.row,e)">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--列表分页-->
        <div class="pagination-container">
          <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                         :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                         layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                         @size-change="(size)=>ListMethods().pageLimitChange(size)"
                         :page-count="lists.pages.totalPages">
          </el-pagination>
        </div>
        <!--详情弹窗-新增和编辑-->
        <el-dialog
          :title="entityInfo.title"
          :visible.sync="entityInfo.dialog"
          :close-on-click-modal="false"
          :append-to-body="true"
          width="1000px"
          center
          v-el-drag-dialog>
          <div class="dialog-container" style="max-height: 600px;overflow-y: scroll">
            <el-form label-width="120px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
              <el-form-item label="版本类型:" prop="versionType">
                <el-select v-model="entityInfo.edit.versionType">
                  <el-option v-for="item in versionTypes" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="版本号:" prop="versionNumber">
                <div class="flex flex-start">
                  <el-input placeholder="大版本号(正整数)" v-model="entityInfo.edit.bigVersionNumber"
                            style="width: 220px"></el-input>
                  <span style="width: 30px;text-align: center">.</span>
                  <el-input placeholder="小版本号" v-model="entityInfo.edit.littleVersionNumber"></el-input>
                </div>
              </el-form-item>
              <el-form-item label="包文件:" prop="fileList">
                <div>
                  <el-upload
                    action="dev"
                    :show-file-list="false"
                    :on-success="data=>UploadMethods().uploadSuccess(data)"
                    :http-request="data=>UploadMethods().uploadRequest(data)"
                    :before-upload="data=>UploadMethods().beforeUpload(data)">
                    <el-button size="small" type="primary" :loading="entityInfo.loading">点击上传</el-button>
                  </el-upload>
                  <div v-if="upload.per>0" class="flex flex-dr flex-center" style="margin-top: 20px">
                    <div style="margin-bottom: 10px"> 正在上传中，请稍后</div>
                    <div class="per">{{ upload.per }}%</div>
                  </div>
                </div>
                <!--显示上传列表-->
                <div style="margin-top: 10px">
                  <el-table :data="entityInfo.edit.fileList" fit
                            style="width: 100%;">
                    <el-table-column label="存储位置" align="center" width="100">
                      <template slot-scope="scope">
                        <span>{{ scope.row.location }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="下载密码" align="center" width="200">
                      <template slot-scope="scope">
                        <span>{{ scope.row.password }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="下载链接" align="center">
                      <template slot-scope="scope">
                        <div v-if="scope.$index===1&&entityInfo.type==='add'">

                        </div>
                        <div v-else>
                          <div v-if="scope.row.password==='无密码'">{{ scope.row.url }}</div>
                          <div v-if="scope.row.password!=='无密码'">
                            {{ scope.row.url + "&experimentPackageId=" + entityInfo.edit.experimentPackageId }}
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-form-item>
              <el-form-item label="是否允许下载:" prop="allowDownload">
                <el-select v-model="entityInfo.edit.allowDownload">
                  <el-option label="是" :value="true"></el-option>
                  <el-option label="否" :value="false"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="版本名称:" prop="name">
                <el-input v-model.trim="entityInfo.edit.name">
                <span slot="suffix" v-if="entityInfo.edit.name">
                  <span class="el-input__count">
                    <span class="el-input__count-inner">
                      {{ entityInfo.edit.name.length }} / 30
                    </span>
                  </span>
                </span>
                </el-input>
              </el-form-item>
              <el-form-item label="更新说明:" prop="description">
                <tinymce
                  ref="tinymce_description"
                  v-model="entityInfo.edit.description"
                  :height="300"
                />
              </el-form-item>
              <el-form-item label="备注:" prop="remark">
                <el-input type="textarea" v-model="entityInfo.edit.remark"></el-input>
              </el-form-item>

            </el-form>
          </div>
          <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="entityInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityMethods().clickAddBtn()" v-if="entityInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityMethods().clickEditBtn()"
                   v-if="entityInfo.type==='edit'">提 交</el-button>
      </span>
        </el-dialog>
      </div>
    </div>


  </div>
</template>

<script>
import ListSearchFilter from '@/views/components/list/listSearchFilter'
import enums from '@/enums/'
import elDragDialog from '@/directive/el-drag-dialog'
import {msg_confirm, msg_err, msg_success} from '@/utils/ele_component'
import {arrToLVArr, date_format, generateSomeNumber, objectToLVArr, searchWordFiltration} from '@/utils/common'
import {mapState} from 'vuex'
import permission from '@/directive/permission'
import {ExperimentPackageModel} from "@/model/versionManager/ExperimentPackageModel";
import {DepartmentModel} from "@/model/exp/DepartmentModel";
import {dateFormat} from "@/filters";
import enumsVersionManager from "@/enums/versionManager";
import Tinymce from "@/components/Tinymce"
import {BaseUploadModel} from "@/model/BaseUploadModel";
import {ExperimentModel} from "@/model/versionManager/ExperimentModel";
import {OFFICIAL_WEB_URL} from "@/model/ConfigModel";

export default {
  name: 'versionManagerGamePackageExperiment',
  components: {ListSearchFilter, Tinymce},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles
    })
  },
  filters: {dateFormat},
  data() {
    // 校检大小版本号
    const validateVersionNumber = (rule, value, callback) => {
      if (!this.entityInfo.edit.bigVersionNumber) {
        callback(new Error("请输入大版本号"))
      } else {
        let reg = /^[0-9]+$/
        if (!reg.test(this.entityInfo.edit.bigVersionNumber)) {
          callback(new Error('只能输入正整数'))
        }
      }
      if (!this.entityInfo.edit.littleVersionNumber) {
        callback(new Error("请输入小版本号"))
      } else {

      }
      callback()
    }
    // 校检文件
    const validateFileList = (rule, value, callback) => {
      if (this.entityInfo.edit.fileList.length === 0) {
        callback(new Error("请先上传包文件"))
      }
      callback()
    }
    return {
      // 路由参数
      experimentId: this.$route.query["experimentId"],
      // 外部方法
      date_format: date_format,
      // 枚举
      enums: enums,
      enumsVersionManager: enumsVersionManager,
      versionTypes: objectToLVArr(enumsVersionManager.versionType),
      branchTypes: [],
      // 分支tab
      branchType: "PC",
      // 版本tab
      versionType: "lts",
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '版本号',
              placeholder: "请输入版本号",
              key: 'versionNumber',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              },
              value: ''
            },
            {
              type: 'input',
              label: '版本名称',
              placeholder: "请输入版本名称",
              key: 'name',
              format: (v) => {
                v = searchWordFiltration(v, 'mongo')
                return {'$regex': `.*${v}.*`}
              },
              value: ''
            }
          ],
          filter: []
        }
      },
      experimentInfo: {},
      entityInfo: {
        dialog: false,
        title: "新增版本",
        type: "add",
        filter: {},
        edit: {
          name: "",
          bigVersionNumber: "",
          littleVersionNumber: "",
          fileList: [],
          allowDownload: true
        },
        // 输入检测
        formRules: {
          'versionNumber': {
            required: true,
            validator: validateVersionNumber,
            trigger: 'blur'
          },
          'description': {required: true, message: "请输入更新说明", trigger: 'blur'},
          'fileList': {
            required: true,
            validator: validateFileList,
            trigger: 'blur'
          },
          'versionType': {required: true, message: "请选择版本类型", trigger: 'blur'},
          'allowDownload': {required: true, message: "请选择是否可以下载", trigger: 'blur'},
        },
      },
      upload: {
        uploading: true,
        per: ""
      }
    }
  },
  async created() {

  },
  async mounted() {
    // 获取实验详情
    this.experimentInfo = await ExperimentModel.getOne({
      experimentId: this.experimentId
    })
    // 生成分支筛选
    this.branchTypes = arrToLVArr(this.experimentInfo.branchTypes)
    this.branchType = this.experimentInfo.branchTypes[0]
    // 获取列表
    this.ListMethods().getList(0, this.lists.pages.size, this.lists.query)
  },
  methods: {
    // Tab方法集
    TabMethods() {
      let $this = this;
      return {
        // 点击分支类型tab
        clickBranchType(tab) {
          $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击版本tab
        clickVersionType(tab) {
          $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
        }
      }
    },
    // 列表方法集
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let queryBase = {
            branchType: $this.branchType,
            versionType: $this.versionType,
            experimentId: $this.experimentId
          }
          query = Object.assign(query, queryBase)
          let listResult = await ExperimentPackageModel.getPageList(page - 1, size, "", query)
          $this.lists.pages = listResult[1]
          let list = listResult[0]
          $this.lists.list = list;
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "新增版本"
          $this.entityInfo.edit = {
            allowDownload: true
          };
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
            $this.$refs["tinymce_description"].setContent("")
          }, 300)
        },
        // 点击编辑按钮
        async clickEditBtn(entity) {
          entity = JSON.parse(JSON.stringify(entity))
          $this.entityInfo.dialog = true;
          // 分割大小版本号
          let dotIndex = entity.versionNumber.indexOf(".")
          entity.bigVersionNumber = entity.versionNumber.substring(0, dotIndex)
          entity.littleVersionNumber = entity.versionNumber.substring(dotIndex + 1)
          $this.$set($this.entityInfo, "edit", entity);
          $this.entityInfo.type = "edit"
          $this.entityInfo.title = "编辑版本"
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
        // 点击删除按钮
        async clickDeleteBtn(entity) {
          if (await msg_confirm("确认是否删除该版本？删除后不可恢复，请谨慎操作！")) {
            await ExperimentPackageModel.deleteOne({
              experimentPackageId: entity.experimentPackageId,
              experimentId: entity.experimentId,
            })
            $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
            msg_success("删除此版本成功！")
          }
        }
      }
    },
    // 实体方法集
    EntityMethods() {
      let $this = this;
      return {
        // 初始化筛选
        initFilter() {
          $this.$set($this.entityInfo.filter, "branchTypes", objectToLVArr($this.enumsVersionManager.branchType))
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 合并版本号
              let versionNumber = $this.entityInfo.edit.bigVersionNumber + "." + $this.entityInfo.edit.littleVersionNumber
              // 判断是否有同版本号包
              let listResult = await ExperimentPackageModel.getList({
                versionNumber: versionNumber,
                branchType: $this.branchType,
                experimentId: $this.experimentId,
              })
              if (listResult.length > 0) {
                msg_err("已存在同版本号的包，请修改后再试！")
                return
              }
              $this.entityInfo.edit.versionNumber = versionNumber
              // 添加实验编号
              $this.entityInfo.edit.experimentId = $this.experimentId
              // 添加分支
              $this.entityInfo.edit.branchType = $this.branchType
              if (await msg_confirm('确认要新增该版本吗？')) {
                let result = await ExperimentPackageModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("新增成功")
                  this.clickCancelBtn()
                  // 修改分支类型显示
                  $this.$set($this, "versionType", $this.entityInfo.edit.versionType)
                  // 显示列表
                  $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },
        // 点击编辑按钮
        async clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 合并版本号
              let versionNumber = $this.entityInfo.edit.bigVersionNumber + "." + $this.entityInfo.edit.littleVersionNumber
              // 判断是否有同版本号包
              let listResult = await ExperimentPackageModel.getList({
                versionNumber: versionNumber,
                branchType: $this.branchType,
                experimentId: $this.experimentId,
                experimentPackageId: {
                  "$ne": $this.entityInfo.edit.experimentPackageId
                },
              })
              if (listResult.length > 0) {
                msg_err("已存在同版本号的包，请修改后再试！")
                return
              }
              $this.entityInfo.edit.versionNumber = versionNumber
              if (await msg_confirm('确认要编辑该实验吗？')) {
                let result = await ExperimentPackageModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("修改成功")
                  this.clickCancelBtn()
                  // 修改分支类型显示
                  $this.$set($this, "versionType", $this.entityInfo.edit.versionType)
                  // 显示列表
                  $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },
        // 点击取消按钮
        clickCancelBtn() {
          $this.entityInfo.dialog = false
        },
      }
    },
    // 上传方法集
    UploadMethods() {
      let $this = this;
      return {
        // 上传前检测
        beforeUpload(file) {
          if($this.entityInfo.uploading){
            msg_err("有包正在上传中，请稍后!")
            return false
          }
          $this.entityInfo.uploading = true
          return true
        },
        // 上传成功后
        uploadSuccess(data) {
          let url = data.data
          // 得到url中的文件地址
          let fileName = url.split("/")[3]
          $this.$set($this.entityInfo.edit, "fileList", [
            {
              url: url,
              location: "七牛云",
              password: "无密码"
            },
            {
              url: `${OFFICIAL_WEB_URL}experimentPackageDownload?fileIndex=1`,
              realUrl: url,
              location: "七牛云",
              password: generateSomeNumber(6)
            },
            {
              url: `http://192.168.0.99:8906/list/${fileName}`,
              password: "无密码",
              location: "本地服务器"
            }
          ])
          $this.upload.uploading = false
          $this.$refs['entityInfoForm'].validate();
          $this.$set($this.entityInfo, "loading", false)
          return true
        },
        // 上传请求
        async uploadRequest(upload) {
          $this.$set($this.entityInfo, "loading", true)
          let file = upload.file
          return await new Promise((resolve, reject) => {
            BaseUploadModel.qiNiuUpload(file, {
              next: (result) => {
                // 上传进度显示
                $this.upload.per = result.total.percent.toFixed(0);
                if ($this.upload.per == 100) {
                  $this.upload.per = 0;
                }
              },
              error: (errResult) => {
                console.log(errResult)
                msg_err('上传失败')
              },
              complete: (result) => {
                let domain = BaseUploadModel.getBucketDomain(file)
                let url = domain + '/' + result.key + ''
                resolve({data: url})
              }
            })
          })
        },
      }
    }
  }
}
</script>

<style scoped lang="scss">
.page-title {
  text-align: center;
  font-weight: bold;

  > div:nth-child(1) {
    font-size: 26px;
    padding-bottom: 5px;
    margin-bottom: 5px;
    border-bottom: 1px dashed #cecece;
  }

  > div:nth-child(1) {

  }
}
</style>
