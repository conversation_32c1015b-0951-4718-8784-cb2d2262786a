<template>
  <div class="app-container">
    <div class="page-container">
      <div class="header-box">
        <div class="image imagesBg doc flex flex-center flex-dr">
          <h2 contenteditable="true">{{ aboutManage.texts[0] }}</h2>
          <h3 contenteditable="true">{{ aboutManage.texts[1] }}</h3>
          <el-button type="danger" @click="clickImagesBg(0)">换图（1920x620）</el-button>
        </div>
      </div>
      <div class="who-box flex flex-between">
        <div class="title" contenteditable="true">{{ aboutManage.texts[2] }}</div>
        <div class="des">
          <p contenteditable="true">{{ aboutManage.texts[3] }}</p>
          <p contenteditable="true">{{ aboutManage.texts[4] }}</p>
          <p contenteditable="true">{{ aboutManage.texts[5] }}</p>
        </div>
      </div>
      <div class="middle-img-1 imagesBg flex flex-dr flex-center" >
        <el-button type="danger" @click="clickImagesBg(1)">换图（1280x600）</el-button>
      </div>
      <div class="research-box">
        <h2 class="title" contenteditable="true">{{ aboutManage.texts[6] }}</h2>
        <h3 class="des" contenteditable="true">{{ aboutManage.texts[7] }}</h3>
        <div class="four-box">
          <div class="li flex flex-between">
            <div class="square">
              <div class="title" contenteditable="true">{{ aboutManage.texts[8] }}</div>
              <div class="des" contenteditable="true">{{ aboutManage.texts[9] }}</div>
            </div>
            <div class="square">
              <div class="title" contenteditable="true">{{ aboutManage.texts[10] }}</div>
              <div class="des" contenteditable="true">{{ aboutManage.texts[11] }}</div>
            </div>
          </div>
          <div class="li flex flex-between">
            <div class="square">
              <div class="title" contenteditable="true">{{ aboutManage.texts[12] }}</div>
              <div class="des" contenteditable="true">{{ aboutManage.texts[13] }}</div>
            </div>
            <div class="square">
              <div class="title" contenteditable="true">{{ aboutManage.texts[14] }}</div>
              <div class="des" contenteditable="true">{{ aboutManage.texts[15] }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="get-box imagesBg doc-box  flex flex-dr flex-center">
        <h2 contenteditable="true">{{ aboutManage.texts[16] }}</h2>
        <div class="li-box flex flex-between">
          <div class="li flex flex-dr flex-center"><img
            src="http://resouce.cdzyhd.com/543ced94-cb1a-4dc5-941c-a394792a326a.svg" alt="">
            <div class="title" contenteditable="true">{{ aboutManage.texts[17] }}</div>
            <div class="des" contenteditable="true">{{ aboutManage.texts[18] }}</div>
          </div>
          <div class="li flex flex-dr flex-center"><img
            src="http://resouce.cdzyhd.com/10f39c51-cce4-4155-97f2-98c4492d9ff0.svg" alt="">
            <div class="title" contenteditable="true">{{ aboutManage.texts[19] }}</div>
            <div class="des" contenteditable="true">{{ aboutManage.texts[20] }}</div>
          </div>
          <div class="li flex flex-dr flex-center"><img
            src="http://resouce.cdzyhd.com/6e23ebc7-605a-4494-a6a1-96b24dc9070e.svg" alt="">
            <div class="title" contenteditable="true">{{ aboutManage.texts[21] }}</div>
            <div class="des" contenteditable="true">{{ aboutManage.texts[22] }}</div>
          </div>
          <div class="li flex flex-dr flex-center"><img
            src="http://resouce.cdzyhd.com/03cdd292-df0e-488e-9380-f3214fb3a043.svg" alt="">
            <div class="title" contenteditable="true">{{ aboutManage.texts[23] }}</div>
            <div class="des" contenteditable="true">{{ aboutManage.texts[24] }}</div>
          </div>
        </div>
        <el-button type="danger" style="margin-top: 50px" @click="clickImagesBg(2)">换图（1920x600）</el-button>
      </div>
      <div class="open-box flex flex-dr flex-center">
        <h2 contenteditable="true">{{ aboutManage.texts[25] }}</h2>
        <h3 contenteditable="true">{{ aboutManage.texts[26] }}</h3>
        <div class="li-box flex flex-around">
          <div class="li flex flex-center flex-dr"><img
            src="http://resouce.cdzyhd.com/c4d62c60-5f44-4803-8a8d-17553b416ed1.svg" alt="">
            <div class="title" contenteditable="true">{{ aboutManage.texts[27] }}</div>
            <div class="des" contenteditable="true">{{ aboutManage.texts[28] }}</div>
          </div>
          <div class="li flex flex-center flex-dr"><img
            src="http://resouce.cdzyhd.com/5dd5614c-b9c8-42cd-b8bb-922111592e76.svg" alt="">
            <div class="title" contenteditable="true">{{ aboutManage.texts[29] }}</div>
            <div class="des" contenteditable="true">{{ aboutManage.texts[30] }}</div>
          </div>
          <div class="li flex flex-center flex-dr"><img
            src="http://resouce.cdzyhd.com/bdc64f33-7163-4ee3-9f9e-d7c21f42b7df.svg" alt="">
            <div class="title" contenteditable="true">{{ aboutManage.texts[31] }}</div>
            <div class="des" contenteditable="true">{{ aboutManage.texts[32] }}</div>
          </div>
        </div>
      </div>
    </div>
    <!--按钮组-->
    <div class="bottom-button-container flex flex-center">
      <el-button type="primary" @click="clickSaveBtn()">保 存</el-button>
      <el-button style="visibility: hidden">取 消</el-button>
    </div>
    <!--背景图片上传input-->
    <input
      id="imagesBgInput"
      type="file"
      style="display: none"
      @change="(files)=>{imagesBgFileChange(files)}"
    >
  </div>

</template>

<script>
import $ from 'jquery';
import {msg_confirm, msg_err, msg_success} from "@/utils/ele_component";
import {ConfigModel} from "@/model/erp/ConfigModel";
import {CONFIG_NAME_OFFICIAL_WEB} from "@/model/ConfigModel";
import {BaseUploadModel} from "@/model/BaseUploadModel";
import {TeacherModel} from "@/model/exp/TeacherModel";

export default {
  name: "aboutManage",
  data() {
    return {
      aboutManage: {
        texts: [],
        images: []
      },
      // 当前正在上传的背景图id
      uploadImagesBgIndex: 0,
    }
  },
  async mounted() {
    // 获取关于我们配置
    let aboutManage = JSON.parse(await ConfigModel.getEdit(CONFIG_NAME_OFFICIAL_WEB, "aboutManage"));
    this.$set(this, "aboutManage", aboutManage);
    this.$nextTick(() => {
      // 渲染背景图
      let allImageElement = $(".imagesBg");
      for (let i = 0; i < allImageElement.length; i++) {
        let ele = allImageElement[i]
        $(ele).css("backgroundImage", `url("${this.aboutManage.images[i]}")`)
      }
    })
  },
  methods: {
    // 点击保存按钮
    async clickSaveBtn() {
      // 获取所有可编辑的文本，组成数组
      let allTextElement = $("[contenteditable=true]");
      let texts = []
      for (let i = 0; i < allTextElement.length; i++) {
        let ele = allTextElement[i]
        texts.push($(ele).text())
      }
      // 获取所有图片地址
      let allImageElement = $(".imagesBg");
      let images = []
      for (let i = 0; i < allImageElement.length; i++) {
        let ele = allImageElement[i]
        let backgroundImage = $(ele).css("backgroundImage")
        let url = backgroundImage.replace('url("', '').replace('")', '');
        images.push(url)
      }
      let aboutManage = JSON.parse(JSON.stringify(this.aboutManage))
      aboutManage.texts = texts
      aboutManage.images = images;
      if (await msg_confirm("确定要修改关于我们页面吗？")) {
        if (await ConfigModel.editEdit(CONFIG_NAME_OFFICIAL_WEB, "aboutManage", aboutManage)) {
          msg_success("保存成功")
        }
      }
    },
    // 点击可以设置的背景图片
    async clickImagesBg(index) {
      const uploader = document.getElementById('imagesBgInput')
      uploader.click()
      this.uploadImagesBgIndex = index
    },
    // 背景图上传文件改变
    async imagesBgFileChange(files) {
      const file = files.target.files[0]
      document.getElementById('imagesBgInput').value = ''
      await BaseUploadModel.qiNiuUpload(file, {
        next: (result) => {
        },
        error: (errResult) => {
          console.log(errResult)
          msg_err('上传失败')
        },
        complete: (result) => {
          let domain = BaseUploadModel.getBucketDomain(file)
          let url = domain + '/' + result.key + ''
          $(`.imagesBg:eq(${this.uploadImagesBgIndex})`).css("backgroundImage", `url("${url}")`)
        }
      })
    },
  }
}
</script>

<style scoped lang="scss">
.header-box {
  position: relative;
  width: 100%;
  height: 620px;
  margin-bottom: 70px;
}

.header-box .image {
  position: absolute;
  left: 0px;
  top: 0px;
  z-index: 0;
  width: 100%;
  height: 620px;
  background-size: cover;
  background-position: 50% 50%;
  -webkit-backface-visibility: hidden;
  //background-image: url("http://resouce.cdzyhd.com/8ca605eb-5c8b-47c5-aa86-5733a03a5339.png");
}

.header-box .doc {
  color: #fff;
}

.header-box .doc h2 {
  font-size: 36px;
  font-weight: 500;
  line-height: 61px;
  color: #FFFFFF;
}

.header-box .doc h3 {
  font-size: 16px;
  font-weight: 500;
  line-height: 27px;
  color: #FFFFFF;
}

.who-box {
  width: 1280px;
  margin: 0 auto;
}

.who-box .title {
  width: 400px;
  height: 245px;
  font-size: 36px;
  font-weight: 500;
  line-height: 200px;
  color: #333333;
}

.who-box .des {
  width: 880px;
  height: 245px;
  font-size: 16px;
  font-weight: 400;
  line-height: 27px;
  color: #333333;
  opacity: 1;
}

.who-box .des p {
  margin: 0;
  padding: 0;
  text-indent: 2em;
}

.middle-img-1 {
  width: 1280px;
  height: 600px;
  margin: 0 auto;
  background-size: cover;
  background-position: 50% 50%;
  -webkit-backface-visibility: hidden;
  margin-bottom: 50px;
  //background-image: url("http://resouce.cdzyhd.com/8b833646-c673-4c7a-892c-d46823e9e686.png");
}

.research-box {
  width: 1280px;
  margin: 0 auto;
}

.research-box h2.title {
  font-size: 36px;
  font-weight: 500;
  line-height: 61px;
  color: #333333;
  text-align: center;
}

.research-box .des {
  width: 1275px;
  height: 97px;
  font-size: 16px;
  font-weight: 400;
  line-height: 27px;
  color: #333333;
  text-indent: 2em;
}

.research-box .four-box {
  width: 700px;
  margin: 0 auto;
}

.research-box .four-box .li {
  width: 100%;
}

.research-box .four-box .li .square {
  width: 250px;
}

.research-box .four-box .li .square .title {
  width: 250px;
  font-size: 36px;
  font-weight: 500;
  line-height: 61px;
  color: #333333;
  margin-bottom: 20px;
  text-align: center;
}

.research-box .four-box .li .square .des {
  width: 250px;
  font-size: 16px;
  font-weight: 400;
  line-height: 27px;
  color: #888888;
  opacity: 1;
}

.get-box {
  position: relative;
  width: 100%;
  height: 600px;
  background-size: cover;
  background-position: 50% 50%;
  -webkit-backface-visibility: hidden;
  //background-image: url("http://resouce.cdzyhd.com/636b7ca8-1f7c-484f-9f43-1f8bd523e4d0.png");
}


.get-box h2 {
  font-size: 36px;
  font-weight: 500;
  line-height: 61px;
  color: #FFFFFF;
  margin-bottom: 100px;
}

.get-box .li-box {
  width: 1000px;
}

.get-box .li img {
  width: 62px;
  height: 47px;
  margin-bottom: 20px;
}

.get-box .li .title {
  font-size: 26px;
  font-weight: 500;
  line-height: 44px;
  color: #FFFFFF;
  margin-bottom: 20px;
}

.get-box .li .des {
  font-size: 16px;
  font-weight: 500;
  line-height: 27px;
  color: #FFFFFF;
  max-width: 200px;
  text-align: center;
}

.open-box {
  width: 1280px;
  margin: 0 auto;
  margin-bottom: 70px;
}

.open-box h2 {
  font-size: 36px;
  font-weight: 500;
  line-height: 61px;
  color: #333333;
}

.open-box h3 {
  font-size: 16px;
  font-weight: 400;
  line-height: 27px;
  color: #333333;
  margin-bottom: 50px;
  width: 670px;
  text-align: center;
}

.open-box .li-box {
  width: 1100px;
}

.open-box .li img {
  width: 56px;
  height: 56px;
  margin-bottom: 10px;
}

.open-box .li .title {
  font-size: 26px;
  font-weight: 500;
  line-height: 44px;
  color: #333333;
  margin-bottom: 20px;
}

.open-box .li .des {
  font-size: 16px;
  font-weight: 400;
  line-height: 27px;
  color: #333333;
  max-width: 200px;
  text-align: center;
}
</style>
