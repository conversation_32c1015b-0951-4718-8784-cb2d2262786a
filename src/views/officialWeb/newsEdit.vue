<template>
  <div class="app-container">
    <el-form label-width="120px" ref="editForm" :model="edit" :rules="editForm">
      <el-form-item label="新闻标题:" prop="title">
        <el-input v-model="edit.title"></el-input>
      </el-form-item>
      <el-form-item label="新闻简介:" prop="description">
        <el-input v-model="edit.description" type="textarea"></el-input>
      </el-form-item>
      <el-form-item label="发布时间:" prop="createTime">
        <el-date-picker
          v-model="edit.createTime"
          type="datetime"
          placeholder="选择发布时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="新闻头图:" prop="headImgUrl">
        <!--pc-首页-->
        <erp-uploader-one-pic :img-in="edit.headImgUrl" style="width: 200px"
                              uploader-id="headImgUrl"
                              uploader-title="新闻头图" :uploader-size="[200,150]" :pixel-limit="[200,150]"
                              :size-limit="512"
                              @uploadSuccess="data=>fileUpload(data,edit)"
                              @afterDelete="data=>fileDelete(data,edit)"></erp-uploader-one-pic>
      </el-form-item>
      <el-form-item label="新闻内容:" prop="content">
        <tinymce
          ref="tinymce_content"
          v-model="edit.content"
          :height="500"
        />
      </el-form-item>
    </el-form>
    <div class="buttons" style="width: 100%;text-align: center">
      <el-button type="default"
                 @click="$router.go(-1)">返 回
      </el-button>
      <el-button type="primary" @click="clickPublishBtn()">发布</el-button>
    </div>
  </div>
</template>

<script>
import Tinymce from "@/components/Tinymce";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import {OfficialWebNewsModel} from "@/model/erp/OfficialWebNewsModel";
import {validateMaxLength} from "@/utils/validate";
import {msg_confirm, msg_success} from "@/utils/ele_component";
import {ExperimentModel} from "@/model/versionManager/ExperimentModel";

export default {
  name: "newsManageEdit",
  components: {Tinymce, erpUploaderOnePic},
  data() {
    return {
      editType: !!this.$route.query["id"],
      id: this.$route.query["id"],
      edit: {

      },
      editForm: {
        'title': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 45, "新闻标题"), trigger: 'blur'},
        'description': {
          required: true,
          validator: (r, v, c) => validateMaxLength(r, v, c, 250, "新闻描述"),
          trigger: 'blur'
        },
        'headImgUrl': {required: true, message: "请上传新闻头图", trigger: ['change', 'blur']},
        'content': {required: true, message: '请填写新闻内容', trigger: 'change'},
        'createTime': {required: true, message: "请选择发布时间", trigger: ['change', 'blur']},
      }
    }
  },
  async mounted() {
    // 如果是编辑模式
    if (this.editType) {
      this.edit = await OfficialWebNewsModel.getOne(this.id);
    }else{
      this.edit={
        owner:"zyhd"
      }
    }
  },
  methods: {
    // 点击发布按钮
    clickPublishBtn() {
      this.$refs['editForm'].validate(async validate => {
        if (validate) {
          if (await msg_confirm(this.editType ? "确认要保存修改并发布该新闻吗？" : "确认要发布该新闻吗？")) {
            let result = await OfficialWebNewsModel.addOrEdit(this.edit)
            if (result.code === "000000") {
              msg_success("发布成功")
              if (!this.editType) {// 如果是新增，就返回上一页
                this.$router.go(-1);
              }
            }
          }
        }
      });
    },
    // 通用-文件上传成功
    fileUpload(params, target) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
    },
    // 通用-文件删除
    fileDelete(params, target) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
    },
  }
}
</script>

<style scoped lang="less">

</style>
