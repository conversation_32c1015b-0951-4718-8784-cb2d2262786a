<template>
  <div class="table-form">
    <el-table :data="list" border fit>
      <el-table-column :label="'*产品名称\n(30字以内)'" align="center" width="400px">
        <template slot-scope="scope">
          <el-form :ref="'form_name$'+scope.$index" :model="scope.row" :rules="formRules">
            <el-form-item prop="name">
              <!--              <el-input v-model="scope.row.name" maxlength="20" show-word-limit></el-input>-->
              <span class="input_box ellipsis"
                    @click="openTextEditor('name',scope.$index,30,'产品名称(30字以内)')">{{
                  scope.row.name
                }}</span>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column :label="'*链接地址'" align="center" width="400px">
        <template slot-scope="scope">
          <el-form :ref="'form_link$'+scope.$index" :model="scope.row" :rules="formRules">
            <el-form-item prop="link">
              <!--              <el-input v-model="scope.row.link"></el-input>-->
              <span class="input_box ellipsis"
                    @click="openTextEditor('link',scope.$index,100,'链接地址')">{{
                  scope.row.link
                }}</span>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column :label="'*产品简介\n(250字以内)'" align="center">
        <template slot-scope="scope">
          <el-form :ref="'form_description$'+scope.$index" :model="scope.row" :rules="formRules">
            <el-form-item prop="description">
          <span class="input_box ellipsis"
                @click="openTextEditor('description',scope.$index,250,'产品简介(250字以内)')">{{
              scope.row.description
            }}</span>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column :label="'*产品封面\n(400*400,400KB以内)'" align="center" width="200px">
        <template slot-scope="scope">
          <el-form :ref="'form_product_bg$'+scope.$index" :model="scope.row" :rules="formRules">
            <el-form-item prop="product_bg">
              <erp-uploader-one-pic :img-in="scope.row.product_bg?scope.row.product_bg+'?imageView2/1/w/80/h/80/q/75':''"
                                    :uploader-id="'product_bg__'+scope.row.id"
                                    :show-des="false"
                                    :uploader-size="[80,80]" :pixel-limit="[400,400]"
                                    :size-limit="400"
                                    @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"
                                    @afterDelete="v=>onImgDelete(v,scope.$index)"
              ></erp-uploader-one-pic>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="60px">
        <template slot-scope="scope">
          <div>
            <el-button :disabled="scope.$index===0" size="small" type="text"
                       @click="clickMoveUpBtn(scope.row.id,scope.$index)">上移
            </el-button>
          </div>
          <div>
            <el-button size="small" type="text" @click="clickDeleteBtn(scope.row.id,scope.$index)"
                       :disabled="list.length===1">删除
            </el-button>
          </div>
          <div>
            <el-button :disabled="scope.$index===list.length-1" size="small" type="text"
                       @click="clickMoveDownBtn(scope.row.id,scope.$index)">下移
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!--弹窗文字编辑-->
    <el-dialog
      :title="textEditorTypeName"
      :visible.sync="textEditorShow"
      width="500px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <!--todo 不允许换行和中间输入空格-->
        <el-input v-if="!textEditorTrim" type="textarea" v-model="textEditorValue" show-word-limit
                  :maxlength="textEditorMaxLength"
                  @input="onTextEditorValueChange"
                  rows="10"></el-input>
        <el-input v-if="textEditorTrim" type="textarea" v-model.trim="textEditorValue" show-word-limit
                  :maxlength="textEditorMaxLength"
                  @input="onTextEditorValueChange"
                  rows="10"></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="textEditorShow=false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import erpUploaderOneVideo from "@/views/components/erpUploaderOneVideo";
import {msg_confirm} from "@/utils/ele_component";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import $ from "jquery";

export default {
  name: "productExperimentTable",
  directives: {
    elDragDialog
  },
  components: {erpUploaderOneVideo, erpUploaderOnePic},
  props: {
    list: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      // form-rule
      formRules: {
        'name': {
          required: true,
          trigger: 'blur',
          message: "请输入",
        },
        'link': {
          required: true,
          trigger: 'blur',
          message: "请输入",
        },
        'description': {
          required: true,
          trigger: 'blur',
          message: "请输入",
        },
        'product_bg': {
          required: true,
          trigger: 'blur',
          message: "请设置",
        },
      },
      // 文本弹窗编辑
      textEditorShow: false,
      textEditorValue: "",
      textEditorIndex: 0,
      textEditorType: "",
      textEditorMaxLength: 0,
      textEditorTypeName: "",
      textEditorTrim: false,
      // 产品简介弹窗编辑
      descriptionDialogShow: false,
      // 产品详情-实验简介
      experimentDescriptionDialogShow: false
    }
  },
  methods: {
    // 表格中的表单验证
    validateForm() {
      let checkResult = true
      let $this = this
      // 获取所有refs
      let refs = this.$refs
      // 遍历
      for (let k in refs) {
        let $index = k.split("$")[1]
        let name = k.split("$")[0]
        // 非直接变量 需要单独验证器
        if (refs[k]) {
          refs[k].validate(validate => {
            if (!validate) {
              checkResult = false
            }
          })
        }
      }
      // 返回检测结果
      // console.log(checkResult)
      this.$emit("onFormCheckResult", {result: checkResult})
      return checkResult
    },
    // 打开文本弹窗编辑
    openTextEditor(type, index, maxLength, typeName) {
      this.textEditorType = type
      this.textEditorValue = this.list[index][type]
      this.textEditorMaxLength = maxLength
      this.textEditorIndex = index
      this.textEditorTypeName = typeName
      this.textEditorShow = true
    },
    // 改变文本弹窗编辑值
    onTextEditorValueChange(v) {
      this.$set(this.list[this.textEditorIndex], this.textEditorType, v)
    },
    // 点击-上移按钮
    /**
     * params {{id 实验id,index 实验排序号}}
     */
    clickMoveUpBtn(id, index) {
      this.$emit("onClickVrMoveUpBtn", {id, index})
    },
    // 点击-下移按钮
    clickMoveDownBtn(id, index) {
      this.$emit("onClickVrMoveDownBtn", {id, index})
    },
    // 点击删除按钮
    async clickDeleteBtn(id, index) {
      if (await msg_confirm("确认是否删除该行？")) {
        this.$emit("onClickVrDeleteBtn", {id, index})
      }
    },
    // 图片上传成功
    onImgUploadSuccess(params, index) {
      this.$emit("onImgUploadSuccess", {
        params, index
      })
    },
    // 图片删除
    onImgDelete(params, index) {
      this.$emit("onImgDelete", {
        params, index
      })
    },
    // 视频上传成功
    onVideoUploadSuccess(params, index) {
      this.$emit("onImgUploadSuccess", {
        params, index
      })
    },
    // 图片删除
    onVideoDelete(params, index) {
      this.$emit("onImgDelete", {
        params, index
      })
    }
  }
}
</script>

<style scoped lang="scss">
// 输入框-点击弹窗大窗编辑
.input_box {
  background: #FFFFFF;
  line-height: initial;
  display: inline-block;
  padding: 5px 5px;
  border-radius: 5px;
  border: 1px solid #dcdfe6;
  width: 300px;
  cursor: pointer;
  height: 32px;
}
</style>
