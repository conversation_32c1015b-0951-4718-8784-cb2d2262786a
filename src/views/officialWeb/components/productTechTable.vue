<template>
  <div class="table-form">
    <el-table :data="list" border fit>
      <el-table-column :label="'*子楼层名称\n(20字以内)'" align="center">
        <template slot-scope="scope">
          <el-form :ref="'form_name$'+scope.$index" :model="scope.row" :rules="formRules">
            <el-form-item prop="name">
<!--              <el-input v-model="scope.row.name" maxlength="20" show-word-limit></el-input>-->
              <span class="input_box ellipsis"
              @click="openTextEditor('name',scope.$index,20,'子楼层名称(20字以内)')">{{
                scope.row.name
              }}</span>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column :label="'*背景图\n(1920*250,3M以内)'" align="center">
        <template slot-scope="scope">
          <el-form :ref="'form_child_floor_bg$'+scope.$index" :model="scope.row" :rules="formRules">
            <el-form-item prop="child_floor_bg">
              <erp-uploader-one-pic :img-in="scope.row.child_floor_bg"
                                    :uploader-id="'child_floor_bg__'+scope.row.id"
                                    :show-des="false"
                                    :uploader-size="[150,75]" :pixel-limit="[1920,250]"
                                    :size-limit="3072"
                                    @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"
                                    @afterDelete="v=>onImgDelete(v,scope.$index)"
              ></erp-uploader-one-pic>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column :label="'*弹窗介绍文本\n(500字以内)'" align="center">
        <template slot-scope="scope">
          <el-form :ref="'form_des$'+scope.$index" :model="scope.row" :rules="formRules">
            <el-form-item prop="des">
          <span class="input_box ellipsis"
                @click="openTextEditor('des',scope.$index,500,'弹窗介绍文本(500字以内)')">{{
              scope.row.des
            }}</span>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column :label="'*弹窗背景图\n(1200*360,3M以内)'" align="center">
        <template slot-scope="scope">
          <el-form :ref="'form_child_floor_dialog_bg$'+scope.$index" :model="scope.row" :rules="formRules">
            <el-form-item prop="child_floor_dialog_bg">
              <erp-uploader-one-pic :img-in="scope.row.child_floor_dialog_bg"
                                    :uploader-id="'child_floor_dialog_bg__'+scope.row.id"
                                    :show-des="false"
                                    :uploader-size="[150,75]" :pixel-limit="[1200,360]"
                                    :size-limit="3072"
                                    @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"
                                    @afterDelete="v=>onImgDelete(v,scope.$index)"
              ></erp-uploader-one-pic>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column :label="'*弹窗按钮文字\n(20字以内)'" align="center">
        <template slot-scope="scope">
          <el-form :ref="'form_dialog_button_text$'+scope.$index" :model="scope.row" :rules="formRules">
            <el-form-item prop="dialog_button_text">
          <span class="input_box ellipsis"
                @click="openTextEditor('dialog_button_text',scope.$index,20,'弹窗按钮文字(20字以内)')">{{
              scope.row.dialog_button_text
            }}</span>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column :label="'*是否显示'" align="center" width="100px">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.show">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="60px">
        <template slot-scope="scope">
          <div>
            <el-button :disabled="scope.$index===0" size="small" type="text"
                       @click="clickMoveUpBtn(scope.row.id,scope.$index)">上移
            </el-button>
          </div>
          <div>
            <el-button size="small" type="text" @click="clickDeleteBtn(scope.row.id,scope.$index)"
                       :disabled="list.length===1">删除
            </el-button>
          </div>
          <div>
            <el-button :disabled="scope.$index===list.length-1" size="small" type="text"
                       @click="clickMoveDownBtn(scope.row.id,scope.$index)">下移
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!--弹窗文字编辑-->
    <el-dialog
      :title="textEditorTypeName"
      :visible.sync="textEditorShow"
      width="500px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <!--todo 不允许换行和中间输入空格-->
        <el-input v-if="!textEditorTrim" type="textarea" v-model="textEditorValue" show-word-limit
                  :maxlength="textEditorMaxLength"
                  @input="onTextEditorValueChange"
                  rows="10"></el-input>
        <el-input v-if="textEditorTrim" type="textarea" v-model.trim="textEditorValue" show-word-limit
                  :maxlength="textEditorMaxLength"
                  @input="onTextEditorValueChange"
                  rows="10"></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="textEditorShow=false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import erpUploaderOneVideo from "@/views/components/erpUploaderOneVideo";
import {msg_confirm} from "@/utils/ele_component";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import $ from "jquery";

export default {
  name: "productTechTable",
  directives: {
    elDragDialog
  },
  components: {erpUploaderOneVideo, erpUploaderOnePic},
  props: {
    list: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      // form-rule
      formRules: {
        'name': {
          required: true,
          trigger: 'blur',
          message: "请输入",
        },
        'child_floor_bg': {
          required: true,
          trigger: 'blur',
          message: "请设置",
        },
        'des': {
          required: true,
          trigger: 'blur',
          message: "请输入",
        },
        'child_floor_dialog_bg': {
          required: true,
          trigger: 'blur',
          message: "请设置",
        },
        'dialog_button_text': {
          required: true,
          trigger: 'blur',
          message: "请设置",
        }
      },
      // 文本弹窗编辑
      textEditorShow: false,
      textEditorValue: "",
      textEditorIndex: 0,
      textEditorType: "",
      textEditorMaxLength: 0,
      textEditorTypeName: "",
      textEditorTrim: false,
    }
  },
  methods: {
    // 表格中的表单验证
    validateForm() {
      let checkResult = true
      let $this = this
      // 获取所有refs
      let refs = this.$refs
      // 遍历
      for (let k in refs) {
        let $index = k.split("$")[1]
        let name = k.split("$")[0]
        // 非直接变量 需要单独验证器
        if(refs[k]){
          refs[k].validate(validate => {
            if (!validate) {
              checkResult = false
            }
          })
        }
      }
      // 返回检测结果
      // console.log(checkResult)
      this.$emit("onFormCheckResult", {result: checkResult})
      return checkResult
    },
    // 打开文本弹窗编辑
    openTextEditor(type, index, maxLength, typeName) {
      this.textEditorType = type
      this.textEditorValue = this.list[index][type]
      this.textEditorMaxLength = maxLength
      this.textEditorIndex = index
      this.textEditorTypeName = typeName
      this.textEditorShow = true
    },
    // 改变文本弹窗编辑值
    onTextEditorValueChange(v) {
      this.$set(this.list[this.textEditorIndex], this.textEditorType, v)
    },
    // 点击-上移按钮
    /**
     * params {{id 实验id,index 实验排序号}}
     */
    clickMoveUpBtn(id, index) {
      this.$emit("onClickMoveUpBtn", {id, index})
    },
    // 点击-下移按钮
    clickMoveDownBtn(id, index) {
      this.$emit("onClickMoveDownBtn", {id, index})
    },
    // 点击删除按钮
    async clickDeleteBtn(id, index) {
      if (await msg_confirm("确认是否删除该行？")) {
        this.$emit("onClickDeleteBtn", {id, index})
      }
    },
    // 图片上传成功
    onImgUploadSuccess(params, index) {
      this.$emit("onImgUploadSuccess", {
        params, index
      })
    },
    // 图片删除
    onImgDelete(params, index) {
      this.$emit("onImgDelete", {
        params, index
      })
    },
    // 视频上传成功
    onVideoUploadSuccess(params, index) {
      this.$emit("onImgUploadSuccess", {
        params, index
      })
    },
    // 图片删除
    onVideoDelete(params, index) {
      this.$emit("onImgDelete", {
        params, index
      })
    }
  }
}
</script>

<style>
.table-form .el-form-item__error {
  position: relative;
}
</style>
<style scoped lang="scss">
// 输入框-点击弹窗大窗编辑
.input_box {
  background: #FFFFFF;
  line-height: initial;
  display: inline-block;
  padding: 5px 5px;
  border-radius: 5px;
  border: 1px solid #dcdfe6;
  width: 200px;
  cursor: pointer;
  height: 32px;
}
</style>
