<template>
  <div class="app-container">
    <!--产品页设置-->
    <el-card style="margin-bottom: 20px" v-if="false">
      <div slot="header" class="clearfix">
        <span>产品页设置</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="pageInfo.cardShow=!pageInfo.cardShow">
          <i class="el-icon-arrow-up" v-show="pageInfo.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!pageInfo.cardShow"></i>
          {{ pageInfo.cardShow ? '收起' : '展开' }}
        </el-button>
      </div>
      <div class="card-container" v-show="pageInfo.cardShow">
        <el-form>

        </el-form>
      </div>
    </el-card>
    <!--实验设置-->
    <a href="#" name="experimentInfo"></a>
    <el-card style="margin-bottom: 20px" id="experimentInfo">
      <div slot="header" class="clearfix">
        <span>虚拟仿真实验楼层</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="experimentInfo.cardShow=!experimentInfo.cardShow">
          <i class="el-icon-arrow-up" v-show="experimentInfo.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!experimentInfo.cardShow"></i>
          {{ experimentInfo.cardShow ? '收起' : '展开' }}
        </el-button>
        <el-button v-if="false" style="float:right" size="small" @click="clickSaveBtn(['experimentInfo'])">保存
        </el-button>
      </div>
      <div class="card-container" v-show="experimentInfo.cardShow">
        <el-form ref="experimentInfo" :model="experimentInfo" :rules="experimentInfo.formRules">
          <el-form-item label-width="100px" label="在官网显示：">
            <el-switch @change="v=>onFloorChangeShow(v,experimentInfo)"
                       v-model="experimentInfo.show">
            </el-switch>
          </el-form-item>
          <el-form-item label-width="100px" label="楼层名称：" prop="name">
            <el-input v-model.trim="experimentInfo.name" placeholder="请输入楼层名称" style="width: 500px">
              <div slot="suffix" v-if="experimentInfo.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ experimentInfo.name.length }} / 20
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label-width="100px" label="英文名称：" prop="englishName">
            <el-input v-model.trim="experimentInfo.englishName" placeholder="请输入英文名称" style="width: 500px">
              <div slot="suffix" v-if="experimentInfo.englishName">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ experimentInfo.englishName.length }} / 70
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label-width="120px" label="头图：" prop="experiment_big_bg">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="experimentInfo.experiment_big_bg"
                                    uploader-id="experiment_big_bg"
                                    uploader-title="头图"
                                    :uploader-size="[200,100]" :pixel-limit="[1920,480]"
                                    :size-limit="3072"
                                    @uploadSuccess="data=>fileUpload(data,experimentInfo)"
                                    @afterDelete="data=>fileDelete(data,experimentInfo)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label-width="120px" label="副图(蓝色)：" prop="experiment_second_bg_blue">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="experimentInfo.experiment_second_bg_blue"
                                    uploader-id="experiment_second_bg_blue"
                                    uploader-title="副图(蓝色)"
                                    :uploader-size="[200,100]" :pixel-limit="[1300,340]"
                                    :size-limit="3072"
                                    @uploadSuccess="data=>fileUpload(data,experimentInfo)"
                                    @afterDelete="data=>fileDelete(data,experimentInfo)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label-width="120px" label="副图(红色)：" prop="experiment_second_bg_red">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="experimentInfo.experiment_second_bg_red"
                                    uploader-id="experiment_second_bg_red"
                                    uploader-title="副图(红色)"
                                    :uploader-size="[200,100]" :pixel-limit="[1300,340]"
                                    :size-limit="3072"
                                    @uploadSuccess="data=>fileUpload(data,experimentInfo)"
                                    @afterDelete="data=>fileDelete(data,experimentInfo)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label-width="100px" label="楼层描述：" prop="description">
            <el-input type="textarea" :rows="5" v-model="experimentInfo.description" placeholder="请输入楼层描述"
                      maxlength="100" show-word-limit
                      style="width: 500px">
            </el-input>
          </el-form-item>
          <el-form-item label-width="100px" label="实验库：" prop="experimentList"  >
            <el-card class="box-card">
              <div class="container">
                <!--设置表格-->
                <product-experiment-table ref="experimentInfo_experimentList"
                                          @onClickExperimentMoveUpBtn="v=>experimentMethods().clickExperimentMoveUpBtn_list(v)"
                                          @onClickExperimentMoveDownBtn="v=>experimentMethods().clickExperimentMoveDownBtn_list(v)"
                                          @onClickExperimentDeleteBtn="v=>experimentMethods().clickExperimentDeleteBtn_list(v)"
                                          @onImgUploadSuccess="v=>experimentMethods().onImgUploadSuccess_list(v)"
                                          @onImgDelete="v=>experimentMethods().onImgDelete_list(v)"
                                          :list="experimentInfo.experimentList"></product-experiment-table>
                <el-button size="small" style="margin-top: 10px"
                           @click="experimentMethods().clickExperimentAddBtn_list()">新建实验
                </el-button>
                <!--                <el-button size="small" style="margin-top: 10px"-->
                <!--                           @click="experimentMethods().clickExperimentList_Input()">获取有分类有系列列表的所有实验-->
                <!--                </el-button>-->
              </div>
            </el-card>
          </el-form-item>
          <el-form-item label-width="100px" label="产品设置：" prop="lists" class="lists-form-item">
            <!--切换按钮-->
            <div class="flex flex-start">
              <span class="flex flex-start">
                <span style="margin-right: 5px">分类展示</span>
                <el-switch
                  v-model="experimentInfo.showCategory"
                  active-color="#13ce66"
                  inactive-color="#ff4949">
              </el-switch>
              </span>
              <span class="flex flex-start" style="margin-left: 20px">
                <span style="margin-right: 5px">产品系列</span>
                <el-switch
                  v-model="experimentInfo.showSeries"
                  active-color="#13ce66"
                  inactive-color="#ff4949">
              </el-switch>
              </span>
            </div>
            <!--设置列表-->
            <div class="lists">
              <!--有分类，有系列-->
              <div class="list" v-if="experimentShowModel===0">
                <el-card class="box-card" v-for="(categoryItem,categoryIndex) in experimentInfo.list[0]"
                         style="margin-bottom: 20px">
                  <div class="flex flex-start" style="margin-bottom: 15px">
                    <span>分类名称：</span>
                    <input class="fake-el-input" type="text" v-model="categoryItem.name"
                           style="width: 300px;margin-right: 15px">
                    <el-button type="text" @click="experimentMethods().clickCategoryDeleteBtn(categoryIndex)"
                               v-if="experimentInfo.list[0].length>1">删除分类
                    </el-button>
                    <el-button type="text" :disabled="categoryIndex===0"
                               @click="experimentMethods().clickCategoryUpBtn(categoryIndex)"
                               v-if="experimentInfo.list[0].length>1">上移
                    </el-button>
                    <el-button type="text" :disabled="categoryIndex===experimentInfo.list[0].length-1"
                               @click="experimentMethods().clickCategoryDownBtn(categoryIndex)"
                               v-if="experimentInfo.list[0].length>1">下移
                    </el-button>
                  </div>
                  <el-divider></el-divider>
                  <div class="container">
                    <div class="series-li" v-for="(seriesItem,seriesIndex) in categoryItem.series"
                         style="margin-top: 15px">
                      <!--系列标题设置-->
                      <div class="flex flex-start" style="margin-bottom: 15px">
                        <span style="margin-right: 10px">系列{{ seriesIndex + 1 }}名称:</span>
                        <input class="fake-el-input" type="text" v-model="seriesItem.name"
                               style="width: 300px;margin-right: 15px">
                        <el-button type="text"
                                   @click="experimentMethods().clickSeriesDeleteBtn(seriesIndex,categoryIndex)"
                                   v-if="categoryItem.series.length>1">删除系列
                        </el-button>
                        <el-button type="text" :disabled="seriesIndex===0"
                                   @click="experimentMethods().clickSeriesUpBtn(seriesIndex,categoryIndex)"
                                   v-if="categoryItem.series.length>1">上移
                        </el-button>
                        <el-button type="text" :disabled="seriesIndex===categoryItem.series.length-1"
                                   @click="experimentMethods().clickSeriesDownBtn(seriesIndex,categoryIndex)"
                                   v-if="categoryItem.series.length>1">下移
                        </el-button>
                      </div>
                      <!--设置表格-->
                      <product-experiment-table ref="experimentInfo_experimentList_0"
                                                @onClickExperimentMoveUpBtn="v=>experimentMethods().clickExperimentMoveUpBtn(categoryIndex,seriesIndex,v)"
                                                @onClickExperimentMoveDownBtn="v=>experimentMethods().clickExperimentMoveDownBtn(categoryIndex,seriesIndex,v)"
                                                @onClickExperimentDeleteBtn="v=>experimentMethods().clickExperimentDeleteBtn(categoryIndex,seriesIndex,v)"
                                                @onImgUploadSuccess="v=>experimentMethods().onImgUploadSuccess(categoryIndex,seriesIndex,v)"
                                                @onImgDelete="v=>experimentMethods().onImgDelete(categoryIndex,seriesIndex,v)"
                                                :list="seriesItem.experiments"></product-experiment-table>
                      <!--                      <el-button size="small" style="margin-top: 10px"-->
                      <!--                                 @click="experimentMethods().clickExperimentAddBtn(seriesIndex,categoryIndex)">新建实验-->
                      <!--                      </el-button>-->
                      <el-button size="small" style="margin-top: 10px"
                                 @click="experimentMethods().openChooseDialog(seriesIndex,categoryIndex)">选择实验
                      </el-button>
                    </div>
                    <div>
                      <el-button style="margin-top: 15px"
                                 @click="experimentMethods().clickSeriesAddBtn(categoryIndex)">
                        新建系列
                      </el-button>
                    </div>
                  </div>
                </el-card>
                <div>
                  <el-button style="margin-top: 15px" @click="experimentMethods().clickCategoryAddBtn()">新建分类
                  </el-button>
                </div>
              </div>
              <!--有分类，无系列-->
              <div class="list" v-if="experimentShowModel===1">
                <el-card class="box-card" v-for="(categoryItem,categoryIndex) in experimentInfo.list[1]"
                         style="margin-bottom: 20px">
                  <div class="flex flex-start" style="margin-bottom: 15px">
                    <span @click="experimentMethods().clickCategoryAddBtn()">分类名称：</span>
                    <!--                    <el-input style="width: 200px;margin-right: 10px" v-model="categoryItem.name"></el-input>-->
                    <input class="fake-el-input" type="text" v-model="categoryItem.name"
                           style="width: 300px;margin-right: 15px">
                    <el-button type="text" @click="experimentMethods().clickCategoryDeleteBtn(categoryIndex)"
                               v-if="experimentInfo.list[1].length>1">删除分类
                    </el-button>
                    <el-button type="text" :disabled="categoryIndex===0"
                               @click="experimentMethods().clickCategoryUpBtn(categoryIndex)"
                               v-if="experimentInfo.list[1].length>1">上移
                    </el-button>
                    <el-button type="text" :disabled="categoryIndex===experimentInfo.list[1].length-1"
                               @click="experimentMethods().clickCategoryDownBtn(categoryIndex)"
                               v-if="experimentInfo.list[1].length>1">下移
                    </el-button>
                  </div>
                  <div class="container">
                    <product-experiment-table ref="experimentInfo_experimentList_1"
                                              @onClickExperimentMoveUpBtn="v=>experimentMethods().clickExperimentMoveUpBtn(categoryIndex,null,v)"
                                              @onClickExperimentMoveDownBtn="v=>experimentMethods().clickExperimentMoveDownBtn(categoryIndex,null,v)"
                                              @onClickExperimentDeleteBtn="v=>experimentMethods().clickExperimentDeleteBtn(categoryIndex,null,v)"
                                              @onImgUploadSuccess="v=>experimentMethods().onImgUploadSuccess(categoryIndex,null,v)"
                                              @onImgDelete="v=>experimentMethods().onImgDelete(categoryIndex,null,v)"
                                              :list="categoryItem.experiments"></product-experiment-table>
                    <!--                    <el-button size="small" style="margin-top: 10px"-->
                    <!--                               @click="experimentMethods().clickExperimentAddBtn(null,categoryIndex)">新建实验-->
                    <!--                    </el-button>-->
                    <el-button size="small" style="margin-top: 10px"
                               @click="experimentMethods().openChooseDialog(null,categoryIndex)">选择实验
                    </el-button>
                  </div>
                </el-card>
                <a href="#" name="experimentInfo-2"></a>
                <div>
                  <el-button style="margin-top: 15px" @click="experimentMethods().clickCategoryAddBtn()">新建分类
                  </el-button>
                </div>
              </div>
              <!--无分类，有系列-->
              <div class="list" v-if="experimentShowModel===2">
                <el-card class="box-card" v-for="(seriesItem,seriesIndex) in experimentInfo.list[2]">
                  <div class="flex flex-start" style="margin-bottom: 15px">
                    <span>系列名称：</span>
                    <!--                    <el-input style="width: 200px;margin-right: 10px" v-model="seriesItem.name"></el-input>-->
                    <input class="fake-el-input" type="text" v-model="seriesItem.name"
                           style="width: 300px;margin-right: 15px">
                    <el-button type="text"
                               @click="experimentMethods().clickSeriesDeleteBtn(seriesIndex)"
                               v-if="experimentInfo.list[2].length>1">删除系列
                    </el-button>
                    <el-button type="text" :disabled="seriesIndex===0"
                               @click="experimentMethods().clickSeriesUpBtn(seriesIndex,0)"
                               v-if="experimentInfo.list[2].length>1">上移
                    </el-button>
                    <el-button type="text" :disabled="seriesIndex===experimentInfo.list[2].length-1"
                               @click="experimentMethods().clickSeriesDownBtn(seriesIndex,0)"
                               v-if="experimentInfo.list[2].length>1">下移
                    </el-button>
                  </div>
                  <div class="container">
                    <!--设置表格-->
                    <product-experiment-table ref="experimentInfo_experimentList_2"
                                              @onClickExperimentMoveUpBtn="v=>experimentMethods().clickExperimentMoveUpBtn(null,seriesIndex,v)"
                                              @onClickExperimentMoveDownBtn="v=>experimentMethods().clickExperimentMoveDownBtn(null,seriesIndex,v)"
                                              @onClickExperimentDeleteBtn="v=>experimentMethods().clickExperimentDeleteBtn(null,seriesIndex,v)"
                                              @onImgUploadSuccess="v=>experimentMethods().onImgUploadSuccess(null,seriesIndex,v)"
                                              @onImgDelete="v=>experimentMethods().onImgDelete(null,seriesIndex,v)"
                                              :list="seriesItem.experiments"></product-experiment-table>
                    <!--                    <el-button size="small" style="margin-top: 10px"-->
                    <!--                               @click="experimentMethods().clickExperimentAddBtn(seriesIndex)">新建实验-->
                    <!--                    </el-button>-->
                    <el-button size="small" style="margin-top: 10px"
                               @click="experimentMethods().openChooseDialog(seriesIndex,null)">选择实验
                    </el-button>
                    <el-divider></el-divider>
                  </div>
                </el-card>
                <a href="#" name="experimentInfo-3"></a>
                <div>
                  <el-button style="margin-top: 15px" @click="experimentMethods().clickSeriesAddBtn()">新建系列
                  </el-button>
                </div>
              </div>
              <!--无分类，无系列-->
              <div class="list" v-if="experimentShowModel===3">
                <el-card class="box-card">
                  <div class="container">
                    <!--设置表格-->
                    <product-experiment-table ref="experimentInfo_experimentList_3"
                                              @onClickExperimentMoveUpBtn="v=>experimentMethods().clickExperimentMoveUpBtn(null,null,v)"
                                              @onClickExperimentMoveDownBtn="v=>experimentMethods().clickExperimentMoveDownBtn(null,null,v)"
                                              @onClickExperimentDeleteBtn="v=>experimentMethods().clickExperimentDeleteBtn(null,null,v)"
                                              @onImgUploadSuccess="v=>experimentMethods().onImgUploadSuccess(null,null,v)"
                                              @onImgDelete="v=>experimentMethods().onImgDelete(null,null,v)"
                                              :list="experimentInfo.list[3]"></product-experiment-table>
                    <!--                    <el-button size="small" style="margin-top: 10px"-->
                    <!--                               @click="experimentMethods().clickExperimentAddBtn()">新建实验-->
                    <!--                    </el-button>-->
                    <el-button size="small" style="margin-top: 10px"
                               @click="experimentMethods().openChooseDialog(null,null)">选择实验
                    </el-button>
                  </div>
                </el-card>
                <a href="#" name="experimentInfo-4"></a>
              </div>
            </div>
            <a href="#" name="experimentInfoErr"></a>
          </el-form-item>
          <!--详情弹窗-选择新增的实验-->
          <el-dialog
            title="从实验库中选择实验"
            :visible.sync="chooseExperiment.dialog"
            width="670px"
            center
            v-el-drag-dialog>
            <div class="dialog-container">
              <el-form label-width="120px">
                <el-form-item label="选择实验:" prop="schoolName">
                  <el-select v-model="chooseExperiment.chooseId">
                    <el-option v-for="item in chooseExperiment.list" :label="item.label" :value="item.value"
                               :key="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="chooseExperiment.dialog=false">取 消</el-button>
        <el-button type="success" @click="experimentMethods().clickChooseExperimentBtn()">提 交</el-button>
      </span>
          </el-dialog>
        </el-form>
      </div>
    </el-card>
    <!--全景设置-->
    <a href="#" name="vrInfo"></a>
    <el-card style="margin-bottom: 20px" id="vrInfo">
      <div slot="header" class="clearfix">
        <span>VR数字展馆</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="vrInfo.cardShow=!vrInfo.cardShow">
          <i class="el-icon-arrow-up" v-show="vrInfo.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!vrInfo.cardShow"></i>
          {{ vrInfo.cardShow ? '收起' : '展开' }}
        </el-button>
        <el-button v-if="false" style="float:right" size="small" @click="clickSaveBtn(['vrInfo'])">保存</el-button>
      </div>
      <div class="card-container" v-show="vrInfo.cardShow">
        <el-form ref="vrInfo" :rules="vrInfo.formRules" :model="vrInfo">
          <el-form-item label-width="100px" label="在官网显示：">
            <el-switch @change="v=>onFloorChangeShow(v,vrInfo)"
                       v-model="vrInfo.show">
            </el-switch>
          </el-form-item>
          <el-form-item label-width="100px" label="楼层名称：" prop="name">
            <el-input v-model.trim="vrInfo.name" placeholder="请输入楼层名称" style="width: 500px">
              <div slot="suffix" v-if="vrInfo.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ vrInfo.name.length }} / 20
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label-width="100px" label="英文名称：" prop="englishName">
            <el-input v-model.trim="vrInfo.englishName" placeholder="请输入英文名称" style="width: 500px">
              <div slot="suffix" v-if="vrInfo.englishName">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ vrInfo.englishName.length }} / 70
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label-width="100px" label="楼层描述：" prop="description">
            <el-input type="textarea" :rows="5" v-model="vrInfo.description" placeholder="请输入楼层描述" maxlength="100"
                      show-word-limit
                      style="width: 500px">
            </el-input>
          </el-form-item>
          <el-form-item label-width="100px" label="头图：" prop="vr_big_bg">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="vrInfo.vr_big_bg"
                                    uploader-id="vr_big_bg"
                                    uploader-title="头图"
                                    :uploader-size="[200,100]" :pixel-limit="[1920,480]"
                                    :size-limit="3072"
                                    @uploadSuccess="data=>fileUpload(data,vrInfo)"
                                    @afterDelete="data=>fileDelete(data,vrInfo)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label-width="100px" label="副图(蓝色)：" prop="vr_second_bg_blue">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="vrInfo.vr_second_bg_blue"
                                    uploader-id="vr_second_bg_blue"
                                    uploader-title="副图(蓝色)"
                                    :uploader-size="[200,100]" :pixel-limit="[1300,340]"
                                    :size-limit="3072"
                                    @uploadSuccess="data=>fileUpload(data,vrInfo)"
                                    @afterDelete="data=>fileDelete(data,vrInfo)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label-width="100px" label="副图(红色)：" prop="vr_second_bg_red">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="vrInfo.vr_second_bg_red"
                                    uploader-id="vr_second_bg_red"
                                    uploader-title="副图(红色)"
                                    :uploader-size="[200,100]" :pixel-limit="[1300,340]"
                                    :size-limit="3072"
                                    @uploadSuccess="data=>fileUpload(data,vrInfo)"
                                    @afterDelete="data=>fileDelete(data,vrInfo)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label-width="100px" label="产品设置：" prop="lists" class="lists-form-item">
            <!--切换按钮-->
            <div class="flex flex-start">
              <span class="flex flex-start" style="margin-left: 20px">
                <span style="margin-right: 5px">产品系列</span>
                <el-switch
                  v-model="vrInfo.showSeries"
                  active-color="#13ce66"
                  inactive-color="#ff4949">
              </el-switch>
              </span>
            </div>
            <div class="lists">
              <!--有系列-->
              <div class="list" v-show="vrShowModel===0">
                <el-card class="box-card" v-for="(seriesItem,seriesIndex) in vrInfo.list[0]"
                         style="margin-bottom: 20px">
                  <div class="flex flex-start" style="margin-bottom: 15px">
                    <span>系列{{ seriesIndex + 1 }}名称：</span>
                    <!--                    <el-input style="width: 200px;margin-right: 10px" v-model="seriesItem.name"></el-input>-->
                    <input class="fake-el-input" type="text" v-model="seriesItem.name"
                           style="width: 300px;margin-right: 15px">
                    <el-button type="text"
                               @click="vrMethods().clickSeriesDeleteBtn(seriesIndex)" v-if="vrInfo.list[0].length>1">
                      删除系列
                    </el-button>
                  </div>
                  <div class="container">
                    <!--设置表格-->
                    <product-vr-table ref="vrInfo_list_0"
                                      @onClickVrMoveUpBtn="v=>vrMethods().clickVrMoveUpBtn(null,seriesIndex,v)"
                                      @onClickVrMoveDownBtn="v=>vrMethods().clickVrMoveDownBtn(null,seriesIndex,v)"
                                      @onClickVrDeleteBtn="v=>vrMethods().clickVrDeleteBtn(null,seriesIndex,v)"
                                      @onImgUploadSuccess="v=>vrMethods().onImgUploadSuccess(null,seriesIndex,v)"
                                      @onImgDelete="v=>vrMethods().onImgDelete(null,seriesIndex,v)"
                                      :list="seriesItem.vrs"></product-vr-table>
                    <el-button style="margin-top: 10px" size="small"
                               @click="vrMethods().clickVrAddBtn(seriesIndex)">新建VR
                    </el-button>
                  </div>
                </el-card>
                <div>
                  <el-button style="margin-top: 15px" @click="vrMethods().clickSeriesAddBtn()">新建系列
                  </el-button>
                </div>
              </div>
              <!--无系列-->
              <div class="list" v-show="vrShowModel===1">
                <el-card class="box-card">
                  <div class="container">
                    <!--设置表格-->
                    <product-vr-table ref="vrInfo_list_1"
                                      @onClickVrMoveUpBtn="v=>vrMethods().clickVrMoveUpBtn(null,null,v)"
                                      @onClickVrMoveDownBtn="v=>vrMethods().clickVrMoveDownBtn(null,null,v)"
                                      @onClickVrDeleteBtn="v=>vrMethods().clickVrDeleteBtn(null,null,v)"
                                      @onImgUploadSuccess="v=>vrMethods().onImgUploadSuccess(null,null,v)"
                                      @onImgDelete="v=>vrMethods().onImgDelete(null,null,v)"
                                      :list="vrInfo.list[1]"></product-vr-table>
                    <el-button size="small" style="margin-top: 10px"
                               @click="vrMethods().clickVrAddBtn()">新建VR
                    </el-button>
                  </div>
                </el-card>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-card >
    <!--考试系统设置-->
    <a href="#" name="examInfo"></a>
    <el-card style="margin-bottom: 20px" id="examInfo">
      <div slot="header" class="clearfix">
        <span>固定图片楼层</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="examInfo.cardShow=!examInfo.cardShow">
          <i class="el-icon-arrow-up" v-show="examInfo.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!examInfo.cardShow"></i>
          {{ examInfo.cardShow ? '收起' : '展开' }}
        </el-button>
        <el-button v-if="false" style="float:right" size="small" @click="clickSaveBtn(['examInfo'])">保存</el-button>
      </div>
      <div class="card-container" v-show="examInfo.cardShow">
        <el-form ref="examInfo" :model="examInfo" :rules="examInfo.formRules">
          <el-form-item label-width="100px" label="在官网显示：">
            <el-switch @change="v=>onFloorChangeShow(v,examInfo)"
                       v-model="examInfo.show">
            </el-switch>
          </el-form-item>
          <el-form-item label-width="100px" label="楼层名称：" prop="name">
            <el-input v-model.trim="examInfo.name" placeholder="请输入楼层名称" style="width: 500px">
              <div slot="suffix" v-if="examInfo.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ examInfo.name.length }} / 20
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label-width="100px" label="英文名称：" prop="englishName">
            <el-input v-model.trim="examInfo.englishName" placeholder="请输入英文名称" style="width: 500px">
              <div slot="suffix" v-if="examInfo.englishName">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ examInfo.englishName.length }} / 75
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label-width="100px" label="介绍文字：" prop="des">
            <el-input type="textarea" :rows="5" v-model="examInfo.des" placeholder="请输入介绍文字"
                      maxlength="300" show-word-limit
                      style="width: 500px">
            </el-input>
          </el-form-item>
          <el-form-item label-width="100px" label="介绍大图：" prop="exam_big_img">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="examInfo.exam_big_img"
                                    uploader-id="exam_big_img"
                                    uploader-title="大图"
                                    :uploader-size="[200,100]" :pixel-limit="[1280,700]"
                                    :size-limit="2048"
                                    @uploadSuccess="data=>examMethods().fileUploadSuccess(data)"
                                    @afterDelete="data=>examMethods().fileDelete(data)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <!--技术支持设置-->
    <a href="#" name="teachInfo"></a>
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>横屏图片楼层</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="techInfo.cardShow=!techInfo.cardShow">
          <i class="el-icon-arrow-up" v-show="techInfo.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!techInfo.cardShow"></i>
          {{ techInfo.cardShow ? '收起' : '展开' }}
        </el-button>
        <el-button v-if="false" style="float:right" size="small" @click="clickSaveBtn(['techInfo'])">保存</el-button>
      </div>
      <div class="card-container" v-show="techInfo.cardShow">
        <el-form ref="techInfo" :rules="techInfo.formRules" :model="techInfo" label-width="120px">
          <el-form-item label="在官网显示：">
            <el-switch @change="v=>onFloorChangeShow(v,techInfo)"
                       v-model="techInfo.show">
            </el-switch>
          </el-form-item>
          <el-form-item label="楼层名称：" prop="name">
            <el-input v-model.trim="techInfo.name" placeholder="请输入楼层名称" style="width: 500px">
              <div slot="suffix" v-if="techInfo.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ techInfo.name.length }} / 20
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="英文名称：" prop="englishName">
            <el-input v-model.trim="techInfo.englishName" placeholder="请输入英文名称" style="width: 500px">
              <div slot="suffix" v-if="techInfo.englishName">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ techInfo.englishName.length }} / 70
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="子楼层列表：" prop="lists" class="floorType2">
            <div class="list">
              <el-card class="box-card">
                <div class="container">
                  <!--设置表格-->
                  <product-tech-table ref="techInfoList"
                                      @onClickMoveUpBtn="v=>techMethods().clickMoveUpBtn(v)"
                                      @onClickMoveDownBtn="v=>techMethods().clickMoveDownBtn(v)"
                                      @onClickDeleteBtn="v=>techMethods().clickDeleteBtn(v)"
                                      @onImgUploadSuccess="v=>techMethods().onImgUploadSuccess(v)"
                                      @onImgDelete="v=>techMethods().onImgDelete(v)"
                                      :list="techInfo.list"></product-tech-table>
                  <el-divider></el-divider>
                  <div class="flex flex-center" style="margin-bottom: 15px">
                    <el-button type="default"
                               @click="techMethods().clickAddBtn()">新建子楼层
                    </el-button>
                  </div>
                </div>
              </el-card>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <!--自定义楼层列表-->
    <div class="floor-list">
      <a href="#" name="floorInfo"></a>
      <template v-for="(floorItem,floorIndex) in floorInfo.lists">
        <el-card style="margin-bottom: 20px" v-if="floorItem.type==='1'">
          <a href="#" :name="'floorInfo-'+floorIndex"></a>
          <div slot="header" class="clearfix">
            <span>固定图片楼层</span>
            <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                       @click="floorItem.cardShow=!floorItem.cardShow">
              <i class="el-icon-arrow-up" v-show="floorItem.cardShow"></i>
              <i class="el-icon-arrow-down" v-show="!floorItem.cardShow"></i>
              {{ floorItem.cardShow ? '收起' : '展开' }}
            </el-button>
            <el-button style="float:right" type="text" @click="floorMethods().clickDeleteFloor(floorIndex)">删除楼层
            </el-button>
          </div>
          <div class="card-container" v-show="floorItem.cardShow">
            <el-form ref="floors" :model="floorItem" :rules="floorInfo.type1Rules">
              <el-form-item label-width="100px" label="在官网显示：">
                <el-switch @change="v=>floorMethods().onFloorChangeShow(v,floorItem)"
                           v-model="floorItem.show">
                </el-switch>
              </el-form-item>
              <el-form-item label-width="100px" label="楼层名称：" prop="name">
                <el-input v-model="floorItem.name" placeholder="请输入楼层名称" style="width: 500px">
                  <div slot="suffix" v-if="floorItem.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ floorItem.name.length }} / 20
                  </span>
                </span>
                  </div>
                </el-input>
              </el-form-item>
              <el-form-item label-width="100px" label="英文名称：" prop="englishName">
                <el-input v-model="floorItem.englishName" placeholder="请输入英文名称" style="width: 500px">
                  <div slot="suffix" v-if="floorItem.englishName">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ floorItem.englishName.length }} / 70
                  </span>
                </span>
                  </div>
                </el-input>
              </el-form-item>
              <el-form-item label-width="100px" label="介绍大图：" prop="bigImg">
                <div class="flex flex-start">
                  <erp-uploader-one-pic style="margin-right: 30px" :img-in="floorItem.bigImg"
                                        :uploader-id="'bigImg__'+floorIndex"
                                        uploader-title="大图"
                                        :uploader-size="[200,100]" :pixel-limit="[1280,700]"
                                        :size-limit="2048"
                                        @uploadSuccess="data=>floorMethods().onFloor1ImgUploadSuccess(floorIndex,data)"
                                        @afterDelete="data=>floorMethods().onFloor1ImgDeleteSuccess(floorIndex,data)"></erp-uploader-one-pic>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
        <el-card style="margin-bottom: 20px" v-if="floorItem.type==='2'">
          <a href="#" :name="'floorInfo-'+floorIndex"></a>
          <div slot="header" class="clearfix">
            <span>横屏图片楼层</span>
            <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                       @click="floorItem.cardShow=!floorItem.cardShow">
              <i class="el-icon-arrow-up" v-show="floorItem.cardShow"></i>
              <i class="el-icon-arrow-down" v-show="!floorItem.cardShow"></i>
              {{ floorItem.cardShow ? '收起' : '展开' }}
            </el-button>
            <el-button style="float:right" type="text" @click="floorMethods().clickDeleteFloor(floorIndex)">删除楼层
            </el-button>
          </div>
          <div class="card-container" v-show="floorItem.cardShow">
            <el-form ref="floors" :rules="floorInfo.type2Rules" :model="floorItem" label-width="120px">
              <el-form-item label="在官网显示：">
                <el-switch @change="v=>floorMethods().onFloorChangeShow(v,floorItem)"
                           v-model="floorItem.show">
                </el-switch>
              </el-form-item>
              <el-form-item label="楼层名称：" prop="name">
                <el-input v-model="floorItem.name" placeholder="请输入楼层名称" style="width: 500px">
                  <div slot="suffix" v-if="floorItem.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ floorItem.name.length }} / 20
                  </span>
                </span>
                  </div>
                </el-input>
              </el-form-item>
              <el-form-item label="英文名称：" prop="englishName">
                <el-input v-model="floorItem.englishName" placeholder="请输入英文名称" style="width: 500px">
                  <div slot="suffix" v-if="floorItem.englishName">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ floorItem.englishName.length }} / 70
                  </span>
                </span>
                  </div>
                </el-input>
              </el-form-item>
              <el-form-item label="子楼层列表：" prop="list" class="floorType2">
                <div class="list">
                  <el-card class="box-card">
                    <div class="container">
                      <!--设置表格-->
                      <product-tech-table ref="floor2_list"
                                          @onClickMoveUpBtn="v=>floorMethods().clickMoveUpBtn(v,floorIndex)"
                                          @onClickMoveDownBtn="v=>floorMethods().clickMoveDownBtn(v,floorIndex)"
                                          @onClickDeleteBtn="v=>floorMethods().clickDeleteBtn(v,floorIndex)"
                                          @onImgUploadSuccess="v=>floorMethods().onImgUploadSuccess(v,floorIndex)"
                                          @onImgDelete="v=>floorMethods().onImgDelete(v,floorIndex)"
                                          :list="floorItem.list"></product-tech-table>
                      <el-divider></el-divider>
                      <div class="flex flex-center" style="margin-bottom: 15px">
                        <el-button type="default"
                                   @click="floorMethods().clickAddBtn(floorIndex)">新建子楼层
                        </el-button>
                      </div>
                    </div>
                  </el-card>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </template>
      <div class="buttons flex flex-center">
        <el-button type="success" @click="floorMethods().clickAddFloorBtn()">添加楼层</el-button>
      </div>
    </div>
    <!--按钮组-->
    <div class="bottom-button-container flex flex-center">
      <el-button type="primary" @click="clickSaveBtn()">保 存</el-button>
      <el-button @click="clickCancelBtn">取 消</el-button>
    </div>
  </div>
</template>

<script>
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import erpUploaderOneVideo from "@/views/components/erpUploaderOneVideo";
import productExperimentTable from "./components/productExperimentTable"
import productTechTable from "./components/productTechTable"
import productVrTable from "./components/productVrTable"
import {msg_confirm, msg_confirm_choose, msg_err, msg_success} from "@/utils/ele_component";
import {validateMaxLength} from "@/utils/validate";
import {ConfigModel} from "@/model/erp/ConfigModel";
import {CONFIG_NAME_OFFICIAL_WEB} from "@/model/ConfigModel";
import {VEAModel} from "@/model/VEAModel";
import $ from 'jquery'
import {findObjectArrSomeObjFirstOne, randomNumber} from "@/utils/common";
import {CommonModel} from "@/model/CommonModel";
import elDragDialog from "@/directive/el-drag-dialog";

export default {
  components: {erpUploaderOneVideo, erpUploaderOnePic, productExperimentTable, productVrTable, productTechTable},
  name: "productManage",
  directives: {
    elDragDialog
  },
  computed: {
    // 计算属性-实验设置-分类和系列显示改变后，显示的列表值
    experimentShowModel() {
      let category = Number(this.experimentInfo.showCategory);
      let series = Number(this.experimentInfo.showSeries)
      if (category === 1 && series === 1) {
        return 0;
      }
      if (category === 1 && series === 0) {
        return 1
      }
      if (category === 0 && series === 1) {
        return 2
      }
      if (category === 0 && series === 0) {
        return 3
      }
    },
    // 计算属性-VR设置-系列显示改变后，显示的列表值
    vrShowModel() {
      let series = Number(this.vrInfo.showSeries)
      if (series === 0) {
        return 1;
      }
      if (series === 1) {
        return 0
      }
    }
  },
  data() {
    // 验证-产品设置-实验库
    const validate_experimentInfo_list = (rule, value, callback) => {
      if (!this.$refs.experimentInfo_experimentList.validateForm()) {
        callback(new Error("请将缺失项或错误项修改正确"))
      }
      callback()
    }
    // 验证-产品设置-列表
    const validate_experimentInfo_lists = (rule, value, callback) => {
      let refIndex = 0
      // 有分类，有系列
      if (this.experimentShowModel === 0) {
        this.experimentInfo.list[0].forEach((categoryItem, categoryIndex) => {
          // 检测是否填写分类名称
          if (!categoryItem.name) {
            this.experimentMethods().clearListErrTip()
            callback(new Error(`第${categoryIndex + 1}个分类未设置分类名称`))
          }
          categoryItem.series.forEach((seriesItem, seriesIndex) => {
            if (!seriesItem.name) {
              this.experimentMethods().clearListErrTip()
              callback(new Error(`第${categoryIndex + 1}个分类,第${seriesIndex + 1}个系列未设置系列名称`))
            }
            if (!this.$refs["experimentInfo_experimentList_0"][refIndex].validateForm()) {
              callback(new Error("请将缺失项或错误项修改正确"))
            }
            refIndex += 1
            // seriesItem.experiments.forEach((experiment, experimentIndex) => {
            //   this.experimentMethods().validateExperiment(experiment, experimentIndex, callback, categoryIndex, seriesIndex)
            // })
          })
        })

      }
      // 有分类，无系列
      if (this.experimentShowModel === 1) {
        this.experimentInfo.list[1].forEach((categoryItem, categoryIndex) => {
          // 检测是否填写分类名称
          if (!categoryItem.name) {
            this.experimentMethods().clearListErrTip()
            callback(new Error(`第${categoryIndex + 1}个分类未设置分类名称`))
          }
          if (!this.$refs["experimentInfo_experimentList_1"][refIndex].validateForm()) {
            callback(new Error("请将缺失项或错误项修改正确"))
          }
          refIndex += 1
          // categoryItem.experiments.forEach((experiment, experimentIndex) => {
          //   this.experimentMethods().validateExperiment(experiment, experimentIndex, callback, categoryIndex, null)
          // })
        })
      }
      // 无分类，有系列
      if (this.experimentShowModel === 2) {
        // 检测是否填写分类名称
        this.experimentInfo.list[2].forEach((seriesItem, seriesIndex) => {
          if (!seriesItem.name) {
            this.experimentMethods().clearListErrTip()
            callback(new Error(`第${seriesIndex + 1}个系列未设置系列名称`))
          }
          if (!this.$refs["experimentInfo_experimentList_2"][refIndex].validateForm()) {
            callback(new Error("请将缺失项或错误项修改正确"))
          }
          refIndex += 1
          // seriesItem.experiments.forEach((experiment, experimentIndex) => {
          //   this.experimentMethods().validateExperiment(experiment, experimentIndex, callback, null, seriesIndex)
          // })
        })
      }
      // 无分类，无系列
      if (this.experimentShowModel === 3) {
        if (!this.$refs["experimentInfo_experimentList_3"].validateForm()) {
          callback(new Error("请将缺失项或错误项修改正确"))
        }
        // this.experimentInfo.list[3].forEach((experiment, experimentIndex) => {
        //   this.experimentMethods().validateExperiment(experiment, experimentIndex, callback, null, null)
        // })
      }
      callback();
    }
    // 验证-全景设置-列表
    const validate_vrInfo_lists = (rule, value, callback) => {
      // 有系列
      if (this.vrShowModel === 0) {
        // 检测是否填写分类名称
        this.vrInfo.list[0].forEach((seriesItem, seriesIndex) => {
          if (!seriesItem.name) {
            this.vrMethods().clearListErrTip()
            callback(new Error(`第${seriesIndex + 1}个系列未设置系列名称`))
          }
          if (!this.$refs["vrInfo_list_0"][seriesIndex].validateForm()) {
            callback(new Error("请将缺失项或错误项修改正确"))
          }
          // seriesItem.vrs.forEach((vr, vrIndex) => {
          //   this.vrMethods().validateVr(vr, vrIndex, callback, seriesIndex)
          // })
        })
      }
      // 无系列
      if (this.vrShowModel === 1) {
        if (!this.$refs["vrInfo_list_1"].validateForm()) {
          callback(new Error("请将缺失项或错误项修改正确"))
        }
        // this.vrInfo.list[1].forEach((vr, vrIndex) => {
        //   this.vrMethods().validateVr(vr, vrIndex, callback, null, null)
        // })
      }
      callback();
    }
    // 验证-技术支持设置-列表
    const validate_techInfo_lists = (rule, value, callback) => {
      // this.techInfo.list.forEach((tech, techIndex) => {
      //   this.techMethods().validate(tech, techIndex, callback)
      // })
      if (!this.$refs["techInfoList"].validateForm()) {
        callback(new Error("请将缺失项或错误项修改正确"))
      }
      callback();
    }
    // 验证-自定义楼层-横屏楼层-子楼层验证
    const validate_floor_type2_list = (rule, value, callback) => {
      value.forEach((li, index) => {
        if (!this.$refs["floor2_list"][index].validateForm()) {
          callback(new Error("请将缺失项或错误项修改正确"))
        }
        // this.floorMethods().validate(li, index, callback)
      })
      callback();
    }
    return {
      productManage: {},
      // 产品页设置
      pageInfo: {
        cardShow: true,
        colorType: "blue"
      },
      // 实验管理
      experimentInfo: {
        show: true,
        cardShow: true,
        name: "1",
        englishName: "2",
        description: "3",
        showCategory: true,
        showSeries: true,
        experiment_big_bg: "",
        experiment_second_bg_blue: "",
        experiment_second_bg_red: "",
        validateJump: false,
        experimentList: [],
        list: [
          // 有分类，有系列
          [
            {
              name: "",
              series: []
            }
          ],
          // 有分类，无系列
          [
            {
              name: "",
              experiments: []
            }
          ],
          // 无分类，有系列
          [
            {
              name: "",
              experiments: []
            }
          ],
          // 无分类，无系列
          [],
        ],
        formRules: {
          'name': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 20, "楼层名称"),
            trigger: 'blur'
          },
          'englishName': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 75, "英文名称"),
            trigger: 'blur'
          },
          'description': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 300, "楼层描述"),
            trigger: 'blur'
          },
          'lists': {
            required: false,
            validator: validate_experimentInfo_lists,
            trigger: 'change'
          },
          'experimentList': {
            required: false,
            validator: validate_experimentInfo_list,
            trigger: 'blur'
          },
          'experiment_big_bg': {
            required: true,
            message: "头图未设置",
            trigger: 'change'
          },
          'experiment_second_bg_blue': {
            required: true,
            message: "副图(蓝色)未设置",
            trigger: 'change'
          },
          'experiment_second_bg_red': {
            required: true,
            message: "副图(红色)未设置",
            trigger: 'change'
          }
        }
      },
      chooseExperiment: {
        dialog: false,
        list: [],
        chooseId: ""
      },
      // 全景管理
      vrInfo: {
        show: true,
        cardShow: true,
        name: "1",
        englishName: "2",
        description: "3",
        showSeries: true,
        vr_big_bg: "",
        vr_second_bg_blue: "",
        vr_second_bg_red: "",
        validateJump: false,
        list: [
          // 有系列
          [],
          // 无系列
          []
        ],
        formRules: {
          'name': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 20, "楼层名称"),
            trigger: 'blur'
          },
          'englishName': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 70, "英文名称"),
            trigger: 'blur'
          },
          'description': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 300, "楼层描述"),
            trigger: 'blur'
          },
          'lists': {
            required: true,
            validator: validate_vrInfo_lists,
            trigger: 'blur'
          },
          'vr_big_bg': {
            required: true,
            message: "头图未设置",
            trigger: 'change'
          },
          'vr_second_bg_blue': {
            required: true,
            message: "副图(蓝色)未设置",
            trigger: 'change'
          },
          'vr_second_bg_red': {
            required: true,
            message: "副图(红色)未设置",
            trigger: 'change'
          }
        }
      },
      // 考试系统管理
      examInfo: {
        show: true,
        cardShow: true,
        name: "1",
        englishName: "2",
        exam_big_img: "",
        des: "",
        formRules: {
          'name': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 20, "楼层名称"),
            trigger: 'blur'
          },
          'englishName': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 75, "英文名称"),
            trigger: 'blur'
          },
          'exam_big_img': {
            required: true,
            message: "介绍大图未设置",
            trigger: 'change'
          },
          'des': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 300, "介绍文字"),
            trigger: 'blur'
          },
        }
      },
      // 技术支持管理
      techInfo: {
        show: true,
        cardShow: true,
        name: "1",
        englishName: "2",
        list: [],
        formRules: {
          'name': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 20, "楼层名称"),
            trigger: 'blur'
          },
          'englishName': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 70, "英文名称"),
            trigger: 'blur'
          },
          'lists': {
            required: true,
            validator: validate_techInfo_lists,
            trigger: 'change'
          }
        }
      },
      // 自定义楼层
      floorInfo: {
        lists: [],
        type1Rules: {
          'name': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 20, "楼层名称"),
            trigger: 'blur'
          },
          'englishName': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 70, "英文名称"),
            trigger: 'blur'
          },
          'bigImg': {
            required: true,
            message: "介绍大图未设置",
            trigger: 'change'
          }
        },
        type2Rules: {
          'name': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 20, "楼层名称"),
            trigger: 'blur'
          },
          'englishName': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 70, "英文名称"),
            trigger: 'blur'
          },
          'list': {
            required: true,
            validator: validate_floor_type2_list,
            trigger: 'change'
          }
        }
      }
    }
  },
  mounted() {
    // 读取配置信息
    this.getConfig()
    // 底部按钮组大小监听
    VEAModel.bottomButtonContainerWidthChange()
  },
  methods: {
    // 通用-文件上传成功
    fileUpload(params, target) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
    },
    // 通用-文件删除
    fileDelete(params, target) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
    },
    // 通用-楼层在前台显示和隐藏
    async onFloorChangeShow(v, target) {
      if (v) {
        if (!await msg_confirm("是否确认开启该楼层，开启该楼层后，该楼层将在官网上可见")) {
          this.$set(target, "show", !v)
        }
      } else {
        if (!await msg_confirm("是否确认隐藏该楼层，隐藏该楼层后，该楼层将在官网上不可见")) {
          this.$set(target, "show", !v)
        }
      }
    },
    // 读取配置
    async getConfig() {
      let data = await ConfigModel.getEdit(CONFIG_NAME_OFFICIAL_WEB, "productManage")
      let productManage = JSON.parse(data)
      this.productManage = JSON.parse(data)
      // 设置各项配置
      this.pageInfo = Object.assign(this.pageInfo, productManage.pageInfo)
      this.experimentInfo = Object.assign(this.experimentInfo, productManage.experimentInfo)
      this.vrInfo = Object.assign(this.vrInfo, productManage.vrInfo)
      this.examInfo = Object.assign(this.examInfo, productManage.examInfo)
      this.techInfo = Object.assign(this.techInfo, productManage.techInfo)
      this.floorInfo = Object.assign(this.floorInfo, productManage.floorInfo)
    },
    // 点击保存方法
    async clickSaveBtn(floors) {
      if (!floors) {
        floors = ['experimentInfo', 'vrInfo', 'techInfo', 'examInfo', 'floorInfo']
      }
      // 每个项目的合法性检测
      this.errAJumped = false
      if (true) {
        let empty = true
        let deleteCount = [0, 0, 0, 0] // 记录每个类型删除的数量，以正确使用splice
        let productDeleteCount = 0
        // 虚拟仿真楼层检测
        if (floors.indexOf('experimentInfo') > -1) {
          // 如果分类或系列某个信息为空，保存时就删除
          let experimentInfoListTemp = JSON.parse(JSON.stringify(this.experimentInfo.list[this.experimentShowModel]))
          let productCheckKeyList = [
            'name',
            'subName',
            'description',
            'experimentDescription',
            'product_bg',
            'product_icon',
            'product_iconLeft',
            'product_info_bg',
            'product_video',
            'product_info_img_1',
            'product_info_img_2',
            'product_info_img_3',
            'product_info_img_4'
          ]
          experimentInfoListTemp.forEach((li, index) => {
            switch (this.experimentShowModel) {
              case 0:
                empty = true
                li.name !== '' ? empty = false : '' // 分类名称
                let seriesDeleteCount = 0
                let seriesNotEmptyCount = 0
                // 先判断有几个非空系列，非空实验
                li.series.forEach((seriesItem, seriesIndex) => {
                  let seriesEmpty = true
                  if (seriesItem.name) {
                    empty = false
                    seriesEmpty = false
                  }
                  li.series[seriesIndex].productNotEmptyCount = 0
                  seriesItem.experiments.forEach((experimentItem, experimentIndex) => {
                    let productEmpty = true
                    productCheckKeyList.forEach(key => {
                      if (experimentItem[key]) {
                        empty = false
                        seriesEmpty = false
                        productEmpty = false
                      }
                    })
                    if (!productEmpty) {
                      li.series[seriesIndex].productNotEmptyCount += 1
                    } else {
                      li.series[seriesIndex]["experiments"][experimentIndex]['empty'] = true
                    }
                  })
                  if (!seriesEmpty) {
                    seriesNotEmptyCount += 1
                  } else {
                    li.series[seriesIndex]['empty'] = true
                  }
                })
                // 再次遍历，删除多个系列中的空，多个实验中的空
                li.series.forEach((seriesItem, seriesIndex) => {
                  productDeleteCount = 0
                  seriesItem.experiments.forEach((experimentItem, experimentIndex) => {
                    if (experimentItem.empty && li.series[seriesIndex].productNotEmptyCount > 0) {
                      this.experimentInfo.list[0][index]['series'][seriesIndex]["experiments"].splice(experimentIndex - productDeleteCount, 1)
                      productDeleteCount += 1
                    }
                  })
                  if (seriesItem.empty && seriesNotEmptyCount > 0) {
                    this.experimentInfo.list[0][index]['series'].splice(seriesIndex - seriesDeleteCount, 1)
                    seriesDeleteCount += 1
                  }
                })
                if (empty) {
                  this.experimentInfo.list[this.experimentShowModel].splice(index - deleteCount[0], 1)
                  deleteCount[0] += 1
                }
                break
              case 1:
                empty = true
                li.name !== '' ? empty = false : ''// 分类名称
                li.productNotEmptyCount = 0
                li.experiments.forEach((experimentItem, experimentIndex) => {
                  let productEmpty = true
                  productCheckKeyList.forEach(key => {
                    if (experimentItem[key]) {
                      empty = false
                      productEmpty = false
                    }
                  })
                  if (!productEmpty) {
                    li.productNotEmptyCount += 1
                  } else {
                    li["experiments"][experimentIndex]['empty'] = true
                  }
                })
                productDeleteCount = 0
                console.log(li)
                li.experiments.forEach((experimentItem, experimentIndex) => {
                  if (experimentItem.empty && li.productNotEmptyCount > 0) {
                    this.experimentInfo.list[1][index]["experiments"].splice(experimentIndex - productDeleteCount, 1)
                    productDeleteCount += 1
                  }
                })
                if (empty) {
                  this.experimentInfo.list[this.experimentShowModel].splice(index - deleteCount[1], 1)
                  deleteCount[1] += 1
                }
                break
              case 2:
                empty = true
                li.name !== '' ? empty = false : ''// 系列名称
                li.productNotEmptyCount = 0
                li.experiments.forEach((experimentItem, experimentIndex) => {
                  let productEmpty = true
                  productCheckKeyList.forEach(key => {
                    if (experimentItem[key]) {
                      empty = false
                      productEmpty = false
                    }
                  })
                  if (!productEmpty) {
                    li.productNotEmptyCount += 1
                  } else {
                    li["experiments"][experimentIndex]['empty'] = true
                  }
                })
                productDeleteCount = 0
                li.experiments.forEach((experimentItem, experimentIndex) => {
                  if (experimentItem.empty && li.productNotEmptyCount > 0) {
                    this.experimentInfo.list[2][index]["experiments"].splice(experimentIndex - productDeleteCount, 1)
                    productDeleteCount += 1
                  }
                })
                if (empty) {
                  this.experimentInfo.list[this.experimentShowModel].splice(index - deleteCount[2], 1)
                  deleteCount[2] += 1
                }
                break
              case 3:
                empty = true
                productCheckKeyList.forEach(key => {
                  li[key] ? empty = false : ''
                })
                if (empty && this.experimentInfo.list[this.experimentShowModel].length > 1) {
                  this.experimentInfo.list[this.experimentShowModel].splice(index - deleteCount[3], 1)
                  deleteCount[3] += 1
                }
                break
            }
          })
          this.experimentInfo.validateJump = true
          this.$refs["experimentInfo"].validate(validate => {
            if (validate) {

            } else {
              if (!this.errAJumped) {
                this.experimentInfo.cardShow === false ? this.experimentInfo.cardShow = true : ""
                // location.hash = "experimentInfo";
                this.errAJumped = true
              }
            }
          })
        }
        // VR楼层检测
        if (floors.indexOf('vrInfo') > -1) {
          this.vrInfo.validateJump = true
          // 如果分类或系列某个信息为空，保存时就删除
          let vrInfoListTemp = JSON.parse(JSON.stringify(this.vrInfo.list[this.vrShowModel]))
          let vrCheckKeyList = [
            'name',
            'description',
            'link',
            'product_bg',
          ]
          empty = true
          deleteCount = [0, 0] // 记录每个类型删除的数量，以正确使用splice
          productDeleteCount = 0
          vrInfoListTemp.forEach((li, index) => {
            switch (this.vrShowModel) {
              case 0:
                empty = true
                li.name !== '' ? empty = false : ''// 系列名称
                li.productNotEmptyCount = 0
                li.vrs.forEach((experimentItem, experimentIndex) => {
                  let productEmpty = true
                  vrCheckKeyList.forEach(key => {
                    if (experimentItem[key]) {
                      empty = false
                      productEmpty = false
                    }
                  })
                  if (!productEmpty) {
                    li.productNotEmptyCount += 1
                  } else {
                    li["vrs"][experimentIndex]['empty'] = true
                  }
                })
                productDeleteCount = 0
                li.vrs.forEach((experimentItem, experimentIndex) => {
                  if (experimentItem.empty && li.productNotEmptyCount > 0) {
                    this.vrInfo.list[0][index]["vrs"].splice(experimentIndex - productDeleteCount, 1)
                    productDeleteCount += 1
                  }
                })
                if (empty) {
                  this.vrInfo.list[0].splice(index - deleteCount[0], 1)
                  deleteCount[0] += 1
                }
                break
              case 1:
                empty = true
                vrCheckKeyList.forEach(key => {
                  li[key] ? empty = false : ''
                })
                if (empty && this.vrInfo.list[1].length > 1) {
                  this.vrInfo.list[1].splice(index - deleteCount[1], 1)
                  deleteCount[1] += 1
                }
                break
            }
          })
          this.$refs["vrInfo"].validate(validate => {
            if (validate) {

            } else {
              if (!this.errAJumped) {
                this.vrInfo.cardShow === false ? this.vrInfo.cardShow = true : ""
                this.errAJumped = true
              }
            }
          })
        }
        // 考试系统楼层检测
        if (floors.indexOf('examInfo') > -1) {
          this.$refs["examInfo"].validate(validate => {
            if (validate) {

            } else {
              if (!this.errAJumped) {
                this.examInfo.cardShow === false ? this.examInfo.cardShow = true : ""
                // location.hash = "examInfo";
                this.errAJumped = true
              }
            }
          })
        }
        // 技术支持系统楼层检测
        if (floors.indexOf('techInfo') > -1) {
          // 删除空楼层
          let techInfoTempList = JSON.parse(JSON.stringify(this.techInfo.list))
          let techDeleteCount = 0
          let techCheckKeyList = [
            'name',
            'tech_bg',
            'child_floor_bg',
          ]
          techInfoTempList.forEach((li, index) => {
            empty = true
            techCheckKeyList.forEach(key => {
              li[key] ? empty = false : ''
            })
            if (empty && this.techInfo.list.length > 1) {
              this.techInfo.list.splice(index - techDeleteCount, 1)
              techDeleteCount += 1
            }
          })
          this.$refs["techInfo"].validate(validate => {
            if (validate) {

            } else {
              if (!this.errAJumped) {
                this.techInfo.cardShow === false ? this.techInfo.cardShow = true : ""
                // location.hash = "techInfo";
                this.errAJumped = true
              }
            }
          })
        }
        if (floors.indexOf('floorInfo') > -1) {
          // 自定义楼层检测
          let floor2CheckKeyList = [
            'name',
            'floor2_bg',
            'child_floor_bg',
          ]
          this.floorInfo.lists.forEach((floorItem, floorIndex) => {
            // 删除空的横屏楼层
            if (floorItem.type === '2') {
              let floor2InfoTempList = JSON.parse(JSON.stringify(this.floorInfo.lists[floorIndex].list))
              let floor2DeleteCount = 0
              floor2InfoTempList.forEach((li, index) => {
                empty = true
                floor2CheckKeyList.forEach(key => {
                  li[key] ? empty = false : ''
                })
                if (empty && this.floorInfo.lists[floorIndex].list.length > 1) {
                  this.floorInfo.lists[floorIndex].list.splice(index - floor2DeleteCount, 1)
                  floor2DeleteCount += 1
                }
              })
            }
            this.$refs['floors'][floorIndex].validate(validate => {
              if (validate) {

              } else {
                if (!this.errAJumped) {
                  floorItem.cardShow === false ? floorItem.cardShow = true : ""
                  // location.hash = "floorInfo-" + floorIndex;
                  this.errAJumped = true
                }
              }
            })
          })
        }
        // 全局滚动条跳转变量，用来标记是否已经跳转，实现滚动到第一个错误提示处功能
        if (this.errAJumped) { // 表单验证有误
          setTimeout(() => {
            // 跳转到第一个错误处
            let firstErrEl = $("body .el-form-item__error")
            let top = firstErrEl.offset().top - 200
            // 跳转到指定位置
            document.body.scrollTop = top;
            document.documentElement.scrollTop = top;
          }, 1000)
          return
        }
      }
      //  todo 判断是否已经更改
      let confirmSaveButton = await msg_confirm_choose("内容有更改，是否提交？", "确认保存", "我再想想", '确认提交')
      if (confirmSaveButton === "right") {
        let productManage = this.productManage
        // 删除不必要元素
        delete productManage.pageInfo.cardShow
        if (floors.indexOf('experimentInfo') > -1) {
          productManage.experimentInfo = JSON.parse(JSON.stringify(this.experimentInfo))
          delete productManage.experimentInfo.formRules
          delete productManage.experimentInfo.cardShow
          delete productManage.experimentInfo.validateJump
        }
        if (floors.indexOf('vrInfo') > -1) {
          productManage.vrInfo = JSON.parse(JSON.stringify(this.vrInfo))
          delete productManage.vrInfo.formRules
          delete productManage.vrInfo.cardShow
        }
        if (floors.indexOf('examInfo') > -1) {
          productManage.examInfo = JSON.parse(JSON.stringify(this.examInfo))
          delete productManage.examInfo.formRules
          delete productManage.examInfo.cardShow
        }
        if (floors.indexOf('techInfo') > -1) {
          productManage.techInfo = JSON.parse(JSON.stringify(this.techInfo))
          delete productManage.techInfo.formRules
          delete productManage.techInfo.cardShow
        }
        if (floors.indexOf('floorInfo') > -1) {
          productManage.floorInfo = JSON.parse(JSON.stringify(this.floorInfo))
          delete productManage.floorInfo.type1Rules
          delete productManage.floorInfo.type2Rules
        }
        console.log(productManage)
        // 保存接口
        if (await ConfigModel.editEdit(CONFIG_NAME_OFFICIAL_WEB, "productManage", productManage)) {
          msg_success("保存成功")
          setTimeout(() => {
            window.location.href = '#/officialWeb/product';
            window.location.reload()
          }, 1000)
        }
      } else {// 点击了我再想想或关闭按钮

      }
    },
    // 点击取消按钮
    async clickCancelBtn() {
      if (await msg_confirm_choose("是否取消更改？", "取消更改", "我再想想", '确认取消') === "right") {
        msg_success("取消成功")
        setTimeout(() => {
          window.location.href = '#/officialWeb/product';
          window.location.reload()
        }, 1000)
      }
    },
    // 实验相关方法
    experimentMethods() {
      let $this = this;
      return {
        // 清除lists验证的提示
        clearListErrTip() {
          setTimeout(() => {
            // 跳转到指定位置
            if ($this.experimentInfo.validateJump) {
              let errTop = document.querySelector("#vrInfo").offsetTop - (document.body.offsetHeight / 2)
              document.body.scrollTop = errTop;
              document.documentElement.scrollTop = errTop;
              $this.experimentInfo.validateJump = false
            }
            // 清除错误提示红线
            $("#experimentInfo .lists-form-item").removeClass('is-error')
          }, 200)
        },
        // 检测-实验填写合法性检测
        validateExperiment(experiment, experimentIndex, callback, categoryIndex, seriesIndex) {
          let validateResult = false
          let rules = [
            {key: 'name', name: "产品名称"},
            {key: 'subName', name: "副标题"},
            {key: 'description', name: "产品简介"},
            {key: 'experimentDescription', name: "实验简介"},
            {key: 'product_bg', name: "产品封面"},
            {key: 'product_icon', name: "产品Icon"},
            // {key: 'product_iconLeft', name: "左上角Icon"},
            {key: 'product_info_bg', name: "产品详情-头图"},
            // {key: 'product_video', name: "产品详情-视频"},
            {key: 'product_info_img_1', name: "产品详情-图片介绍第1张"},
            {key: 'product_info_img_2', name: "产品详情-图片介绍第2张"},
            {key: 'product_info_img_3', name: "产品详情-图片介绍第3张"},
            {key: 'product_info_img_4', name: "产品详情-图片介绍第4张"},
          ]
          if (categoryIndex != null) {
            if (seriesIndex != null) {
              rules.forEach((rule, index) => {
                if (!experiment[rule.key]) {
                  this.clearListErrTip()
                  callback(new Error(`第${categoryIndex + 1}个分类，第${seriesIndex + 1}个系列，第${experimentIndex + 1}行未设置${rule.name}`))
                }
              })
            } else {
              rules.forEach((rule, index) => {
                if (!experiment[rule.key]) {
                  this.clearListErrTip()
                  callback(new Error(`第${categoryIndex + 1}个分类，第${experimentIndex + 1}行未设置${rule.name}`))
                }
              })
            }
          } else {
            if (seriesIndex != null) {
              rules.forEach((rule, index) => {
                if (!experiment[rule.key]) {
                  this.clearListErrTip()
                  callback(new Error(`第${seriesIndex + 1}个系列，第${experimentIndex + 1}行未设置${rule.name}`))
                }
              })
            } else {
              rules.forEach((rule, index) => {
                if (!experiment[rule.key]) {
                  this.clearListErrTip()
                  callback(new Error(`第${experimentIndex + 1}行未设置${rule.name}`))
                }
              })
            }
          }
        },
        // 工厂-新建实验
        factoryCreateNewExperiment() {
          return {
            id: new Date().getTime() + randomNumber(1, 1000)
          }
        },
        // 点击-分类-删除按钮
        async clickCategoryDeleteBtn(categoryIndex) {
          // 有分类，有系列
          if ($this.experimentShowModel === 0) {
            if (await msg_confirm("确认是否删除该分类？")) {
              $this.experimentInfo.list[0].splice(categoryIndex, 1)
            }
          }
          // 有分类，无系列
          if ($this.experimentShowModel === 1) {
            if (await msg_confirm("确认是否删除该分类？")) {
              $this.experimentInfo.list[1].splice(categoryIndex, 1)
            }
          }
        },
        // 点击-分类-新建按钮
        clickCategoryAddBtn() {
          // 有分类，有系列
          if ($this.experimentShowModel === 0) {
            let newCategory = {
              name: "",
              series: [{
                name: "",
                experiments: []
              }]
            }
            newCategory.series[0].experiments.push(this.factoryCreateNewExperiment())
            $this.experimentInfo.list[0].push(newCategory);
          }
          // 有分类，无系列
          if ($this.experimentShowModel === 1) {
            let newCategory = {
              name: "",
              experiments: []
            }
            newCategory.experiments.push(this.factoryCreateNewExperiment())
            $this.experimentInfo.list[1].push(newCategory)
          }
        },
        // 点击-分类-下移按钮
        clickCategoryDownBtn(categoryIndex) {
          let arr = [];
          // 有分类，有系列
          if ($this.experimentShowModel === 0) {
            arr = $this.experimentInfo.list[0]
            arr[categoryIndex] = arr.splice(categoryIndex + 1, 1, arr[categoryIndex])[0];
            $this.$set($this.experimentInfo.list, 0, arr)
          }
          // 有分类，无系列
          if ($this.experimentShowModel === 1) {
            arr = $this.experimentInfo.list[1]
            arr[categoryIndex] = arr.splice(categoryIndex + 1, 1, arr[categoryIndex])[0];
            $this.$set($this.experimentInfo.list, 1, arr)
          }
        },
        // 点击-分类-上移按钮
        clickCategoryUpBtn(categoryIndex) {
          let arr = [];
          // 有分类，有系列
          if ($this.experimentShowModel === 0) {
            arr = $this.experimentInfo.list[0]
            arr[categoryIndex] = arr.splice(categoryIndex - 1, 1, arr[categoryIndex])[0];
            $this.$set($this.experimentInfo.list, 0, arr)
          }
          // 有分类，无系列
          if ($this.experimentShowModel === 1) {
            arr = $this.experimentInfo.list[1]
            arr[categoryIndex] = arr.splice(categoryIndex - 1, 1, arr[categoryIndex])[0];
            $this.$set($this.experimentInfo.list, 1, arr)
          }
        },
        // 点击-系列-删除按钮
        async clickSeriesDeleteBtn(seriesIndex, categoryIndex) {
          // 有分类，有系列
          if ($this.experimentShowModel === 0) {
            if (await msg_confirm("确认是否删除该系列？")) {
              $this.experimentInfo.list[0][categoryIndex].series.splice(seriesIndex, 1)
            }
          }
          // 无分类，有系列
          if ($this.experimentShowModel === 2) {
            if (await msg_confirm("确认是否删除该系列？")) {
              $this.experimentInfo.list[2].splice(seriesIndex, 1)
            }
          }
        },
        // 点击-系列-新建按钮
        clickSeriesAddBtn(categoryIndex) {
          // 有分类，有系列
          if ($this.experimentShowModel === 0) {
            let newSeries = {
              name: "",
              experiments: []
            }
            newSeries.experiments.push(this.factoryCreateNewExperiment())
            $this.experimentInfo.list[0][categoryIndex]["series"].push(newSeries)
          }
          // 无分类，有系列
          if ($this.experimentShowModel === 2) {
            let newSeries = {
              name: "",
              experiments: []
            }
            newSeries.experiments.push(this.factoryCreateNewExperiment())
            $this.experimentInfo.list[2].push(newSeries)
          }
        },
        // 点击-系列-下移按钮
        clickSeriesDownBtn(seriesIndex, categoryIndex) {
          let arr = [];
          switch ($this.experimentShowModel) {
            // 有分类，有系列
            case 0:
              arr = $this.experimentInfo.list[0][categoryIndex]["series"]
              arr[seriesIndex] = arr.splice(seriesIndex + 1, 1, arr[seriesIndex])[0];
              $this.$set($this.experimentInfo.list[0][categoryIndex], "series", arr)
              break
            // 有分类，无系列
            case 1:
              break
            // 无分类，有系列
            case 2:
              arr = $this.experimentInfo.list[2]
              arr[seriesIndex] = arr.splice(seriesIndex + 1, 1, arr[seriesIndex])[0]
              $this.$set($this.experimentInfo.list, "2", arr)
              break
            // 无分类，无系列
            case 3:
              break
          }
        },
        // 点击-系列-上移按钮
        clickSeriesUpBtn(seriesIndex, categoryIndex) {
          let arr = [];
          switch ($this.experimentShowModel) {
            // 有分类，有系列
            case 0:
              arr = $this.experimentInfo.list[0][categoryIndex]["series"]
              arr[seriesIndex] = arr.splice(seriesIndex - 1, 1, arr[seriesIndex])[0];
              $this.$set($this.experimentInfo.list[0][categoryIndex], "series", arr)
              break
            // 有分类，无系列
            case 1:
              break
            // 无分类，有系列
            case 2:
              arr = $this.experimentInfo.list[2]
              arr[seriesIndex] = arr.splice(seriesIndex - 1, 1, arr[seriesIndex])[0]
              $this.$set($this.experimentInfo.list, "2", arr)
              break
            // 无分类，无系列
            case 3:
              break
          }
        },
        // 点击-实验-新建按钮
        clickExperimentAddBtn(seriesIndex, categoryIndex) {
          switch ($this.experimentShowModel) {
            // 有分类，有系列
            case 0:
              $this.experimentInfo.list[0][categoryIndex]["series"][seriesIndex]["experiments"].push(this.factoryCreateNewExperiment())
              break
            // 有分类，无系列
            case 1:
              $this.experimentInfo.list[1][categoryIndex]["experiments"].push(this.factoryCreateNewExperiment())
              break
            // 无分类，有系列
            case 2:
              $this.experimentInfo.list[2][seriesIndex]["experiments"].push(this.factoryCreateNewExperiment())
              break
            // 无分类，无系列
            case 3:
              $this.experimentInfo.list[3].push(this.factoryCreateNewExperiment())
              break
          }
        },
        // 点击-实验-删除按钮
        clickExperimentDeleteBtn(categoryIndex, seriesIndex, experimentParams) {
          console.log(categoryIndex, seriesIndex, experimentParams)
          let experimentIndex = experimentParams.index
          switch ($this.experimentShowModel) {
            // 有分类，有系列
            case 0:
              $this.experimentInfo.list[0][categoryIndex]["series"][seriesIndex]["experiments"].splice(experimentIndex, 1)
              break
            // 有分类，无系列
            case 1:
              $this.experimentInfo.list[1][categoryIndex]["experiments"].splice(experimentIndex, 1)
              break
            // 无分类，有系列
            case 2:
              $this.experimentInfo.list[2][seriesIndex]["experiments"].splice(experimentIndex, 1)
              break
            // 无分类，无系列
            case 3:
              $this.experimentInfo.list[3].splice(experimentIndex, 1)
              break
          }
        },
        // 点击-实验-上移按钮
        clickExperimentMoveUpBtn(categoryIndex, seriesIndex, experimentParams) {
          let experimentIndex = experimentParams.index
          let arr = []
          switch ($this.experimentShowModel) {
            // 有分类，有系列
            case 0:
              arr = $this.experimentInfo.list[0][categoryIndex]["series"][seriesIndex]["experiments"]
              arr[experimentIndex] = arr.splice(experimentIndex - 1, 1, arr[experimentIndex])[0];
              $this.$set($this.experimentInfo.list[0][categoryIndex]["series"][seriesIndex], "experiments", arr)
              break
            // 有分类，无系列
            case 1:
              arr = $this.experimentInfo.list[1][categoryIndex]["experiments"]
              arr[experimentIndex] = arr.splice(experimentIndex - 1, 1, arr[experimentIndex])[0];
              $this.$set($this.experimentInfo.list[1][categoryIndex], "experiments", arr)
              break
            // 无分类，有系列
            case 2:
              arr = $this.experimentInfo.list[2][seriesIndex]["experiments"]
              arr[experimentIndex] = arr.splice(experimentIndex - 1, 1, arr[experimentIndex])[0]
              $this.$set($this.experimentInfo.list[2][seriesIndex], "experiments", arr)
              break
            // 无分类，无系列
            case 3:
              arr = $this.experimentInfo.list[3]
              arr[experimentIndex] = arr.splice(experimentIndex - 1, 1, arr[experimentIndex])[0]
              $this.$set($this.experimentInfo.list, 3, arr)
              break
          }
        },
        // 点击-实验-下移按钮
        clickExperimentMoveDownBtn(categoryIndex, seriesIndex, experimentParams) {
          let experimentIndex = experimentParams.index
          let arr = []
          switch ($this.experimentShowModel) {
            // 有分类，有系列
            case 0:
              arr = $this.experimentInfo.list[0][categoryIndex]["series"][seriesIndex]["experiments"]
              arr[experimentIndex] = arr.splice(experimentIndex + 1, 1, arr[experimentIndex])[0];
              $this.$set($this.experimentInfo.list[0][categoryIndex]["series"][seriesIndex], "experiments", arr)
              break
            // 有分类，无系列
            case 1:
              arr = $this.experimentInfo.list[1][categoryIndex]["experiments"]
              arr[experimentIndex] = arr.splice(experimentIndex + 1, 1, arr[experimentIndex])[0];
              $this.$set($this.experimentInfo.list[1][categoryIndex], "experiments", arr)
              break
            // 无分类，有系列
            case 2:
              arr = $this.experimentInfo.list[2][seriesIndex]["experiments"]
              arr[experimentIndex] = arr.splice(experimentIndex + 1, 1, arr[experimentIndex])[0]
              $this.$set($this.experimentInfo.list[2][seriesIndex], "experiments", arr)
              break
            // 无分类，无系列
            case 3:
              arr = $this.experimentInfo.list[3]
              arr[experimentIndex] = arr.splice(experimentIndex + 1, 1, arr[experimentIndex])[0]
              $this.$set($this.experimentInfo.list, 3, arr)
              break
          }
        },
        // 监听-图片上传成功
        onImgUploadSuccess(categoryIndex, seriesIndex, experimentParams) {
          let experimentIndex = experimentParams.index // 实验编号
          let imgSrc = experimentParams.params[1] // 成功后图片地址
          // 判断上传的什么图片
          let uploaderId = experimentParams.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          switch ($this.experimentShowModel) {
            // 有分类，有系列
            case 0:
              $this.$set($this.experimentInfo.list[0][categoryIndex]["series"][seriesIndex]["experiments"][experimentIndex], uploaderKey, imgSrc)
              break
            // 有分类，无系列
            case 1:
              $this.$set($this.experimentInfo.list[1][categoryIndex]["experiments"][experimentIndex], uploaderKey, imgSrc)
              break
            // 无分类，有系列
            case 2:
              $this.$set($this.experimentInfo.list[2][seriesIndex]["experiments"][experimentIndex], uploaderKey, imgSrc)
              break
            // 无分类，无系列
            case 3:
              $this.$set($this.experimentInfo.list[3][experimentIndex], uploaderKey, imgSrc)
              break
          }
        },
        // 监听-图片删除
        onImgDelete(categoryIndex, seriesIndex, experimentParams) {
          let experimentIndex = experimentParams.index // 实验编号
          // 判断上传的什么图片
          let uploaderId = experimentParams.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          switch ($this.experimentShowModel) {
            // 有分类，有系列
            case 0:
              $this.$set($this.experimentInfo.list[0][categoryIndex]["series"][seriesIndex]["experiments"][experimentIndex], uploaderKey, "")
              break
            // 有分类，无系列
            case 1:
              $this.$set($this.experimentInfo.list[1][categoryIndex]["experiments"][experimentIndex], uploaderKey, "")
              break
            // 无分类，有系列
            case 2:
              $this.$set($this.experimentInfo.list[2][seriesIndex]["experiments"][experimentIndex], uploaderKey, "")
              break
            // 无分类，无系列
            case 3:
              $this.$set($this.experimentInfo.list[3][experimentIndex], uploaderKey, "")
              break
          }
        },

        // 实验库-点击-实验-新建按钮
        clickExperimentAddBtn_list() {
          $this.experimentInfo.experimentList.push(this.factoryCreateNewExperiment())
        },
        // 实验库-点击-实验-删除按钮
        clickExperimentDeleteBtn_list(experimentParams) {
          let experimentIndex = experimentParams.index
          $this.experimentInfo.experimentList.splice(experimentIndex, 1)
        },
        // 实验库-点击-实验-上移按钮
        clickExperimentMoveUpBtn_list(experimentParams) {
          let experimentIndex = experimentParams.index
          let arr = []
          arr = $this.experimentInfo.experimentList
          arr[experimentIndex] = arr.splice(experimentIndex - 1, 1, arr[experimentIndex])[0]
          $this.$set($this.experimentInfo, "experimentList", arr)
        },
        // 实验库-点击-实验-下移按钮
        clickExperimentMoveDownBtn_list(experimentParams) {
          let experimentIndex = experimentParams.index
          let arr = []
          arr = $this.experimentInfo.experimentList
          arr[experimentIndex] = arr.splice(experimentIndex + 1, 1, arr[experimentIndex])[0]
          $this.$set($this.experimentInfo, "experimentList", arr)
        },
        // 实验库-监听-图片上传成功
        onImgUploadSuccess_list(experimentParams) {
          let experimentIndex = experimentParams.index // 实验编号
          let imgSrc = experimentParams.params[1] // 成功后图片地址
          // 判断上传的什么图片
          let uploaderId = experimentParams.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          $this.$set($this.experimentInfo.experimentList[experimentIndex], uploaderKey, imgSrc)
        },
        // 实验库-监听-图片删除
        onImgDelete_list(experimentParams) {
          let experimentIndex = experimentParams.index // 实验编号
          // 判断上传的什么图片
          let uploaderId = experimentParams.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          $this.$set($this.experimentInfo.experimentList[experimentIndex], uploaderKey, "")
        },
        // 实验库-从现有列表填充
        clickExperimentList_Input() {
          $this.experimentInfo.experimentList = []
          let list = []
          $this.experimentInfo.list[0].forEach(category => {
            category.series.forEach(series => {
              series.experiments.forEach(experiment => {
                let experimentNew = JSON.parse(JSON.stringify(experiment))
                experimentNew.id = new Date().getTime() + randomNumber(1, 1000)
                list.push(experimentNew)
              })
            })
          })
          $this.experimentInfo.experimentList = list
        },
        // 实验-点击选择按钮-打开弹窗
        openChooseDialog(seriesIndex, categoryIndex) {
          let generateListFilter0 = CommonModel.generateListFilterOptions('name', 'id', $this.experimentInfo.experimentList, false)
          $this.$set($this.chooseExperiment, "list", generateListFilter0[0])
          $this.chooseExperiment.categoryIndex = categoryIndex
          $this.chooseExperiment.seriesIndex = seriesIndex
          $this.chooseExperiment.dialog = true
        },
        // 实验选择框-点击选择按钮
        clickChooseExperimentBtn() {
          let categoryIndex = $this.chooseExperiment.categoryIndex
          let seriesIndex = $this.chooseExperiment.seriesIndex
          // 找到对应的实验
          let experimentChoose = findObjectArrSomeObjFirstOne($this.experimentInfo.experimentList, "id", $this.chooseExperiment.chooseId)
          let experiment = JSON.parse(JSON.stringify(experimentChoose))
          experiment.id = new Date().getTime() + randomNumber(1, 10000)
          switch ($this.experimentShowModel) {
            // 有分类，有系列
            case 0:
              $this.experimentInfo.list[0][categoryIndex]["series"][seriesIndex]["experiments"].push(experiment)
              break
            // 有分类，无系列
            case 1:
              $this.experimentInfo.list[1][categoryIndex]["experiments"].push(experiment)
              break
            // 无分类，有系列
            case 2:
              $this.experimentInfo.list[2][seriesIndex]["experiments"].push(experiment)
              break
            // 无分类，无系列
            case 3:
              $this.experimentInfo.list[3].push(experiment)
              break
          }

          $this.chooseExperiment.dialog = false
        }
      }
    },
    // 全景相关方法
    vrMethods() {
      let $this = this;
      return {
        // 清除lists验证的提示
        clearListErrTip() {
          setTimeout(() => {
            if ($this.vrInfo.validateJump) {
              let errTop = document.querySelector("#examInfo").offsetTop - (document.body.offsetHeight / 2)
              document.body.scrollTop = errTop;
              document.documentElement.scrollTop = errTop;
              $this.vrInfo.validateJump = false
            }
            $("#vrInfo .lists-form-item").removeClass('is-error')
          }, 200)

        },
        // 检测-实验填写合法性检测
        validateVr(vr, vrIndex, callback, seriesIndex) {
          let rules = [
            {key: 'name', name: "产品名称"},
            {key: 'description', name: "产品简介"},
            {key: 'link', name: "链接地址"},
            {key: 'product_bg', name: "产品封面"},
          ]
          if (seriesIndex != null) {
            rules.forEach((rule, index) => {
              if (!vr[rule.key]) {
                this.clearListErrTip()
                callback(new Error(`第${seriesIndex + 1}个系列，第${vrIndex + 1}行未设置${rule.name}`))
              }
            })
          } else {
            rules.forEach((rule, index) => {
              if (!vr[rule.key]) {
                this.clearListErrTip()
                callback(new Error(`第${vrIndex + 1}行未设置${rule.name}`))
              }
            })
          }
        },
        // 工厂-新建全景
        factoryCreateNewVr() {
          return {
            id: new Date().getTime() // 随机id
          }
        },
        // 点击-系列-删除按钮
        async clickSeriesDeleteBtn(seriesIndex, categoryIndex) {
          // 有系列
          if ($this.vrShowModel === 0) {
            if (await msg_confirm("确认是否删除该系列？")) {
              $this.vrInfo.list[0].splice(seriesIndex, 1)
            }
          }
        },
        // 点击-系列-新建按钮
        clickSeriesAddBtn(categoryIndex) {
          // 有系列
          if ($this.vrShowModel === 0) {
            let newSeries = {
              name: "",
              vrs: []
            }
            newSeries.vrs.push(this.factoryCreateNewVr())
            $this.vrInfo.list[0].push(newSeries)
          }
        },
        // 点击-全景-新建按钮
        clickVrAddBtn(seriesIndex, categoryIndex) {
          switch ($this.vrShowModel) {
            // 有系列
            case 0:
              $this.vrInfo.list[0][seriesIndex]["vrs"].push(this.factoryCreateNewVr())
              break
            // 无系列
            case 1:
              $this.vrInfo.list[1].push(this.factoryCreateNewVr())
              break
          }
        },
        // 点击-全景-删除按钮
        clickVrDeleteBtn(categoryIndex, seriesIndex, vrParams) {
          let vrIndex = vrParams.index
          switch ($this.vrShowModel) {
            // zss有系列
            case 0:
              $this.vrInfo.list[0][seriesIndex]["vrs"].splice(vrIndex, 1)
              break
            // 无系列
            case 1:
              $this.vrInfo.list[1].splice(vrIndex, 1)
              break
          }
        },
        // 点击-产品-上移按钮
        clickVrMoveUpBtn(categoryIndex, seriesIndex, vrParams) {
          let vrIndex = vrParams.index
          let arr = []
          switch ($this.vrShowModel) {
            // 有系列
            case 0:
              arr = $this.vrInfo.list[0][seriesIndex]["vrs"]
              arr[vrIndex] = arr.splice(vrIndex - 1, 1, arr[vrIndex])[0];
              $this.$set($this.vrInfo.list[0][seriesIndex], "vrs", arr)
              break
            // 无系列
            case 1:
              arr = $this.vrInfo.list[1]
              arr[vrIndex] = arr.splice(vrIndex - 1, 1, arr[vrIndex])[0]
              $this.$set($this.vrInfo.list, 1, arr)
              break
          }
        },
        // 点击-产品-下移按钮
        clickVrMoveDownBtn(categoryIndex, seriesIndex, vrParams) {
          let vrIndex = vrParams.index
          let arr = []
          switch ($this.vrShowModel) {
            // 有系列
            case 0:
              arr = $this.vrInfo.list[0][seriesIndex]["vrs"]
              arr[vrIndex] = arr.splice(vrIndex + 1, 1, arr[vrIndex])[0];
              $this.$set($this.vrInfo.list[0][seriesIndex], "vrs", arr)
              break
            // 无系列
            case 1:
              arr = $this.vrInfo.list[1]
              arr[vrIndex] = arr.splice(vrIndex + 1, 1, arr[vrIndex])[0]
              $this.$set($this.vrInfo.list, 1, arr)
              break
          }
        },
        // 监听-图片上传成功
        onImgUploadSuccess(categoryIndex, seriesIndex, vrParams) {
          let vrIndex = vrParams.index // 全景编号
          let imgSrc = vrParams.params[1] // 成功后图片地址
          // 判断上传的什么图片
          let uploaderId = vrParams.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          switch ($this.vrShowModel) {
            // 有系列
            case 0:
              $this.$set($this.vrInfo.list[0][seriesIndex]["vrs"][vrIndex], uploaderKey, imgSrc)
              break
            // 无系列
            case 1:
              $this.$set($this.vrInfo.list[1][vrIndex], uploaderKey, imgSrc)
              break
          }
        },
        // 监听-图片删除
        onImgDelete(categoryIndex, seriesIndex, vrParams) {
          let vrIndex = vrParams.index // 全景编号
          // 判断上传的什么图片
          let uploaderId = vrParams.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          switch ($this.vrShowModel) {
            // 有系列
            case 0:
              $this.$set($this.vrInfo.list[0][seriesIndex]["vrs"][vrIndex], uploaderKey, "")
              break
            // 无系列
            case 1:
              $this.$set($this.vrInfo.list[1][vrIndex], uploaderKey, "")
              break
          }
        }
      }
    },
    // 考试系统相关方案
    examMethods() {
      let $this = this;
      return {
        // 大图上传成功
        fileUploadSuccess(data) {
          $this.examInfo.exam_big_img = data[1]
        },
        // 大图删除
        fileDelete(data) {
          $this.examInfo.exam_big_img = ""
        }
      }
    },
    // 技术支持楼层相关方法
    techMethods() {
      let $this = this;
      return {
        // 检测-实验填写合法性检测
        validate(tech, techIndex, callback) {
          let rules = [
            {key: 'name', name: "楼层名称"},
            {key: 'child_floor_bg', name: "背景图"},
            {key: 'des', name: "弹窗介绍文字"},
            {key: 'child_floor_dialog_bg', name: "弹窗背景图"},
            {key: 'dialog_button_text', name: "弹窗按钮文字"},
          ]
          rules.forEach((rule, index) => {
            if (!tech[rule.key]) {
              callback(new Error(`第${techIndex + 1}行未设置${rule.name}`))
              setTimeout(() => {
                $(".floorType2").removeClass('is-error')
              }, 300)
            }
          })
        },
        // 工厂-新建全景
        factoryCreateNew() {
          return {
            id: new Date().getTime() // 随机id
          }
        },
        // 点击-楼层-新建按钮
        clickAddBtn() {
          $this.techInfo.list.push(this.factoryCreateNew())
        },
        // 点击-楼层-删除按钮
        clickDeleteBtn(params) {
          let index = params.index
          $this.techInfo.list.splice(index, 1)
        },
        // 点击-产品-上移按钮
        clickMoveUpBtn(params) {
          let index = params.index
          let arr = []
          arr = $this.techInfo.list
          arr[index] = arr.splice(index - 1, 1, arr[index])[0]
          $this.$set($this.techInfo, "list", arr)
        },
        // 点击-产品-下移按钮
        clickMoveDownBtn(params) {
          let index = params.index
          let arr = []
          arr = $this.techInfo.list
          arr[index] = arr.splice(index + 1, 1, arr[index])[0]
          $this.$set($this.techInfo, "list", arr)
        },
        // 监听-图片上传成功
        onImgUploadSuccess(params) {
          let index = params.index // 楼层编号
          let imgSrc = params.params[1] // 成功后图片地址
          // 判断上传的什么图片
          let uploaderId = params.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          $this.$set($this.techInfo.list[index], uploaderKey, imgSrc)
        },
        // 监听-图片删除
        onImgDelete(params) {
          let index = params.index // 楼层编号
          // 判断上传的什么图片
          let uploaderId = params.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          $this.$set($this.techInfo.list[index], uploaderKey, "")
        }
      }
    },
    // 自定义楼层方法
    floorMethods() {
      let $this = this;
      return {
        // 检测-实验填写合法性检测
        validate(li, liIndex, callback) {
          let rules = [
            {key: 'name', name: "子楼层名称"},
            {key: 'child_floor_bg', name: "背景图"},
          ]
          rules.forEach((rule, index) => {
            if (!li[rule.key]) {
              setTimeout(() => {
                $(".floorType2").removeClass('is-error')
              }, 300)
              callback(new Error(`第${liIndex + 1}行未设置${rule.name}`))
            }
          })
        },
        // 楼层切换官网显示
        async onFloorChangeShow(v, target) {
          if (v) {
            if (!await msg_confirm("是否确认开启该楼层，开启该楼层后，该楼层将在官网上可见")) {
              $this.$set(target, "show", !v)
            }
          } else {
            if (!await msg_confirm("是否确认隐藏该楼层，隐藏该楼层后，该楼层将在官网上不可见")) {
              $this.$set(target, "show", !v)
            }
          }
        },
        // 监听-横屏楼层图片上传成功
        async onFloor1ImgUploadSuccess(index, uploadData) {
          let imgSrc = uploadData[1] // 成功后图片地址
          // 判断上传的什么图片
          let uploaderId = uploadData[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          $this.$set($this.floorInfo.lists[index], uploaderKey, imgSrc)
        },
        // 监听-横屏楼层图片上传成功
        async onFloor1ImgDeleteSuccess(index, uploadData) {
          // 判断上传的什么图片
          let uploaderId = uploadData[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          $this.$set($this.floorInfo.lists[index], uploaderKey, "")
        },
        // 工厂-新建全景
        factoryCreateNew() {
          return {
            id: new Date().getTime() // 随机id
          }
        },
        // 点击-子楼层-新建按钮
        clickAddBtn(floorIndex) {
          $this.floorInfo.lists[floorIndex].list.push(this.factoryCreateNew())
        },
        // 点击-子楼层-删除按钮
        clickDeleteBtn(params, floorIndex) {
          let index = params.index
          $this.floorInfo.lists[floorIndex].list.splice(index, 1)
        },
        // 点击-产品-上移按钮
        clickMoveUpBtn(params, floorIndex) {
          let index = params.index
          let arr = []
          arr = $this.floorInfo.lists[floorIndex].list
          arr[index] = arr.splice(index - 1, 1, arr[index])[0]
          $this.$set($this.floorInfo.lists[floorIndex], "list", arr)
        },
        // 点击-产品-下移按钮
        clickMoveDownBtn(params, floorIndex) {
          let index = params.index
          let arr = []
          arr = $this.floorInfo.lists[floorIndex].list
          arr[index] = arr.splice(index + 1, 1, arr[index])[0]
          $this.$set($this.floorInfo.lists[floorIndex], "list", arr)
        },
        // 监听-图片上传成功
        onImgUploadSuccess(params, floorIndex) {
          let index = params.index // 子楼层编号
          let imgSrc = params.params[1] // 成功后图片地址
          // 判断上传的什么图片
          let uploaderId = params.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          $this.$set($this.floorInfo.lists[floorIndex].list[index], uploaderKey, imgSrc)
        },
        // 监听-图片删除
        onImgDelete(params, floorIndex) {
          let index = params.index // 子楼层编号
          // 判断上传的什么图片
          let uploaderId = params.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          $this.$set($this.floorInfo.lists[floorIndex].list[index], uploaderKey, "")
        },

        // 点击删除楼层按钮
        async clickDeleteFloor(floorIndex) {
          if (await msg_confirm("确定要删除此楼层吗?")) {
            $this.floorInfo.lists.splice(floorIndex, 1)
          }
        },
        // 点击新增楼层按钮
        async clickAddFloorBtn() {
          let chooseFloorType = await msg_confirm_choose("选择楼层类型", "添加楼层", "固定图片楼层", "横屏图片楼层")
          if (chooseFloorType === 'left') {
            $this.floorInfo.lists.push(
              {
                show: true,
                cardShow: true,
                type: "1",
                name: "",
                englishName: "",
                bigImg: "",
              }
            )
          }
          if (chooseFloorType === 'right') {
            $this.floorInfo.lists.push(
              {
                show: true,
                cardShow: true,
                type: "2",
                name: "",
                englishName: "",
                list: [],
              }
            )
          }
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  padding-bottom: 150px;
}
</style>
