<template>
  <div class="app-container flex flex-around">
    <!--生成并导出体验码-->
    <el-card style="width: 500px;" v-permission="['administrator']">
      <div slot="header">
        <span>生成体验码</span>
      </div>
      <el-form>
        <el-form-item label="生成个数">
          <el-input v-model="create.codeNumber" type="number"></el-input>
        </el-form-item>
        <el-form-item label="可使用次数">
          <el-input v-model="create.number" type="number"></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="create.remarks"></el-input>
        </el-form-item>
        <div style="text-align: center">
          <el-button @click="clickCreateBtn" :loading="create.loading">生成并导出</el-button>
        </div>
      </el-form>
    </el-card>

    <!--查询体验码信息-->
    <el-card style="width: 1100px;" v-permission="['administrator','seller']">
      <div slot="header">
        <span>查询体验码</span>
      </div>
      <el-form>
        <el-form-item label="体验码(支持多个，每行一个)">
          <el-input v-model="info.code" type="textarea"></el-input>
        </el-form-item>
        <div style="text-align: center">
          <el-button @click="clickInfoBtn" :loading="info.loading">查询</el-button>
        </div>
        <el-form-item label="查询结果">
          <el-table :data="info.infos" v-loading="info.loading" element-loading-text="加载中" border fit
                    highlight-current-row
                    style="width: 100%;">
            <el-table-column label="体验码" align="center" width="300px">
              <template slot-scope="scope">
                <span>{{ scope.row.experimentCode }}</span>
              </template>
            </el-table-column>
            <el-table-column label="剩余次数" align="center" width="80px">
              <template slot-scope="scope">
                <span>{{ scope.row.number }}</span>
              </template>
            </el-table-column>
            <el-table-column label="备注" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.remarks }}</span>
              </template>
            </el-table-column>
            <el-table-column label="使用记录" align="center">
              <template slot-scope="scope">
                <div>{{ scope.row.useInfoArrResult }}</div>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import {msg_err} from "@/utils/ele_component";
import {ExperimentCodeModel} from "@/model/erp/activity/ExperimentCodeModel";
import {date_format, getIpInfo} from "@/utils/common";
import {excel_export_from_json} from "@/utils/excel";
import permission from "@/directive/permission";
import {ToolsModel} from "@/model/erp/ToolsModel";

export default {
  name: "activityXicaiMeeting",
  directives: {
    permission
  },
  data() {
    return {
      create: {
        codeNumber: 1,
        number: 2,
        remarks: "",
        loading: false
      },
      info: {
        code: "",
        infos: [],
        loading: false
      }
    }
  },
  mounted() {

  },
  methods: {
    // 统计体验使用次数
    async cal() {
      let data = await ExperimentCodeModel.getList({})
      let totalUse = 0;
      data.forEach(li => {
        totalUse += li.useInfoArr.length
      })
      console.log(totalUse)
    },
    // 生成并导出体验码列表
    async clickCreateBtn() {
      // 检测参数
      let reg = /^[0-9]+$/
      if (!reg.test(this.create.number)) {
        msg_err("'只能输入正整数'")
        return
      }
      if (!reg.test(this.create.codeNumber)) {
        msg_err("'只能输入正整数'")
        return
      }
      this.create.loading = true
      let data = await ExperimentCodeModel.createCodes(this.create.codeNumber, this.create.number, this.create.remarks).catch(e => {
        this.create.loading = false
      })
      if (data) {
        // 导出列表
        // map reduce生成arr
        function formatJson(filterVal, jsonData) {
          return jsonData.map(v => filterVal.map(j => {
            let value = '';
            switch (j) {
              default:
                value = v[j]
            }
            return value
          }))
        }

        const header = ['体验码', '可体验次数', "备注"];
        const filter = ["experimentCode", "number", "remarks"];
        // 导出excel
        excel_export_from_json(data, header, filter, formatJson, "体验码列表-" + date_format(new Date(), "yyyy-MM-dd HH:mm"))
        this.create.loading = false
      }
    },
    // 点击查询信息按钮
    async clickInfoBtn() {
      if (!this.info.code) {
        msg_err("请先输入体验码")
      }
      this.$set(this.info, "infos", [])
      let codes = this.info.code.split("\n")
      this.info.loading = true
      for (let i = 0; i < codes.length; i++) {
        let code = codes[i]
        let data = await ExperimentCodeModel.getExperimentCodeInfo(code)
        if (data) {
          let str = ""
          for (let i = 0; i < data.useInfoArr.length; i++) {
            str += await ToolsModel.getIpInfoOnline1(data.useInfoArr[i].ip) + "   " + date_format(data.useInfoArr[i].useDate, "yyyy-MM-dd HH:mm:ss") + "\n"
          }
          data.useInfoArrResult = str
          this.info.infos.push(data)
        } else {
          this.info.infos.push({
            experimentCode: code,
            number: "未找到",
            useInfoArr: []
          })
        }
      }
      this.info.loading = false
    },

  }
}
</script>

<style scoped lang="scss">

</style>
