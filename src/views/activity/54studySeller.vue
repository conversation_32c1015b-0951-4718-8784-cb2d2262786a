<template>
  <div class="app-container">
    <!--报名信息-->
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>申请列表</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="inputInfo.cardShow=!inputInfo.cardShow">
          <i class="el-icon-arrow-up" v-show="inputInfo.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!inputInfo.cardShow"></i>
          {{ inputInfo.cardShow ? '收起' : '展开' }}
        </el-button>
        <el-button style="float:right" size="small" @click="ExportMethods().clickExportBtn()">导出Excel</el-button>
      </div>
      <div class="card-container" v-show="inputInfo.cardShow">
        <!--列表-->
        <el-table :data="inputInfo.list" v-loading="inputInfo.loading" element-loading-text="加载中" border fit
                  highlight-current-row
                  style="width: 100%;">
          <el-table-column label="提交时间" align="center">
            <template slot-scope="scope">
              <span>{{ date_format(scope.row.createTime, "yyyy-MM-dd HH:mm") }}</span>
            </template>
          </el-table-column>
          <el-table-column label="姓名" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.info.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="学校" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.info.schoolName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="职务" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.info.duty }}</span>
            </template>
          </el-table-column>
          <el-table-column label="联系电话" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.info.phone }}</span>
            </template>
          </el-table-column>
                    <el-table-column label="实验人数" align="center">
                      <template slot-scope="scope">
                        <span>{{ scope.row.info.number }}</span>
                      </template>
                    </el-table-column>
        </el-table>
        <!--列表分页-->
        <div class="pagination-container">
          <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                         :current-page.sync="inputInfo.pages.number" :page-size.sync="inputInfo.pages.size"
                         layout="total,prev, pager, next,jumper,sizes" :total="inputInfo.pages.totalElements"
                         :page-sizes="[10,20,50,100,200,500]"
                         @size-change="(size)=>ListMethods().pageLimitChange(size)"
                         :page-count="inputInfo.pages.totalPages">
          </el-pagination>
        </div>
      </div>
      <el-dialog
        title="选择要导出的日期"
        :visible.sync="exportInfo.dialog"
        width="700px"
        center
        v-el-drag-dialog>
        <div>
          <div style="text-align: center">
            <el-date-picker
              v-model="exportInfo.exportDate"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00','23:59:59']"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button type="default"
                     @click="exportInfo.dialog=false">取 消
          </el-button>
          <el-button type="success" @click="ExportMethods().clickDateExport()">导 出</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import {msg_confirm_choose, msg_err} from "@/utils/ele_component";
import {date_format} from "@/utils/common";
import {excel_export_from_json} from "@/utils/excel";
import {CommonInfoModel} from "@/model/erp/CommonInfoModel";
import {mapGetters} from 'vuex'
import elDragDialog from "@/directive/el-drag-dialog";

export default {
  name: "activity54studySeller",
  components: {},
  computed: {
    ...mapGetters([
      'roles'
    ])
  },
  directives: {
    elDragDialog
  },
  data() {
    return {
      date_format: date_format,
      study54Config: {},
      inputInfo: {
        cardShow: true,
        list: [],
        loading: false,
        query: {},
        pages: {
          size: 20
        },
      },
      exportInfo: {
        dialog: false,
        exportDate: ""
      }
    }
  },
  mounted() {
    // 获取列表
    this.ListMethods().getList(1, this.inputInfo.pages.size, this.inputInfo.query)
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.inputInfo.loading = true;
          [$this.inputInfo.list, $this.inputInfo.pages] = await CommonInfoModel.getPageList(page - 1, size, "", query)
          $this.inputInfo.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.inputInfo.pages.size, $this.inputInfo.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.inputInfo.pages.page, size, $this.inputInfo.query)
        },
        // 初始化筛选列表
        async initFilter() {

        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.inputInfo.query = query
          this.getList(0, $this.inputInfo.pages.size, $this.inputInfo.query)
        },

      }
    },
    // 导出Methods
    ExportMethods() {
      let $this = this;
      return {
        // 点击导出按钮
        async clickExportBtn() {
          msg_confirm_choose("选择全部导出或选择日期", "导出EXCEL", "全部导出", "选择导出日期").then(res => {
            if (res) {
              if (res === "left") {
                $this.inputInfo.query = {}
                this.exportExcel()
              }
              if (res === "right") {
                $this.exportInfo.dialog = true
              }
            }
          })
        },
        // 点击按时间导出按钮
        clickDateExport() {
          if ($this.exportInfo.exportDate.length === 2) {
            const start = new Date($this.exportInfo.exportDate[0]).getTime()
            const end = new Date($this.exportInfo.exportDate[1]).getTime()
            const query = {
              '$and': [
                {'createTime': {'$gte': start}},
                {'createTime': {'$lte': end}}
              ]
            }
            $this.inputInfo.query = query
            this.exportExcel()
          } else {
            msg_err("请先选择导出时间范围")
          }
        },
        // 导出excel
        async exportExcel() {
          // 生成查询参数
          let query = $this.inputInfo.query
          let [list] = await CommonInfoModel.getList(query)
          if (list.length > 0) {
            // map reduce生成arr
            function formatJson(filterVal, jsonData) {
              return jsonData.map(v => filterVal.map(j => {
                let value = '';
                switch (j) {
                  case "name":
                    value = v["info"]["name"];
                    break;
                  case "schoolName":
                    value = v["info"]["schoolName"];
                    break;
                  case "duty":
                    value = v["info"]["duty"];
                    break;
                  case "phone":
                    value = v["info"]["phone"];
                    break;
                  case "number":
                    value = v["info"]["number"];
                    break;
                  case "createTime":
                    value = date_format(v["createTime"], "yyyy-MM-dd HH:mm");
                    break;
                  default:
                    value = v[j]
                }
                return value
              }))
            }

            const header = ['姓名', '学校', "职务", "联系电话","实验人数", '提交时间'];
            const filter = ["name", "schoolName", "duty", "phone","number", 'createTime'];
            // 导出excel
            excel_export_from_json(list, header, filter, formatJson, "党史活动-申请列表-" + date_format(new Date(), "yyyy-MM-dd HH:mm"))
          } else {
            msg_err('要导出的列表为空')
          }
        },
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
