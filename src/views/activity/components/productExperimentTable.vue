<template>
  <div>
    <el-table :data="list" border fit>
      <el-table-column :label="'*产品名称\n(10字以内)'" align="center" width="145">
        <template slot-scope="scope">
          <span class="input_box ellipsis"
                @click="openTextEditor('name',scope.$index,10,'产品名称(10字以内)')">{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="'*产品副标题\n(18个字以内)'" align="center" width="145">
        <template slot-scope="scope">
          <span class="input_box ellipsis"
                @click="openTextEditor('subName',scope.$index,18,'产品副标题(18个字以内)')">{{ scope.row.subName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="'*产品简介\n(60字以内)'" align="center" width="145">
        <template slot-scope="scope">
          <span class="input_box ellipsis"
                @click="openTextEditor('description',scope.$index,60,'产品简介(60字以内)')">{{ scope.row.description }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="'*产品详情-实验简介\n(800字以内)'" align="center" width="145">
        <template slot-scope="scope">
          <span class="input_box ellipsis"
                @click="openTextEditor('experimentDescription',scope.$index,800,'产品详情-实验简介(800字以内)')">{{
              scope.row.experimentDescription
            }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="'*下载链接\n(100字以内)'" align="center" width="145">
        <template slot-scope="scope">
          <span class="input_box ellipsis"
                @click="openTextEditor('downloadLink',scope.$index,100,'下载链接')">{{
              scope.row.downloadLink
            }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="'*产品封面\n(400*250,400KB以内)'" align="center">
        <template slot-scope="scope">
          <erp-uploader-one-pic :img-in="scope.row.product_bg"
                                :uploader-id="'product_bg__'+scope.row.id"
                                :show-des="false"
                                :uploader-size="[80,80]" :pixel-limit="[400,250]"
                                :size-limit="400"
                                @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"
                                @afterDelete="v=>onImgDelete(v,scope.$index)"
          ></erp-uploader-one-pic>
        </template>
      </el-table-column>
      <el-table-column :label="'*产品Icon\n(400*400,400KB以内)'" align="center">
        <template slot-scope="scope">
          <erp-uploader-one-pic :img-in="scope.row.product_icon"
                                :uploader-id="'product_icon__'+scope.row.id"
                                :show-des="false"
                                :uploader-size="[80,80]" :pixel-limit="[400,400]"
                                @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"
                                @afterDelete="v=>onImgDelete(v,scope.$index)"
                                :size-limit="400"></erp-uploader-one-pic>
        </template>
      </el-table-column>
      <el-table-column :label="'左上角Icon\n(100*100,50KB以内)'" align="center">
        <template slot-scope="scope">
          <erp-uploader-one-pic :img-in="scope.row.product_iconLeft"
                                :uploader-id="'product_iconLeft__'+scope.row.id"
                                :show-des="false"
                                :uploader-size="[80,80]" :pixel-limit="[100,100]"
                                @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"
                                @afterDelete="v=>onImgDelete(v,scope.$index)"
                                :size-limit="50"></erp-uploader-one-pic>
        </template>
      </el-table-column>
      <el-table-column :label="'*产品详情-头图\n(1200x330,3MB以内)'" align="center">
        <template slot-scope="scope">
          <erp-uploader-one-pic :img-in="scope.row.product_info_bg"
                                :uploader-id="'product_info_bg__'+scope.row.id"
                                :show-des="false"
                                :uploader-size="[80,80]" :pixel-limit="[1200,330]"
                                @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"
                                @afterDelete="v=>onImgDelete(v,scope.$index)"
                                :size-limit="3076"></erp-uploader-one-pic>
        </template>
      </el-table-column>
      <el-table-column :label="'*产品详情-视频介绍\n(20MB以内)'" align="center">
        <template slot-scope="scope">
          <erp-uploader-one-video :video-in="scope.row.product_video" :uploader-id="'product_video__'+scope.row.id"
                                  :show-des="false"
                                  :uploader-size="[100,50]" :pixel-limit="[2560,1080]"
                                  @uploadSuccess="v=>onVideoUploadSuccess(v,scope.$index)"
                                  @afterDelete="v=>onVideoDelete(v,scope.$index)"
                                  :size-limit="20480"></erp-uploader-one-video>
        </template>
      </el-table-column>
      <el-table-column :label="'*产品详情-大图介绍\n(3MB以内)'" align="center">
        <template slot-scope="scope">
          <erp-uploader-one-pic :img-in="scope.row.product_info_img"
                                :uploader-id="'product_info_img__'+scope.row.id"
                                :show-des="false"
                                :uploader-size="[50,50]" :pixel-limit="[1200,330]"
                                @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"
                                @afterDelete="v=>onImgDelete(v,scope.$index)"
                                :size-limit="3076"></erp-uploader-one-pic>
        </template>
      </el-table-column>
      <el-table-column :label="'*产品详情-图片介绍\n(3MB以内)'" align="center" width="170px">
        <template slot-scope="scope">
          <div class="flex flex-around flex-wrap">
            <erp-uploader-one-pic :img-in="scope.row.product_info_img_1"
                                  :uploader-id="'product_info_img_1__'+scope.row.id"
                                  :show-des="false"
                                  :uploader-size="[50,50]" :pixel-limit="[1200,330]"
                                  @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"
                                  @afterDelete="v=>onImgDelete(v,scope.$index)"
                                  :size-limit="3076"></erp-uploader-one-pic>
            <erp-uploader-one-pic :img-in="scope.row.product_info_img_2"
                                  :uploader-id="'product_info_img_2__'+scope.row.id"
                                  :show-des="false"
                                  :uploader-size="[50,50]" :pixel-limit="[1200,330]"
                                  @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"
                                  @afterDelete="v=>onImgDelete(v,scope.$index)"
                                  :size-limit="3076"></erp-uploader-one-pic>
            <erp-uploader-one-pic :img-in="scope.row.product_info_img_3"
                                  :uploader-id="'product_info_img_3__'+scope.row.id"
                                  :show-des="false"
                                  :uploader-size="[50,50]" :pixel-limit="[1200,330]"
                                  @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"
                                  @afterDelete="v=>onImgDelete(v,scope.$index)"
                                  :size-limit="3076"></erp-uploader-one-pic>
            <erp-uploader-one-pic :img-in="scope.row.product_info_img_4"
                                  :uploader-id="'product_info_img_4__'+scope.row.id"
                                  :show-des="false"
                                  :uploader-size="[50,50]" :pixel-limit="[1200,330]"
                                  @uploadSuccess="v=>onImgUploadSuccess(v,scope.$index)"
                                  @afterDelete="v=>onImgDelete(v,scope.$index)"
                                  :size-limit="3076"></erp-uploader-one-pic>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="60px">
        <template slot-scope="scope">
          <div>
            <el-button :disabled="scope.$index===0" size="small" type="text"
                       @click="clickMoveUpBtn(scope.row.id,scope.$index)">上移
            </el-button>
          </div>
          <div>
<!--            <el-button size="small" type="text" @click="clickDeleteBtn(scope.row.id,scope.$index)" :disabled="list.length===1">删除</el-button>-->
          </div>
          <div>
            <el-button :disabled="scope.$index===list.length-1" size="small" type="text"
                       @click="clickMoveDownBtn(scope.row.id,scope.$index)">下移
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!--弹窗文字编辑-->
    <el-dialog
      :title="textEditorTypeName"
      :visible.sync="textEditorShow"
      width="500px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <!--todo 不允许换行和中间输入空格-->
        <el-input v-if="!textEditorTrim" type="textarea" v-model="textEditorValue" show-word-limit
                  :maxlength="textEditorMaxLength"
                  @input="onTextEditorValueChange"
                  rows="10"></el-input>
        <el-input v-if="textEditorTrim" type="textarea" v-model.trim="textEditorValue" show-word-limit
                  :maxlength="textEditorMaxLength"
                  @input="onTextEditorValueChange"
                  rows="10"></el-input>

      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="textEditorShow=false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import erpUploaderOneVideo from "@/views/components/erpUploaderOneVideo";
import {msg_confirm} from "@/utils/ele_component";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";

export default {
  name: "productExperimentTable",
  directives: {
    elDragDialog
  },
  components: {erpUploaderOneVideo, erpUploaderOnePic},
  props: {
    list: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      // 文本弹窗编辑
      textEditorShow: false,
      textEditorValue: "",
      textEditorIndex: 0,
      textEditorType: "",
      textEditorMaxLength: 0,
      textEditorTypeName: "",
      textEditorTrim: false,
      // 产品简介弹窗编辑
      descriptionDialogShow: false,
      // 产品详情-实验简介
      experimentDescriptionDialogShow: false
    }
  },
  methods: {
    // 打开文本弹窗编辑
    openTextEditor(type, index, maxLength, typeName) {
      console.log(index)
      this.textEditorType = type
      this.textEditorValue = this.list[index][type]
      this.textEditorIndex=index
      this.textEditorMaxLength = maxLength
      this.textEditorTypeName = typeName
      this.textEditorShow = true
    },
    // 改变文本弹窗编辑值
    onTextEditorValueChange(v) {
      this.$set(this.list[this.textEditorIndex], this.textEditorType, v)
    },
    // 点击-上移按钮
    /**
     * params {{id 实验id,index 实验排序号}}
     */
    clickMoveUpBtn(id, index) {
      this.$emit("onClickExperimentMoveUpBtn", {id, index})
    },
    // 点击-下移按钮
    clickMoveDownBtn(id, index) {
      this.$emit("onClickExperimentMoveDownBtn", {id, index})
    },
    // 点击删除按钮
    async clickDeleteBtn(id, index) {
      if (await msg_confirm("确认是否删除该行？")) {
        this.$emit("onClickExperimentDeleteBtn", {id, index})
      }
    },
    // 图片上传成功
    onImgUploadSuccess(params, index) {
      this.$emit("onImgUploadSuccess", {
        params, index
      })
    },
    // 图片删除
    onImgDelete(params, index) {
      this.$emit("onImgDelete", {
        params, index
      })
    },
    // 视频上传成功
    onVideoUploadSuccess(params, index) {
      this.$emit("onImgUploadSuccess", {
        params, index
      })
    },
    // 图片删除
    onVideoDelete(params, index) {
      this.$emit("onImgDelete", {
        params, index
      })
    }
  }
}
</script>

<style scoped lang="scss">
// 输入框-点击弹窗大窗编辑
.input_box {
  background: #FFFFFF;
  line-height: initial;
  display: inline-block;
  padding: 5px 5px;
  border-radius: 5px;
  border: 1px solid #dcdfe6;
  width: 120px;
  cursor: pointer;
  height: 32px;
}
</style>
