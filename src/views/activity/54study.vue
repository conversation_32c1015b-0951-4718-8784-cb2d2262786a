<template>
  <div class="app-container">
    <!--通用信息-->
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>通用信息</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="commonConfig.cardShow=!commonConfig.cardShow">
          <i class="el-icon-arrow-up" v-show="commonConfig.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!commonConfig.cardShow"></i>
          {{ commonConfig.cardShow ? '收起' : '展开' }}
        </el-button>
        <el-button v-if="false" style="float:right" size="small" @click="clickSaveBtn(['commonConfig'])">保存</el-button>
      </div>
      <div class="card-container" v-show="commonConfig.cardShow">
        <a href="#" name="commonConfig"></a>
        <el-form ref="commonConfig" :model="commonConfig" :rules="commonConfig.formRules" label-width="120px">
          <el-form-item label="实验列表：" prop="experimentList">
            <!--设置表格-->
            <product-experiment-table
              @onClickExperimentMoveUpBtn="v=>experimentMethods().clickExperimentMoveUpBtn(null,null,v)"
              @onClickExperimentMoveDownBtn="v=>experimentMethods().clickExperimentMoveDownBtn(null,null,v)"
              @onClickExperimentDeleteBtn="v=>experimentMethods().clickExperimentDeleteBtn(null,null,v)"
              @onImgUploadSuccess="v=>experimentMethods().onImgUploadSuccess(null,null,v)"
              @onImgDelete="v=>experimentMethods().onImgDelete(null,null,v)"
              :list="commonConfig.experimentList"></product-experiment-table>
            <div class="flex flex-start" style="margin-top: 10px" v-if="commonConfig.experimentList.length<4">
              <el-button
                @click="experimentMethods().clickExperimentAddBtn()">新增实验
              </el-button>
            </div>
          </el-form-item>
          <el-form-item label="VR列表：" prop="vrList">
            <!--设置表格-->
            <product-vr-table
              @onClickVrMoveUpBtn="v=>vrMethods().clickVrMoveUpBtn(null,null,v)"
              @onClickVrMoveDownBtn="v=>vrMethods().clickVrMoveDownBtn(null,null,v)"
              @onClickVrDeleteBtn="v=>vrMethods().clickVrDeleteBtn(null,null,v)"
              @onImgUploadSuccess="v=>vrMethods().onImgUploadSuccess(null,null,v)"
              @onImgDelete="v=>vrMethods().onImgDelete(null,null,v)"
              :list="commonConfig.vrList"></product-vr-table>
            <el-button size="small" style="margin-top: 10px"
                       @click="vrMethods().clickVrAddBtn()">新建VR
            </el-button>
          </el-form-item>
          <el-form-item label="主办单位：" prop="organizerList">
            <el-input type="textarea" :rows="3" v-model="commonConfig.organizerList" placeholder="请输入主办单位"
                      style="width: 500px" maxlength="500" show-word-limit>
            </el-input>
          </el-form-item>
          <el-form-item label="协办单位：" prop="co_organizerList">
            <el-input type="textarea" :rows="3" v-model="commonConfig.co_organizerList" placeholder="请输入协办单位"
                      style="width: 500px" maxlength="500" show-word-limit>
            </el-input>
          </el-form-item>
          <el-form-item label="技术支持单位：" prop="co_organizerList">
            <el-input type="textarea" :rows="3" v-model="commonConfig.tech_organizerList" placeholder="请输入技术支持单位"
                      style="width: 500px" maxlength="500" show-word-limit>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <!--PC信息-->
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>PC信息</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="pcConfig.cardShow=!pcConfig.cardShow">
          <i class="el-icon-arrow-up" v-show="pcConfig.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!pcConfig.cardShow"></i>
          {{ pcConfig.cardShow ? '收起' : '展开' }}
        </el-button>
        <el-button v-if="false" style="float:right" size="small" @click="clickSaveBtn(['pcConfig'])">保存</el-button>
      </div>
      <div class="card-container" v-show="pcConfig.cardShow">
        <a href="#" name="pcConfig"></a>
        <el-form ref="commonConfig" :model="pcConfig" :rules="pcConfig.formRules" label-width="140px">
          <el-form-item label="banner大图：" prop="img_banner_big">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="pcConfig.img_banner_big"
                                    uploader-id="img_banner_big"
                                    :uploader-size="[200,100]" :pixel-limit="[1920,695]"
                                    :size-limit="3072"
                                    @uploadSuccess="data=>fileUpload(data,pcConfig)"
                                    @afterDelete="data=>fileDelete(data,pcConfig)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label="banner中间图：" prop="img_banner_middle">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="pcConfig.img_banner_middle"
                                    uploader-id="img_banner_middle"
                                    :uploader-size="[200,100]" :pixel-limit="[1150,695]"
                                    :size-limit="3072"
                                    @uploadSuccess="data=>fileUpload(data,pcConfig)"
                                    @afterDelete="data=>fileDelete(data,pcConfig)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label="更多实验图：" prop="img_pc_more_experiment">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="pcConfig.img_pc_more_experiment"
                                    uploader-id="img_pc_more_experiment"
                                    :uploader-size="[100,100]" :pixel-limit="[377,403]"
                                    :size-limit="3072"
                                    @uploadSuccess="data=>fileUpload(data,pcConfig)"
                                    @afterDelete="data=>fileDelete(data,pcConfig)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label="更多全景图：" prop="img_pc_more_vr">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="pcConfig.img_pc_more_vr"
                                    uploader-id="img_pc_more_vr"
                                    :uploader-size="[100,100]" :pixel-limit="[375,375]"
                                    :size-limit="3072"
                                    @uploadSuccess="data=>fileUpload(data,pcConfig)"
                                    @afterDelete="data=>fileDelete(data,pcConfig)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <!--H5信息-->
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>H5信息</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="h5Config.cardShow=!h5Config.cardShow">
          <i class="el-icon-arrow-up" v-show="h5Config.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!h5Config.cardShow"></i>
          {{ h5Config.cardShow ? '收起' : '展开' }}
        </el-button>
        <el-button v-if="false" style="float:right" size="small" @click="clickSaveBtn(['h5Config'])">保存</el-button>
      </div>
      <div class="card-container" v-show="h5Config.cardShow">
        <a href="#" name="h5Config"></a>
        <el-form ref="commonConfig" :model="h5Config" :rules="h5Config.formRules" label-width="140px">
          <el-form-item label="页面背景图：" prop="img_page_background">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="h5Config.img_page_background"
                                    uploader-id="img_page_background"
                                    :uploader-size="[100,200]" :pixel-limit="[750,1500]"
                                    :size-limit="3072"
                                    @uploadSuccess="data=>fileUpload(data,h5Config)"
                                    @afterDelete="data=>fileDelete(data,h5Config)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label="头图：" prop="img_page_head">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="h5Config.img_page_head"
                                    uploader-id="img_page_head"
                                    :uploader-size="[100,100]" :pixel-limit="[750,750]"
                                    :size-limit="3072"
                                    @uploadSuccess="data=>fileUpload(data,h5Config)"
                                    @afterDelete="data=>fileDelete(data,h5Config)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label="底图：" prop="img_page_bottom">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="h5Config.img_page_bottom"
                                    uploader-id="img_page_bottom"
                                    :uploader-size="[100,100]" :pixel-limit="[750,750]"
                                    :size-limit="3072"
                                    @uploadSuccess="data=>fileUpload(data,h5Config)"
                                    @afterDelete="data=>fileDelete(data,h5Config)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label="更多实验图：" prop="img_more_experiment">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="h5Config.img_more_experiment"
                                    uploader-id="img_more_experiment"
                                    :uploader-size="[200,100]" :pixel-limit="[630,280]"
                                    :size-limit="3072"
                                    @uploadSuccess="data=>fileUpload(data,h5Config)"
                                    @afterDelete="data=>fileDelete(data,h5Config)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <el-form-item label="更多全景图：" prop="img_more_vr">
            <div class="flex flex-start">
              <erp-uploader-one-pic style="margin-right: 30px" :img-in="h5Config.img_more_vr"
                                    uploader-id="img_more_vr"
                                    :uploader-size="[200,100]" :pixel-limit="[290,185]"
                                    :size-limit="3072"
                                    @uploadSuccess="data=>fileUpload(data,h5Config)"
                                    @afterDelete="data=>fileDelete(data,h5Config)"></erp-uploader-one-pic>
            </div>
          </el-form-item>
          <!--          <el-form-item label="实验弹窗更多系列：">-->
          <!--            <div>长征系列：直接显示官网的长征系列的名称和icon</div>-->
          <!--            <div>红岩系列：直接显示官网的红岩系列的名称和icon</div>-->
          <!--          </el-form-item>-->
        </el-form>
      </div>
    </el-card>
    <!--报名信息-->
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>申请列表</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="inputInfo.cardShow=!inputInfo.cardShow">
          <i class="el-icon-arrow-up" v-show="inputInfo.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!inputInfo.cardShow"></i>
          {{ inputInfo.cardShow ? '收起' : '展开' }}
        </el-button>
        <el-button style="float:right" size="small" @click="ExportMethods().clickExportBtn()">导出Excel</el-button>
      </div>
      <div class="card-container" v-show="inputInfo.cardShow">
        <!--列表-->
        <el-table :data="inputInfo.list" v-loading="inputInfo.loading" element-loading-text="加载中" border fit
                  highlight-current-row
                  style="width: 100%;">
          <el-table-column label="提交时间" align="center">
            <template slot-scope="scope">
              <span>{{ date_format(scope.row.createTime, "yyyy-MM-dd HH:mm") }}</span>
            </template>
          </el-table-column>
          <el-table-column label="姓名" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.info.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="学校" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.info.schoolName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="职务" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.info.duty }}</span>
            </template>
          </el-table-column>
          <el-table-column label="联系电话" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.info.phone }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实验人数" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.info.number }}</span>
            </template>
          </el-table-column>
        </el-table>
        <!--列表分页-->
        <div class="pagination-container">
          <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                         :current-page.sync="inputInfo.pages.number" :page-size.sync="inputInfo.pages.size"
                         layout="total,prev, pager, next,jumper,sizes" :total="inputInfo.pages.totalElements"
                         :page-sizes="[10,20,50,100,200,500]"
                         @size-change="(size)=>ListMethods().pageLimitChange(size)"
                         :page-count="inputInfo.pages.totalPages">
          </el-pagination>
        </div>
        <el-dialog
          title="选择要导出的日期"
          :visible.sync="exportInfo.dialog"
          width="700px"
          center
          v-el-drag-dialog>
          <div>
            <div style="text-align: center">
              <el-date-picker
                v-model="exportInfo.exportDate"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00','23:59:59']"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker>
            </div>
          </div>
          <div slot="footer" class="dialog-footer">
            <el-button type="default"
                       @click="exportInfo.dialog=false">取 消
            </el-button>
            <el-button type="success" @click="ExportMethods().clickDateExport()">导 出</el-button>
          </div>
        </el-dialog>
      </div>
    </el-card>
    <!--按钮组-->
    <div class="bottom-button-container flex flex-center">
      <el-button type="primary" @click="clickSaveBtn()">保 存</el-button>
      <el-button @click="clickCancelBtn">取 消</el-button>
    </div>
  </div>
</template>

<script>
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import productExperimentTable from "./components/productExperimentTable";
import productVrTable from "./components/productVrTable";
import $ from "jquery";
import {msg_confirm, msg_confirm_choose, msg_err, msg_success} from "@/utils/ele_component";
import {ConfigModel} from "@/model/erp/ConfigModel";
import {CONFIG_NAME_ACTIVITY, CONFIG_NAME_OFFICIAL_WEB} from "@/model/ConfigModel";
import Sortable from "sortablejs";
import {VEAModel} from "@/model/VEAModel";
import {FeedbackModel} from "@/model/erp/FeedbackModel";
import {date_format} from "@/utils/common";
import {excel_export_from_json} from "@/utils/excel";
import {CommonInfoModel} from "@/model/erp/CommonInfoModel";
import { mapGetters } from 'vuex'
import elDragDialog from "@/directive/el-drag-dialog";

export default {
  name: "activity54study",
  components: {erpUploaderOnePic, productExperimentTable, productVrTable},
  computed: {
    ...mapGetters([
      'roles'
    ])
  },
  directives: {
    elDragDialog
  },
  data() {
    return {
      date_format: date_format,
      study54Config: {},
      commonConfig: {
        cardShow: false,
        organizerList: "",
        co_organizerList: "",
        tech_organizerList: "",
        experimentList: [],
        vrList: [],
      },
      pcConfig: {
        cardShow: false,
        img_banner_big: "",
        img_banner_middle: "",
        img_pc_more_experiment: "",
        img_pc_more_vr: ""
      },
      h5Config: {
        cardShow: false,
        img_page_background: "",
        img_page_head: "",
        img_page_bottom: "",
        img_more_experiment: "",
        img_more_vr: ""
      },
      inputInfo: {
        cardShow: true,
        list: [],
        loading: false,
        query: {},
        pages: {
          size: 20
        },
      },
      exportInfo: {
        dialog: false,
        exportDate: ""
      }
    }
  },
  mounted() {
    // 读取配置信息
    this.getConfig()
    // 底部按钮组大小监听
    VEAModel.bottomButtonContainerWidthChange()
    // 获取列表
    this.ListMethods().getList(1, this.inputInfo.pages.size, this.inputInfo.query)
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.inputInfo.loading = true;
          [$this.inputInfo.list, $this.inputInfo.pages] = await CommonInfoModel.getPageList(page - 1, size, "", query)
          $this.inputInfo.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.inputInfo.pages.size, $this.inputInfo.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.inputInfo.pages.page, size, $this.inputInfo.query)
        },
        // 初始化筛选列表
        async initFilter() {

        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.inputInfo.query = query
          this.getList(0, $this.inputInfo.pages.size, $this.inputInfo.query)
        },
        // 导出excel
        async exportExcel() {
          // 生成查询参数
          let query = $this.inputInfo.query
          // let list = (await CommonInfoModel.getList(query)).data
          let [list,] = await CommonInfoModel.getPageList(0, 100000, "", query)
          if (list.length > 0) {
            // map reduce生成arr
            function formatJson(filterVal, jsonData) {
              return jsonData.map(v => filterVal.map(j => {
                let value = '';
                switch (j) {
                  case "name":
                    value = v["info"]["name"];
                    break;
                  case "schoolName":
                    value = v["info"]["schoolName"];
                    break;
                  case "duty":
                    value = v["info"]["duty"];
                    break;
                  case "phone":
                    value = v["info"]["phone"];
                    break;
                  case "number":
                    value = v["info"]["number"];
                    break;
                  case "createTime":
                    value = date_format(v["createTime"], "yyyy-MM-dd HH:mm");
                    break;
                  default:
                    value = v[j]
                }
                return value
              }))
            }

            // todo excel中渲染富文本内容
            const header = ['姓名', '学校', "职务", "联系电话",'实验人数','提交时间'];
            const filter = ["name", "schoolName", "duty", "phone",'number','createTime'];
            // 导出excel
            excel_export_from_json(list, header, filter, formatJson, "党史活动-申请列表-"+date_format(new Date(),"yyyy-MM-dd HH:mm"))
          } else {
            msg_err('要导出的列表为空')
          }
        },
      }
    },
    // 读取配置
    async getConfig() {
      let data = await ConfigModel.getEdit(CONFIG_NAME_ACTIVITY, "54study")
      let study54Config = JSON.parse(data)
      this.study54Config = JSON.parse(data)
      // 设置各项配置
      this.commonConfig = Object.assign(this.commonConfig, study54Config.commonConfig)
      this.pcConfig = Object.assign(this.pcConfig, study54Config.pcConfig)
      this.h5Config = Object.assign(this.h5Config, study54Config.h5Config)
    },
    // 导出Methods
    ExportMethods() {
      let $this = this;
      return {
        // 点击导出按钮
        async clickExportBtn() {
          msg_confirm_choose("选择全部导出或选择日期", "导出EXCEL", "全部导出", "选择导出日期").then(res => {
            if (res) {
              if (res === "left") {
                $this.inputInfo.query = {}
                this.exportExcel()
              }
              if (res === "right") {
                $this.exportInfo.dialog = true
              }
            }
          })
        },
        // 点击按时间导出按钮
        clickDateExport() {
          if ($this.exportInfo.exportDate.length === 2) {
            const start = new Date($this.exportInfo.exportDate[0]).getTime()
            const end = new Date($this.exportInfo.exportDate[1]).getTime()
            const query = {
              '$and': [
                {'createTime': {'$gte': start}},
                {'createTime': {'$lte': end}}
              ]
            }
            $this.inputInfo.query = query
            this.exportExcel()
          } else {
            msg_err("请先选择导出时间范围")
          }
        },
        // 导出excel
        async exportExcel() {
          // 生成查询参数
          let query = $this.inputInfo.query
          let list = await CommonInfoModel.getList(query)
          if (list.length > 0) {
            // map reduce生成arr
            function formatJson(filterVal, jsonData) {
              return jsonData.map(v => filterVal.map(j => {
                let value = '';
                switch (j) {
                  case "name":
                    value = v["info"]["name"];
                    break;
                  case "schoolName":
                    value = v["info"]["schoolName"];
                    break;
                  case "duty":
                    value = v["info"]["duty"];
                    break;
                  case "phone":
                    value = v["info"]["phone"];
                    break;
                  case "number":
                    value = v["info"]["number"];
                    break;
                  case "createTime":
                    value = date_format(v["createTime"], "yyyy-MM-dd HH:mm");
                    break;
                  default:
                    value = v[j]
                }
                return value
              }))
            }

            const header = ['姓名', '学校', "职务", "联系电话",'实验人数', '提交时间'];
            const filter = ["name", "schoolName", "duty", "phone",'number', 'createTime'];
            // 导出excel
            excel_export_from_json(list, header, filter, formatJson, "党史活动-申请列表-" + date_format(new Date(), "yyyy-MM-dd HH:mm"))
          } else {
            msg_err('要导出的列表为空')
          }
        },
      }
    },
    // 通用-文件上传成功
    fileUpload(params, target, formRef) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 通用-文件删除
    fileDelete(params, target, formRef) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 点击保存按钮
    async clickSaveBtn(floors) {
      if (!floors) {
        floors = ['commonConfig', 'pcConfig', 'h5Config']
      }
      let saveButtonChoose = await msg_confirm_choose("内容有更改，是否提交？", "确认保存", "我再想想", '确认提交')
      if (saveButtonChoose === "right") {
        let study54Config = this.study54Config

        // delete study54Config.commonConfig.formRules
        // delete study54Config.commonConfig.cardShow
        study54Config.commonConfig = JSON.parse(JSON.stringify(this.commonConfig))
        // delete study54Config.pcConfig.formRules
        // delete study54Config.pcConfig.cardShow
        study54Config.pcConfig = JSON.parse(JSON.stringify(this.pcConfig))
        // delete study54Config.h5Config.formRules
        // delete study54Config.h5Config.cardShow
        study54Config.h5Config = JSON.parse(JSON.stringify(this.h5Config))

        // 保存接口
        if (await ConfigModel.editEdit(CONFIG_NAME_ACTIVITY, "54study", study54Config)) {
          msg_success("保存成功")
          setTimeout(() => {
            window.location.href = '#/activity/54study';
            window.location.reload()
          }, 1000)
        }


      }
      // 每个项目的合法性检测
      this.errAJumped = false
    },
    // 点击取消按钮
    async clickCancelBtn() {
      if (await msg_confirm_choose("是否取消更改？", "取消更改", "我再想想", '确认取消') === "right") {
        msg_success("取消成功")
        setTimeout(() => {
          window.location.href = '#/activity/54study';
          window.location.reload()
        }, 1000)
      }
    },
    // 实验相关方法
    experimentMethods() {
      let $this = this;
      return {
        // 检测-实验填写合法性检测
        validateExperiment(experiment, experimentIndex, callback, categoryIndex, seriesIndex) {
          let rules = [
            {key: 'name', name: "产品名称"},
            {key: 'subName', name: "副标题"},
            {key: 'description', name: "产品简介"},
            {key: 'experimentDescription', name: "实验简介"},
            {key: 'product_bg', name: "产品封面"},
            {key: 'product_icon', name: "产品Icon"},
            // {key: 'product_iconLeft', name: "左上角Icon"},
            {key: 'product_info_bg', name: "产品详情-头图"},
            // {key: 'product_video', name: "产品详情-视频"},
            {key: 'product_info_img_1', name: "产品详情-图片介绍第1张"},
            {key: 'product_info_img_2', name: "产品详情-图片介绍第2张"},
            {key: 'product_info_img_3', name: "产品详情-图片介绍第3张"},
            {key: 'product_info_img_4', name: "产品详情-图片介绍第4张"},
          ]
          rules.forEach((rule, index) => {
            if (!experiment[rule.key]) {
              callback(new Error(`第${experimentIndex + 1}行未设置${rule.name}`))
            }
          })
        },
        // 工厂-新建实验
        factoryCreateNewExperiment() {
          return {
            id: new Date().getTime() // 随机id
          }
        },
        // 点击-实验-新建按钮
        clickExperimentAddBtn(seriesIndex, categoryIndex) {
          if ($this.commonConfig.experimentList.length < 4) {// 最多4个
            $this.commonConfig.experimentList.push(this.factoryCreateNewExperiment())
          }
        },
        // 点击-实验-删除按钮
        clickExperimentDeleteBtn(categoryIndex, seriesIndex, experimentParams) {
          let experimentIndex = experimentParams.index
          $this.commonConfig.experimentList.splice(experimentIndex, 1)
        },
        // 点击-实验-上移按钮
        clickExperimentMoveUpBtn(categoryIndex, seriesIndex, experimentParams) {
          let experimentIndex = experimentParams.index
          let arr = []
          arr = $this.commonConfig.experimentList
          arr[experimentIndex] = arr.splice(experimentIndex - 1, 1, arr[experimentIndex])[0]
          $this.$set($this.commonConfig.experimentList, "list", arr)
        },
        // 点击-实验-下移按钮
        clickExperimentMoveDownBtn(categoryIndex, seriesIndex, experimentParams) {
          let experimentIndex = experimentParams.index
          let arr = []
          arr = $this.commonConfig.experimentList
          arr[experimentIndex] = arr.splice(experimentIndex + 1, 1, arr[experimentIndex])[0]
          $this.commonConfig.experimentList = arr
          $this.$set($this.commonConfig.experimentList, "list", arr)

        },
        // 监听-图片上传成功
        onImgUploadSuccess(categoryIndex, seriesIndex, experimentParams) {
          let experimentIndex = experimentParams.index // 实验编号
          let imgSrc = experimentParams.params[1] // 成功后图片地址
          // 判断上传的什么图片
          let uploaderId = experimentParams.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          $this.$set($this.commonConfig.experimentList[experimentIndex], uploaderKey, imgSrc)
        },
        // 监听-图片删除
        onImgDelete(categoryIndex, seriesIndex, experimentParams) {
          let experimentIndex = experimentParams.index // 实验编号
          // 判断上传的什么图片
          let uploaderId = experimentParams.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          $this.$set($this.commonConfig.experimentList[experimentIndex], uploaderKey, "")
        }
      }
    },
    // 全景相关方法
    vrMethods() {
      let $this = this;
      return {
        // 清除lists验证的提示
        clearListErrTip() {
          setTimeout(() => {
            if ($this.vrInfo.validateJump) {
              let errTop = document.querySelector("#examInfo").offsetTop - (document.body.offsetHeight / 2)
              document.body.scrollTop = errTop;
              document.documentElement.scrollTop = errTop;
              $this.vrInfo.validateJump = false
            }
            $("#vrInfo .lists-form-item").removeClass('is-error')
          }, 200)

        },
        // 检测-实验填写合法性检测
        validateVr(vr, vrIndex, callback, seriesIndex) {
          let rules = [
            {key: 'name', name: "产品名称"},
            {key: 'description', name: "产品简介"},
            {key: 'link', name: "链接地址"},
            {key: 'product_bg', name: "产品封面"},
          ]
          if (seriesIndex != null) {
            rules.forEach((rule, index) => {
              if (!vr[rule.key]) {
                this.clearListErrTip()
                callback(new Error(`第${seriesIndex + 1}个系列，第${vrIndex + 1}行未设置${rule.name}`))
              }
            })
          } else {
            rules.forEach((rule, index) => {
              if (!vr[rule.key]) {
                this.clearListErrTip()
                callback(new Error(`第${vrIndex + 1}行未设置${rule.name}`))
              }
            })
          }
        },
        // 工厂-新建全景
        factoryCreateNewVr() {
          return {
            id: new Date().getTime() // 随机id
          }
        },
        // 点击-全景-新建按钮
        clickVrAddBtn() {
          $this.commonConfig.vrList.push(this.factoryCreateNewVr())
        },
        // 点击-全景-删除按钮
        clickVrDeleteBtn(categoryIndex, seriesIndex, vrParams) {
          let vrIndex = vrParams.index
          $this.commonConfig.vrList.splice(vrIndex, 1)
        },
        // 点击-产品-上移按钮
        clickVrMoveUpBtn(categoryIndex, seriesIndex, vrParams) {
          let vrIndex = vrParams.index
          let arr = []
          arr = $this.commonConfig.vrList
          arr[vrIndex] = arr.splice(vrIndex - 1, 1, arr[vrIndex])[0]
          $this.$set($this.commonConfig, "vrList", arr)
        },
        // 点击-产品-下移按钮
        clickVrMoveDownBtn(categoryIndex, seriesIndex, vrParams) {
          let vrIndex = vrParams.index
          let arr = []
          arr = $this.commonConfig.vrList
          arr[vrIndex] = arr.splice(vrIndex + 1, 1, arr[vrIndex])[0]
          $this.$set($this.commonConfig, "vrList", arr)
        },
        // 监听-图片上传成功
        onImgUploadSuccess(categoryIndex, seriesIndex, vrParams) {
          let vrIndex = vrParams.index // 全景编号
          let imgSrc = vrParams.params[1] // 成功后图片地址
          // 判断上传的什么图片
          let uploaderId = vrParams.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          $this.$set($this.commonConfig.vrList[vrIndex], uploaderKey, imgSrc)
        },
        // 监听-图片删除
        onImgDelete(categoryIndex, seriesIndex, vrParams) {
          let vrIndex = vrParams.index // 全景编号
          // 判断上传的什么图片
          let uploaderId = vrParams.params[0] // 上传id
          let uploaderKey = uploaderId.split("__")[0] // 以__符号分割
          $this.$set($this.commonConfig.vrList[vrIndex], uploaderKey, "")
        }
      }
    },
  }
}
</script>

<style scoped lang="scss">

</style>
