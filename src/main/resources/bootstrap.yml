eureka:
  instance:
    hostname: cdzyhd-formal-1
  client:
    registerWithEureka: false
    fetchRegistry: true
    serviceUrl:
      defaultZone: http://cdzyhd-formal-1:8800/eureka/

server:
  port: 8900

spring:
  # cloud相关
  cloud:
    config:
      name: erp-master
      label: master
      discovery:
        enabled: true
        serviceId: microservice-config
  application:
    name: erp

feign:
  client:
    config:
      default:  # 默认配置，适用于所有Feign客户端
        connectTimeout: 50000  # 连接超时时间，单位毫秒
        readTimeout: 300000    # 读取超时时间，单位毫秒

ribbon:
  ReadTimeout: 60000
  ConnectTimeout: 400000

# JBS配置
jbs:
  checkIp: true   # 是否启用IP白名单检查