package com.cdzyhd.erp.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FeignConfiguration {

    /**
     * Contract feign的默认契约 有这个才能使用@RequestLine和@Feign的其他如Headers之类的注解
     * class 上不能有@Configuration或@Component注解 否则所有feign都会继承
     * @return
     */
//    @Bean
//    public Contract feignContract() {
//        return new feign.Contract.Default();
//    }

    /**
     * 自定义拦截器
     * @return
     */
    @Bean
    public FeignInterceptor basicAuthRequestInterceptor() {
        return new FeignInterceptor();
    }

}

