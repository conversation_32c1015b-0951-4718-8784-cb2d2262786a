package com.cdzyhd.erp.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

// 通用配置项
@Data
@Configuration
public class CommonConfig {
    // redis配置项
    @Value("${common.platformId}")
    public String platformId;

    // 默认token过期时间天数
    public Integer tokenExpireDayDefault = 1;

    // JBS IP白名单检查开关
    @Value("${jbs.checkIp:false}")
    public Boolean jbsCheckIp;
}
