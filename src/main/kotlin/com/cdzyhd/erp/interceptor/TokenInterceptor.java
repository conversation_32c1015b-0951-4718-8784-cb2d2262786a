package com.cdzyhd.erp.interceptor;


import com.cdzyhd.base.common.interceptor.TokenInterceptorBase;
import com.cdzyhd.base.common.util.JsonWebTokenManagerBase;
import com.cdzyhd.erp.a.StaticBean;
import com.cdzyhd.erp.annotation.NeedAdminToken;
import com.cdzyhd.erp.annotation.NeedPackageSystemSchoolToken;
import com.cdzyhd.erp.annotation.NeedToken;
import com.cdzyhd.erp.model.AdminUserModel;
import com.cdzyhd.erp.model.packageSystem.PackageSystemTokenModel;
import io.jsonwebtoken.Claims;
import org.springframework.web.method.HandlerMethod;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Method;

public class TokenInterceptor extends TokenInterceptorBase {

    private JsonWebTokenManagerBase jsonWebTokenManager = new JsonWebTokenManagerBase();

    /**
     * 在请求处理之前进行调用（Controller方法调用之前）
     *
     * @param req
     * @param resp
     * @param handler
     * @return
     * @throws Exception
     */
    @Override
    public boolean preHandle(HttpServletRequest req, HttpServletResponse resp, Object handler) throws Exception {
        // 如果不是映射到方法直接通过
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        // 判断是否有needtoken注解
        NeedToken methodNeedToken = method.getAnnotation(NeedToken.class);
        // 判断是否有needadmintoken注解
        NeedAdminToken methodNeedAdminToken = method.getAnnotation(NeedAdminToken.class);
        // 判定是否有needpackagesystemschooltoken注解，用于包管理系统学校端验真
        NeedPackageSystemSchoolToken methodNeedPackageSystemSchoolToken = method.getAnnotation(NeedPackageSystemSchoolToken.class);

        // 如果前台token检测
//        if (methodNeedToken != null) {
//            String authorization = req.getHeader("Authorization");
//            // token格式检测
//            if (!tokenBaseCheck(authorization, resp)) {
//                return false;
//            }
//            String token = authorization.split(" ")[1];
//            Claims claims = jsonWebTokenManager.parse_token(token);
//            TokenModel tokenModel = new TokenModel();
//            // 使用基础用户系统检验
//            if (!tokenModel.tokenCheck(token)) {
//                resp.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Token Check Fail");
//                return false;
//            }
//            // 将uid存入request中
//            String uid = claims.getId();
//            req.setAttribute("uid", uid);
//            return true;
//        }
        // 如果后台token检测
        if (methodNeedAdminToken != null) {
            String authorization = req.getHeader("Authorization");
            // token格式检测
            String token = authorization.split(" ")[1];
            if (!tokenBaseCheck(authorization, resp)) {
                return false;
            }
            Claims claims = jsonWebTokenManager.parse_token(token);
            // 获取前台请求的平台id
            String tokenPlatform = claims.getIssuer();
            // 同平台token
            if (tokenPlatform.equals(StaticBean.commonConfig.platformId)) {
                if (tokenOnLineCheck(req, resp, token, claims)) {
                    // 获取和设置本平台的相关信息
                    String adminUserId = claims.getId();
                    req.setAttribute("adminUserId", adminUserId);
                    return true;
                }
            } else {// 不是该平台签发的token
                return tokenOnLineCheck(req, resp, token, claims);
            }
        }
        // 如果包管理系统学校端token检测
        if (methodNeedPackageSystemSchoolToken != null) {
            // token格式检测
            String authorization = req.getHeader("Authorization");
            if (!tokenBaseCheck(authorization, resp)) {
                return false;
            }
            String token = authorization.split(" ")[1];
            Claims claims = jsonWebTokenManager.parse_token(token);
            return tokenOnLineCheckOfPackageSystemSchool(req, resp, token,claims);
        }
        return true;
    }

    // token检测-管理员
    private boolean tokenOnLineCheck(HttpServletRequest req, HttpServletResponse resp, String token, Claims claims) throws IOException {
        if (!new AdminUserModel().tokenCheck(token,claims)) {
            resp.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Admin Token Check Fail");
            return false;
        }
        return true;
    }

    // token检测-包管理系统学校端
    private boolean tokenOnLineCheckOfPackageSystemSchool(HttpServletRequest req, HttpServletResponse resp, String token, Claims claims) throws IOException {
        if (!new PackageSystemTokenModel().checkSchoolToken(token)) {
            resp.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Package System School Token Check Fail");
            return false;
        }
        String adminUserId = claims.getId();
        req.setAttribute("packageSystemSchoolId", adminUserId);
        return true;
    }

    /**
     * token数据格式验证
     *
     * @param resp
     * @return
     * @throws Exception
     */
    private boolean tokenBaseCheck(String authorization, HttpServletResponse resp) throws Exception {
        if (authorization == null) {
            resp.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Token Authorization Header Required");
            return false;
        }
        if (!authorization.startsWith("Bearer ")) {
            resp.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Invalid Authorization Type");
            return false;
        }
        String token = authorization.split(" ")[1];
        if (token.equals("null")) {
            resp.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Token Needed");
            return false;
        }
        // 验证JWT数据
        Claims claims;
        try {
            claims = jsonWebTokenManager.parse_token(token);
        } catch (Exception e) {
            resp.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Token Format Error");
            return false;
        }

        if (claims == null) {
            resp.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Token Expired");
            return false;
        }
        return true;
    }

}