package com.cdzyhd.erp.model.packageSystem

import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.exception.CommonException
import com.cdzyhd.erp.model.JWTModel
import io.jsonwebtoken.Claims

class PackageSystemTokenModel {
    // 生成token存储key
    private fun genTokenKey(roleName: String, token: String): String {
        return "packageSystem~$roleName~$token"
    }

    // 学校用户-生成token并保存到redis
    fun genSchoolTokenAndSave(
        userId: String,
        expireDay: Int
    ): String {
        try {
            val roleName="school"
            // 生成token
            val jwtModel = JWTModel()
            jwtModel.expireDays = expireDay
            val token = jwtModel.gen_token("packageSystem", userId, userId, roleName)
            // 存入redis 记录格式
            val tokenName = genTokenKey(roleName, token)
            StaticBean.redisService.set(tokenName, userId, jwtModel.expireDays * 24 * 3600L)
            return token
        } catch (e: Exception) {
            throw CommonException("000001", "生成token失败")
        }
    }

    // 学校用户-token检测
    fun checkSchoolToken(token: String): Boolean {
        val roleName="school"
        val jwtModel = JWTModel()
        val claims: Claims = jwtModel.parse_token(token)
        return if (claims.isEmpty()) {
            false
        } else {
            StaticBean.redisService.hasKey(genTokenKey(roleName, token))
        }
    }
}