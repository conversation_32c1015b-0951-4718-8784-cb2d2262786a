package com.cdzyhd.erp.model.jbs

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.entity.jbs.JbsOrderEntity
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.util.StringUtils
import java.util.*

class JbsOrderModel {

    private val redisService = StaticBean.redisService

    private val commonConfig=StaticBean.commonConfig   

    /**
     * 创建订单
     * @param orderInfo 订单信息
     * @param clientIp 客户端IP
     * @return OutResponse
     */
    fun createOrder(orderInfo: JSONObject, clientIp: String): OutResponse<Any> {
        try {
            // IP白名单校验，仅在启用配置时进行检查
            if (commonConfig.jbsCheckIp) {
                val whiteListKey = "jbs_order:post_ip_whitelist"
                val ipWhitelist = redisService.get(whiteListKey)?.toString()?.split(",") ?: emptyList()
                if (ipWhitelist.isNotEmpty() && !ipWhitelist.contains(clientIp)) {
                    return OutResponse("999999", "IP地址不在白名单内", null)
                }
            }

            // 必填参数校验
            if (!orderInfo.containsKey("organId") || StringUtils.isEmpty(orderInfo.getString("organId"))) {
                return OutResponse<Any>("999999", "机构ID不能为空", null)
            }
            if (!orderInfo.containsKey("scriptId") || StringUtils.isEmpty(orderInfo.getString("scriptId"))) {
                return OutResponse<Any>("999999", "剧本ID不能为空", null)
            }
            if (!orderInfo.containsKey("appId") || StringUtils.isEmpty(orderInfo.getString("appId"))) {
                return OutResponse<Any>("999999", "应用ID不能为空", null)
            }
            if (!orderInfo.containsKey("userId") || StringUtils.isEmpty(orderInfo.getString("userId"))) {
                return OutResponse<Any>("999999", "用户ID不能为空", null)
            }
            if (!orderInfo.containsKey("totalAmount")) {
                return OutResponse<Any>("999999", "订单总金额不能为空", null)
            }

            // 金额校验（转换为分）
            val totalAmount = orderInfo.getLong("totalAmount")
            if (totalAmount <= 0) {
                return OutResponse<Any>("999999", "订单总金额必须大于0", null)
            }

            // 状态校验
            val status = orderInfo.getInteger("status") ?: 0
            if (status !in listOf(0, 1, 2, 3, 4)) {
                return OutResponse<Any>("999999", "无效的订单状态值", null)
            }

            // 支付方式校验（如果提供）
            orderInfo.getInteger("payType")?.let {
                if (it !in listOf(1, 2)) {
                    return OutResponse<Any>("999999", "无效的支付方式，只支持：1-微信，2-支付宝", null)
                }
            }

            // 创建订单实体
            val order = JbsOrderEntity().apply {
                // 基础信息
                this.organId = orderInfo.getString("organId")
                this.scriptId = orderInfo.getString("scriptId")
                this.appId = orderInfo.getString("appId")
                this.userId = orderInfo.getString("userId")
                this.status = status
                this.totalAmount = totalAmount
                
                // 可选字段
                orderInfo.getLong("payAmount")?.let { this.payAmount = it }
                orderInfo.getLong("payTime")?.let { this.payTime = it }
                orderInfo.getInteger("payType")?.let { this.payType = it }
                orderInfo.getString("remark")?.let { this.remark = it }
                orderInfo.getString("paymentNo")?.let { this.paymentNo = it }
                
                // 其他信息
                orderInfo.getJSONObject("otherInfo")?.let { this.otherInfo = it }
                
                // 系统字段
                this.createTime = Date().time
                this.updateTime = Date().time
                this.deleted = 0
            }

            // 保存订单
            StaticBean.jbsOrderRepository.save(order)
            return OutResponse<Any>("000000", "订单创建成功", null)

        } catch (e: Exception) {
            return OutResponse<Any>("999999", "订单创建失败: ${e.message}", null)
        }
    }
}