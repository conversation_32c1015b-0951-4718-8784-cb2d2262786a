package com.cdzyhd.erp.model

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.a.StaticBean.userFeign
import io.jsonwebtoken.Claims

class AdminUserModel {

    // token检测
    fun tokenCheck(token: String, tokenClaims: Claims): Boolean {
        val checkData: JSONObject
        val platformId = tokenClaims.issuer
        if (platformId.equals(StaticBean.commonConfig.platformId)) {
            checkData = StaticBean.userFeign.tokenAdminCheck(StaticBean.commonConfig.platformId, token)
        } else {
            checkData = StaticBean.userFeign.tokenCheck(platformId, tokenClaims.audience, token)
        }
        return checkData["data"] as Boolean
    }

    // Pico管理系统-重置经销商
    fun resetDealerPassword(dealerId: String): JSONObject {
        val dealerResult = StaticBean.userFeign.getAdminUserInfo("picoSystem", dealerId)
        val dealer = dealerResult["data"] as LinkedHashMap<String, Any>
        dealer["password"] = "e5e234465a4319ca7fe67732a6094df53c9d927c5514554a" // md5加盐
        val dealerJsonString = JSON.toJSONString(dealer)
        val resetResult = userFeign.editAdminUser(dealerJsonString)
        val phone = dealer["phoneNumber"] as String
        if (resetResult["code"] == "000000") {
            // 发送重置密码短信验证码
            SmsModel().sendPicoDealerResetPasswordSms(phone)
            return resetResult
        } else {
            return resetResult
        }
    }
}