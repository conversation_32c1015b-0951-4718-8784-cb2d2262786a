package com.cdzyhd.erp.model

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.erp.a.StaticBean.messageFeign


// 短信发送model
class SmsModel {
    /**
     * 发送短信（阿里云）
     * @param phone        短信接收号码,支持以逗号分隔的形式进行批量调用，批量上限为1000个手机号码,
     * @param templateCode  短信模板ID，发送国际/港澳台消息时，请使用国际/港澳台短信模版
     * @param templateParam 短信模板变量替换JSON串,友情提示:如果JSON中需要带换行符,请参照标准的JSON协议。
     */
    @Throws(Exception::class)
    fun sendSMS(phone: String, templateParam: JSONObject, type: String) {
        var sendBody = ""
        when (type) {
            "Pico经销商重置密码通知" -> {
                sendBody = """
                    {
                        "phone":"$phone",
                        "signName":"智云鸿道",
                        "templateCode":"SMS_223543353",
                        "templateParam":$templateParam
                    }
                """.trimIndent()
            }
        }
        messageFeign.smsSend(sendBody)
    }

    // Pico管理系统-重置密码短信提示
    fun sendPicoDealerResetPasswordSms(phone: String) {
        val templateJson = JSONObject()
        templateJson["password"] = "123456"
        this.sendSMS(phone, templateJson, "Pico经销商重置密码通知")
    }
}
