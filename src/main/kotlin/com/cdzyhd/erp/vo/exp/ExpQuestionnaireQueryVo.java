package com.cdzyhd.erp.vo.exp;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class ExpQuestionnaireQueryVo {
    // 选择的学校id
    public String schoolId;

    // 教学班内的学生account列表
    public JSONArray accountList;

    // 选择的学生学号
    public String account;

    // 选择的时间范围-开始时间
    public Long startTime;

    // 选择的时间范围-结束时间
    public Long endTime;

}