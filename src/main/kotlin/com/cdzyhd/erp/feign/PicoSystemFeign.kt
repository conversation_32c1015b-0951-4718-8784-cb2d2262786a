package com.cdzyhd.erp.feign

import com.alibaba.fastjson.JSONObject
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.stereotype.Component
import com.cdzyhd.base.common.feign.FileFeignCommon
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestParam

@Component
@FeignClient(name = "picoSystem")
interface PicoSystemFeign {
    // 获取设备列表
    @PostMapping("v1/equipment/list")
    fun getEquipmentList(@RequestBody body: String): JSONObject
}