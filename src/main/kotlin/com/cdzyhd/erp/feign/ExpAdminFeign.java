package com.cdzyhd.erp.feign;

import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.ResponseDataVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@Component
@FeignClient(value = "exp-admin")
public interface ExpAdminFeign {

    // 获取实验列表
    @RequestMapping("/admin/experiment/list")
    JSONObject getExperimentList(@RequestParam Map<String, Object> params);

    // 获取某个实验的信息
    @RequestMapping("/admin/experiment/info/{id}")
    JSONObject getExperimentInfo(@PathVariable("id") String id);

    // 通过学校名称获取学校信息
    @RequestMapping("/admin/school/getByName/{schoolname}")
    JSONObject getSchoolInfoByName(@PathVariable("schoolname") String schoolname);
}
