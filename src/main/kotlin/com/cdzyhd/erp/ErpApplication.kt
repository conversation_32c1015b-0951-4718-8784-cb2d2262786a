package com.cdzyhd.erp

import com.cdzyhd.base.common.util.PasswordUtil
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
import org.springframework.boot.runApplication
import org.springframework.cloud.client.discovery.EnableDiscoveryClient
import org.springframework.cloud.openfeign.EnableFeignClients

@EnableDiscoveryClient
@EnableFeignClients
@SpringBootApplication()
class ErpApplication

fun main(args: Array<String>) {
    runApplication<ErpApplication>(*args)
}
