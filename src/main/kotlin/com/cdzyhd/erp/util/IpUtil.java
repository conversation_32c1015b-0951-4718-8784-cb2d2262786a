package com.cdzyhd.erp.util;

import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.io.IoUtil;
import com.cdzyhd.erp.a.StaticBean;
import org.lionsoul.ip2region.DataBlock;
import org.lionsoul.ip2region.DbConfig;
import org.lionsoul.ip2region.DbMakerConfigException;
import org.lionsoul.ip2region.DbSearcher;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.util.Date;

public class IpUtil {
    /**
     * 根据IP地址获取城市
     * https://gitee.com/lionsoul/ip2region
     *
     * @param ip
     * @return
     */
    public static String getCityInfo(String ip) throws Exception {
        String ipAddress = "";
        String ipKey = "ipAddressCache_" + ip;
        if (StaticBean.redisService.hasKey(ipKey)) {
            ipAddress = StaticBean.redisService.get(ipKey).toString();
            return ipAddress;
        }
        //读取jar包内的配置文件信息
        DbSearcher searcher = null;
        Method method = null;
        InputStream inputStream = null;
        try {
            ClassPathResource resource = new ClassPathResource("/ip2region.db");
            inputStream = resource.getInputStream();
            byte[] dbBinStr = IoUtil.readBytes(inputStream);
            DbConfig config = new DbConfig();
            searcher = new DbSearcher(config, dbBinStr);
            // 查询算法memory，采用二进制方式初始化DBSearcher需要使用MEMORY_ALGORITYM，
            // 否则会抛出null。
            method = searcher.getClass().getMethod("memorySearch", String.class);
        } catch (IOException | DbMakerConfigException | IORuntimeException | NoSuchMethodException | SecurityException e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
        String info = ((DataBlock) method.invoke(searcher, ip)).getRegion();
        if (info.contains("内网")) {
            ipAddress = "内网IP";
        } else {
            String[] infos = info.split("\\|");
            if (infos.length == 5) {
                ipAddress = infos[2] + "_" + infos[3] + "__" + infos[4];
            } else {
                ipAddress = "未找到";
            }
        }
        // 存入redis缓存，一天时间
        StaticBean.redisService.set(ipKey, ipAddress, 86400);
        return ipAddress;
    }
}
