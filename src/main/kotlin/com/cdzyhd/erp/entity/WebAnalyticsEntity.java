package com.cdzyhd.erp.entity;

import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * 2023.4.4
 * 网站统计信息
 */
@Document(collection = "webAnalytics")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class WebAnalyticsEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    /**
     * id
     */
    public String webAnalyticsId = new SnowflakeIdWorker(1, 0).nextId();

    /**
     *
     */
    public Long createTime = System.currentTimeMillis();

    /**
     * 类型 进入网页、离开网页、通用事件
     */
    public String type;

    // 所属网站id
    public String webSiteId;

    // 所属日期 2022-03-30 格式 用于统计筛选，按天筛选
    public String day;

    /**
     * 信息
     */
    public JSONObject info = new JSONObject();

    // 额外信息
    public JSONObject extra = new JSONObject();
}