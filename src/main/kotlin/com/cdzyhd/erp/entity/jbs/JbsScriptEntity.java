package com.cdzyhd.erp.entity.jbs;

import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * <AUTHOR>
 * 剧本杀-剧本实体
 */
@Document(collection = "jbs_script")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class JbsScriptEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // 创建时间
    public Long createTime = new Date().getTime();

    // 更新时间
    public Long updateTime = new Date().getTime();

    // id
    public String jbsScriptId = new SnowflakeIdWorker(1, 0).nextId();

    // 剧本名称
    public String name;

    // 剧本备注
    public String remark;

    // 逻辑删除标识：0-未删除，1-已删除
    public Integer deleted = 0;

    // 其它信息
    public JSONObject otherInfo = new JSONObject();
} 