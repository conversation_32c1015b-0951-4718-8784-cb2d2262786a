package com.cdzyhd.erp.entity.officialWeb;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * 官网新闻试题
 */
@Document(collection = "official_web_ news")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class OfficialWebNewEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    /**
     * id
     */
    public String officialWebNewsId = new SnowflakeIdWorker(1, 0).nextId();

    /**
     *
     */
    public Long createTime = System.currentTimeMillis();

    // 标题
    public String title;

    // 头图链接地址
    public String headImgUrl;

    // 简介
    public String description;

    // 内容
    public String content;

    // 标签列表
    public JSONArray tagsArr;

    // 排序权重
    public Long sortOrder = System.currentTimeMillis();

    // 是否已推送到百度收录
    public Boolean postedUrl = false;

    // 所属网站 zyhd xhyj
    public String owner = "zyhd";
}