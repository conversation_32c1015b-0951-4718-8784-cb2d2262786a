package com.cdzyhd.erp.entity.packageSystem;

import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * 推送日志，记录学校端同步的日志信息
 * 学校端主动请求公司端，生成记录
 */
@Document(collection = "packageManager_packagePushLog")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class PackagePushLogEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    public String packagePushLogId = new SnowflakeIdWorker(1, 0).nextId();

    public Long createTime = System.currentTimeMillis();

    public JSONObject extraInfo = new JSONObject();

    // 所属推送id
    public String packagePushId;

    // 所属学校用户id
    public String packageSystemSchoolId;

    // 类型 accept 接受了推送，download 完成了下载
    public String type;

    // 消息
    public String message;

    // 设备id
    public String mac;

    // ip地址
    public String ip;
    
}