package com.cdzyhd.erp.entity.packageSystem;

import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * 客户端更新管理
 */
@Document(collection = "packageManager_clientUpdate")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class PackageClientUpdateEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String packageClientUpdateId = new SnowflakeIdWorker(1, 0).nextId();

    public Long createTime = System.currentTimeMillis();

    public JSONObject extraInfo = new JSONObject();

    // 版本号 (格式: 25052901)
    public Long versionNumber;

    // 客户端类型 school teacher
    public String type;

    // 推送状态 (true: 已推送, false: 未推送)
    public Boolean isPushed = false;

    // 更新配置json 针对不同类型的客户端，设置不同的结构
    // 学校端 (type: 'school') 配置结构:
    // {
    //   "schoolUpdateDes": "学校端更新说明",
    //   "webDistZipDownloadUrl": "学校端Web前端包下载地址",
    //   "webDistZipFileHash": "学校端Web前端包文件哈希值",
    //   "studentClientDownloadUrl": "学生端客户端下载地址",
    //   "studentClientUpdateDes": "学生端更新说明",
    //   "studentClientFileHash": "学生端文件哈希值",
    //   "schoolClientLinuxDownloadUrl": "学校端Linux版本下载地址",
    //   "schoolClientLinuxFileHash": "学校端Linux版本文件哈希值",
    //   "schoolClientWindowsDownloadUrl": "学校端Windows版本下载地址",
    //   "schoolClientWindowsFileHash": "学校端Windows版本文件哈希值"
    // }
    // 教师端 (type: 'teacher') 配置结构:
    // {
    //   "teacherUpdateDes": "教师端更新说明",
    //   "teacherClientDownloadUrl": "教师端客户端下载地址",
    //   "teacherClientFileHash": "教师端文件哈希值"
    // }
    public JSONObject updateConfig=new JSONObject();
}