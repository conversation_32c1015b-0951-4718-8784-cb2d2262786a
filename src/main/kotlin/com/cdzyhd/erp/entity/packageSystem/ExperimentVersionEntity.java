package com.cdzyhd.erp.entity.packageSystem;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * 实验的版本，一个实验可能有多个版本
 * 比如正式版，适用于很多学校
 * 每个学校可能有自己的需求版本
 */
@Document(collection = "packageManager_experimentVersion")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ExperimentVersionEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    public String experimentVersionId = new SnowflakeIdWorker(1, 0).nextId();

    public Long createTime = System.currentTimeMillis();

    public JSONObject extraInfo = new JSONObject();

    // 所属实验id
    public String experimentId;

    // 所属实验名称
    public String experimentName;

    // 版本名称
    public String name;

    // 是否是主版本
    public Boolean asMainVersion = true;

    // 版本介绍
    public String description;

    // 版本启用状态
    public Boolean enable = true;

    // 适用学校ids
    public JSONArray schoolIds;
}