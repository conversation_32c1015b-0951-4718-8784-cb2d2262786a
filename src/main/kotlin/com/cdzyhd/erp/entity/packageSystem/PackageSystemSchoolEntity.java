package com.cdzyhd.erp.entity.packageSystem;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * 包管理系统对应的学校端用户信息
 */
@Document(collection = "packageManager_packageSystemSchool")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class PackageSystemSchoolEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    public String packageSystemSchoolId = new SnowflakeIdWorker(1, 0).nextId();

    public Long createTime = System.currentTimeMillis();

    public JSONObject extraInfo = new JSONObject();

    // 所属学校id
    public String schoolId;

    // 登录密码
    public String password;

    // 账号名称
    public String username;

    // 登录状态
    public Integer hasLogin=0;

    // 是否禁用
    public Integer disabled=0;

    // 绑定mac地址
    public JSONArray bindMacList=new JSONArray();
    
}