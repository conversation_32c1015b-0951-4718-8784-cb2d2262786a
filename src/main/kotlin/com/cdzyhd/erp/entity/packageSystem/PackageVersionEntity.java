package com.cdzyhd.erp.entity.packageSystem;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 */
@Document(collection = "packageManager_packageVersion")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class PackageVersionEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id，也当做最新的版本号使用，这个版本号特别重要，要能从版本号判定版本先后顺序，且只能由管理端生成
    public String packageVersionId = new SnowflakeIdWorker(1, 0).nextId();

    public Long createTime = System.currentTimeMillis();

    public JSONObject extraInfo = new JSONObject();

    // 所属实验id
    public String experimentId;

    // 所属实验版本id
    public String experimentVersionId;

    // 所属实验版本名称
    public String experimentVersionName;

    // 文件hash值
    public String fileHash;

    public JSONObject fileInfo=new JSONObject();

    // 下载连接
    public String downloadUrl;

    // 程序版本号，程序那边书写的版本号
    public String version;

    // 版本名称，程序填写
    public String versionName;

    // 版本说明,程序填写
    public String versionDesc;

    // 已推送过的学校id列表
    public JSONArray pushedSchoolIds=new JSONArray();
}