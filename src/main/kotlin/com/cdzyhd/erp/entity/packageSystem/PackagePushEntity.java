package com.cdzyhd.erp.entity.packageSystem;

import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 */
@Document(collection = "packageManager_packagePush")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class PackagePushEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    public String packagePushId = new SnowflakeIdWorker(1, 0).nextId();

    public Long createTime = System.currentTimeMillis();

    public JSONObject extraInfo = new JSONObject();

    // 所属版本id
    public String packageVersionId;

    // 所属学校id
    public String schoolId;
    
    // 学校端接受状态
    public Boolean schoolAccept = false;

    // 学校端接受时间
    public Long schoolAcceptTime;

    // 学校端完成下载时间
    public Long schoolDownloadTime;

    // 学校端完成下载状态
    public Boolean schoolDownload = false;
}