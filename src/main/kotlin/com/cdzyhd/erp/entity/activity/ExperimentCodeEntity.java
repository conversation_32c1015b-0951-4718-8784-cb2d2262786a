package com.cdzyhd.erp.entity.activity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 */
@Document(collection = "activity_experimentCode")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ExperimentCodeEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    /**
     * id
     */
    public String experimentCodeId = new SnowflakeIdWorker(1, 0).nextId();

    /**
     *
     */
    public Long createTime = System.currentTimeMillis();

    // 实验码
    public String experimentCode;

    // 可用于的实验列表数组
    //public JSONArray experimentIdArr = new JSONArray();

    // todo 过期时间？

    // 剩余次数
    public Integer number;

    // 备注信息
    public String remarks;

    // 使用信息记录数组
    public JSONArray useInfoArr = new JSONArray();
}