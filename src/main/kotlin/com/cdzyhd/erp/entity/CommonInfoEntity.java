package com.cdzyhd.erp.entity;

import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Document(collection = "common_info")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class CommonInfoEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    /**
     * id
     */
    public String commonInfoId = new SnowflakeIdWorker(1, 0).nextId();

    /**
     *
     */
    public Long createTime = System.currentTimeMillis();

    /**
     * 类型
     */
    public String type;

    /**
     * 信息
     */
    public JSONObject info = new JSONObject();
}