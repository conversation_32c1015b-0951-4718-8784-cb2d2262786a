package com.cdzyhd.erp.entity.resourcePlatform;

import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 */
@Document(collection = "resource_platform")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ResourcePlatformEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    public String resourcePlatformId = new SnowflakeIdWorker(2, 2).nextId();

    public Long createTime = System.currentTimeMillis();

    // 排序权重 默认是时间戳
    public Long sortOrder = System.currentTimeMillis();

    // 类型 schoolCooperation学校合作新闻 experiment实验课程
    public String type;

    // 逻辑删除
    public Integer deleted=0;

    public JSONObject info = new JSONObject();
}