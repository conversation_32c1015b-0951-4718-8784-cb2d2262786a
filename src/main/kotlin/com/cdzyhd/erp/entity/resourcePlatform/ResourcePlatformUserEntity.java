package com.cdzyhd.erp.entity.resourcePlatform;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 */
@Document(collection = "resource_platform_user")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ResourcePlatformUserEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    public String resourcePlatformUserId = new SnowflakeIdWorker(2, 2).nextId();

    public Long createTime = System.currentTimeMillis();

    // 逻辑删除
    public Integer deleted=0;

    public String unionUserId;

    public String username;

    public String password;

    // 收藏列表-虚仿课程
    public JSONArray favExperiment=new JSONArray();

    // 收藏列表-3D微视频
    public JSONArray fav3dVideo=new JSONArray();

    // 收藏列表-全景资源
    public JSONArray favPanorama=new JSONArray();

    // 收藏列表-党建干训
    public JSONArray favPartyBuilding=new JSONArray();

    public JSONObject otherInfo = new JSONObject();

    // 是否是首次登录
    public Boolean isFirstLogin=true;

    // 用户角色 developer 研发，seller 销售，user 用户
    public String role;

    // 绑定的设备号，可以绑定多个设备
    public JSONArray deviceIds=new JSONArray();

    // 最多绑定设备数量 默认为2
    public Integer maxDeviceNumber=2;
}
