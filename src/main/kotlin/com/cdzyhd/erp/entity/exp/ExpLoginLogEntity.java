package com.cdzyhd.erp.entity.exp;

import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * <AUTHOR>
 * 实验平台-其他平台使用登录记录
 */
@Document(collection = "exp_login_log")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ExpLoginLogEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // 创建时间
    public Long createTime = new Date().getTime();

    // 实验注册码日志id
    public String expLoginLogId = new SnowflakeIdWorker(1, 0).nextId();

    // 信息
    public JSONObject info=new JSONObject();
}