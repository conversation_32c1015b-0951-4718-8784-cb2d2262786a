package com.cdzyhd.erp.entity.exp;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * <AUTHOR>
 * 实验平台-设备注册码日志
 */
@Document(collection = "exp_reg_code_log")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ExpRegCodeLogEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // 创建时间
    public Long createTime = new Date().getTime();

    // 实验注册码日志id
    public String expRegCodeLogId = new SnowflakeIdWorker(1, 0).nextId();

    // 关联的实验注册码id-唯一
    public String expRegCodeId;

    // 行为 bind绑定 验证
    public String action;

    // 设备序列号
    public String deviceSerial;

    // 设备名称
    public String deviceName;

    // 实验名称
    public String experimentName;

    // 实验id
    public String experimentId;

    // 结果code
    public String code;

    // 结果msg
    public String msg;

    // 用户ip
    public String ip;
}