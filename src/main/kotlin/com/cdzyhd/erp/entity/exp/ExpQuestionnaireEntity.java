package com.cdzyhd.erp.entity.exp;

import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 */
@Document(collection = "exp_questionnaire")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ExpQuestionnaireEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    public String expQuestionnaireId = new SnowflakeIdWorker(1, 0).nextId();

    public Long createTime = System.currentTimeMillis();

    // 学号
    public String account;

    // 学校id
    public String schoolId;

    // 问卷提交时间
    public Long submitTime;

    // 是否是旧问卷，如果同一account多次提交，就只计算最新的一次
    public boolean asOld = false;

    // 信息
    public JSONObject info = new JSONObject();
}