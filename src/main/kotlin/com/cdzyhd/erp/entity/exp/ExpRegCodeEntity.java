package com.cdzyhd.erp.entity.exp;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * <AUTHOR>
 * 实验平台-设备注册码
 */
@Document(collection = "exp_reg_code")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ExpRegCodeEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // 创建时间
    public Long createTime = new Date().getTime();

    // 实验注册码id-唯一
    public String expRegCodeId = new SnowflakeIdWorker(2, 2).nextId();

    // 所属机构名称
    public String organName;

    // 注册码名称
    public String name;

    // 备注信息
    public String remarks;

    // 是否启用
    public Boolean active = true;

    // 是否限制授权时间
    public Boolean limitTime = false;

    // 授权时间开始和结束时间 ["2023-06-27 00:00:00","2023-06-28 23:59:59"]
    public JSONArray limitTimeDate;

    // 是否限制绑定时间
    public Boolean limitBindTime = false;

    // 限制绑定时间开始和结束时间 ["2023-06-27 00:00:00","2023-06-28 23:59:59"]
    public JSONArray limitBindTimeDate;

    // 是否现在登录ip
    public Boolean limitIp = false;

    // 现在登录ip范围
    public JSONArray limitIpList = new JSONArray();

    // 是否限制运行次数
    public Boolean limitRunNumber = false;

    // 剩余运行次数
    public Integer limitRunNumberCount;

    // 验证成功后是否需要用实验平台账号登录
    public Boolean needLogin = true;

    // 最大绑定实验设备数量
    public Integer maxDevicesNumber = 1;

    // 已绑定的实验设备序列号列表
    public JSONObject bindDevicesList = new JSONObject();

    // 授权的实验id列表
    public JSONArray experimentList = new JSONArray();

    // 其他额外信息
    public JSONObject extraInfo = new JSONObject();
}