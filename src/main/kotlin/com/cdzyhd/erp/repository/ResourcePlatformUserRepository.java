package com.cdzyhd.erp.repository;

import com.cdzyhd.erp.entity.resourcePlatform.ResourcePlatformEntity;
import com.cdzyhd.erp.entity.resourcePlatform.ResourcePlatformUserEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ResourcePlatformUserRepository extends MongoRepository<ResourcePlatformUserEntity, ObjectId> {
    // 是否存在
    boolean existsByResourcePlatformUserId(String id);

    // 获取第一个
    ResourcePlatformUserEntity findFirstByResourcePlatformUserId(String id);

    // 删除
    Integer deleteByResourcePlatformUserId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<ResourcePlatformUserEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<ResourcePlatformUserEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    ResourcePlatformUserEntity getOne(Document d);

    // 判断用户名是否已存在
    Boolean existsByUsername(String  username);

    // 通过用户名获取一个
    ResourcePlatformUserEntity findFirstByUsername(String username);
}
