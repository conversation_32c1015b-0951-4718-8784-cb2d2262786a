package com.cdzyhd.erp.repository;

import com.cdzyhd.erp.entity.resourcePlatform.ResourcePlatformEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ResourcePlatformRepository extends MongoRepository<ResourcePlatformEntity, ObjectId> {
    // 是否存在
    boolean existsByResourcePlatformId(String id);

    // 获取第一个
    ResourcePlatformEntity findFirstByResourcePlatformId(String id);

    // 获取第一个
    ResourcePlatformEntity findFirstByResourcePlatformIdAndDeleted(String id,Integer deleted);

    // 删除
    Integer deleteByResourcePlatformId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<ResourcePlatformEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<ResourcePlatformEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    ResourcePlatformEntity getOne(Document d);
}
