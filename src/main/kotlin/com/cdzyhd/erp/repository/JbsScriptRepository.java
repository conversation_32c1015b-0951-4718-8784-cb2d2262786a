package com.cdzyhd.erp.repository;

import com.cdzyhd.erp.entity.jbs.JbsScriptEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface JbsScriptRepository extends MongoRepository<JbsScriptEntity, ObjectId> {
    boolean existsByJbsScriptId(String id);
    JbsScriptEntity findFirstByJbsScriptId(String id);
    Integer deleteByJbsScriptId(String id);
    
    @Query(value = "?0")
    Page<JbsScriptEntity> getPageList(Document d, Pageable pageable);
    
    @Query(value = "?0")
    List<JbsScriptEntity> getList(Document d);
    
    @Query(value = "?0")
    JbsScriptEntity getOne(Document d);
} 