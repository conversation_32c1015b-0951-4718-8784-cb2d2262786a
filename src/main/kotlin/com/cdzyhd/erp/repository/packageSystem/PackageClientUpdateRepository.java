package com.cdzyhd.erp.repository.packageSystem;


import com.cdzyhd.erp.entity.packageSystem.PackageClientUpdateEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PackageClientUpdateRepository extends MongoRepository<PackageClientUpdateEntity, ObjectId> {
    // 获取第一个
    PackageClientUpdateEntity findFirstByPackageClientUpdateId(String id);

    // 删除
    Integer deleteByPackageClientUpdateId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<PackageClientUpdateEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<PackageClientUpdateEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    PackageClientUpdateEntity getOne(Document d);
}
