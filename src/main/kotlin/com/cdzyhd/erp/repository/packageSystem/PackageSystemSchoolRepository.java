package com.cdzyhd.erp.repository.packageSystem;


import com.cdzyhd.erp.entity.packageSystem.PackageSystemSchoolEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PackageSystemSchoolRepository extends MongoRepository<PackageSystemSchoolEntity, ObjectId> {
    // 获取第一个
    PackageSystemSchoolEntity findFirstByPackageSystemSchoolId(String id);

    // 删除
    Integer deleteByPackageSystemSchoolId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<PackageSystemSchoolEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<PackageSystemSchoolEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    PackageSystemSchoolEntity getOne(Document d);

    Integer countBySchoolIdAndUsername(String schoolId, String username);

    PackageSystemSchoolEntity findFirstBySchoolIdAndUsername(String schoolId, String username);
}
