package com.cdzyhd.erp.repository.packageSystem;


import com.cdzyhd.erp.entity.packageSystem.PackageVersionEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PackageVersionRepository extends MongoRepository<PackageVersionEntity, ObjectId> {
    // 获取第一个
    PackageVersionEntity findFirstByPackageVersionId(String id);

    // 删除
    Integer deleteByPackageVersionId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<PackageVersionEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<PackageVersionEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    PackageVersionEntity getOne(Document d);
}
