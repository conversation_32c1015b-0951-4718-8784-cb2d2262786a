package com.cdzyhd.erp.repository.packageSystem;


import com.cdzyhd.erp.entity.packageSystem.PackagePushLogEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PackagePushLogRepository extends MongoRepository<PackagePushLogEntity, ObjectId> {
    // 获取第一个
    PackagePushLogEntity findFirstByPackagePushLogId(String id);

    // 删除
    Integer deleteByPackagePushLogId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<PackagePushLogEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<PackagePushLogEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    PackagePushLogEntity getOne(Document d);
}
