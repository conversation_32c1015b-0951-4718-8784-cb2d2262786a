package com.cdzyhd.erp.repository.packageSystem;


import com.cdzyhd.erp.entity.packageSystem.PackagePushEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PackagePushRepository extends MongoRepository<PackagePushEntity, ObjectId> {
    // 获取第一个
    PackagePushEntity findFirstByPackagePushId(String id);

    // 删除
    Integer deleteByPackagePushId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<PackagePushEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<PackagePushEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    PackagePushEntity getOne(Document d);

    PackagePushEntity findFirstByPackageVersionIdAndSchoolId(String packageVersionId, String schoolId);
}
