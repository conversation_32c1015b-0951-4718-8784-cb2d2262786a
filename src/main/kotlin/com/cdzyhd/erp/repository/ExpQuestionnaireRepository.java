package com.cdzyhd.erp.repository;

import com.cdzyhd.erp.entity.exp.ExpQuestionnaireEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ExpQuestionnaireRepository extends MongoRepository<ExpQuestionnaireEntity, ObjectId> {
    // 是否存在
    boolean existsByExpQuestionnaireId(String id);

    // 是否存在account
    boolean existsByAccount(String account);

    // 获取第一个
    ExpQuestionnaireEntity findFirstByExpQuestionnaireId(String id);

    // 删除
    Integer deleteByExpQuestionnaireId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<ExpQuestionnaireEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<ExpQuestionnaireEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    ExpQuestionnaireEntity getOne(Document d);

    // 获取某个学校的所有调查表数量
    Integer countBySchoolId(String schoolId);

    // 获取某个学校的填写调查表的人数
    Integer countBySchoolIdAndAsOld(String schoolId, Boolean old);
}
