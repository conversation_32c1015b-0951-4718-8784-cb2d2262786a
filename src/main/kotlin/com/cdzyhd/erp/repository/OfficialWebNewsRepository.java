package com.cdzyhd.erp.repository;

import com.cdzyhd.erp.entity.officialWeb.OfficialWebNewEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OfficialWebNewsRepository extends MongoRepository<OfficialWebNewEntity, ObjectId> {
    // 是否存在
    boolean existsByOfficialWebNewsId(String officialWebNewsId);

    // 获取第一个
    OfficialWebNewEntity findFirstByOfficialWebNewsId(String officialWebNewsId);

    // 删除
    Integer deleteByOfficialWebNewsId(String officialWebNewsId);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<OfficialWebNewEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<OfficialWebNewEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    OfficialWebNewEntity getOne(Document d);
}
