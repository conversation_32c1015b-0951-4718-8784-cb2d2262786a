package com.cdzyhd.erp.repository;

import com.cdzyhd.erp.entity.exp.ExpRegCodeLogEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ExpRegCodeLogRepository extends MongoRepository<ExpRegCodeLogEntity, ObjectId> {
    // 是否存在
    boolean existsByExpRegCodeLogId(String id);

    // 获取第一个
    ExpRegCodeLogEntity findFirstByExpRegCodeLogId(String id);

    // 删除
    Integer deleteByExpRegCodeLogId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<ExpRegCodeLogEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<ExpRegCodeLogEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    ExpRegCodeLogEntity getOne(Document d);
}
