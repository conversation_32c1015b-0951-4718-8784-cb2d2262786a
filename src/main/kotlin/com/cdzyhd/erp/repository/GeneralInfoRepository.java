package com.cdzyhd.erp.repository;

import com.cdzyhd.erp.entity.CommonInfoEntity;
import com.cdzyhd.erp.entity.GeneralInfoEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface GeneralInfoRepository extends MongoRepository<GeneralInfoEntity, ObjectId> {
    // 是否存在
    boolean existsByGeneralInfoId(String id);

    // 获取第一个
    GeneralInfoEntity findFirstByGeneralInfoId(String id);

    // 删除
    Integer deleteByGeneralInfoId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<GeneralInfoEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<GeneralInfoEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    GeneralInfoEntity getOne(Document d);
}
