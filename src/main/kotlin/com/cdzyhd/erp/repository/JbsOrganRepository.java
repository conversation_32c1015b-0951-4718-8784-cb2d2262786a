package com.cdzyhd.erp.repository;

import com.cdzyhd.erp.entity.jbs.JbsOrganEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface JbsOrganRepository extends MongoRepository<JbsOrganEntity, ObjectId> {
    boolean existsByJbsOrganId(String id);
    JbsOrganEntity findFirstByJbsOrganId(String id);
    Integer deleteByJbsOrganId(String id);
    
    @Query(value = "?0")
    Page<JbsOrganEntity> getPageList(Document d, Pageable pageable);
    
    @Query(value = "?0")
    List<JbsOrganEntity> getList(Document d);
    
    @Query(value = "?0")
    JbsOrganEntity getOne(Document d);
} 