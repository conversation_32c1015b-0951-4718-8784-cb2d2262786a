package com.cdzyhd.erp.repository;

import com.cdzyhd.erp.entity.CommonInfoEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CommonInfoRepository extends MongoRepository<CommonInfoEntity, ObjectId> {
    // 是否存在
    boolean existsByCommonInfoId(String commonInfoId);

    // 获取第一个
    CommonInfoEntity findFirstByCommonInfoId(String commonInfoId);

    // 删除
    Integer deleteByCommonInfoId(String commonInfoId);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<CommonInfoEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<CommonInfoEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    CommonInfoEntity getOne(Document d);
}
