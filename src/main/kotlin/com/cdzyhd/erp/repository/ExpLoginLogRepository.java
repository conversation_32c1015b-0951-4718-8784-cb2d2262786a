package com.cdzyhd.erp.repository;

import com.cdzyhd.erp.entity.exp.ExpLoginLogEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ExpLoginLogRepository extends MongoRepository<ExpLoginLogEntity, ObjectId> {
    // 是否存在
    boolean existsByExpLoginLogId(String id);

    // 获取第一个
    ExpLoginLogEntity findFirstByExpLoginLogId(String id);

    // 删除
    Integer deleteByExpLoginLogId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<ExpLoginLogEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<ExpLoginLogEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    ExpLoginLogEntity getOne(Document d);
}
