package com.cdzyhd.erp.repository;

import com.cdzyhd.erp.entity.activity.ExperimentCodeEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ExperimentCodeRepository extends MongoRepository<ExperimentCodeEntity, ObjectId> {
    // 是否存在
    boolean existsByExperimentCodeId(String id);

    // 获取第一个
    ExperimentCodeEntity findFirstByExperimentCodeId(String id);

    // 通过实验码获取
    ExperimentCodeEntity findFirstByExperimentCode(String code);

    // 删除
    Integer deleteByExperimentCodeId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<ExperimentCodeEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<ExperimentCodeEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    ExperimentCodeEntity getOne(Document d);
}
