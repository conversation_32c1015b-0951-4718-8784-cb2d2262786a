package com.cdzyhd.erp.repository;

import com.cdzyhd.erp.entity.jbs.JbsOrderEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface JbsOrderRepository extends MongoRepository<JbsOrderEntity, ObjectId> {
    // 是否存在
    boolean existsByJbsOrderId(String id);

    // 获取第一个
    JbsOrderEntity findFirstByJbsOrderId(String id);

    // 删除
    Integer deleteByJbsOrderId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<JbsOrderEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<JbsOrderEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    JbsOrderEntity getOne(Document d);
} 