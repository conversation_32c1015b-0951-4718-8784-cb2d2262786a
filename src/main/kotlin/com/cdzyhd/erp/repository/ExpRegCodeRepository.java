package com.cdzyhd.erp.repository;

import com.cdzyhd.erp.entity.CommonInfoEntity;
import com.cdzyhd.erp.entity.exp.ExpRegCodeEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ExpRegCodeRepository extends MongoRepository<ExpRegCodeEntity, ObjectId> {
    // 是否存在
    boolean existsByExpRegCodeId(String id);

    // 获取第一个
    ExpRegCodeEntity findFirstByExpRegCodeId(String id);

    // 删除
    Integer deleteByExpRegCodeId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<ExpRegCodeEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<ExpRegCodeEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    ExpRegCodeEntity getOne(Document d);
}
