package com.cdzyhd.erp.repository;

import com.cdzyhd.erp.entity.jbs.JbsAppEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface JbsAppRepository extends MongoRepository<JbsAppEntity, ObjectId> {
    boolean existsByJbsAppId(String id);
    JbsAppEntity findFirstByJbsAppId(String id);
    Integer deleteByJbsAppId(String id);
    
    @Query(value = "?0")
    Page<JbsAppEntity> getPageList(Document d, Pageable pageable);
    
    @Query(value = "?0")
    List<JbsAppEntity> getList(Document d);
    
    @Query(value = "?0")
    JbsAppEntity getOne(Document d);
} 