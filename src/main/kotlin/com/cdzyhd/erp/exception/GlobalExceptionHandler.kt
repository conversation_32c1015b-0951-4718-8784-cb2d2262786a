package com.cdzyhd.erp.exception

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.util.OutResponse
import org.springframework.http.converter.HttpMessageNotReadableException
import org.springframework.web.HttpRequestMethodNotSupportedException
import org.springframework.web.bind.MissingServletRequestParameterException
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import javax.servlet.http.HttpServletResponse

// 全局异常处理
@ControllerAdvice
class GlobalExceptionHandler {
    // 返回封装
    fun outError(response: HttpServletResponse, e: Exception, code: String, msg: String) {
        response.characterEncoding = "UTF-8"
        response.contentType = "application/json"
        response.status = 200
        val writeData = JSONObject.toJSONString(OutResponse(code, msg, ""))
        response.writer.write(writeData)
        e.printStackTrace()
    }

    // 全局异常处理-所有未拦截到的异常
    @ExceptionHandler(Exception::class)
    fun globalExceptionHandlerAll(response: HttpServletResponse, e: Exception) {
        outError(response, e, "000001", e.message as String)
    }

    // 自定义异常-CommonException
    @ExceptionHandler(CommonException::class)
    fun commonException(response: HttpServletResponse, e: CommonException) {
        outError(response, e, e.code, e.message)
    }

    // controller-参数缺失
    @ExceptionHandler(MissingServletRequestParameterException::class)
    fun missingServletRequestParameterException(response: HttpServletResponse, e: Exception) {
        outError(response, e, "000001", "请求缺少必要Query参数-->${e.message}")
    }

    // controller-body参数缺失
    @ExceptionHandler(HttpMessageNotReadableException::class)
    fun httpMessageNotReadableException(response: HttpServletResponse, e: Exception) {
        outError(response, e, "000001", "请求缺少必要Body参数")
    }

    // controller-请求方式不支持
    @ExceptionHandler(HttpRequestMethodNotSupportedException::class)
    fun httpRequestMethodNotSupportedException(response: HttpServletResponse, e: Exception) {
        outError(response, e, "000001", "该请求不支持此类型Method")
    }
}