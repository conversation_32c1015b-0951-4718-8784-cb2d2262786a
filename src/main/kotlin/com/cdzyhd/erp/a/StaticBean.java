package com.cdzyhd.erp.a;

import com.cdzyhd.erp.config.CommonConfig;
import com.cdzyhd.erp.feign.*;
import com.cdzyhd.erp.repository.*;
import com.cdzyhd.erp.repository.packageSystem.*;
import com.cdzyhd.erp.service.RedisService;
import org.springframework.data.mongodb.core.MongoTemplate;

public class StaticBean {
    // CommonConfig
    public static CommonConfig commonConfig = SpringUtil.getBeanByClass(CommonConfig.class);

    // User Feign
    public static UserFeign userFeign = SpringUtil.getBeanByClass(UserFeign.class);

    // Message Feign
    public static MessageFeign messageFeign = SpringUtil.getBeanByClass(MessageFeign.class);

    // Config Feign
    public static ConfigFeign configFeign = SpringUtil.getBeanByClass(ConfigFeign.class);

    public static MongoTemplate mongoTemplate = SpringUtil.getBeanByClass(MongoTemplate.class);

    // File Feign
    public static FileFeign fileFeign = SpringUtil.getBeanByClass(FileFeign.class);

    // CommonInfo Entity
    public static CommonInfoRepository commonInfoRepository = SpringUtil.getBeanByClass(CommonInfoRepository.class);

    public static OfficialWebNewsRepository officialWebNewsRepository = SpringUtil.getBeanByClass(OfficialWebNewsRepository.class);

    public static PicoSystemFeign picoSystemFeign = SpringUtil.getBeanByClass(PicoSystemFeign.class);

    public static ExperimentCodeRepository experimentCodeRepository = SpringUtil.getBeanByClass(ExperimentCodeRepository.class);

    public static GeneralInfoRepository generalInfoRepository = SpringUtil.getBeanByClass(GeneralInfoRepository.class);

    public static WebAnalyticsRepository webAnalyticsRepository = SpringUtil.getBeanByClass(WebAnalyticsRepository.class);

    public static RedisService redisService = SpringUtil.getBeanByClass(RedisService.class);

    public static ExpRegCodeRepository expRegCodeRepository = SpringUtil.getBeanByClass(ExpRegCodeRepository.class);

    public static ExpRegCodeLogRepository expRegCodeLogRepository = SpringUtil.getBeanByClass(ExpRegCodeLogRepository.class);

    public static ExpQuestionnaireRepository expQuestionnaireRepository = SpringUtil.getBeanByClass(ExpQuestionnaireRepository.class);

    public static ExpLoginLogRepository expLoginLogRepository = SpringUtil.getBeanByClass(ExpLoginLogRepository.class);

    public static ResourcePlatformRepository resourcePlatformRepository = SpringUtil.getBeanByClass(ResourcePlatformRepository.class);

    public static ResourcePlatformUserRepository resourcePlatformUserRepository = SpringUtil.getBeanByClass(ResourcePlatformUserRepository.class);

    // JBS订单Repository
    public static JbsOrderRepository jbsOrderRepository = SpringUtil.getBeanByClass(JbsOrderRepository.class);

    // JBS机构Repository
    public static JbsOrganRepository jbsOrganRepository = SpringUtil.getBeanByClass(JbsOrganRepository.class);

    // JBS剧本Repository
    public static JbsScriptRepository jbsScriptRepository = SpringUtil.getBeanByClass(JbsScriptRepository.class);

    // JBS应用Repository
    public static JbsAppRepository jbsAppRepository = SpringUtil.getBeanByClass(JbsAppRepository.class);

    // 包系统-实验子版本Repository
    public static ExperimentVersionRepository experimentVersionRepository = SpringUtil.getBeanByClass(ExperimentVersionRepository.class);

    // 包系统-包版本Repository
    public static PackageVersionRepository packageVersionRepository = SpringUtil.getBeanByClass(PackageVersionRepository.class);

    // 包系统-包推送Repository
    public static PackagePushRepository packagePushRepository = SpringUtil.getBeanByClass(PackagePushRepository.class);

    // 包系统-包推送日志Repository
    public static PackagePushLogRepository packagePushLogRepository = SpringUtil.getBeanByClass(PackagePushLogRepository.class);

    public static ExpAdminFeign expAdminFeign = SpringUtil.getBeanByClass(ExpAdminFeign.class);

    // 包系统-学校Repository
    public static PackageSystemSchoolRepository packageSystemSchoolRepository = SpringUtil.getBeanByClass(PackageSystemSchoolRepository.class);

    public static PackageClientUpdateRepository packageClientUpdateRepository = SpringUtil.getBeanByClass(PackageClientUpdateRepository.class);
}