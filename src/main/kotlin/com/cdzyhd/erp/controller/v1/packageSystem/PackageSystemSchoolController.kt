package com.cdzyhd.erp.controller.v1.packageSystem

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.base.common.util.PasswordUtil
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.annotation.NeedPackageSystemSchoolToken
import com.cdzyhd.erp.entity.packageSystem.PackageSystemSchoolEntity
import com.cdzyhd.erp.model.packageSystem.PackageSystemTokenModel
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/v1/packageSystem/packageSystemSchool/")
class PackageSystemSchoolController {
    val packageSystemTokenModel: PackageSystemTokenModel = PackageSystemTokenModel()

    // 学校用户登录
    @PostMapping("login")
    fun login(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        val username = infoObject.getString("username")
        val password = infoObject.getString("password")
        val schoolName = infoObject.getString("schoolName")
        var schoolId: String = ""
        var schoolInfo= JSONObject()
        // 先通过学校名称获取学校id
        val schoolInfoObject = StaticBean.expAdminFeign.getSchoolInfoByName(schoolName)
        if (schoolInfoObject.getInteger("code") == 20000) {
            if(schoolInfoObject.getJSONObject("data")==null){
                return OutResponse("000001", "学校名称输入错误！", null)
            }
            schoolInfo=schoolInfoObject.getJSONObject("data")
            schoolId = schoolInfo.getString("id")
        }else{
            return OutResponse("000001", "学校名称输入错误！", null)
        }

        val schoolUserEntity = StaticBean.packageSystemSchoolRepository.findFirstBySchoolIdAndUsername(schoolId, username)
        if (schoolUserEntity == null) {
            return OutResponse("000001", "该用户名不存在！", null)
        }
        // 判断密码是否匹配
        if (password != schoolUserEntity.password) {
            return OutResponse("000001", "密码错误！", null)
        }
        // 生成token 10年过期时间
        val token = packageSystemTokenModel.genSchoolTokenAndSave(schoolUserEntity.packageSystemSchoolId, 1) // todo 上线后改成长期
        val returnObject= JSONObject()
        returnObject.put("token", token)
        // 添加学校信息
        val returnSchoolObject= JSONObject()
        returnSchoolObject.put("name",schoolName)
        returnSchoolObject.put("id",schoolId)
        returnObject.put("schoolInfo",returnSchoolObject)
        // 添加学校用户信息
        schoolUserEntity.password=null
        returnObject.put("userInfo",schoolUserEntity)
        // 更新学校的登录状态
        StaticBean.mongoTemplate.updateFirst(
            Query.query(Criteria.where("packageSystemSchoolId").`is`(schoolUserEntity.packageSystemSchoolId)),
            Update.update("hasLogin", 1),
            PackageSystemSchoolEntity::class.java
        )
        return OutResponse("000000", "登录成功", returnObject)
    }

    // 获取列表-不分页
    @PostMapping("list")
    @NeedPackageSystemSchoolToken
    fun getList(@RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getList(StaticBean.packageSystemSchoolRepository, queryObject.toJSONString())
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    @NeedAdminToken
    fun getPageList(

        @RequestBody query: String, @PageableDefault(
            value = 15, sort = ["packageSystemSchoolId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(
                StaticBean.packageSystemSchoolRepository,
                queryObject.toJSONString(),
                pageable
            )
        )
    }

    // 获取一个
    @GetMapping("")
    @NeedAdminToken
    fun getOne(@RequestParam packageSystemSchoolId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(
                StaticBean.packageSystemSchoolRepository,
                "packageSystemSchoolId",
                packageSystemSchoolId
            )
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam packageSystemSchoolId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(
                StaticBean.packageSystemSchoolRepository,
                "packageSystemSchoolId",
                packageSystemSchoolId
            )
        )
    }

    // 批量删除
    @PostMapping("delete/multiple")
    @NeedAdminToken
    fun deleteMultiple(@RequestBody packageSystemSchoolIdList: String): OutResponse<Any> {
        val idArr = JSONArray.parseArray(packageSystemSchoolIdList)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteMultipleById(
                StaticBean.packageSystemSchoolRepository,
                "packageSystemSchoolId",
                idArr
            )
        )
    }

    // 新增或修改消息
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        // 判定是新增还是编辑
        if (!infoObject.contains("packageSystemSchoolId")) {
            // 判断同一个学校下是否已存在相同账号
            val username = infoObject.getString("username")
            val schoolId = infoObject.getString("schoolId")
            val usernameCount = StaticBean.packageSystemSchoolRepository.countBySchoolIdAndUsername(schoolId, username)
            if (usernameCount > 0) {
                return OutResponse("000001", "该学校下已存在相同账号！", null)
            }
        }
        // 明文存储密码，内部用，不需要学校修改，应该没问题
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.packageSystemSchoolRepository,
                PackageSystemSchoolEntity(),
                "packageSystemSchoolId",
                infoObject
            )
        )
    }

    // 批量新增或修改消息
    @PostMapping("/multiple")
    @NeedAdminToken
    fun addOrEditMultiple(@RequestBody info: String): OutResponse<Any> {
        val infoArr = JSONArray.parseArray(info)
        val infoArrResult = JSONArray()
        val packageSystemSchoolEntity =
            PackageSystemSchoolEntity()
        for (infoObject in infoArr) {
            infoObject as JSONObject
            infoArrResult.add(
                CommonMongoEntityModel.addOrEdit(
                    StaticBean.packageSystemSchoolRepository,
                    packageSystemSchoolEntity,
                    "packageSystemSchoolId",
                    infoObject
                )
            )
        }
        return OutResponse("000000", "", infoArrResult)
    }
}