package com.cdzyhd.erp.controller.v1

import cn.hutool.extra.servlet.ServletUtil
import cn.hutool.http.useragent.UserAgentUtil
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.annotation.NeedToken
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.DateTimeTools
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.entity.WebAnalyticsEntity
import com.cdzyhd.erp.util.IpUtil
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*
import java.util.*
import javax.servlet.http.HttpServletRequest

@RestController
@RequestMapping(value = ["/v1/webAnalytics/"])
class WebAnalyticsController {
    // 获取列表-不分页
    @PostMapping("list")
    @NeedAdminToken
    fun getList(@RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getList(StaticBean.webAnalyticsRepository, queryObject.toJSONString())
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    @NeedAdminToken
    fun getPageList(
        @RequestBody query: String, @PageableDefault(
            value = 15, sort = ["createTime"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(StaticBean.webAnalyticsRepository, queryObject.toJSONString(), pageable)
        )
    }

    // 批量获取某个网站满足条件的事件数量
    @PostMapping("oneWebOneEventNumberByInfo")
    @NeedAdminToken
    fun oneWebOneEventNumberByInfo(
        @RequestBody query: String
    ): OutResponse<Any> {
        val queryArray = JSONArray.parseArray(query)
        val resultArray=JSONArray()
        for(queryObject in queryArray){
            queryObject as JSONObject
            // 构建查询参数
            val criteria =
                Criteria("webSiteId").`is`(queryObject.getString("webSiteId")) // 网站编号
            criteria.and("info.eventName").`is`(queryObject.getString("eventName")) // 事件名称
            criteria.and(queryObject.getString("queryName1")).`is`(queryObject.getString("queryValue1")) // 查询字段1和查询内容1
            if(queryObject.containsKey("queryName2")){
                criteria.and(queryObject.getString("queryName2")).`is`(queryObject.getString("queryValue2")) // 查询字段2和查询内容2
            }
            if(queryObject.containsKey("queryName3")){
                criteria.and(queryObject.getString("queryName3")).`is`(queryObject.getString("queryValue3")) // 查询字段3和查询内容3
            }
            val countAggregation = Aggregation.count().`as`("count")
            val matchOperation = Aggregation.match(criteria)
            val aggregation = Aggregation.newAggregation(matchOperation, countAggregation)
            val countResult =
                StaticBean.mongoTemplate.aggregate(
                    aggregation,
                    "webAnalytics",
                    HashMap::class.java
                ).mappedResults
            val totalElements = if (countResult.size == 0) 0 else countResult[0]["count"] as Int
            resultArray.add(totalElements)
        }

        return OutResponse(
            "000000",
            "",
            resultArray)
    }

    // 获取一个
    @GetMapping("")
    @NeedAdminToken
    fun getOne(@RequestParam webAnalyticsId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(StaticBean.webAnalyticsRepository, "webAnalyticsId", webAnalyticsId)
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam webAnalyticsId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.webAnalyticsRepository, "webAnalyticsId", webAnalyticsId)
        )
    }

    // 批量删除
    @PostMapping("delete/multiple")
    @NeedAdminToken
    fun deleteMultiple(@RequestBody webAnalyticsIdList: String): OutResponse<Any> {
        val idArr = JSONArray.parseArray(webAnalyticsIdList)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteMultipleById(StaticBean.webAnalyticsRepository, "webAnalyticsId", idArr)
        )
    }

    // 新增或修改消息
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.webAnalyticsRepository,
                WebAnalyticsEntity(),
                "webAnalyticsId",
                infoObject
            )
        )
    }

    // 新增一条进入网页统计信息
    @PostMapping("/{webId}/{type}")
    fun webAddOne(
        @PathVariable("webId") webId: String,
        @PathVariable("type") type: String,
        request: HttpServletRequest,
        @RequestBody info: String
    ) {
        var webAnalyticsEntity = WebAnalyticsEntity()
        val info = JSONObject.parseObject(info)
        val pageId = info.getString("pageId")
        webAnalyticsEntity.webAnalyticsId = pageId
        // type不是enter、leave、event
        if (type != "enter" && type != "leave" && type != "event") {
            return
        }
        if (type == "leave") {// 如果是离开页面,就只记录停留时间
            webAnalyticsEntity = StaticBean.webAnalyticsRepository.findFirstByWebAnalyticsId(pageId)
            webAnalyticsEntity.info["stayTime"] = info.getLong("stayTime")
            StaticBean.webAnalyticsRepository.save(webAnalyticsEntity)
            return
        }
        webAnalyticsEntity.type = type
        webAnalyticsEntity.day = DateTimeTools.dateFormat(Date(), "yyyy-MM-dd")
        webAnalyticsEntity.webSiteId = webId
        webAnalyticsEntity.info = info
        val ip = ServletUtil.getClientIP(request)
        info["ip"] = ip
        // 获取ip归属地
        info["ipAddress"] = IpUtil.getCityInfo(ip)
        StaticBean.webAnalyticsRepository.save(webAnalyticsEntity)
//        return OutResponse(
//            "000000",
//            "",
//            webAnalyticsEntity.webAnalyticsId
//        )
    }

}
