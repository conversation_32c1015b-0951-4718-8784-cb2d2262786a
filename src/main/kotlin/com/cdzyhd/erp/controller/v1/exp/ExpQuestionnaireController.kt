package com.cdzyhd.erp.controller.v1.exp

import cn.hutool.core.date.DateUtil
import cn.hutool.poi.excel.ExcelReader
import cn.hutool.poi.excel.ExcelUtil
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.entity.exp.ExpQuestionnaireEntity
import com.cdzyhd.erp.vo.exp.ExpQuestionnaireQueryVo
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.io.InputStream


@RestController
@RequestMapping(value = ["/v1/exp/questionnaire/"])
class ExpQuestionnaireController {
    // 获取列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getList(StaticBean.expQuestionnaireRepository, queryObject.toJSONString())
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    @NeedAdminToken
    fun getPageList(

        @RequestBody query: String, @PageableDefault(
            value = 15, sort = ["expQuestionnaireId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(
                StaticBean.expQuestionnaireRepository,
                queryObject.toJSONString(),
                pageable
            )
        )
    }

    // 获取一个
    @GetMapping("")
    @NeedAdminToken
    fun getOne(@RequestParam expQuestionnaireId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(
                StaticBean.expQuestionnaireRepository,
                "expQuestionnaireId",
                expQuestionnaireId
            )
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam expQuestionnaireId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(
                StaticBean.expQuestionnaireRepository,
                "expQuestionnaireId",
                expQuestionnaireId
            )
        )
    }

    // 新增或修改消息
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.expQuestionnaireRepository,
                ExpQuestionnaireEntity(),
                "expQuestionnaireId",
                infoObject
            )
        )
    }

    // 导入调查表
    @PostMapping("import")
    fun importExcel(
        @RequestParam file: MultipartFile,
        @RequestParam schoolId: String
    ): OutResponse<Any> {
        // 读取excel文件
        val excelReader = multipartToInputStream(file)
        if (excelReader != null) {
            val readAll: List<Map<String, Any>> = excelReader.readAll()
            var line = 2
            for (li in readAll) {
                val questionnaireEntity = ExpQuestionnaireEntity()
                questionnaireEntity.schoolId = schoolId
                val info = JSONObject()
                val scoreObject = JSONObject()// 分数对象
                scoreObject["1"] = 0L // 人物知识
                scoreObject["2"] = 0L // 历史知识
                scoreObject["3"] = 0L // 情感
                scoreObject["4"] = 0L // 价值观
                scoreObject["5"] = 0L // 精神谱系
                scoreObject["6"] = 0L // 思政学科知识
                var row = 1
                for (key in li.keys) {
                    //println(key)
                    when (key) {
                        "提交答卷时间" -> {
                            try {
                                val dateStr = (li[key] as String).toString()
                                questionnaireEntity.submitTime = DateUtil.parse(dateStr, "yyyy/MM/dd HH:mm:ss").time
                                println(dateStr)
                            } catch (e: Exception) {
                                throw IllegalArgumentException("第${line}行，第${row}列，提交答卷时间格式错误")
                            }
                        }
                        "1、学号：" -> {
                            try {
                                questionnaireEntity.account = (li[key] as Long).toString()
                            } catch (e: Exception) {
                                throw IllegalArgumentException("第${line}行，第${row}列，学号格式错误")
                            }
                        }
                        "总分" -> {
                            try {
                                info["totalScore"] = li[key] as Long
                            } catch (e: Exception) {
                                throw IllegalArgumentException("第${line}行，第${row}列，总分格式错误")
                            }
                        }
                        "你的性别" -> {
                            try {
                                info["sex"] = li[key] as String
                            } catch (e: Exception) {
                                throw IllegalArgumentException("第${line}行，第${row}列，性别格式错误")
                            }
                        }
                        "你喜欢什么样的学习方式？" -> {
                            try {
                                val studyMethods = li[key] as String
                                val studyMethodsList = studyMethods.split("┋")
                                info["studyMethods"] = studyMethodsList
                            } catch (e: Exception) {
                                throw IllegalArgumentException("第${line}行，第${row}列，你喜欢什么样的学习方式？格式错误")
                            }
                        }
                        "你认为自己是什么类型的学习者？" -> {
                            try {
                                val studyer = (li[key] as String)
                                info["studyer"] = studyer.subSequence(0, studyer.indexOf("（"))
                            } catch (e: Exception) {
                                throw IllegalArgumentException("第${line}行，第${row}列，你认为自己是什么类型的学习者？格式错误")
                            }
                        }
                    }
                    if (key.contains("【人物知识】")) {
                        try {
                            scoreObject["1"] = scoreObject.getLong("1") + li[key] as Long
                        } catch (e: Exception) {
                            throw IllegalArgumentException("第${line}行，第${row}列，人物知识分，格式错误")
                        }
                    }
                    if (key.contains("【历史知识】")) {
                        try {
                            scoreObject["2"] = scoreObject.getLong("2") + li[key] as Long
                        } catch (e: Exception) {
                            throw IllegalArgumentException("第${line}行，第${row}列，历史知识分，格式错误")
                        }
                    }
                    if (key.contains("【情感】")) {
                        try {
                            scoreObject["3"] = scoreObject.getLong("3") + li[key] as Long
                        } catch (e: Exception) {
                            throw IllegalArgumentException("第${line}行，第${row}列，情感分，格式错误")
                        }
                    }
                    if (key.contains("【价值观】")) {
                        try {
                            scoreObject["4"] = scoreObject.getLong("4") + li[key] as Long
                        } catch (e: Exception) {
                            throw IllegalArgumentException("第${line}行，第${row}列，价值观分，格式错误")
                        }
                    }
                    if (key.contains("【精神谱系】")) {
                        try {
                            scoreObject["5"] = scoreObject.getLong("5") + li[key] as Long
                        } catch (e: Exception) {
                            throw IllegalArgumentException("第${line}行，第${row}列，精神谱系分，格式错误")
                        }
                    }
                    if (key.contains("【思政学科知识】")) {
                        try {
                            scoreObject["6"] = scoreObject.getLong("6") + li[key] as Long
                        } catch (e: Exception) {
                            throw IllegalArgumentException("第${line}行，第${row}列，思政学科知识分，格式错误")
                        }
                    }
                    row++
                }
                info["scoreObject"] = scoreObject
                questionnaireEntity.info = info
//                println(questionnaireEntity)
                if (StaticBean.expQuestionnaireRepository.existsByAccount(questionnaireEntity.account)) {// 如果已存在同账户的调查表
                    // 找到同账号调查表
                    val sameAccountList = StaticBean.expQuestionnaireRepository.getList(
                        Document.parse(
                            """
                                {
                                    "account":"${questionnaireEntity.account}"
                                }
                            """.trimIndent()
                        )
                    )
                    // 都设置asOld为true
                    for (paper in sameAccountList) {
                        paper.asOld = true
                        StaticBean.expQuestionnaireRepository.save(paper)
                    }
                }
                StaticBean.expQuestionnaireRepository.save(questionnaireEntity)
                line++
            }



            return OutResponse(
                "000000",
                "",
                readAll
            )
        }
        return OutResponse(
            "000000",
            "",
            ""
        )
    }

    // cMultipartFile读取ExcelReader
    @Throws(IOException::class)
    fun multipartToInputStream(multipartFile: MultipartFile): ExcelReader? {
        var inputStream: InputStream? = null
        var file: File? = null
        var reader: ExcelReader? = null
        try {
            // 创建临时文件
            file = File.createTempFile("erpExpUploadQuestionnaireExcel", null)
            // 把multipartFile写入临时文件
            multipartFile.transferTo(file)
            // 使用文件创建 inputStream 流
            inputStream = file?.let { FileInputStream(it) }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            // 最后记得删除文件
            file?.deleteOnExit()
            reader = ExcelUtil.getReader(inputStream)
            // 关闭流
            inputStream?.close()
        }
        return reader
    }

    // 获取课后知识平均分-学校
    @PostMapping("/avgScoreOfSchool")
    fun getAvgScoreOfSchool(@RequestBody qv: ExpQuestionnaireQueryVo): OutResponse<Any> {
        val and = "$" + "and"
        val gte = "$" + "gte"
        val lte = "$" + "lte"
        // 获取校平均分
        val schoolQuery = JSONObject.parseObject(
            """
            {
                "schoolId":"${qv.schoolId}",
                "asOld":false
            }
        """.trimIndent()
        )
        if (qv.startTime != null) {
            schoolQuery[and] = JSONArray.parseArray(
                """
                [
                    {
                        "createTime": {
                            "$gte": ${qv.startTime}
                        }
                    },
                    {
                        "createTime": {
                            "$lte": ${qv.endTime}
                    }
                    }
                ]
            """.trimIndent()
            )
        }
        val schoolList = StaticBean.expQuestionnaireRepository.getList(Document.parse(schoolQuery.toJSONString()))
        val schoolAvgObject = this.getQuestionnaireAvgScore(schoolList)
        // 返回信息
        return OutResponse(
            "000000",
            "",
            schoolAvgObject
        )
    }

    // 获取课后知识平均分-班级
    @PostMapping("/avgScoreOfClass")
    fun getAvgScoreOfClass(@RequestBody qv: ExpQuestionnaireQueryVo): OutResponse<Any> {
        val and = "$" + "and"
        val gte = "$" + "gte"
        val lte = "$" + "lte"
        val inw = "$" + "in"
        // 获取班级平均分
        val clazzQuery = JSONObject.parseObject(
            """
            {
                "schoolId":"${qv.schoolId}",
                "asOld":false,
                "account":{
                    "$inw":${qv.accountList}
                }
            }
        """.trimIndent()
        )
        if (qv.startTime != null) {
            clazzQuery[and] = JSONArray.parseArray(
                """
                [
                    {
                        "createTime": {
                            "$gte": ${qv.startTime}
                        }
                    },
                    {
                        "createTime": {
                            "$lte": ${qv.endTime}
                    }
                    }
                ]
            """.trimIndent()
            )
        }
        val clazzList = StaticBean.expQuestionnaireRepository.getList(Document.parse(clazzQuery.toJSONString()))
        val clazzAvgObject = this.getQuestionnaireAvgScore(clazzList)
        // 返回信息

        return OutResponse(
            "000000",
            "",
            clazzAvgObject
        )
    }

    // 获取课后知识平均分-个人
    @PostMapping("/avgScoreOfStudent")
    fun getAvgScoreOfStudent(@RequestBody qv: ExpQuestionnaireQueryVo): OutResponse<Any> {
        // 获取学生成绩
        var studentAvgObject = JSONObject()
        if (qv.account != null) {

            val studentQuery = Document.parse(
                """
            {
                "schoolId":"${qv.schoolId}",
                "asOld":false,
                "account":"${qv.account}"
            }
        """.trimIndent()
            )
            val studentList = StaticBean.expQuestionnaireRepository.getList(studentQuery)
            studentAvgObject = this.getQuestionnaireAvgScore(studentList)
        }
        // 返回信息
        return OutResponse(
            "000000",
            "",
            studentAvgObject
        )
    }

    // 获取调查表的平均分
    fun getQuestionnaireAvgScore(list: List<ExpQuestionnaireEntity>): JSONObject {
        val size = list.size
        var totalScoreAll = 0L
        val scoreObjectAll = HashMap<String, Long>()
        scoreObjectAll["1"] = 0L
        scoreObjectAll["2"] = 0L
        scoreObjectAll["3"] = 0L
        scoreObjectAll["4"] = 0L
        scoreObjectAll["5"] = 0L
        scoreObjectAll["6"] = 0L
        for (qu in list) {
            val info = qu.info
            totalScoreAll += info.getLong("totalScore")
            val scoreObject = info.getJSONObject("scoreObject")
            scoreObjectAll["1"] = scoreObjectAll.getValue("1") + scoreObject.getLong("1")
            scoreObjectAll["2"] = scoreObjectAll.getValue("2") + scoreObject.getLong("2")
            scoreObjectAll["3"] = scoreObjectAll.getValue("3") + scoreObject.getLong("3")
            scoreObjectAll["4"] = scoreObjectAll.getValue("4") + scoreObject.getLong("4")
            scoreObjectAll["5"] = scoreObjectAll.getValue("5") + scoreObject.getLong("5")
            scoreObjectAll["6"] = scoreObjectAll.getValue("6") + scoreObject.getLong("6")
        }
        var totalScoreAvg = 0L
        val scoreObjectAvg = HashMap<String, Long>()
        scoreObjectAvg["1"] = 0L
        scoreObjectAvg["2"] = 0L
        scoreObjectAvg["3"] = 0L
        scoreObjectAvg["4"] = 0L
        scoreObjectAvg["5"] = 0L
        scoreObjectAvg["6"] = 0L
        if (size > 0) {
            totalScoreAvg = totalScoreAll / size
            scoreObjectAvg["1"] = scoreObjectAll.getValue("1") / size
            scoreObjectAvg["2"] = scoreObjectAll.getValue("2") / size
            scoreObjectAvg["3"] = scoreObjectAll.getValue("3") / size
            scoreObjectAvg["4"] = scoreObjectAll.getValue("4") / size
            scoreObjectAvg["5"] = scoreObjectAll.getValue("5") / size
            scoreObjectAvg["6"] = scoreObjectAll.getValue("6") / size
        }
        val returnObject = JSONObject()
        returnObject["totalScoreAvg"] = totalScoreAvg
        returnObject["scoreObjectAvg"] = scoreObjectAvg
        return returnObject
    }

    // 获取某个学校调查表数量
    @GetMapping("/schoolQuestionnaireCount")
    fun getSchoolQuestionnaireCount(@RequestParam("schoolId") schoolId: String): OutResponse<Any> {
        val totalCount = StaticBean.expQuestionnaireRepository.countBySchoolId(schoolId)
        val studentCount = StaticBean.expQuestionnaireRepository.countBySchoolIdAndAsOld(schoolId, false)
        val returnObject = JSONObject()
        returnObject["totalCount"] = totalCount
        returnObject["studentCount"] = studentCount
        return OutResponse(
            "000000",
            "",
            returnObject
        )
    }
}