package com.cdzyhd.erp.controller.v1.officialWeb

import cn.hutool.http.HttpUtil
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.entity.officialWeb.OfficialWebNewEntity
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*
import java.lang.IllegalStateException

@RestController
@RequestMapping(value = ["/v1/officialWeb/news"])
class OfficialWebNewsController {
    // 获取列表-不分页
    @PostMapping("list")
    fun getFeedbackList(@RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
                "000000",
                "",
                CommonMongoEntityModel.getList(StaticBean.officialWebNewsRepository, queryObject.toJSONString())
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    fun getFeedbackPageList(

            @RequestBody query: String, @PageableDefault(
                    value = 15, sort = ["officialWebNewsId"],
                    direction = Sort.Direction.DESC
            ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
                "000000",
                "",
                CommonMongoEntityModel.getPageList(StaticBean.officialWebNewsRepository, queryObject.toJSONString(), pageable)
        )
    }

    // 获取一个
    @GetMapping("")
    fun getOne(@RequestParam officialWebNewsId: String): OutResponse<Any> {
        return OutResponse(
                "000000",
                "",
                CommonMongoEntityModel.getOneById(StaticBean.officialWebNewsRepository, "officialWebNewsId", officialWebNewsId)
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam officialWebNewsId: String): OutResponse<Any> {
        return OutResponse(
                "000000",
                "",
                CommonMongoEntityModel.deleteOneById(StaticBean.officialWebNewsRepository, "officialWebNewsId", officialWebNewsId)
        )
    }

    // 新增或修改
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
                "000000",
                "",
                CommonMongoEntityModel.addOrEdit(
                        StaticBean.officialWebNewsRepository, OfficialWebNewEntity(),
                        "officialWebNewsId",
                        infoObject
                )
        )
    }

    // 新增或修改
    @PostMapping("/postUrlToBaidu")
    @NeedAdminToken
    fun postUrlToBaidu(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        val url = infoObject.getString("url")
        val newsId = infoObject.getString("newsId")
        val baiduPostUrl = "http://data.zz.baidu.com/urls?site=www.cdzyhd.com&token=j2F6RPNNctfX572I"
        val resultString = HttpUtil.post(baiduPostUrl, url)
        if (resultString.contains("success")) {// 推送成功
            // 保存推送结果
            val news = CommonMongoEntityModel.getOneById(StaticBean.officialWebNewsRepository, "officialWebNewsId", newsId)
            news as OfficialWebNewEntity
            news.postedUrl = true
            StaticBean.officialWebNewsRepository.save(news)
            return OutResponse(
                    "000000",
                    "",
                    "推送成功"
            )
        } else {// 推送失败
            val resultJson = JSONObject.parseObject(resultString)
            throw IllegalStateException("推送到百度搜索失败，" + resultJson.getString("message"))
        }
    }

}