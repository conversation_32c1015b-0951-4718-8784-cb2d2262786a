package com.cdzyhd.erp.controller.v1

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.annotation.SysOperaLog
import com.cdzyhd.erp.config.CommonConfig
import com.cdzyhd.erp.feign.UserFeign
import com.cdzyhd.erp.model.AdminUserModel
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/v1/adminUser/")
class AdminUserController {
    val userFeign: UserFeign = StaticBean.userFeign
    val commonConfig: CommonConfig = StaticBean.commonConfig

    // 获取已登录用户自己的用户信息
    @GetMapping("/mineInfo")
    @NeedAdminToken
    fun getMineInfo(@RequestAttribute adminUserId: String): JSONObject {
        //val adminUserId = req.getAttribute("adminUserId") as String
        return userFeign.getAdminUserInfo(commonConfig.platformId, adminUserId)
    }

    // 创建管理员用户
    @PostMapping("user")
    @SysOperaLog(moduleName = "管理员用户", methodName = "创建管理员")
    @NeedAdminToken
    fun createAdminUser(
            @RequestParam username: String,
            @RequestParam phoneNumber: String,
            @RequestParam password: String,
            @RequestBody userInfo: String
    ): JSONObject {
        var returnObject = JSONObject()

        val userInfoObject = JSONObject.parseObject(userInfo)
        val platformId = userInfoObject.getJSONArray("platforms")[0].toString()
        // 如果是pico，逻辑删除了，直接启用
        // 用手机号判断，如果是同一手机号就直接启用
        if (platformId == "picoSystem") {
            val queryExist = """
                {
                    "deleted":1,
                    "platforms":{
                        "${"$" + "all"}":["picoSystem"]
                    },
                    "phoneNumber":"${userInfoObject["phoneNumber"]}"
                }
            """.trimIndent()
            val result = userFeign.getAdminUserList("all", queryExist)
            val data = result["data"] as ArrayList<LinkedHashMap<String, Any>>
            if (data.size > 0) {
                val existedOne = data[0]
                existedOne["deleted"] = 0
                val dealerJsonString = JSON.toJSONString(existedOne)
                val result = StaticBean.userFeign.editAdminUser(dealerJsonString)
                returnObject = result
                return returnObject
            }
        }
        returnObject = userFeign.createAdminUser(
                username,
                phoneNumber,
                password,
                userInfoObject.getJSONArray("platforms")[0].toString(),
                userInfo
        )
        return returnObject
    }

    // 批量创建管理员
    @PostMapping("user/multiple")
    @NeedAdminToken
    fun createUserMultiple(@RequestBody userList: String): JSONObject {
        return userFeign.createAdminUserMultiple(userList)
    }

    // 管理员登录并获取token
    @GetMapping("token")
    @SysOperaLog(moduleName = "管理员用户", methodName = "管理员登录")
    fun adminLogin(
            @RequestParam account: String,
            @RequestParam password: String,
    ): JSONObject {
        return userFeign.adminLogin(
                "all",
                account,
                password,
                commonConfig.tokenExpireDayDefault,
                commonConfig.platformId
        )
    }

    // 管理员-刷新token
    @GetMapping("token/refresh")
    @NeedAdminToken
    fun tokenRefresh(
            @RequestParam token: String,
    ): JSONObject {
        return userFeign.tokenAdminRefresh(token, commonConfig.platformId, commonConfig.tokenExpireDayDefault)
    }

    // 管理员token验证
    @GetMapping("token/check")
    @NeedAdminToken
    fun tokenCheck(
            @RequestParam token: String
    ): JSONObject {
        return userFeign.tokenAdminCheck(token, commonConfig.platformId)
    }

    // 获取管理员列表-不分页
    @PostMapping("user/list")
    @NeedAdminToken
    fun getAdminUserList(@RequestBody query: String): JSONObject {
        val queryObject = JSONObject.parseObject(query)
        queryObject["deleted"] = JSONObject.parseObject(
                """
             {"${"$" + "ne"}":1}
        """.trimIndent()
        )
        return userFeign.getAdminUserList("all", queryObject.toJSONString())
    }

    // 获取管理员列表-不分页-按系统分
    @PostMapping("user/list/byPlatformId")
    @NeedAdminToken
    fun getAdminUserList(@RequestBody query: String, @RequestParam platformId: String): JSONObject {
        val queryObject = JSONObject.parseObject(query)
        queryObject["deleted"] = JSONObject.parseObject(
                """
             {"${"$" + "ne"}":1}
        """.trimIndent()
        )
        return userFeign.getAdminUserList(platformId, queryObject.toJSONString())
    }

    // 获取管理员列表-分页
    @PostMapping("user/pageList")
    @NeedAdminToken
    fun getAdminUserPageList(
            @RequestBody query: String,
            @RequestParam page: Int,
            @RequestParam size: Int,
            @RequestParam(required = false) sort: String,
            @RequestParam(required = false, defaultValue = "all") platformId: String
    ): JSONObject {
        val queryObject = JSONObject.parseObject(query)
        queryObject["deleted"] = JSONObject.parseObject(
                """
             {"${"$" + "ne"}":1}
        """.trimIndent()
        )
        return userFeign.getAdminUserPageList(
                platformId,
                page, size, sort,
                queryObject.toJSONString()
        )
    }

    // 修改管理员信息
    @PutMapping("user")
    @NeedAdminToken
    @SysOperaLog(moduleName = "管理员用户", methodName = "修改管理员消息")
    fun editAdminUser(@RequestBody userInfo: String): JSONObject {
        return userFeign.editAdminUser(userInfo)
    }

    // 删除管理员-单个
    @DeleteMapping("user")
    @SysOperaLog(moduleName = "管理员用户", methodName = "删除管理员")
    @NeedAdminToken
    fun deleteOneAdminUser(@RequestParam adminUserId: String): JSONObject {
        return userFeign.deleteOneAdminUser(adminUserId)
    }

    // 删除管理员-多个
    @PostMapping("user/delete/multiple")
    @NeedAdminToken
    fun deleteAdminUserMultiple(@RequestBody userIdList: String): JSONObject {
        return userFeign.deleteAdminUserMultiple(userIdList)
    }

    // 删除token
    @DeleteMapping("token")
    @NeedAdminToken
    fun removeToken(@RequestParam token: String): JSONObject {
        return userFeign.removeAdminToken(commonConfig.platformId, token)
    }

    // 直接生成某个平台、某个管理员的token 2021.8.5
    @NeedAdminToken
    @GetMapping("generateAdminToken")
    fun generateOnePlatformAdminToken(
            @RequestParam platformId: String,
            @RequestParam adminUserId: String
    ): JSONObject {
        return userFeign.generateOnePlatformAdminToken(platformId, adminUserId, 1)
    }

    // Pico-重置管理员密码 2021.9.9
    @GetMapping("/pico/resetPassword")
    fun resetPassword(@RequestParam dealerId: String): JSONObject {
        val resetResult = AdminUserModel().resetDealerPassword(dealerId)
        return resetResult
    }

    // Pico-逻辑删除经销商 2021.9.9
    @DeleteMapping("/pico/dealer")
    fun logicDeleteDealer(@RequestParam dealerId: String): JSONObject {
        // 判断经销商是否绑定了pico，如果绑定了不能删除
        val listResult = StaticBean.picoSystemFeign.getEquipmentList("""
            {
                "dealerId":"$dealerId"
            }
        """.trimIndent())
        var listResultArr = listResult["data"] as ArrayList<Any>
        if (listResultArr.size > 0) {
            return JSONObject.parseObject("""
               {
                    "code":"000001",
                    "msg":"此经销商已绑定了${listResultArr.size}个设备，请先解绑后再删除！",
                    "data":""
               }
           """.trimIndent())
        }

        val dealerResult = StaticBean.userFeign.getAdminUserInfo("picoSystem", dealerId)
        val dealer = dealerResult["data"] as LinkedHashMap<String, Any>
        dealer["deleted"] = 1
        val dealerJsonString = JSON.toJSONString(dealer)
        val result = StaticBean.userFeign.editAdminUser(dealerJsonString)
        return result
    }

    // 生成erp平台实验平台教师用户token
    @GetMapping("generateErpExpTeacherToken")
    fun generateErpExpTeacherToken(
            @RequestParam teacherId: String
    ): JSONObject {
        // todo 加强安全验证 验证teacher token 并记录
        // 获取固定的expTeacher Token
        return userFeign.generateOnePlatformAdminToken("erp", "10049351887532640", 1)
    }
}