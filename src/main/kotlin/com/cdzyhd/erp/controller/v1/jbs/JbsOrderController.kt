package com.cdzyhd.erp.controller.v1.jbs

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.entity.jbs.JbsOrderEntity
import com.cdzyhd.erp.model.jbs.JbsOrganModel
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*

/**
 * <AUTHOR>
 * 剧本杀-机构管理
 */
@RestController
@RequestMapping("/v1/jbs/order")
class JbsOrderController {
    private val model = JbsOrganModel()

    // 获取列表-不分页
    @PostMapping("list")
    @NeedAdminToken
    fun getList( @RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getList(StaticBean.jbsOrderRepository, queryObject.toJSONString())
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    @NeedAdminToken
    fun getPageList(

        @RequestBody query: String, @PageableDefault(
            value = 15, sort = ["jbsOrderId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(StaticBean.jbsOrderRepository, queryObject.toJSONString(), pageable)
        )
    }

    // 获取一个
    @GetMapping("")
    @NeedAdminToken
    fun getOne(@RequestParam jbsOrderId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(StaticBean.jbsOrderRepository, "jbsOrderId", jbsOrderId)
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam jbsOrderId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.jbsOrderRepository, "jbsOrderId", jbsOrderId)
        )
    }

    // 新增或修改消息
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit( @RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.jbsOrderRepository,
                JbsOrderEntity(),
                "jbsOrderId",
                infoObject
            )
        )
    }
} 