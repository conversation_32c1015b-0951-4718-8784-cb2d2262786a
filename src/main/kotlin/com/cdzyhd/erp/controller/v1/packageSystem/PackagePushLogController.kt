package com.cdzyhd.erp.controller.v1.packageSystem

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.annotation.NeedPackageSystemSchoolToken
import com.cdzyhd.erp.entity.packageSystem.PackagePushEntity
import com.cdzyhd.erp.entity.packageSystem.PackagePushLogEntity
import com.cdzyhd.erp.entity.packageSystem.PackageSystemSchoolEntity
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*
import java.util.Date

@RestController
@RequestMapping("/v1/packageSystem/packagePushLog/")
class PackagePushLogController {

    // 新增某个学校的推送日志信息
    @PostMapping("addPushLog")
    @NeedPackageSystemSchoolToken
    fun addPushLog(
        @RequestAttribute packageSystemSchoolId: String,
        @RequestBody info: String
    ): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        infoObject["packageSystemSchoolId"] = packageSystemSchoolId
        if(infoObject.getString("type")=="accept"){// 同步修改推送的状态
            StaticBean.mongoTemplate.updateFirst(
                Query.query(Criteria.where("packagePushId").`is`(infoObject.getString("packagePushId"))),
                Update.update("schoolAccept", true),
                PackagePushEntity::class.java
            )
            StaticBean.mongoTemplate.updateFirst(
                Query.query(Criteria.where("packagePushId").`is`(infoObject.getString("packagePushId"))),
                Update.update("schoolAcceptTime", Date().time),
                PackagePushEntity::class.java
            )
        }
        if(infoObject.getString("type")=="download"){ // 同步修改下载的状态
            StaticBean.mongoTemplate.updateFirst(
                Query.query(Criteria.where("packagePushId").`is`(infoObject.getString("packagePushId"))),
                Update.update("schoolDownload", true),
                PackagePushEntity::class.java
            )
            StaticBean.mongoTemplate.updateFirst(
                Query.query(Criteria.where("packagePushId").`is`(infoObject.getString("packagePushId"))),
                Update.update("schoolDownloadTime", Date().time),
                PackagePushEntity::class.java
            )
        }
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.packagePushLogRepository,
                PackagePushLogEntity(),
                "packagePushLogId",
                infoObject
            ))
    }

     // 获取列表-不分页
     @PostMapping("list")
     @NeedAdminToken
     fun getList(@RequestBody query: String): OutResponse<Any> {
         val queryObject = JSONObject.parseObject(query)
         return OutResponse(
             "000000",
             "",
             CommonMongoEntityModel.getList(StaticBean.packagePushLogRepository, queryObject.toJSONString())
         )
     }
 
     // 获取列表-分页
     @PostMapping("pageList")
     @NeedAdminToken
     fun getPageList(
 
         @RequestBody query: String, @PageableDefault(
             value = 15, sort = ["packagePushLogId"],
             direction = Sort.Direction.DESC
         ) pageable: Pageable
     ): OutResponse<Any> {
         val queryObject = JSONObject.parseObject(query)
         return OutResponse(
             "000000",
             "",
             CommonMongoEntityModel.getPageList(StaticBean.packagePushLogRepository, queryObject.toJSONString(), pageable)
         )
     }
 
     // 获取一个
     @GetMapping("")
     @NeedAdminToken
     fun getOne(@RequestParam packagePushLogId: String): OutResponse<Any> {
         return OutResponse(
             "000000",
             "",
             CommonMongoEntityModel.getOneById(StaticBean.packagePushLogRepository, "packagePushLogId", packagePushLogId)
         )
     }
 
     // 删除一个
     @DeleteMapping("")
     @NeedAdminToken
     fun deleteOne(@RequestParam packagePushLogId: String): OutResponse<Any> {
         return OutResponse(
             "000000",
             "",
             CommonMongoEntityModel.deleteOneById(StaticBean.packagePushLogRepository, "packagePushLogId", packagePushLogId)
         )
     }
 
     // 批量删除
     @PostMapping("delete/multiple")
     @NeedAdminToken
     fun deleteMultiple(@RequestBody packagePushLogIdList: String): OutResponse<Any> {
         val idArr = JSONArray.parseArray(packagePushLogIdList)
         return OutResponse(
             "000000",
             "",
             CommonMongoEntityModel.deleteMultipleById(StaticBean.packagePushLogRepository, "packagePushLogId", idArr)
         )
     }
 
     // 新增或修改消息
     @PostMapping("")
     @NeedAdminToken
     fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
         val infoObject = JSONObject.parseObject(info)
         return OutResponse(
             "000000",
             "",
             CommonMongoEntityModel.addOrEdit(
                 StaticBean.packagePushLogRepository,
                 PackagePushLogEntity(),
                 "packagePushLogId",
                 infoObject
             )
         )
     }
 
     // 批量新增或修改消息
     @PostMapping("/multiple")
     @NeedAdminToken
     fun addOrEditMultiple(@RequestBody info: String): OutResponse<Any> {
         val infoArr = JSONArray.parseArray(info)
         val infoArrResult = JSONArray()
         val packagePushLogEntity =
             PackagePushLogEntity()
         for (infoObject in infoArr) {
             infoObject as JSONObject
             infoArrResult.add(
                 CommonMongoEntityModel.addOrEdit(
                     StaticBean.packagePushLogRepository,
                     packagePushLogEntity,
                     "packagePushLogId",
                     infoObject
                 )
             )
         }
         return OutResponse("000000", "", infoArrResult)
     }
}