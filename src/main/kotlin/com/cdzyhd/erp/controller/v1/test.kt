package com.cdzyhd.erp.controller.v1

import cn.hutool.core.util.CharsetUtil
import cn.hutool.crypto.Mode
import cn.hutool.crypto.Padding
import cn.hutool.crypto.SecureUtil
import cn.hutool.crypto.symmetric.AES
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.base.common.util.PasswordUtil
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.model.AdminUserModel
import com.cdzyhd.erp.model.SmsModel
import com.cdzyhd.erp.model.TestModel
import com.cdzyhd.erp.util.IpUtil
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/v1/test")
class test {

    @GetMapping("/test")
    fun test1(): OutResponse<Any> {
        val key = "cdzyhdaes1234567"
        // 构建
        val aes = AES(Mode.CBC, Padding.PKCS5Padding, key.toByteArray(), key.toByteArray())
        val s = aes.encryptHex("123456")
        println(s)
        println(aes.decryptStr(s, CharsetUtil.CHARSET_UTF_8))
        return OutResponse("000000", "", IpUtil.getCityInfo("**************"))
    }

    @GetMapping("/generatePassword")
    fun generatePassword(@RequestParam password: String): OutResponse<Any> {
        return OutResponse("000000", "", PasswordUtil.generate(password))
    }
}