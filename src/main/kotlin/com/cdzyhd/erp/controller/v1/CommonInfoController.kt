package com.cdzyhd.erp.controller.v1

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.entity.CommonInfoEntity
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(value = ["/v1/commonInfo/"])
class CommonInfoController {
    // 获取列表-不分页
    @PostMapping("list")
    fun getList( @RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getList(StaticBean.commonInfoRepository, queryObject.toJSONString())
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    fun getPageList(

        @RequestBody query: String, @PageableDefault(
            value = 15, sort = ["commonInfoId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(StaticBean.commonInfoRepository, queryObject.toJSONString(), pageable)
        )
    }

    // 获取一个
    @GetMapping("")
    fun getOne(@RequestParam commonInfoId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(StaticBean.commonInfoRepository, "commonInfoId", commonInfoId)
        )
    }

    // 删除一个
    @DeleteMapping("")
    fun deleteOne(@RequestParam commonInfoId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.commonInfoRepository, "commonInfoId", commonInfoId)
        )
    }

    // 批量删除
    @PostMapping("delete/multiple")
    fun deleteMultiple(@RequestBody commonInfoIdList: String): OutResponse<Any> {
        val idArr = JSONArray.parseArray(commonInfoIdList)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteMultipleById(StaticBean.commonInfoRepository, "commonInfoId", idArr)
        )
    }

    // 新增或修改消息
    @PostMapping("")
    fun addOrEdit( @RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.commonInfoRepository,
                CommonInfoEntity(),
                "commonInfoId",
                infoObject
            )
        )
    }

    // 批量新增或修改消息
    @PostMapping("/multiple")
    fun addOrEditMultiple( @RequestBody info: String): OutResponse<Any> {
        val infoArr = JSONArray.parseArray(info)
        val infoArrResult = JSONArray()
        val commonInfoEntity = CommonInfoEntity()
        for (infoObject in infoArr) {
            infoObject as JSONObject
            infoArrResult.add(
                CommonMongoEntityModel.addOrEdit(
                    StaticBean.commonInfoRepository,
                    commonInfoEntity,
                    "commonInfoId",
                    infoObject
                )
            )
        }
        return OutResponse("000000", "", infoArrResult)
    }
}