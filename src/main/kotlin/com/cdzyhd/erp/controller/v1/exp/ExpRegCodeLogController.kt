package com.cdzyhd.erp.controller.v1.exp

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.entity.exp.ExpRegCodeLogEntity
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(value = ["/v1/exp/reg_code_log"])
class ExpRegCodeLogController {
    // 获取列表-不分页
    @PostMapping("list")
    @NeedAdminToken
    fun getList( @RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getList(StaticBean.expRegCodeLogRepository, queryObject.toJSONString())
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    @NeedAdminToken
    fun getPageList(

        @RequestBody query: String, @PageableDefault(
            value = 15, sort = ["expRegCodeLogId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(StaticBean.expRegCodeLogRepository, queryObject.toJSONString(), pageable)
        )
    }

    // 获取一个
    @GetMapping("")
    @NeedAdminToken
    fun getOne(@RequestParam expRegCodeLogId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(StaticBean.expRegCodeLogRepository, "expRegCodeLogId", expRegCodeLogId)
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam expRegCodeLogId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.expRegCodeLogRepository, "expRegCodeLogId", expRegCodeLogId)
        )
    }

    // 新增或修改消息
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit( @RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.expRegCodeLogRepository,
                ExpRegCodeLogEntity(),
                "expRegCodeLogId",
                infoObject
            )
        )
    }
}