package com.cdzyhd.erp.controller.v1.packageSystem

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.entity.packageSystem.ExperimentVersionEntity
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/v1/packageSystem/experimentVersion/")
class ExperimentVersionController {
    // 获取实验列表
    // todo expAdmin这个接口的size最多500？学校超过500的适应修改
    @GetMapping("allExperimentList")
    fun getAllExperimentList(): OutResponse<Any> {
        // 获取实验平台的所有实验列表
        val params = HashMap<String, Any>()
        params["page"] = 1
        params["size"] = 5000
        params["sort"] = "createtime,desc"
        val expListObject = StaticBean.expAdminFeign.getExperimentList(params)
        val expList = expListObject.getJSONObject("data").getJSONArray("records")
        // 获取实验版本列表
        val expVersionList = StaticBean.experimentVersionRepository.findAll()

        // 遍历实验列表，为每个实验添加版本
        for (i in 0 until expList.size) {
            val exp = expList.getJSONObject(i)
            val experimentId = exp.getString("id")

            // 查找与当前实验关联的版本
            val versionList = JSONArray()
            var hasMainVersion = false

            expVersionList.forEach { version ->
                if (version.experimentId == experimentId) {
                    version.id=null
                    versionList.add(JSONObject.parseObject(JSONObject.toJSONString(version)))
                    // 检查是否有"主版本"
                    if (version.name == "主版本") {
                        hasMainVersion = true
                    }
                }
            }

            // 如果没有主版本，则创建一个
            if (!hasMainVersion) {
                val mainVersion = ExperimentVersionEntity()
                mainVersion.asMainVersion = true
                mainVersion.experimentId = experimentId
                mainVersion.name = "主版本"
                mainVersion.experimentName=exp.getString("name")
                mainVersion.description = "通用主版本"
                mainVersion.enable = true
                mainVersion.schoolIds = JSONArray()

                // 保存到数据库
                val savedVersion = StaticBean.experimentVersionRepository.save(mainVersion)
                savedVersion.id=null

                // 添加到版本列表
                versionList.add(JSONObject.parseObject(JSONObject.toJSONString(savedVersion)))
            }

            // 将版本列表添加到实验对象中
            exp["versionList"] = versionList
        }

        return OutResponse("000000", "", expList)
    }

    // 获取列表-不分页
    @PostMapping("list")
    @NeedAdminToken
    fun getList(@RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getList(StaticBean.experimentVersionRepository, queryObject.toJSONString())
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    @NeedAdminToken
    fun getPageList(

        @RequestBody query: String, @PageableDefault(
            value = 15, sort = ["experimentVersionId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(
                StaticBean.experimentVersionRepository,
                queryObject.toJSONString(),
                pageable
            )
        )
    }

    // 获取一个
    @GetMapping("")
    @NeedAdminToken
    fun getOne(@RequestParam experimentVersionId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(
                StaticBean.experimentVersionRepository,
                "experimentVersionId",
                experimentVersionId
            )
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam experimentVersionId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(
                StaticBean.experimentVersionRepository,
                "experimentVersionId",
                experimentVersionId
            )
        )
    }

    // 批量删除
    @PostMapping("delete/multiple")
    @NeedAdminToken
    fun deleteMultiple(@RequestBody experimentVersionIdList: String): OutResponse<Any> {
        val idArr = JSONArray.parseArray(experimentVersionIdList)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteMultipleById(
                StaticBean.experimentVersionRepository,
                "experimentVersionId",
                idArr
            )
        )
    }

    // 新增或修改消息
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.experimentVersionRepository,
                ExperimentVersionEntity(),
                "experimentVersionId",
                infoObject
            )
        )
    }

    // 批量新增或修改消息
    @PostMapping("/multiple")
    @NeedAdminToken
    fun addOrEditMultiple(@RequestBody info: String): OutResponse<Any> {
        val infoArr = JSONArray.parseArray(info)
        val infoArrResult = JSONArray()
        val experimentVersionVersionEntity =
            ExperimentVersionEntity()
        for (infoObject in infoArr) {
            infoObject as JSONObject
            infoArrResult.add(
                CommonMongoEntityModel.addOrEdit(
                    StaticBean.experimentVersionRepository,
                    experimentVersionVersionEntity,
                    "experimentVersionId",
                    infoObject
                )
            )
        }
        return OutResponse("000000", "", infoArrResult)
    }
}