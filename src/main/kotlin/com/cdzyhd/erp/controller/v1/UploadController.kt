package com.cdzyhd.erp.controller.v1

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/v1/upload/")
class UploadController {

    // 获取七牛云上传token
    @NeedAdminToken
    @GetMapping("qiniuToken")
    fun getQiniuUploadToken(@RequestParam("bucket", required = false) bucket: String): OutResponse<*> {
        return OutResponse("000000", "", StaticBean.fileFeign.getQiniuUploadToken(bucket).getString("data"))
    }
}