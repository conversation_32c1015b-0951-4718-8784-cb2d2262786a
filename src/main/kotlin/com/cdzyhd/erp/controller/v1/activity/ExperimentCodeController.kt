package com.cdzyhd.erp.controller.v1.activity

import cn.hutool.extra.servlet.ServletUtil
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.entity.activity.ExperimentCodeEntity
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*
import java.util.*
import javax.servlet.http.HttpServletRequest

@RestController
@RequestMapping(value = ["/v1/activity/experimentCode"])
class ExperimentCodeController {
    // 创建实验码
    @PostMapping("/newCodes")
    @NeedAdminToken
    fun createNewCodes(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        val codeNumber = infoObject.getInteger("codeNumber") // 生成个数
        val number = infoObject.getInteger("number") // 可以实验次数
        val remarks = infoObject.getString("remarks") // 备注
        val codeList = ArrayList<ExperimentCodeEntity>();
        // 循环创建
        for (i in 1..codeNumber) {
            val experimentCode = ExperimentCodeEntity()
            // 实验码算法 uuid去掉符号 32位英文和数字排列
            experimentCode.experimentCode = UUID.randomUUID().toString().replace("-", "")
            experimentCode.remarks = remarks
            experimentCode.number = number
            codeList.add(experimentCode)
            StaticBean.experimentCodeRepository.save(experimentCode)
        }
        // 返回列表
        return OutResponse(
                "000000",
                "",
                codeList)
    }

    // 核销实验码
    @PostMapping("useCode")
    fun useCode(request: HttpServletRequest, @RequestBody info: JSONObject): OutResponse<Any> {
        val code = info.getString("code") // 体验码
        // 获取实验码详情
        val experimentCode = StaticBean.experimentCodeRepository.findFirstByExperimentCode(code)
        // 判断合法性
        if (experimentCode == null) {
            return OutResponse(
                    "000001",
                    "体验码输入错误，请检查是否输入正确！",
                    "")
        }
        // 判断剩余次数
        if (experimentCode.number <= 0) {
            return OutResponse(
                    "000002",
                    "该体验码已超过使用次数限制，请使用其它体验码！",
                    "")
        }
        // 减少次数
        experimentCode.number -= 1
        // 记录使用信息 ip 时间等
        val useInfo = JSONObject()
        useInfo["ip"] = ServletUtil.getClientIP(request)
        useInfo["useDate"] = Date().time
        experimentCode.useInfoArr.add(useInfo)
        StaticBean.experimentCodeRepository.save(experimentCode)
        // 返回
        return OutResponse(
                "000000",
                "",
                "")
    }

    // 获取体验码详情
    @GetMapping("info")
    @NeedAdminToken
    fun getExperimentCodeInfo(@RequestParam experimentCode: String): OutResponse<Any> {
        return OutResponse(
                "000000",
                "",
                StaticBean.experimentCodeRepository.findFirstByExperimentCode(experimentCode)
        )
    }

    // 获取列表-不分页
    @PostMapping("list")
    @NeedAdminToken
    fun getList(@RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
                "000000",
                "",
                CommonMongoEntityModel.getList(StaticBean.experimentCodeRepository, queryObject.toJSONString())
        )
    }

    // 获取列表-分页
    @NeedAdminToken
    @PostMapping("pageList")
    fun getPageList(

            @RequestBody query: String, @PageableDefault(
                    value = 15, sort = ["experimentCodeId"],
                    direction = Sort.Direction.DESC
            ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
                "000000",
                "",
                CommonMongoEntityModel.getPageList(StaticBean.experimentCodeRepository, queryObject.toJSONString(), pageable)
        )
    }

    // 获取一个
    @GetMapping("")
    @NeedAdminToken
    fun getOne(@RequestParam experimentCodeId: String): OutResponse<Any> {
        return OutResponse(
                "000000",
                "",
                CommonMongoEntityModel.getOneById(StaticBean.experimentCodeRepository, "experimentCodeId", experimentCodeId)
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam experimentCodeId: String): OutResponse<Any> {
        return OutResponse(
                "000000",
                "",
                CommonMongoEntityModel.deleteOneById(StaticBean.experimentCodeRepository, "experimentCodeId", experimentCodeId)
        )
    }

    // 批量删除
    @PostMapping("delete/multiple")
    @NeedAdminToken
    fun deleteMultiple(@RequestBody experimentCodeIdList: String): OutResponse<Any> {
        val idArr = JSONArray.parseArray(experimentCodeIdList)
        return OutResponse(
                "000000",
                "",
                CommonMongoEntityModel.deleteMultipleById(StaticBean.experimentCodeRepository, "experimentCodeId", idArr)
        )
    }

    // 新增或修改消息
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
                "000000",
                "",
                CommonMongoEntityModel.addOrEdit(
                        StaticBean.experimentCodeRepository,
                        ExperimentCodeEntity(),
                        "experimentCodeId",
                        infoObject
                )
        )
    }

    // 批量新增或修改消息
    @PostMapping("/multiple")
    @NeedAdminToken
    fun addOrEditMultiple(@RequestBody info: String): OutResponse<Any> {
        val infoArr = JSONArray.parseArray(info)
        val infoArrResult = JSONArray()
        val commonInfoEntity = ExperimentCodeEntity()
        for (infoObject in infoArr) {
            infoObject as JSONObject
            infoArrResult.add(
                    CommonMongoEntityModel.addOrEdit(
                            StaticBean.experimentCodeRepository,
                            commonInfoEntity,
                            "experimentCodeId",
                            infoObject
                    )
            )
        }
        return OutResponse("000000", "", infoArrResult)
    }
}