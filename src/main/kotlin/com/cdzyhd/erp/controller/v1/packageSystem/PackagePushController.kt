package com.cdzyhd.erp.controller.v1.packageSystem

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.annotation.NeedPackageSystemSchoolToken
import com.cdzyhd.erp.entity.packageSystem.PackagePushEntity
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.AggregationOperation
import org.springframework.data.mongodb.core.aggregation.LookupOperation
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/v1/packageSystem/packagePush/")
class PackagePushController {

    // 新建推送
    @PostMapping("createPush")
    @NeedAdminToken
    fun createPush(@RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val packageVersionId = queryObject.getString("packageVersionId")
        val schoolIds = queryObject.getJSONArray("schoolIds")
        val existSchoolIds = JSONArray()
        // 遍历学校id
        for (schoolId in schoolIds) {
            // 判定是否已存在同学校的推送
            schoolId as String
            val packagePush =
                StaticBean.packagePushRepository.findFirstByPackageVersionIdAndSchoolId(packageVersionId, schoolId)
            if (packagePush != null) {
                existSchoolIds.add(schoolId)
            }
        }
        if (existSchoolIds.size > 0) {
            return OutResponse("000001", "推送失败，某些学校已经推送过", existSchoolIds)
        }
        // 再次遍历学校id
        for (schoolId in schoolIds) {
            schoolId as String
            val packagePush = PackagePushEntity()
            packagePush.packageVersionId = packageVersionId
            packagePush.schoolId = schoolId
            StaticBean.packagePushRepository.save(packagePush)
        }
        // 获取包信息，更新包已推送学校ids
        val packageVersion = StaticBean.packageVersionRepository.findFirstByPackageVersionId(packageVersionId)
        // 更新包已推送学校ids,求并集
        val lastSchoolIdsSet = packageVersion.pushedSchoolIds.union(schoolIds).toSet()
        // 将set转为fastjson JSONArray
//        packageVersion.schoolIds=JSONArray(JSONArray(lastSchoolIdsSet as List<String>) )
        val lastSchoolIds = JSONArray()
        lastSchoolIds.addAll(lastSchoolIdsSet)
        packageVersion.pushedSchoolIds = lastSchoolIds

        StaticBean.packageVersionRepository.save(packageVersion)
        return OutResponse("000000", "新建推送成功", "success")
    }

    // 获取某个学校的推送表
    @PostMapping("oneSchoolPushList")
    @NeedPackageSystemSchoolToken
    fun oneSchoolPushList(
        @RequestAttribute packageSystemSchoolId: String,
        @RequestBody query: String
    ): OutResponse<Any> {
        // 获取学校用户关联的学校id
        val packageSystemSchoolEntity= StaticBean.packageSystemSchoolRepository.findFirstByPackageSystemSchoolId(packageSystemSchoolId)
        val schoolId=packageSystemSchoolEntity.schoolId
        val queryObject = JSONObject.parseObject(query)
        
        // 构建查询条件
        val criteria = Criteria()
        
        // 添加学校ID条件
        criteria.and("schoolId").`is`(schoolId)
        
        // 解析其他查询条件
        for (key in queryObject.keys) {
            if (key != "\$and" && key != "\$or") {
                criteria.and(key).`is`(queryObject[key])
            }
        }
        
        // 创建聚合操作
        val matchOperation = Aggregation.match(criteria)
        
        // 创建第一层关联查询 - 关联PackageVersion
        val lookupPackageVersionOperation = LookupOperation.newLookup()
            .from("packageManager_packageVersion")  // 关联的集合名称
            .localField("packageVersionId")         // 本地字段
            .foreignField("packageVersionId")       // 外部字段
            .`as`("packageVersion")                 // 结果字段名
            
        // 创建第二层关联查询 - 关联ExperimentVersion
        val lookupExperimentVersionOperation = LookupOperation.newLookup()
            .from("packageManager_experimentVersion")  // 关联的集合名称
            .localField("packageVersion.experimentVersionId")  // 本地字段
            .foreignField("experimentVersionId")       // 外部字段
            .`as`("experimentVersion")                 // 结果字段名
            
        // 排序 - 按创建时间倒序
        //val sortOperation = Aggregation.sort(Sort.Direction.DESC, "createTime")
        
        // 创建聚合管道
        val aggregation = Aggregation.newAggregation(
            matchOperation,
            lookupPackageVersionOperation,
            lookupExperimentVersionOperation
        )


        
        // 执行聚合查询
        val results = StaticBean.mongoTemplate.aggregate(
            aggregation,
            "packageManager_packagePush",
            Document::class.java
        ).mappedResults
        
        return OutResponse("000000", "", results)
    }

    // 获取列表-不分页
    @PostMapping("list")
    @NeedAdminToken
    fun getList(@RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getList(StaticBean.packagePushRepository, queryObject.toJSONString())
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    @NeedAdminToken
    fun getPageList(

        @RequestBody query: String, @PageableDefault(
            value = 15, sort = ["packagePushId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(StaticBean.packagePushRepository, queryObject.toJSONString(), pageable)
        )
    }

    // 获取列表-分页-包含包版本信息
    @NeedAdminToken
    @PostMapping("pageListWithPackageVersion")
    fun pageListWithPackageVersion(
        @RequestBody query: String, @PageableDefault(
            value = 15, sort = ["packagePushId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)

        // 构建查询条件
        val criteria = Criteria()

        // 解析查询条件
        for (key in queryObject.keys) {
            if (key != "\$and" && key != "\$or") {
                criteria.and(key).`is`(queryObject[key])
            }
        }

        // 创建聚合操作
        val matchOperation = Aggregation.match(criteria)

        // 创建关联查询
        val lookupOperation = LookupOperation.newLookup()
            .from("packageManager_packageVersion")  // 关联的集合名称
            .localField("packageVersionId")         // 本地字段
            .foreignField("packageVersionId")       // 外部字段
            .`as`("packageVersion")                 // 结果字段名

        // 排序
        val sortOperation = Aggregation.sort(
            Sort.by(
                if (pageable.sort.isSorted) {
                    pageable.sort.map {
                        if (it.direction == Sort.Direction.ASC) Sort.Order.asc(it.property)
                        else Sort.Order.desc(it.property)
                    }.toList()
                } else {
                    listOf(Sort.Order.desc("packagePushId"))
                }
            )
        )

        // 分页
        val skipOperation = Aggregation.skip((pageable.pageNumber * pageable.pageSize).toLong())
        val limitOperation = Aggregation.limit(pageable.pageSize.toLong())

        // 创建聚合管道
        val aggregation = Aggregation.newAggregation(
            matchOperation,
            lookupOperation,
            sortOperation,
            skipOperation,
            limitOperation
        )

        // 执行聚合查询
        val results = StaticBean.mongoTemplate.aggregate(
            aggregation,
            "packageManager_packagePush",
            Document::class.java
        ).mappedResults

        // 获取总数
        val countAggregation = Aggregation.newAggregation(
            matchOperation,
            Aggregation.count().`as`("count")
        )
        val countResult = StaticBean.mongoTemplate.aggregate(
            countAggregation,
            "packageManager_packagePush",
            Document::class.java
        ).mappedResults

        val totalElements = if (countResult.isNotEmpty()) {
            (countResult[0]["count"] as Int).toLong()
        } else {
            0L
        }

        // 构建分页结果
        val resultObject = JSONObject()
        resultObject["content"] = results
        resultObject["totalElements"] = totalElements
        resultObject["totalPages"] = Math.ceil(totalElements.toDouble() / pageable.pageSize).toInt()
        resultObject["size"] = pageable.pageSize
        resultObject["number"] = pageable.pageNumber

        return OutResponse(
            "000000",
            "",
            resultObject
        )
    }

    // 获取一个
    @GetMapping("")
    @NeedAdminToken
    fun getOne(@RequestParam packagePushId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(StaticBean.packagePushRepository, "packagePushId", packagePushId)
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam packagePushId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.packagePushRepository, "packagePushId", packagePushId)
        )
    }

    // 批量删除
    @PostMapping("delete/multiple")
    @NeedAdminToken
    fun deleteMultiple(@RequestBody packagePushIdList: String): OutResponse<Any> {
        val idArr = JSONArray.parseArray(packagePushIdList)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteMultipleById(StaticBean.packagePushRepository, "packagePushId", idArr)
        )
    }

    // 新增或修改消息
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.packagePushRepository,
                PackagePushEntity(),
                "packagePushId",
                infoObject
            )
        )
    }

    // 批量新增或修改消息
    @PostMapping("/multiple")
    @NeedAdminToken
    fun addOrEditMultiple(@RequestBody info: String): OutResponse<Any> {
        val infoArr = JSONArray.parseArray(info)
        val infoArrResult = JSONArray()
        val packagePushEntity =
            PackagePushEntity()
        for (infoObject in infoArr) {
            infoObject as JSONObject
            infoArrResult.add(
                CommonMongoEntityModel.addOrEdit(
                    StaticBean.packagePushRepository,
                    packagePushEntity,
                    "packagePushId",
                    infoObject
                )
            )
        }
        return OutResponse("000000", "", infoArrResult)
    }
}