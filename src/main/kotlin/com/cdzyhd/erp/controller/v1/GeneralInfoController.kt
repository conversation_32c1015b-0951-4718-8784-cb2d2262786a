package com.cdzyhd.erp.controller.v1

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.annotation.NeedToken
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.entity.GeneralInfoEntity
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(value = ["/v1/generalInfo/"])
class GeneralInfoController {
    // 获取列表-不分页
    @PostMapping("list")
    @NeedAdminToken
    fun getList(@RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getList(StaticBean.generalInfoRepository, queryObject.toJSONString())
        )
    }

    // 增加播放量或完播率
    @PostMapping("videoAction")
    fun videoAction(@RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val type = queryObject.getString("type")
        val id = queryObject.getString("id")
        val video = StaticBean.generalInfoRepository.findFirstByGeneralInfoId(id)
        if (video != null) {
            val info = video.info
            when (type) {
                "view" -> { // 浏览量
                    var viewNumber = info.getInteger("vn") + 1
                    info["vn"] = viewNumber
                }
                "complete" -> {
                    var completeNumber = info.getInteger("cn") + 1
                    info["cn"] = completeNumber
                }
            }
            video.info = info
            StaticBean.generalInfoRepository.save(video)
        }
        return OutResponse(
            "000000",
            "",
            ""
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    @NeedAdminToken
    fun getPageList(

        @RequestBody query: String, @PageableDefault(
            value = 15, sort = ["generalInfoId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(StaticBean.generalInfoRepository, queryObject.toJSONString(), pageable)
        )
    }

    // 获取一个
    @GetMapping("")
    fun getOne(@RequestParam generalInfoId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(StaticBean.generalInfoRepository, "generalInfoId", generalInfoId)
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam generalInfoId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.generalInfoRepository, "generalInfoId", generalInfoId)
        )
    }

    // 批量删除
    @PostMapping("delete/multiple")
    @NeedAdminToken
    fun deleteMultiple(@RequestBody generalInfoIdList: String): OutResponse<Any> {
        val idArr = JSONArray.parseArray(generalInfoIdList)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteMultipleById(StaticBean.generalInfoRepository, "generalInfoId", idArr)
        )
    }

    // 新增或修改消息
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.generalInfoRepository,
                GeneralInfoEntity(),
                "generalInfoId",
                infoObject
            )
        )
    }

    // 批量新增或修改消息
    @PostMapping("/multiple")
    @NeedAdminToken
    fun addOrEditMultiple(@RequestBody info: String): OutResponse<Any> {
        val infoArr = JSONArray.parseArray(info)
        val infoArrResult = JSONArray()
        val generalInfoEntity = GeneralInfoEntity()
        for (infoObject in infoArr) {
            infoObject as JSONObject
            infoArrResult.add(
                CommonMongoEntityModel.addOrEdit(
                    StaticBean.generalInfoRepository,
                    generalInfoEntity,
                    "generalInfoId",
                    infoObject
                )
            )
        }
        return OutResponse("000000", "", infoArrResult)
    }
}