package com.cdzyhd.erp.controller.v1

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(value = ["/v1/feedback/"])
class FeedbackController {
    // 获取列表-不分页
    @PostMapping("list")
    @NeedAdminToken
    fun getMessageList(@RequestParam platformId: String, @RequestBody query: String): JSONObject {
        return StaticBean.messageFeign.getFeedbackList(platformId, query)
    }

    // 获取列表-分页
    @PostMapping("pageList")
    @NeedAdminToken
    fun getMessagePageList(
        @RequestParam platformId: String,
        @RequestBody query: String, @PageableDefault(
            value = 15, sort = ["feedbackId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): JSONObject {
        return StaticBean.messageFeign.getFeedbackPageList(
            platformId,
            pageable.pageNumber,
            pageable.pageSize,
            pageable.sort.toString(),
            query
        )
    }

    // 获取一个
    @GetMapping("")
    @NeedAdminToken
    fun getOne(@RequestParam feedbackId: String): JSONObject {
        return StaticBean.messageFeign.getFeedbackOne(feedbackId)
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam feedbackId: String): JSONObject {
        return StaticBean.messageFeign.deleteFeedbackOne(feedbackId)
    }

    // 批量删除
    @PostMapping("delete/multiple")
    @NeedAdminToken
    fun deleteMultiple(@RequestBody feedbackIdList: String): JSONObject {
        return StaticBean.messageFeign.deleteFeedbackMultiple(feedbackIdList)
    }

    // 新增或修改
    @PostMapping("")
    @NeedAdminToken
    fun addOrEditMessage(@RequestParam platformId: String,@RequestBody info: String): JSONObject {
        return StaticBean.messageFeign.addOrEditFeedback(platformId, info)
    }

    // 批量新增或修改
    @PostMapping("/multiple")
    @NeedAdminToken
    fun addOrEditMessageMultiple(@RequestParam platformId: String,@RequestBody info: String): JSONObject {
        return StaticBean.messageFeign.addOrEditFeedbackMultiple(platformId, info)
    }
}