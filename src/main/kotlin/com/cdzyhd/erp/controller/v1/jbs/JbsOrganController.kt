package com.cdzyhd.erp.controller.v1.jbs

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.entity.jbs.JbsOrganEntity
import com.cdzyhd.erp.model.jbs.JbsOrganModel
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*

/**
 * <AUTHOR>
 * 剧本杀-机构管理
 */
@RestController
@RequestMapping("/v1/jbs/organ")
class JbsOrganController {
    private val model = JbsOrganModel()

    // 获取列表-不分页
    @PostMapping("list")
    @NeedAdminToken
    fun getList( @RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getList(StaticBean.jbsOrganRepository, queryObject.toJSONString())
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    @NeedAdminToken
    fun getPageList(

        @RequestBody query: String, @PageableDefault(
            value = 15, sort = ["jbsOrganId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(StaticBean.jbsOrganRepository, queryObject.toJSONString(), pageable)
        )
    }

    // 获取一个
    @GetMapping("")
    @NeedAdminToken
    fun getOne(@RequestParam jbsOrganId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(StaticBean.jbsOrganRepository, "jbsOrganId", jbsOrganId)
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam jbsOrganId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.jbsOrganRepository, "jbsOrganId", jbsOrganId)
        )
    }

    // 新增或修改消息
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit( @RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.jbsOrganRepository,
                JbsOrganEntity(),
                "jbsOrganId",
                infoObject
            )
        )
    }
} 