package com.cdzyhd.erp.controller.v1.packageSystem

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.entity.packageSystem.PackageVersionEntity
import com.cdzyhd.erp.entity.packageSystem.PackageClientUpdateEntity
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/v1/packageSystem/packageClientUpdate/")
class PackageClientUpdateController {

     // 获取列表-不分页
     @PostMapping("list")
     @NeedAdminToken
     fun getList(@RequestBody query: String): OutResponse<Any> {
         val queryObject = JSONObject.parseObject(query)
         return OutResponse(
             "000000",
             "",
             CommonMongoEntityModel.getList(StaticBean.packageClientUpdateRepository, queryObject.toJSONString())
         )
     }

     // 获取列表-分页
     @PostMapping("pageList")
     @NeedAdminToken
     fun getPageList(
         @RequestBody query: String, @PageableDefault(
             value = 15, sort = ["packageClientUpdateId"],
             direction = Sort.Direction.DESC
         ) pageable: Pageable
     ): OutResponse<Any> {
         val queryObject = JSONObject.parseObject(query)
         return OutResponse(
             "000000",
             "",
             CommonMongoEntityModel.getPageList(StaticBean.packageClientUpdateRepository, queryObject.toJSONString(), pageable)
         )
     }

     // 获取一个
     @GetMapping("")
     @NeedAdminToken
     fun getOne(@RequestParam packageClientUpdateId: String): OutResponse<Any> {
         return OutResponse(
             "000000",
             "",
             CommonMongoEntityModel.getOneById(StaticBean.packageClientUpdateRepository, "packageClientUpdateId", packageClientUpdateId)
         )
     }

     // 删除一个
     @DeleteMapping("")
     @NeedAdminToken
     fun deleteOne(@RequestParam packageClientUpdateId: String): OutResponse<Any> {
         return OutResponse(
             "000000",
             "",
             CommonMongoEntityModel.deleteOneById(StaticBean.packageClientUpdateRepository, "packageClientUpdateId", packageClientUpdateId)
         )
     }

     // 批量删除
     @PostMapping("delete/multiple")
     @NeedAdminToken
     fun deleteMultiple(@RequestBody packageClientUpdateIdList: String): OutResponse<Any> {
         val idArr = JSONArray.parseArray(packageClientUpdateIdList)
         return OutResponse(
             "000000",
             "",
             CommonMongoEntityModel.deleteMultipleById(StaticBean.packageClientUpdateRepository, "packageClientUpdateId", idArr)
         )
     }

     // 新增或修改消息
     @PostMapping("")
     @NeedAdminToken
     fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
         val infoObject = JSONObject.parseObject(info)

         // 版本号唯一性验证（同type下版本号不能重复）
         val versionNumber = infoObject.getLong("versionNumber")
         val type = infoObject.getString("type")
         val packageClientUpdateId = infoObject.getString("packageClientUpdateId")

         if (versionNumber != null && type != null && type.isNotEmpty()) {
             val query = Query()
             query.addCriteria(Criteria.where("versionNumber").`is`(versionNumber))
             query.addCriteria(Criteria.where("type").`is`(type))

             // 如果是修改操作，排除当前记录
             if (packageClientUpdateId != null && packageClientUpdateId.isNotEmpty()) {
                 query.addCriteria(Criteria.where("packageClientUpdateId").ne(packageClientUpdateId))
             }

             val existingEntity = StaticBean.mongoTemplate.findOne(query, PackageClientUpdateEntity::class.java)
             if (existingEntity != null) {
                 return OutResponse("400001", "该客户端类型下版本号已存在，请使用其他版本号", null)
             }
         }

         return OutResponse(
             "000000",
             "",
             CommonMongoEntityModel.addOrEdit(
                 StaticBean.packageClientUpdateRepository,
                 PackageClientUpdateEntity(),
                 "packageClientUpdateId",
                 infoObject
             )
         )
     }

     // 批量新增或修改消息
     @PostMapping("/multiple")
     @NeedAdminToken
     fun addOrEditMultiple(@RequestBody info: String): OutResponse<Any> {
         val infoArr = JSONArray.parseArray(info)
         val infoArrResult = JSONArray()
         val packageClientUpdateEntity =
             PackageClientUpdateEntity()
         for (infoObject in infoArr) {
             infoObject as JSONObject
             infoArrResult.add(
                 CommonMongoEntityModel.addOrEdit(
                     StaticBean.packageClientUpdateRepository,
                     packageClientUpdateEntity,
                     "packageClientUpdateId",
                     infoObject
                 )
             )
         }
         return OutResponse("000000", "", infoArrResult)
     }

     // 获取比传入版本号新的、确认推送的最新版本信息
     @GetMapping("getLatestPushedVersion")
     fun getLatestPushedVersion(
         @RequestParam currentVersionNumber: Long,
         @RequestParam type: String
     ): OutResponse<Any> {
         // 首先根据版本号找到当前版本的创建时间
         val currentVersionQuery = Query()
         currentVersionQuery.addCriteria(
             Criteria.where("versionNumber").`is`(currentVersionNumber)
                 .and("type").`is`(type)
         )
         val currentVersion = StaticBean.mongoTemplate.findOne(currentVersionQuery, PackageClientUpdateEntity::class.java)

         val query = Query()

         if (currentVersion != null) {
             // 如果找到当前版本，查询创建时间比当前版本新的、已推送的版本
             query.addCriteria(
                 Criteria.where("createTime").gt(currentVersion.createTime)
                     .and("isPushed").`is`(true)
                     .and("type").`is`(type)
             )
         } else {
             // 如果没找到当前版本（可能是新客户端），查询所有已推送的版本
             query.addCriteria(
                 Criteria.where("isPushed").`is`(true)
                     .and("type").`is`(type)
             )
         }

         // 按创建时间降序排序，获取最新的一个
         query.with(Sort.by(Sort.Direction.DESC, "createTime"))
         query.limit(1)

         val latestVersion = StaticBean.mongoTemplate.findOne(query, PackageClientUpdateEntity::class.java)

         return if (latestVersion != null) {
             OutResponse("000000", "找到新版本", latestVersion)
         } else {
             OutResponse("000001", "没有找到新版本", null)
         }
     }
}