package com.cdzyhd.erp.controller.v1

import cn.hutool.extra.servlet.ServletUtil
import cn.hutool.http.HttpUtil
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.entity.GeneralInfoEntity
import com.cdzyhd.erp.util.IpUtil
import org.bson.Document
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import javax.servlet.http.HttpServletRequest

@RestController
@RequestMapping("/v1/tools/")
class ToolsController {

    // 获取ip信息
    /**
     * https://ip.useragentinfo.com/json?ip=
     * 在线获取，可以获取区域
     */
    @GetMapping("ipInfoOnline1")
    fun getIpInfoOnline1(@RequestParam("ip", required = true) ip: String): OutResponse<*> {
        val result = HttpUtil.get("https://ip.useragentinfo.com/json?ip=" + ip)
        return OutResponse("000000", "", JSONObject.parseObject(result))
    }

    // 获取ip信息
    /**
     * 本地数据库获取
     */
    @GetMapping("ipInfoOnline2")
    fun getIpInfoOnline2(@RequestParam("ip", required = true) ip: String): OutResponse<*> {
        val result = IpUtil.getCityInfo(ip)
        return OutResponse("000000", "", result)
    }

    // 记录公司内网ip,公司服务器定时请求本地址，记录公司服务器所在的外网ip，用于其他用途
    @GetMapping("saveCompanyExternalIP")
    fun clientIP(request: HttpServletRequest): OutResponse<*> {
        val ip = ServletUtil.getClientIP(request)
        val findList = StaticBean.generalInfoRepository.getList(
            Document.parse("""
            {
                "type" : "companyExternalIP",
                "info" : {
                    "ip" : "$ip"
                }
            }
        """.trimIndent()
            )
        )
        if (findList.size == 0) {// 不存在
            val generalInfoEntity = GeneralInfoEntity()
            generalInfoEntity.type = "companyExternalIP"
            val info = JSONObject()
            info["ip"] = ip
            generalInfoEntity.info = info
            StaticBean.generalInfoRepository.save(generalInfoEntity)
        }
        return OutResponse("000000", "", "")
    }
}