package com.cdzyhd.erp.controller.v1

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.annotation.SysOperaLog
import com.cdzyhd.erp.feign.MessageFeign
import com.cdzyhd.erp.model.MessageModel
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/v1/message")
class MessageController {

    // 发送系统消息推送
    @PostMapping("/systemMessage")
    @NeedAdminToken
    @SysOperaLog(moduleName = "消息管理", methodName = "发送消息推送")
    fun sendExperimentMessage(@RequestBody sendParam: JSONObject): OutResponse<Any> {
        val platformId = sendParam.getString("platformId")
        StaticBean.messageFeign.addOrEditMessage(platformId, sendParam.toJSONString())
        return OutResponse("000000", "", "发送成功")
    }

    // 获取系统消息列表
    @NeedAdminToken
    @PostMapping("/systemMessage/exp/list")
    fun getSystemMessageForExp(
            @RequestParam platformId: String,
            @RequestParam userUnionId: String,
            @RequestParam page: Int,
            @RequestParam size: Int,
            @RequestBody groupCheckUserInfoArr: String
    ): OutResponse<Any> {
        return OutResponse(
                "000000",
                "",
                StaticBean.messageFeign.getSystemMessageForExp(
                        platformId,
                        userUnionId,
                        page,
                        size,
                        JSONArray.parseArray(groupCheckUserInfoArr)
                ).getJSONObject("data")
        )
    }

    // 设置某个系统消息已读
    @PostMapping("/systemMessage/read")
    @NeedAdminToken
    fun setSystemMessageRead(
            @RequestParam messageId: String,
            @RequestParam userUnionId: String,
    ): OutResponse<Any> {
        return OutResponse(
                "000000",
                "",
                StaticBean.messageFeign.setSystemMessageRead(messageId, userUnionId)
        )
    }

    // 获取日志分页列表 22-4-15 edgar
    @PostMapping("/log/pageList")
    fun getLogPageList(
            @RequestParam(required = false, defaultValue = "") platformId: String,
            @RequestParam page: Int,
            @RequestParam size: Int,
            @RequestParam(required = false) sort: String,
            @RequestBody query: String
    ): JSONObject{
        val queryObject = JSONObject.parseObject(query)
        return StaticBean.messageFeign.getSystemLogPageList(
                platformId,
                page,
                size,
                sort,
                queryObject.toJSONString()
        )
    }

}