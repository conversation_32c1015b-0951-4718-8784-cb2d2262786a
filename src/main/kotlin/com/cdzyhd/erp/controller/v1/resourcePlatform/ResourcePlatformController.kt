package com.cdzyhd.erp.controller.v1.resourcePlatform

import cn.hutool.captcha.CaptchaUtil
import cn.hutool.captcha.generator.RandomGenerator
import cn.hutool.extra.servlet.ServletUtil
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.JsonWebTokenManagerBase
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.base.common.util.PasswordUtil
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.entity.resourcePlatform.ResourcePlatformEntity
import com.cdzyhd.erp.entity.resourcePlatform.ResourcePlatformUserEntity
import com.cdzyhd.erp.util.IpUtil
import io.jsonwebtoken.Claims
import org.bson.Document
import org.springframework.core.io.ByteArrayResource
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import java.io.ByteArrayOutputStream
import javax.servlet.http.HttpServletRequest


@RestController
@RequestMapping(value = ["/v1/resourcePlatform/resourcePlatform"])
class ResourcePlatformController {

    // 生成登录和注册使用的图片验证码
    @GetMapping("captcha", produces = ["image/png"])
    fun getStream(@RequestParam sessionId: String, request: HttpServletRequest): ResponseEntity<ByteArrayResource> {
        // 扭曲验证码
        val randomGenerator: RandomGenerator = RandomGenerator("0123456789", 4)
        val captcha3 = CaptchaUtil.createShearCaptcha(200, 100, 4, 4)
        captcha3.generator = randomGenerator
        captcha3.createCode()
        val outputStream = ByteArrayOutputStream()
        captcha3.write(outputStream)
        val byteArray = outputStream.toByteArray()
        val headers = HttpHeaders()

        headers.add("Content-Disposition", "inline; filename=captcha.png")

        // 记录到redis
        val sessionKey = "resourcePlatform_userRegLogin_$sessionId"
        StaticBean.redisService.set(sessionKey, captcha3.code, 300) // 验证码5分钟内有效

        return ResponseEntity
            .status(HttpStatus.OK)
            .headers(headers)
            .body(ByteArrayResource(byteArray))
    }

    // 用户注册
    // 241213 暂停注册
    @PostMapping("userReg_notUse")
    fun userRegNotUse(@RequestBody query: String, request: HttpServletRequest): OutResponse<Any> {
        // ip限制
        var ipLimitRuleNumber = 100  // 一个小时内不能超过的次数
        var ip = ServletUtil.getClientIP(request)
        if (ip == "0:0:0:0:0:0:0:1") {
            ip = "127.0.0.1"
            ipLimitRuleNumber = 10000
        }
        val ipLimitKey = "resourcePlatform_regNumber_$ip"
        var ipLimitNumber = 0
        if (StaticBean.redisService.hasKey(ipLimitKey)) {
            ipLimitNumber = StaticBean.redisService.get(ipLimitKey) as Int
            if (ipLimitNumber >= ipLimitRuleNumber) {
                // 返回错误信息
                return OutResponse(
                    "000001",
                    "同一IP每小时注册次数不能超过${ipLimitRuleNumber}次！",
                    ""
                )
            }
            ipLimitNumber++
            StaticBean.redisService.set(ipLimitKey, ipLimitNumber, 3600)
        } else {
            StaticBean.redisService.set(ipLimitKey, 1, 3600)
        }

        val queryObject = JSONObject.parseObject(query)
        // 判断验证码是否正确
        val sessionId = queryObject.getString("sessionId")
        val captcha = queryObject.getString("captcha")
        val sessionKey = "resourcePlatform_userRegLogin_$sessionId"
        if (StaticBean.redisService.hasKey(sessionKey)) {
            val rightCaptcha = StaticBean.redisService.get(sessionKey) as String
            if (captcha != rightCaptcha) {
                return OutResponse(
                    "000001",
                    "验证码错误！",
                    ""
                )
            } else {// 验证码正确
                StaticBean.redisService.set(sessionKey, rightCaptcha, 1) // 让验证码失效
            }
        } else {
            return OutResponse(
                "000002",
                "验证码已失效！",
                ""
            )
        }

        val username = queryObject.getString("username")
        val password = queryObject.getString("password")
        // 判断用户是否已存在
        if (StaticBean.resourcePlatformUserRepository.existsByUsername(username)) {
            return OutResponse(
                "000003",
                "该用户名已存在！",
                ""
            )
        }

        // todo 判断用户名和密码是否符合规则

        // 注册和创建token 通过user微服务
        val userReturn =
            StaticBean.userFeign.createUser("res", "{}")
        if (userReturn.getString("code") == "000000") {
            val userData = userReturn.getJSONObject("data")
            val unionUserId = userData.getString("unionUserId") // 获取用户微服务的unionUserId

            // 生成用户
            val user = ResourcePlatformUserEntity()
            user.username = username
            user.password = PasswordUtil.generate(password)
            user.unionUserId = unionUserId
            StaticBean.resourcePlatformUserRepository.save(user)

            // 生成token
            val tokenReturn = StaticBean.userFeign.genToken("res", "user", unionUserId, user.resourcePlatformUserId, 15)
            if (tokenReturn.getString("code") == "000000") {
                val token = tokenReturn.getString("data")
                val returnObject = JSONObject()
                returnObject["userId"] = user.resourcePlatformUserId
                returnObject["token"] = token
                return OutResponse(
                    "000000",
                    "注册成功",
                    returnObject
                )
            } else {
                return OutResponse(
                    "000004",
                    "注册成功，但生成token失败，请重新登录！",
                    ""
                )
            }

        } else {
            return OutResponse(
                "000005",
                "注册用户失败!user服务错误！",
                ""
            )
        }
    }

    // 新增用户
    @PostMapping("addUser")
    @NeedAdminToken
    fun addUser(@RequestBody query: String, request: HttpServletRequest): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val username = queryObject.getString("username")
        val role = queryObject.getString("role")
        // 判断用户是否已存在
        if (StaticBean.resourcePlatformUserRepository.existsByUsername(username)) {
            return OutResponse(
                "000003",
                "该用户名已存在！",
                ""
            )
        }

        // 注册和创建token 通过user微服务
        val userReturn =
            StaticBean.userFeign.createUser("res", "{}")
        if (userReturn.getString("code") == "000000") {
            val userData = userReturn.getJSONObject("data")
            val unionUserId = userData.getString("unionUserId") // 获取用户微服务的unionUserId

            // 生成用户
            val user = ResourcePlatformUserEntity()
            user.username = username
            user.password = PasswordUtil.generate("123456")
            user.role = role
            user.unionUserId = unionUserId
            StaticBean.resourcePlatformUserRepository.save(user)

            return OutResponse(
                "000000",
                "新增用户成功！",
                ""
            )

        } else {
            return OutResponse(
                "000005",
                "注册用户失败!user服务错误！",
                ""
            )
        }
    }

    // 用户登录
    // 241213 适配销售登录逻辑
    @PostMapping("userLogin")
    fun userLogin(@RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        // 判断验证码是否正确
        val sessionId = queryObject.getString("sessionId")
        val captcha = queryObject.getString("captcha")
        val sessionKey = "resourcePlatform_userRegLogin_$sessionId"
        if (StaticBean.redisService.hasKey(sessionKey)) {
            val rightCaptcha = StaticBean.redisService.get(sessionKey) as String
            if (captcha != rightCaptcha) {
                return OutResponse(
                    "000001",
                    "验证码错误！",
                    ""
                )
            } else {// 验证码正确

            }
        } else {
            return OutResponse(
                "000002",
                "验证码已失效！",
                ""
            )
        }


        val username = queryObject.getString("username")
        val password = queryObject.getString("password")
        // 判断用户是否已存在
        if (StaticBean.resourcePlatformUserRepository.existsByUsername(username)) {
            val user = StaticBean.resourcePlatformUserRepository.findFirstByUsername(username)

            if (user.deleted == 1) {
                return OutResponse(
                    "000010",
                    "该用户已被禁用！",
                    ""
                )
            }

            // 逻辑-5分钟内，密码错误不能超过10次
            val wrongPassMarkKey =
                "resourcePlatform_userLoginPasswordWrong_${user.resourcePlatformUserId}"
            var wrongPassNumber = 0
            if (StaticBean.redisService.hasKey(wrongPassMarkKey)) {
                wrongPassNumber = StaticBean.redisService.get(wrongPassMarkKey) as Int
                if (wrongPassNumber >= 10) {
                    return OutResponse(
                        "000006",
                        "三分钟内密码输入错误次数已超过10次，请稍后再试！",
                        ""
                    )
                }
            }


            if (PasswordUtil.verify(password, user.password)) {
                // 241213 首次登录逻辑
                if (user.isFirstLogin == true) {
                    val needNewPassword = queryObject.contains("newPassword")
                    if (needNewPassword != true) {
                        return OutResponse(
                            "000009",
                            "首次登录，请修改密码！",
                            ""
                        )
                    } else {
                        // 修改密码
                        val newPassword = queryObject.getString("newPassword")
                        user.password = PasswordUtil.generate(newPassword)
                        user.isFirstLogin = false
                        StaticBean.resourcePlatformUserRepository.save(user)
                        return OutResponse(
                            "000000",
                            "密码修改成功，请重新登录！",
                            ""
                        )
                    }
                }

                // 241213 设备绑定逻辑
               if(user.role=="seller"){ // 如果是销售才强制要求使用客户端绑定
                   val deviceId = queryObject.getString("deviceId")
                   val confirmBindDevice = queryObject.getBoolean("confirmBindDevice")
                   if (confirmBindDevice == true) {// 确认绑定设备
                       // 判断允许绑定的设备个数
                       if (user.deviceIds.size >= user.maxDeviceNumber) {
                           return OutResponse(
                               "000008",
                               "该账号最多绑定${user.maxDeviceNumber}个设备，请先解绑其他设备！",
                               ""
                           )
                       }
                       // 绑定设备
                       user.deviceIds.add(deviceId)
                       StaticBean.resourcePlatformUserRepository.save(user)
                   } else {// 没有传绑定设备
                       // 判断有没有绑定设备id
                       // 如果已绑定设备列表里没有本设备，并且绑定数量小于允许绑定数量
                       if (!user.deviceIds.contains(deviceId) && user.deviceIds.size < user.maxDeviceNumber) {
                           return OutResponse(
                               "000007",
                               "该账号未绑定当前设备，请确认是否绑定当前设备！绑定后不可修改！",
                               ""
                           )
                       }
                   }
               }


                // 生成token
                var expireDay = 15 // 默认15天
                if (user.role == "seller") {
                    expireDay = 1 // 销售1天
                }
                val tokenReturn =
                    StaticBean.userFeign.genToken(
                        "res",
                        "user",
                        user.unionUserId,
                        user.resourcePlatformUserId,
                        expireDay
                    )
                if (tokenReturn.getString("code") == "000000") {
                    val token = tokenReturn.getString("data")
                    val returnObject = JSONObject()
                    returnObject["userId"] = user.resourcePlatformUserId
                    returnObject["token"] = token
                    return OutResponse(
                        "000000",
                        "登录成功",
                        returnObject
                    )
                } else {
                    return OutResponse(
                        "000003",
                        "登录成功，但生成token失败，请重新登录！",
                        ""
                    )
                }


            } else {
                wrongPassNumber++
                StaticBean.redisService.set(wrongPassMarkKey, wrongPassNumber, 300) // 更新此账号密码错误次数，5分钟内有效

                // 密码错误时让验证码也失效
                StaticBean.redisService.set(sessionKey, "9323", 1) // 让验证码失效
                return OutResponse(
                    "000004",
                    "密码错误！",
                    ""
                )
            }
        } else {
            return OutResponse(
                "000005",
                "该用户名不存在！",
                ""
            )
        }
    }

    // 用户退出登录
    @GetMapping("userLogout")
    fun userLogout(request: HttpServletRequest): OutResponse<Any> {
        val token = request.getHeader("Authorization")
        StaticBean.userFeign.removeToken("res", token, "user")
        return OutResponse(
            "000000",
            "已退出登录！",
            ""
        )
    }

    // token检测
    fun tokenCheck(token: String): Boolean {
        val tokenCheckResult = StaticBean.userFeign.tokenCheck("res", "user", token)
        return tokenCheckResult.getString("code") == "000000" && tokenCheckResult.getBoolean("data")
    }

    // 通过token获取用户信息
    @GetMapping("userInfo")
    fun getUserInfo(request: HttpServletRequest): OutResponse<Any> {
        val token = request.getHeader("Authorization")
        if (tokenCheck(token)) {
            val claims: Claims = JsonWebTokenManagerBase().parse_token(token)
            val userId = claims.subject
            val user = StaticBean.resourcePlatformUserRepository.findFirstByResourcePlatformUserId(userId)
            if (user == null) {
                return OutResponse(
                    "401",
                    "用户不存在，请重新登录！",
                    ""
                )
            }
            // 记录ip等信息
            val ip = ServletUtil.getClientIP(request)
            user.otherInfo["ip"] = ip
            // 获取ip归属地
            user.otherInfo["ipAddress"] = IpUtil.getCityInfo(ip)
            // 保存信息
            StaticBean.resourcePlatformUserRepository.save(user)
            user.password = null
            return OutResponse(
                "000000",
                "",
                user
            )
        } else {
            return OutResponse(
                "401",
                "token已失效，请重新登录！",
                ""
            )
        }
    }

    // 用户添加收藏
    @PostMapping("userFav")
    fun userFav(request: HttpServletRequest, @RequestBody query: String): OutResponse<Any> {
        val token = request.getHeader("Authorization")
        if (tokenCheck(token)) {
            val claims: Claims = JsonWebTokenManagerBase().parse_token(token)
            val userId = claims.subject
            val user = StaticBean.resourcePlatformUserRepository.findFirstByResourcePlatformUserId(userId)

            val queryObject = JSONObject.parseObject(query)
            val id = queryObject.getString("id")
            val type = queryObject.getString("type")
            val action = queryObject.getString("action")
            val newArray = JSONArray()
            when (type) {
                "experiment" -> {
                    if (!user.favExperiment.contains(id)) {
                        if (action == "add") {
                            // 实现添加到头部
                            newArray.add(id)
                            for (item in user.favExperiment) {
                                newArray.add(item)
                            }
                            user.favExperiment = newArray
                        }
                    } else {
                        if (action == "del") {
                            user.favExperiment.remove(id)
                        }
                    }
                }

                "3dVideo" -> {
                    if (!user.fav3dVideo.contains(id)) {
                        if (action == "add") {
                            // 实现添加到头部
                            newArray.add(id)
                            for (item in user.fav3dVideo) {
                                newArray.add(item)
                            }
                            user.fav3dVideo = newArray
                        }
                    } else {
                        if (action == "del") {
                            user.fav3dVideo.remove(id)
                        }
                    }
                }

                "panorama" -> {
                    if (!user.favPanorama.contains(id)) {
                        if (action == "add") {
                            // 实现添加到头部
                            newArray.add(id)
                            for (item in user.favPanorama) {
                                newArray.add(item)
                            }
                            user.favPanorama = newArray
                        }
                    } else {
                        if (action == "del") {
                            user.favPanorama.remove(id)
                        }
                    }
                }

                "partyBuilding" -> {
                    if (!user.favPartyBuilding.contains(id)) {
                        if (action == "add") {
                            // 实现添加到头部
                            newArray.add(id)
                            for (item in user.favPartyBuilding) {
                                newArray.add(item)
                            }
                            user.favPartyBuilding = newArray
                        }
                    } else {
                        if (action == "del") {
                            user.favPartyBuilding.remove(id)
                        }
                    }
                }
            }
            StaticBean.resourcePlatformUserRepository.save(user)
            return OutResponse(
                "000000",
                "",
                user
            )
        } else {
            return OutResponse(
                "401",
                "token已失效，请重新登录！",
                ""
            )
        }
    }

    // 获取列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getList(StaticBean.resourcePlatformRepository, queryObject.toJSONString())
        )
    }

    // 获取给定id的列表
    @PostMapping("listByIds")
    fun getListByIds(@RequestBody query: String): OutResponse<Any> {
        val ids = JSONArray.parseArray(query)
        val list = JSONArray()
        for (id in ids) {
            id as String
            val entity =
                StaticBean.resourcePlatformRepository.findFirstByResourcePlatformIdAndDeleted(id, 0) // 获取id和未逻辑删除的
            if (entity != null) {
                list.add(entity)
            }
        }
        return OutResponse(
            "000000",
            "",
            list
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String, @PageableDefault(
            value = 15, sort = ["resourcePlatformId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        var asAdmin = false // 是否是后台请求
        val queryObject = JSONObject.parseObject(query)
        if (queryObject.containsKey("asAdmin")) {
            asAdmin = true
            queryObject.remove("asAdmin") // 清除key，避免影响查询
        }
        val pageList =
            StaticBean.resourcePlatformRepository.getPageList(Document.parse(queryObject.toJSONString()), pageable)
        if (queryObject["type"] == "experiment" && !asAdmin) { // 如果请求类型是虚仿课程、并且不是后台请求
            // 虚仿课程视频列表判断
            for (li in pageList.content) {
                // 判断哪些视频要隐藏
                val lastVideos = JSONArray()
                for (video in li.info.getJSONArray("videos")) {
                    video as LinkedHashMap<String, Any>
                    if (video.containsKey("show")) {
                        if (video["show"] as Boolean) {
                            lastVideos.add(video)
                        }
                    }
                }
                li.info["videos"] = lastVideos
                // 判断所以视频是否加密
                if (li.info.containsKey("videoPassword")) {
                    if (li.info.getBoolean("videoPassword") == true) {
                        val newVideos = JSONArray()
                        for (i in li.info.getJSONArray("videos")) {
                            newVideos.add(JSONObject())
                        }
                        li.info["videos"] = newVideos // 清除视频列表,但是保留同数量对象，供前端渲染
                        li.info["product_video"] = "" // 清除老视频地址
                    }
                }
            }
        }
        if (queryObject["type"] == "3dVideo" && !asAdmin) { // 如果请求类型是3d微视频、并且不是后台请求
            for (li in pageList.content) {
                if (li.info.containsKey("videoShow")) {// 如果视频被隐藏
                    if (!li.info.getBoolean("videoShow")) {
                        li.info["video"] = null
                    }
                } else {
                    li.info["video"] = null
                }
            }
        }
        return OutResponse(
            "000000",
            "",
            pageList
        )
    }

    // 获取一个资源信息
    @GetMapping("")
    fun getOne(@RequestParam resourcePlatformId: String): OutResponse<Any> {
        val one = StaticBean.resourcePlatformRepository.findFirstByResourcePlatformId(resourcePlatformId)
        if (one.type == "experiment") {// 虚仿视频类型
            // 判断哪些视频不能显示
            val lastVideos = JSONArray()
            for (video in one.info.getJSONArray("videos")) {
                video as JSONObject
                if (video.containsKey("show")) {
                    if (video.getBoolean("show") == true) {
                        lastVideos.add(video)
                    }
                }
            }
            one.info["videos"] = lastVideos
            if (one.info.containsKey("videoPassword")) {
                if (one.info.getBoolean("videoPassword") == true) {
                    val newVideos = JSONArray()
                    for (i in one.info.getJSONArray("videos")) {
                        newVideos.add(JSONObject())
                    }
                    one.info["videos"] = newVideos // 清除视频列表,但是保留同数量对象，供前端渲染
                    one.info["product_video"] = "" // 清除老视频地址
                }
            }
        }

        return OutResponse(
            "000000",
            "",
            one
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam resourcePlatformId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(
                StaticBean.resourcePlatformRepository,
                "resourcePlatformId",
                resourcePlatformId
            )
        )
    }

    // 批量删除
    @PostMapping("delete/multiple")
    @NeedAdminToken
    fun deleteMultiple(@RequestBody resourcePlatformIdList: String): OutResponse<Any> {
        val idArr = JSONArray.parseArray(resourcePlatformIdList)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteMultipleById(
                StaticBean.resourcePlatformRepository,
                "resourcePlatformId",
                idArr
            )
        )
    }

    // 新增或修改
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.resourcePlatformRepository,
                ResourcePlatformEntity(),
                "resourcePlatformId",
                infoObject
            )
        )
    }

    // 批量新增或修改
    @PostMapping("/multiple")
    @NeedAdminToken
    fun addOrEditMultiple(@RequestBody info: String): OutResponse<Any> {
        val infoArr = JSONArray.parseArray(info)
        val infoArrResult = JSONArray()
        val resourcePlatform = ResourcePlatformEntity()
        for (infoObject in infoArr) {
            infoObject as JSONObject
            infoArrResult.add(
                CommonMongoEntityModel.addOrEdit(
                    StaticBean.resourcePlatformRepository,
                    resourcePlatform,
                    "resourcePlatformId",
                    infoObject
                )
            )
        }
        return OutResponse("000000", "", infoArrResult)
    }

    // 动作-for 前台
    @PostMapping("actionF")
    fun actionF(
        @RequestParam action: String,
        request: HttpServletRequest,
        @RequestBody params: String
    ): OutResponse<Any> {
        var returnInfo: Any? = null // 通用返回
        val paramsObject = JSONObject.parseObject(params)
        when (action) {
            "checkVideoPassword" -> { // 检测视频密码
                val password = paramsObject.getString("password")
                val rightPasswordResult = StaticBean.configFeign.getHashConfigInfo(
                    "config_resourcePlatform",
                    "resource_experiment_videoPassword_240612"
                )
                val rightPassword = rightPasswordResult.getString("data")
                val id = paramsObject.getString("id")
                val one = StaticBean.resourcePlatformRepository.findFirstByResourcePlatformId(id)
                if (password == rightPassword) {
                    returnInfo = one
                } else {
                    returnInfo = false
                }
            }
            // 获取销售客户端版本
            "getSellerClientVersion" -> {
                // 获取销售客户端版本
                val version =
                    StaticBean.configFeign.getHashConfigInfo("config_resourcePlatform", "client_seller_last_version")
                        .getString("data")
                // 获取最新版本下载地址
                val downloadUrl = StaticBean.configFeign.getHashConfigInfo(
                    "config_resourcePlatform",
                    "client_seller_last_download_url"
                ).getString("data")
                returnInfo = JSONObject()
                returnInfo["version"] = version
                returnInfo["downloadUrl"] = downloadUrl
            }
        }
        return OutResponse("000000", "", returnInfo)
    }
}