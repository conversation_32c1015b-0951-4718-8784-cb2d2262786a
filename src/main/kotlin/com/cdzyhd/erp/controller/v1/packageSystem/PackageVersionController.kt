package com.cdzyhd.erp.controller.v1.packageSystem

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.entity.packageSystem.PackageVersionEntity
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/v1/packageSystem/packageVersion/")
class PackageVersionController {

     // 获取列表-不分页
     @PostMapping("list")
     @NeedAdminToken
     fun getList(@RequestBody query: String): OutResponse<Any> {
         val queryObject = JSONObject.parseObject(query)
         return OutResponse(
             "000000",
             "",
             CommonMongoEntityModel.getList(StaticBean.packageVersionRepository, queryObject.toJSONString())
         )
     }
 
     // 获取列表-分页
     @PostMapping("pageList")
     @NeedAdminToken
     fun getPageList(
 
         @RequestBody query: String, @PageableDefault(
             value = 15, sort = ["packageVersionId"],
             direction = Sort.Direction.DESC
         ) pageable: Pageable
     ): OutResponse<Any> {
         val queryObject = JSONObject.parseObject(query)
         return OutResponse(
             "000000",
             "",
             CommonMongoEntityModel.getPageList(StaticBean.packageVersionRepository, queryObject.toJSONString(), pageable)
         )
     }
 
     // 获取一个
     @GetMapping("")
     @NeedAdminToken
     fun getOne(@RequestParam packageVersionId: String): OutResponse<Any> {
         return OutResponse(
             "000000",
             "",
             CommonMongoEntityModel.getOneById(StaticBean.packageVersionRepository, "packageVersionId", packageVersionId)
         )
     }
 
     // 删除一个
     @DeleteMapping("")
     @NeedAdminToken
     fun deleteOne(@RequestParam packageVersionId: String): OutResponse<Any> {
         return OutResponse(
             "000000",
             "",
             CommonMongoEntityModel.deleteOneById(StaticBean.packageVersionRepository, "packageVersionId", packageVersionId)
         )
     }
 
     // 批量删除
     @PostMapping("delete/multiple")
     @NeedAdminToken
     fun deleteMultiple(@RequestBody packageVersionIdList: String): OutResponse<Any> {
         val idArr = JSONArray.parseArray(packageVersionIdList)
         return OutResponse(
             "000000",
             "",
             CommonMongoEntityModel.deleteMultipleById(StaticBean.packageVersionRepository, "packageVersionId", idArr)
         )
     }
 
     // 新增或修改消息
     @PostMapping("")
     @NeedAdminToken
     fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
         val infoObject = JSONObject.parseObject(info)
         return OutResponse(
             "000000",
             "",
             CommonMongoEntityModel.addOrEdit(
                 StaticBean.packageVersionRepository,
                 PackageVersionEntity(),
                 "packageVersionId",
                 infoObject
             )
         )
     }
 
     // 批量新增或修改消息
     @PostMapping("/multiple")
     @NeedAdminToken
     fun addOrEditMultiple(@RequestBody info: String): OutResponse<Any> {
         val infoArr = JSONArray.parseArray(info)
         val infoArrResult = JSONArray()
         val packageVersionEntity =
             PackageVersionEntity()
         for (infoObject in infoArr) {
             infoObject as JSONObject
             infoArrResult.add(
                 CommonMongoEntityModel.addOrEdit(
                     StaticBean.packageVersionRepository,
                     packageVersionEntity,
                     "packageVersionId",
                     infoObject
                 )
             )
         }
         return OutResponse("000000", "", infoArrResult)
     }
}