package com.cdzyhd.erp.controller.v1.officialWeb

import cn.hutool.http.HttpUtil
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.entity.officialWeb.OfficialWebNewEntity
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*
import java.lang.IllegalStateException

@RestController
@RequestMapping(value = ["/v1/officialWeb/product"])
class OfficialWebProductController {

    @GetMapping("/oneProductConfig")
    fun getOne(@RequestParam configType: String,@RequestParam id: String): OutResponse<Any> {
        var configKey=""
        if(configType=="test"){
            configKey="config_officialWeb_test"
        }
        if(configType=="official"){
            configKey="config_officialWeb"
        }
        val allSeriesList=JSONObject.parseObject(StaticBean.configFeign.getHashConfigInfo(configKey, "productManage")
                .getString("data")).getJSONObject("experimentInfo").getJSONArray("list").getJSONArray(0)

        // 遍历寻找id相同的配置
        var product=JSONObject()
        var find=false

        for(series in allSeriesList){
            series as JSONObject
            val seriesList=series.getJSONArray("series")
            for(serie in seriesList){
                serie as JSONObject
                val experimentList=serie.getJSONArray("experiments")
                for(experiment in experimentList){
                    experiment as JSONObject
                    if(experiment.getString("id")==id){
                        product=experiment
                        find=true
                        break
                    }
                }
                if(find){
                    break
                }
            }
            if(find){
                break
            }
        }
        return OutResponse(
                "000000",
                "",
                product
        )
    }
}