package com.cdzyhd.erp.controller.v1

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.annotation.SysOperaLog
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/v1/config/")
class ConfigController {
    // 获取配置
    @GetMapping("config")
//    @SysOperaLog(moduleName = "配置",methodName = "获取hash配置")
    fun getConfig(@RequestParam key: String, @RequestParam field: String): JSONObject {
        return StaticBean.configFeign.getHashConfigInfo(key, field)
    }

    // 设置配置
    //@SysOperaLog(moduleName = "配置",methodName = "设置hash配置")
    @NeedAdminToken
    @PostMapping("config")
    fun editConfig(@RequestParam key: String, @RequestParam field: String, @RequestBody info: String): JSONObject {
        return StaticBean.configFeign.editHashConfigInfo(key, field, info, -1)
    }
}