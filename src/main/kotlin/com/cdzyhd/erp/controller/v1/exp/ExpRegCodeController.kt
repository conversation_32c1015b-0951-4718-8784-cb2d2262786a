package com.cdzyhd.erp.controller.v1.exp

import cn.hutool.core.util.CharsetUtil
import cn.hutool.crypto.Mode
import cn.hutool.crypto.Padding
import cn.hutool.crypto.symmetric.AES
import cn.hutool.extra.servlet.ServletUtil
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.erp.a.StaticBean
import com.cdzyhd.erp.annotation.NeedAdminToken
import com.cdzyhd.erp.entity.exp.ExpRegCodeEntity
import com.cdzyhd.erp.entity.exp.ExpRegCodeLogEntity
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*
import java.text.SimpleDateFormat
import java.util.*
import javax.servlet.http.HttpServletRequest
import kotlin.collections.LinkedHashMap


@RestController
@RequestMapping(value = ["/v1/exp/reg_code/"])
class ExpRegCodeController {
    // 绑定设备
    @PostMapping("bindDevice")
    fun bindDevice(request: HttpServletRequest, @RequestBody info: JSONObject): OutResponse<Any> {
        var serial = info.getString("serial") // 序列号 传入的经过aes加密的序列号
        serial = aesDecode(serial) // aes解密
        val name = info.getString("name") // 设备名称
        val code = info.getString("code") // 注册码
        val experimentId = info.getString("experimentId") // 实验id
        val ip = ServletUtil.getClientIP(request)
        // 获取注册码详情
        val regCodeEntity = StaticBean.expRegCodeRepository.findFirstByExpRegCodeId(code)
        // 判断是否存在
        if (regCodeEntity == null) {
            saveLog(code, serial, name, "绑定", "000001", "未找到该注册码，请检查是否输入正确！", ip, "", experimentId)
            return OutResponse(
                "000001",
                "未找到该注册码，请检查是否输入正确！",
                ""
            )
        }
        // 判断授权实验
        val experimentList = regCodeEntity.experimentList
        var findExperiment = false
        var experimentName = ""
        for (experiment in experimentList) {
            experiment as LinkedHashMap<String, Any>
            if ((experiment["id"] as String) == experimentId) {
                findExperiment = true
                experimentName = experiment["name"] as String
                break
            }
        }
        if (!findExperiment) {
            saveLog(
                code,
                serial,
                name,
                "绑定",
                "000003",
                "绑定失败！该实验未授权！请联系管理员。",
                ip,
                experimentName,
                experimentId
            )
            return OutResponse(
                "000003",
                "绑定失败！该实验未授权！请联系管理员。",
                ""
            )
        }
        // 判断注册码是否启用
        if (!regCodeEntity.active) {
            saveLog(
                code,
                serial,
                name,
                "绑定",
                "000002",
                "该注册码已被禁用！请联系管理员！",
                ip,
                experimentName,
                experimentId
            )
            return OutResponse(
                "000002",
                "该注册码已被禁用！请联系管理员！",
                ""
            )
        }
        // 241031 判断是否在绑定时间范围内
        if (regCodeEntity.limitBindTime) {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
            val timeStart = dateFormat.parse(regCodeEntity.limitBindTimeDate[0] as String).time
            val timeEnd = dateFormat.parse(regCodeEntity.limitBindTimeDate[1] as String).time
            val timeNow = Date().time
            // 判断现在是否在绑定时间内
            if (timeNow < timeStart || timeNow > timeEnd) {
                saveLog(
                    code,
                    serial,
                    name,
                    "绑定",
                    "000007",
                    "该注册码当前未在规定绑定时间内绑定！请联系管理员！",
                    ip,
                    experimentName,
                    experimentId
                )
                return OutResponse(
                    "000007",
                    "该注册码当前未在规定绑定时间内绑定！请联系管理员！",
                    ""
                )
            }
        }
        // 241031 判断是否在规定ip范围内
        if (regCodeEntity.limitIp) {// 如果要限制ip
            var findIp = false
            val ipList = regCodeEntity.limitIpList
            for (ipInfo in ipList) {
                ipInfo as LinkedHashMap<String, Any>
                val ipLi = ipInfo["ip"] as String
                if (ip.startsWith(ipLi)) { // 241112-开头对比，这样可以兼容前几位、完整ip模式
                    findIp = true
                    break
                }
            }
            if (!findIp) {
                saveLog(
                    code,
                    serial,
                    name,
                    "绑定",
                    "000008",
                    "该设备IP（$ip）未在授权范围内！请联系管理员！",
                    ip,
                    experimentName,
                    experimentId
                )
                return OutResponse(
                    "000008",
                    "该设备IP（$ip）未在授权范围内！请联系管理员！",
                    ""
                )
            }
        }
        // 判断是否已经绑定 241112 设备列表改为json对象格式，因为可能存很多设备，增加查询性能
        val bindDeviceList = regCodeEntity.bindDevicesList
        if (bindDeviceList.contains(serial)) {
            saveLog(
                code,
                serial,
                name,
                "绑定",
                "000000",
                "绑定成功！该设备序列号已和该注册码绑定过，无需再次绑定！",
                ip,
                experimentName,
                experimentId
            )
            return OutResponse(
                "000000",// 返回成功-原因是同一台机器删除实验后可以再次绑定
                "绑定成功！该设备序列号已和该注册码绑定过，无需再次绑定！",
                ""
            )
        }
        // 判断是否到达绑定上限
        val bindDeviceSize = bindDeviceList.size
        if (bindDeviceSize >= regCodeEntity.maxDevicesNumber) {
            saveLog(
                code,
                serial,
                name,
                "绑定",
                "000004",
                "绑定失败！超过该注册码设备数量限制，请联系管理员。",
                ip,
                experimentName,
                experimentId
            )
            return OutResponse(
                "000004",
                "绑定失败！超过该注册码设备数量限制，请联系管理员。",
                ""
            )
        }
        // 绑定
        val bindObject = JSONObject()
        bindObject["name"] = name
        bindDeviceList[serial] = bindObject
        regCodeEntity.bindDevicesList = bindDeviceList
        // 保存操作结果
        StaticBean.expRegCodeRepository.save(regCodeEntity)
        // 返回
        saveLog(
            code,
            serial,
            name,
            "绑定",
            "000000",
            "绑定成功！该设备序列号已和该注册码绑定！",
            ip,
            experimentName,
            experimentId
        )
        return OutResponse(
            "000000",
            "绑定成功！该设备序列号已和该注册码绑定！",
            ""
        )
    }


    // 使用注册码
    @PostMapping("useCode")
    fun useCode(request: HttpServletRequest, @RequestBody info: JSONObject): OutResponse<Any> {
        var serial = info.getString("serial") // 序列号 传入的经过aes加密的序列号
        serial = aesDecode(serial) // aes解密
        val code = info.getString("code") // 注册码
        val experimentId = info.getString("experimentId") // 实验编号
        var name = ""
        val ip = ServletUtil.getClientIP(request)
        // 获取注册码详情
        val regCodeEntity = StaticBean.expRegCodeRepository.findFirstByExpRegCodeId(code)
        // 判断是否存在
        if (regCodeEntity == null) {
            saveLog(code, serial, name, "使用", "000001", "未找到该注册码，请检查是否输入正确！", ip, "", experimentId)
            return OutResponse(
                "000001",
                "未找到该注册码，请检查是否输入正确！",
                ""
            )
        }
        // 判断授权实验
        val experimentList = regCodeEntity.experimentList
        var findExperiment = false
        var experimentName = ""
        for (experiment in experimentList) {
            experiment as LinkedHashMap<String, Any>
            if ((experiment["id"] as String) == experimentId) {
                findExperiment = true
                experimentName = experiment["name"] as String
                break
            }
        }
        if (!findExperiment) {
            saveLog(
                code,
                serial,
                name,
                "使用",
                "000005",
                "该实验未授权！请联系管理员！",
                ip,
                experimentName,
                experimentId
            )
            return OutResponse(
                "000006",
                "该实验未授权！请联系管理员！",
                ""
            )
        }
        // 241031 判断是否在规定ip范围内
        if (regCodeEntity.limitIp) {
            var findIp = false
            val ipList = regCodeEntity.limitIpList
            for (ipInfo in ipList) {
                ipInfo as LinkedHashMap<String, Any>
                val ipLi = ipInfo["ip"] as String
                if (ip.startsWith(ipLi)) { // 241112-开头对比，这样可以兼容前几位、完整ip模式
                    findIp = true
                    break
                }
            }
            if (!findIp) {
                saveLog(
                    code, serial, name, "绑定", "000008",
                    "该设备IP（$ip）未在授权范围内！请联系管理员！", ip, experimentName, experimentId
                )
                return OutResponse(
                    "000008",
                    "该设备IP（$ip）未在授权范围内！请联系管理员！",
                    ""
                )
            }
        }
        // 判断注册码是否启用
        if (!regCodeEntity.active) {
            saveLog(
                code,
                serial,
                name,
                "使用",
                "000002",
                "该注码已被禁用！请联系管理员！",
                ip,
                experimentName,
                experimentId
            )
            return OutResponse(
                "000002",
                "该注码已被禁用！请联系管理员！",
                ""
            )
        }
        // 判断该注册码是否包含该序列号
        val bindDeviceList = regCodeEntity.bindDevicesList
        if (!bindDeviceList.containsKey(serial)) {
            saveLog(
                code,
                serial,
                name,
                "使用",
                "000003",
                "该设备序列号未和注册码绑定！请联系管理员！",
                ip,
                experimentName,
                experimentId
            )
            return OutResponse(
                "000003",
                "该设备序列号未和注册码绑定！请联系管理员！",
                ""
            )
        }
        val deviceInfo=bindDeviceList[serial] as LinkedHashMap<String,Any>
        name=deviceInfo["name"] as String
        // 判断授权时间
        if (regCodeEntity.limitTime) {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
            val timeStart = dateFormat.parse(regCodeEntity.limitTimeDate[0] as String).time
            val timeEnd = dateFormat.parse(regCodeEntity.limitTimeDate[1] as String).time
            val timeNow = Date().time
            // 判断现在是否在授权时间内
            if (timeNow < timeStart || timeNow > timeEnd) {
                saveLog(
                    code,
                    serial,
                    name,
                    "使用",
                    "000004",
                    "该注册码当前未在授权时间内！请联系管理员！",
                    ip,
                    experimentName,
                    experimentId
                )
                return OutResponse(
                    "000004",
                    "该注册码当前未在授权时间内！请联系管理员！",
                    ""
                )
            }
        }
        // 判断使用次数
        if (regCodeEntity.limitRunNumber) {
            if (regCodeEntity.limitRunNumberCount == 0) {
                saveLog(
                    code,
                    serial,
                    name,
                    "使用",
                    "000006",
                    "该注册码的使用次数已用完！请联系管理员！",
                    ip,
                    experimentName,
                    experimentId
                )
                return OutResponse(
                    "000006",
                    "该注册码的使用次数已用完！请联系管理员！",
                    ""
                )
            }
            regCodeEntity.limitRunNumberCount -= 1 // 减去使用次数
            StaticBean.expRegCodeRepository.save(regCodeEntity) // 保存
        }
        // 返还成功信息
        saveLog(code, serial, name, "使用", "000000", "注册码使用成功！", ip, experimentName, experimentId)
        val returnData = JSONObject()
        returnData["needLogin"] = regCodeEntity.needLogin // 返还是否需要登录
        return OutResponse(
            "000000",
            "注册码使用成功！",
            returnData
        )
    }

    // 保存注册码使用日志
    private fun saveLog(
        regCode: String,
        serial: String,
        serialName: String,
        action: String,
        code: String,
        msg: String,
        ip: String,
        experimentName: String,
        experimentId: String,
    ) {
        val expRegCodeLogEntity = ExpRegCodeLogEntity()
        expRegCodeLogEntity.expRegCodeId = regCode
        expRegCodeLogEntity.deviceSerial = serial
        expRegCodeLogEntity.deviceName = serialName
        expRegCodeLogEntity.action = action
        expRegCodeLogEntity.code = code
        expRegCodeLogEntity.msg = msg
        expRegCodeLogEntity.ip = ip
        expRegCodeLogEntity.experimentName = experimentName
        expRegCodeLogEntity.experimentId = experimentId
        StaticBean.expRegCodeLogRepository.save(expRegCodeLogEntity)
    }

    // aes加密 cbc PKCS5Padding
    private fun aesEncode(content: String): String {
        val key = "cdzyhdaes1234567"
        // 构建
        val aes = AES(Mode.CBC, Padding.PKCS5Padding, key.toByteArray(), key.toByteArray())
        // 加密为16进制表示
        return aes.encryptHex(content)
    }

    // aes解密 cbc PKCS5Padding
    private fun aesDecode(encryptHex: String): String {
        val key = "cdzyhdaes1234567"
        // 构建
        val aes = AES(Mode.CBC, Padding.PKCS5Padding, key.toByteArray(), key.toByteArray())
        return aes.decryptStr(encryptHex, CharsetUtil.CHARSET_UTF_8)
    }

    // 获取列表-不分页
    @PostMapping("list")
    @NeedAdminToken
    fun getList(@RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getList(StaticBean.expRegCodeRepository, queryObject.toJSONString())
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    @NeedAdminToken
    fun getPageList(

        @RequestBody query: String, @PageableDefault(
            value = 15, sort = ["expRegCodeId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(StaticBean.expRegCodeRepository, queryObject.toJSONString(), pageable)
        )
    }

    // 获取一个
    @GetMapping("")
    @NeedAdminToken
    fun getOne(@RequestParam expRegCodeId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(StaticBean.expRegCodeRepository, "expRegCodeId", expRegCodeId)
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam expRegCodeId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.expRegCodeRepository, "expRegCodeId", expRegCodeId)
        )
    }

    // 新增或修改消息
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.expRegCodeRepository,
                ExpRegCodeEntity(),
                "expRegCodeId",
                infoObject
            )
        )
    }
}