package com.cdzyhd.erp.event;

import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.erp.a.StaticBean;
import com.cdzyhd.erp.config.CommonConfig;
import com.cdzyhd.erp.entity.SysLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@EnableAsync
public class SysLogListener {
    @Async
    @Order
    @EventListener(SysLogEvent.class)
    public void saveSysLog(SysLogEvent event) {
        SysLog sysLog = (SysLog) event.getSource();
        // 连接feign 保存日志
        try {
            StaticBean.messageFeign.addOrEditSystemLog(StaticBean.commonConfig.platformId, JSONObject.toJSONString(sysLog));
        } catch (Exception ignored) {

        }
    }
}
