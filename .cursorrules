// 智云鸿道-前端-vue2程序开发规范

// 目录结构
- src/
  - api 存放api定义，按功能进行文件区分，比如导出功能就放在ExportApi.js文件中
  `
  import {request_async} from "@/utils/requestAsync";
    import {API_URL_EXP_CONSUMER} from "@/model/ConfigModel";

    // 获取登录日志列表
    export async function export(params) {
        return request_async(API_URL_EXP_CONSUMER + `consumer/export`, "post_json", params);
    }
  `
  - model 存放model类，引用api定义和实现其它逻辑,按功能进行文件区分，比如导出功能就放在ExportModel.js文件中
  `
        import {export} from "@/api/ExportApi";

        /**
        * 导出model
        */
        class ExportModel {
            // 获取列表
            static async export(query) {
                let [data] = await export(query)
                if (data.code === 20000) { // 20000表示成功
                    return data.data;
                } else {
                    return false;
                }
            }
        }

        export {ExportModel} 
  `

// 页面开发规范
- data中的属性要按功能进行分组，比如导出功能都放在 export对象下面
- methods中的方法要按功能进行分组，比如导出功能都放在 exportMethods方法下面，并返回一个方法对象，对象的子方法里面用$this来指向vue实例
`
methods: {
    // 导出方法集合
    exportMethods() {
        let $this = this
        return {
            export() {
                $this.exportDialog.dialog = true
                // 其他逻辑
            }
        }
    }
}
`

