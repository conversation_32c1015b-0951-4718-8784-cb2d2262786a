# 学生端更新API设计文档

## 概述

学生端更新机制已经按照您的要求进行了修正，现在学生端直接从学校端获取更新，而不是从中央端。学生端只更新Windows exe客户端文件，不涉及webDist等其他文件。

## 核心设计原则

### 1. 学生端从学校端获取更新
- **学生端API全部对接学校端**，版本要对应上
- **不需要传递版本参数**，学校端根据自己当前版本返回对应的学生端更新
- **使用相对路径**，学校端返回相对路径，学生端拼接完整URL

### 2. 学生端只更新exe文件
- **只下载Windows客户端exe文件**
- **不需要webDist、Linux版本等其他文件**
- **简化更新流程**，专注于客户端程序更新

## API接口设计

### 学校端新增接口

#### `GET /api/student/update/check`

**请求参数**: 无（学校端根据自己的版本判断）

**响应格式**:

```json
// 有更新时
{
    "code": "000000",
    "message": "发现新版本",
    "data": {
        "packageClientUpdateId": "update_id_123",
        "versionNumber": 25053001,
        "createTime": 1640995200000,
        "type": "student",
        "isPushed": true,
        "updateConfig": {
            "studentClientDownloadUrl": "/updates/student-client-v25053001.exe",
            "studentClientFileHash": "sha256hash...",
            "studentClientUpdateDes": "修复若干问题，提升稳定性"
        }
    }
}

// 无更新时
{
    "code": "000001",
    "message": "当前已是最新版本",
    "data": null
}
```

### 学校端实现逻辑

```go
func CheckStudentUpdate(c *gin.Context) {
    // 1. 获取当前学校端版本
    currentVersion, err := models.GetCurrentVersion()
    
    // 2. 从app_versions表中获取当前版本对应的学生端文件信息
    appVersion, err := models.GetAppVersionByVersion(currentVersion)
    
    // 3. 检查是否有学生端更新文件
    if appVersion == nil || appVersion.StudentClientPath == "" {
        // 没有更新
        return "000001"
    }
    
    // 4. 返回更新信息（相对路径）
    return updateInfo
}
```

## 学生端更新流程

### 1. 更新检查流程

```
学生端启动 → 5秒后检查更新 → 请求学校端API → 解析响应 → 判断是否有更新
```

### 2. 下载流程

```
发现更新 → 拼接完整下载URL → 下载exe文件 → 验证SHA256哈希 → 保存到updates目录
```

### 3. 更新流程

```
下载完成 → 调用外部更新器 → 替换当前exe → 重启程序 → 版本迁移
```

## 文件路径设计

### 学校端存储路径

```
backend/
├── updates/
│   ├── student-client-v25053001.exe    # 学生端exe文件
│   ├── student-client-v25053002.exe
│   └── ...
```

### 学校端API返回

- **相对路径**: `/updates/student-client-v25053001.exe`
- **学生端拼接**: `http://school-server:18080/updates/student-client-v25053001.exe`

### 学生端下载路径

```
student-client/
├── updates/
│   ├── student-client-v25053001.exe    # 下载的新版本
│   └── ...
```

## 数据库设计

### 学校端 app_versions 表

```sql
CREATE TABLE app_versions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    version TEXT NOT NULL UNIQUE,
    installed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_current BOOLEAN DEFAULT FALSE,
    school_client_path TEXT DEFAULT '',           -- 学校端文件路径
    web_dist_path TEXT DEFAULT '',               -- Web前端文件路径
    student_client_path TEXT DEFAULT '',         -- 学生端文件路径（相对路径）
    student_client_file_hash TEXT DEFAULT '',    -- 学生端文件哈希
    student_update_description TEXT DEFAULT '',  -- 学生端更新说明
    package_client_update_id TEXT DEFAULT ''     -- 中央端更新ID
);
```

### 学生端 app_versions 表

```sql
CREATE TABLE app_versions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    version TEXT NOT NULL UNIQUE,
    installed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_current BOOLEAN DEFAULT FALSE,
    student_client_path TEXT DEFAULT '',         -- 本地学生端文件路径
    student_client_file_hash TEXT DEFAULT '',    -- 学生端文件哈希
    student_update_description TEXT DEFAULT '',  -- 学生端更新说明
    package_client_update_id TEXT DEFAULT ''     -- 对应的更新ID
);
```

## 版本对应关系

### 设计原理

1. **学校端版本 = 学生端版本**: 确保兼容性
2. **学校端更新时**: 同时下载对应的学生端exe文件
3. **学生端检查更新**: 获取与学校端版本对应的学生端文件

### 示例场景

```
学校端当前版本: 25053001
├── 对应学生端文件: student-client-v25053001.exe
├── 文件哈希: abc123...
└── 更新说明: "修复若干问题"

学生端请求更新:
├── 请求: GET /api/student/update/check
├── 学校端查询: app_versions WHERE version='25053001'
└── 返回: 学生端文件信息
```

## 静态文件服务

### 学校端配置

```go
// main.go
r.Static("/updates", "./updates") // 提供更新文件的静态服务
```

### 访问URL

```
http://school-server:18080/updates/student-client-v25053001.exe
```

## 安全特性

### 1. 文件完整性验证

- **SHA256哈希验证**: 确保下载文件完整性
- **下载失败重试**: 网络问题时自动重试
- **文件大小检查**: 验证文件大小是否正确

### 2. 路径安全

- **相对路径限制**: 只允许访问updates目录下的文件
- **文件名验证**: 确保文件名符合预期格式
- **权限控制**: 学生端API无需认证，但有访问限制

## 错误处理

### 学校端错误

```json
{
    "code": 500,
    "message": "获取版本信息失败",
    "error": "database connection failed"
}
```

### 学生端错误处理

1. **网络错误**: 重试机制，最多重试3次
2. **哈希验证失败**: 重新下载文件
3. **文件权限错误**: 提示用户以管理员身份运行
4. **磁盘空间不足**: 清理临时文件后重试

## 日志记录

### 学校端日志

```
[INFO] [StudentController] [CheckStudentUpdate] 学生端请求更新检查
[INFO] [StudentController] [CheckStudentUpdate] 返回版本25053001的学生端更新信息
```

### 学生端日志

```
[INFO] [UpdateService] [CheckForUpdates] 开始检查程序更新
[INFO] [UpdateService] [CheckForUpdates] 发现新版本: 25052901 → 25053001
[INFO] [UpdateService] [DownloadUpdate] 开始下载更新文件
[INFO] [UpdateService] [DownloadUpdate] 文件哈希验证成功
[INFO] [UpdateService] [StartUpdate] 启动更新器，程序即将重启
```

## 测试验证

### 1. 功能测试

```bash
# 启动学校端
./school-server

# 启动学生端
./student-client

# 检查更新API
curl http://localhost:18080/api/student/update/check
```

### 2. 更新流程测试

1. **准备测试环境**: 学校端有新版本的学生端文件
2. **触发更新检查**: 学生端启动或手动检查
3. **验证下载**: 确认文件下载到updates目录
4. **验证哈希**: 确认文件哈希验证通过
5. **验证更新**: 确认更新器正常工作

## 总结

修正后的学生端更新机制具有以下特点：

✅ **简化API设计**: 学生端无需传递版本参数
✅ **版本自动对应**: 学校端根据自己版本返回对应学生端文件
✅ **相对路径设计**: 学校端返回相对路径，学生端拼接完整URL
✅ **专注exe更新**: 只更新Windows客户端，不涉及其他文件
✅ **安全可靠**: 哈希验证、错误处理、日志记录完整
✅ **易于维护**: 版本管理集中化，升级流程标准化

这个设计确保了学生端和学校端版本的一致性，同时简化了更新流程，提高了系统的可靠性和可维护性。
