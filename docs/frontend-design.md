# 学校端包管理系统 - 前端开发指南

## 1. 项目概述

学校端包管理系统是一个用于与中央云平台对接，同步和管理包版本的应用。前端部分负责提供直观的用户界面，使学校管理员能够方便地管理包版本、查看同步状态、浏览日志记录和配置系统参数。

### 1.1 技术栈

- **框架**: Vue 3
- **API风格**: 选项式API (Options API)
- **UI组件库**: Element Plus
- **HTTP客户端**: Axios
- **状态管理**: Vuex 4
- **路由管理**: Vue Router 4
- **构建工具**: Vite

### 1.2 目录结构

```
/frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/                # API请求模块
│   ├── assets/             # 资源文件(图片、样式等)
│   ├── components/         # 公共组件
│   │   ├── common/         # 通用组件
│   │   └── business/       # 业务组件
│   ├── layouts/            # 布局组件
│   ├── router/             # 路由配置
│   ├── store/              # Vuex状态管理
│   ├── utils/              # 工具函数
│   ├── views/              # 页面组件
│   ├── App.vue             # 根组件
│   └── main.js             # 入口文件
├── .env                    # 环境变量
├── .eslintrc.js            # ESLint配置
├── package.json            # 项目依赖
└── vite.config.js          # Vite配置
```

## 2. 后端API接口

前端需要与以下后端API接口进行交互：

### 2.1 认证相关

- `POST /api/auth/login`: 登录中央平台
- `GET /api/auth/status`: 获取认证状态
- `POST /api/user/login`: 用户登录
- `GET /api/user/info`: 获取用户信息
- `POST /api/user/logout`: 用户登出

### 2.2 包管理相关

- `GET /api/packages`: 获取包列表
- `GET /api/packages/:id`: 获取包详情
- `POST /api/packages/:id/sync`: 手动同步包
- `GET /api/packages/sync/status`: 获取同步状态

### 2.3 日志相关

- `GET /api/logs/sync`: 获取同步日志
- `GET /api/logs/system`: 获取系统日志
- `GET /api/logs/recent`: 获取最近的日志

### 2.4 配置相关

- `GET /api/config`: 获取配置
- `PUT /api/config`: 更新配置

### 2.5 系统相关

- `GET /api/system/status`: 获取系统状态

## 3. 页面与组件设计

### 3.1 页面结构

1. **登录页面** (`/login`)
   - 用户登录表单
   - 错误提示
   - 记住密码功能

2. **主布局** (包含以下所有页面的外层布局)
   - 顶部导航栏
   - 侧边菜单
   - 内容区域
   - 底部状态栏

3. **仪表盘** (`/dashboard`)
   - 系统状态概览
   - 包版本统计
   - 最近同步记录
   - 磁盘使用情况

4. **包版本列表** (`/packages`)
   - 包版本表格
   - 筛选和搜索功能
   - 同步状态显示
   - 操作按钮(查看详情、手动同步)

5. **包版本详情** (`/packages/:id`)
   - 包基本信息
   - 同步历史记录
   - 文件信息
   - 手动同步按钮

6. **同步日志** (`/logs/sync`)
   - 日志列表
   - 筛选和搜索功能
   - 日志详情查看

7. **系统日志** (`/logs/system`)
   - 日志列表
   - 筛选和搜索功能
   - 日志级别过滤

8. **系统设置** (`/settings`)
   - 同步设置表单
   - 存储设置表单
   - 系统参数设置

9. **用户信息** (`/profile`)
   - 用户基本信息
   - 修改密码表单

### 3.2 组件设计

#### 3.2.1 公共组件

1. **Header.vue**
   - 显示系统标题和logo
   - 用户信息和下拉菜单
   - 系统状态指示器

2. **Sidebar.vue**
   - 导航菜单
   - 折叠/展开功能
   - 当前路由高亮

3. **Footer.vue**
   - 版权信息
   - 系统版本

4. **Breadcrumb.vue**
   - 显示当前页面路径
   - 可点击导航

5. **SearchBar.vue**
   - 搜索输入框
   - 高级筛选选项

6. **Pagination.vue**
   - 分页控制
   - 每页显示数量选择

#### 3.2.2 业务组件

1. **PackageCard.vue**
   - 显示包版本基本信息
   - 同步状态标识
   - 操作按钮

2. **SyncStatusBadge.vue**
   - 显示同步状态(未同步、同步中、已同步、失败)
   - 不同状态使用不同颜色

3. **LogTable.vue**
   - 日志数据表格
   - 级别标识
   - 时间格式化

4. **SystemStatusPanel.vue**
   - 系统状态指标
   - 图表展示
   - 刷新按钮

5. **ConfigForm.vue**
   - 配置表单
   - 验证规则
   - 提交和重置按钮

## 4. 状态管理

使用Vuex管理全局状态，主要包括以下模块：

### 4.1 用户模块 (user.js)

- 存储用户信息和登录状态
- 处理登录、登出和获取用户信息
- 管理JWT令牌

### 4.2 包版本模块 (packages.js)

- 存储包版本列表和详情
- 处理包版本的获取和筛选
- 管理同步操作

### 4.3 日志模块 (logs.js)

- 存储同步日志和系统日志
- 处理日志的获取和筛选
- 管理日志刷新

### 4.4 配置模块 (config.js)

- 存储系统配置
- 处理配置的获取和更新

### 4.5 系统模块 (system.js)

- 存储系统状态信息
- 处理系统状态的获取
- 管理系统状态刷新

## 5. API请求封装

使用Axios封装API请求，创建以下API模块：

### 5.1 auth.js

```javascript
// 登录、登出、获取用户信息等认证相关API
```

### 5.2 packages.js

```javascript
// 获取包列表、包详情、触发同步等包管理相关API
```

### 5.3 logs.js

```javascript
// 获取同步日志、系统日志等日志相关API
```

### 5.4 config.js

```javascript
// 获取和更新配置等配置相关API
```

### 5.5 system.js

```javascript
// 获取系统状态等系统相关API
```

## 6. 路由配置

使用Vue Router配置路由，主要包括：

### 6.1 基础路由

- `/login`: 登录页面
- `/`: 重定向到仪表盘

### 6.2 需要认证的路由

- `/dashboard`: 仪表盘
- `/packages`: 包版本列表
- `/packages/:id`: 包版本详情
- `/logs/sync`: 同步日志
- `/logs/system`: 系统日志
- `/settings`: 系统设置
- `/profile`: 用户信息

### 6.3 路由守卫

- 检查用户是否已登录
- 未登录时重定向到登录页面
- 已登录时访问登录页面重定向到仪表盘

## 7. 实现要点

### 7.1 认证与授权

- 使用JWT进行身份验证
- 在Axios请求拦截器中添加Authorization头
- 处理令牌过期和刷新
- 实现自动登出

### 7.2 实时更新

- 使用轮询定期获取同步状态
- 在关键操作后自动刷新数据
- 提供手动刷新按钮

### 7.3 错误处理

- 全局错误处理
- 友好的错误提示
- 网络错误重试机制

### 7.4 响应式设计

- 适配不同屏幕尺寸
- 移动设备友好的交互
- 合理的布局调整

### 7.5 性能优化

- 组件懒加载
- 数据缓存
- 分页加载大量数据

## 8. 开发流程

### 8.1 环境搭建

1. 安装Node.js和npm
2. 创建Vue项目
3. 安装必要的依赖
4. 配置开发环境

### 8.2 开发步骤

1. 实现基础布局和路由
2. 开发公共组件
3. 实现认证功能
4. 开发各功能页面
5. 集成API请求
6. 实现状态管理
7. 添加错误处理
8. 优化用户体验

### 8.3 测试与部署

1. 单元测试关键组件
2. 端到端测试主要流程
3. 构建生产版本
4. 集成到后端服务

## 9. 设计规范

### 9.1 UI设计规范

- 遵循Element Plus的设计语言
- 主色调：#409EFF（蓝色）
- 辅助色：成功（#67C23A）、警告（#E6A23C）、危险（#F56C6C）、信息（#909399）
- 字体：系统默认字体，主要文字14px，次要文字12px
- 图标：使用Element Plus内置图标

### 9.2 代码规范

- 使用ESLint进行代码检查
- 遵循Vue官方风格指南
- 组件名使用PascalCase命名
- 属性和方法使用camelCase命名
- CSS使用BEM命名规范

## 10. 注意事项

1. 确保与后端API的正确交互
2. 处理大量数据时注意性能
3. 实现友好的错误提示
4. 注意用户体验和交互设计
5. 确保代码的可维护性和可扩展性

## 11. 参考资源

- [Vue 3官方文档](https://v3.vuejs.org/)
- [Element Plus文档](https://element-plus.org/)
- [Vuex 4文档](https://vuex.vuejs.org/)
- [Vue Router 4文档](https://router.vuejs.org/)
- [Axios文档](https://axios-http.com/)
