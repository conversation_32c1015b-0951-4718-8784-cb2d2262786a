# 学校端自动更新系统设计

## 系统概述

学校端自动更新系统采用**集中化版本管理 + 外部更新器**的架构，实现从中央服务器到学校端的完整自动更新流程，包括程序文件更新和数据库迁移。

## 核心架构

### 更新流程图
```
中央服务器 → 学校端检查更新 → 下载完整更新包 → 启动外部更新器 → 程序重启 → 版本迁移 → 正常运行
     ↓
学校端提供学生端更新服务
```

### 关键组件
1. **UpdateService** - 更新服务，负责检查、下载、启动更新
2. **VersionManager** - 版本管理器，负责数据库迁移
3. **外部更新器** - 独立程序，负责文件替换和程序重启
4. **集中化版本配置** - 统一管理所有版本信息

## 详细设计

### 1. 更新检查与下载

#### 启动时自动检查
```go
// main.go 启动流程
func main() {
    // 1. 初始化数据库
    models.InitDB()

    // 2. 版本检查和数据库迁移
    versionManager := utils.NewVersionManager(BuildVersion, logger, models.DB)
    versionManager.CheckAndMigrate()

    // 3. 初始化更新服务（自动检查更新）
    controllers.InitUpdateService(BuildVersion, logger)

    // 4. 启动Web服务
    startWebServer()
}
```

#### 更新检查逻辑
```go
// services/update_service.go
func (us *UpdateService) CheckForUpdates() (*UpdateInfo, error) {
    // 1. 调用中央API检查更新
    checkURL := fmt.Sprintf("%s/v1/packageSystem/packageClientUpdate/getLatestPushedVersion?currentVersionNumber=%d&type=school",
        us.centralAPIURL, currentVersionNumber)

    // 2. 解析响应，判断是否有更新
    if centralResp.Code == "000000" && centralResp.Data != nil {
        updateInfo.HasUpdate = true
        updateInfo.LatestVersion = fmt.Sprintf("%d", centralResp.Data.VersionNumber)
        // 根据操作系统选择下载URL
        if us.isWindows() {
            updateInfo.DownloadURL = centralResp.Data.UpdateConfig.SchoolClientWindowsDownloadUrl
        } else {
            updateInfo.DownloadURL = centralResp.Data.UpdateConfig.SchoolClientLinuxDownloadUrl
        }
    }

    return updateInfo, nil
}
```

#### 完整更新包下载
```go
func (us *UpdateService) DownloadCompleteUpdate(updateData *CentralUpdateData, version string) (*CompleteUpdateFiles, error) {
    // 1. 下载学校端可执行文件
    schoolClientPath, err := us.DownloadUpdateWithHash(schoolClientURL, schoolClientHash, version)

    // 2. 下载Web前端文件
    webDistPath, err := us.DownloadUpdateWithHash(webDistURL, webDistHash, version)

    // 3. 下载学生端可执行文件
    studentClientPath, err := us.DownloadUpdateWithHash(studentClientURL, studentClientHash, version)

    return &CompleteUpdateFiles{
        SchoolClientPath:  schoolClientPath,
        WebDistPath:       webDistPath,
        StudentClientPath: studentClientPath,
    }, nil
}
```

### 2. 外部更新器启动

#### 更新器调用
```go
func (us *UpdateService) StartUpdate(newExePath string) error {
    currentExe, _ := os.Executable()

    // 启动外部更新器
    cmd := exec.Command("./updater",
        "--type=school",
        "--old="+currentExe,
        "--new="+newExePath,
        "--restart")

    cmd.Start()

    // 退出当前程序，让更新器接管
    os.Exit(0)
}
```

#### 更新器设计要求
```bash
# 更新器命令行接口
./updater --type=school --old=./current.exe --new=./new.exe --restart

# 更新器职责
1. 等待原程序退出
2. 备份原程序文件
3. 替换为新程序文件
4. 重启新程序
5. 错误时回滚
```

### 3. 集中化版本管理

#### 版本配置文件
```go
// backend/utils/migrations/version_config.go
func GetVersionConfig() *VersionConfig {
    // ===== 新增版本时，只需要修改这里 =====

    // 1. 定义版本升级路径（按时间顺序，最后一个就是当前版本）
    versionPath := []string{
        "25052901", // 初始版本
        "25053001", // 第一次升级
        "25053002", // 第二次升级
        "25053003", // 第三次升级 - 当前版本
        // 新版本在这里继续添加...
    }

    // 2. 注册迁移实例（每个版本对应一个迁移）
    migrationMap := map[string]Migration{
        "25053001": &Migration_25052901_to_25053001{},
        "25053002": &Migration_25053001_to_25053002{},
        "25053003": &Migration_25053002_to_25053003{},
        // 新迁移在这里继续添加...
    }

    // ===== 修改结束 =====

    // 自动获取当前版本（versionPath的最后一个元素）
    currentVersion := versionPath[len(versionPath)-1]

    return &VersionConfig{
        CurrentVersion: currentVersion,
        VersionPath:    versionPath,
        MigrationMap:   migrationMap,
    }
}
```

#### 自动版本同步
```go
// backend/main.go
var BuildVersion = migrations.GetCurrentVersion() // 自动从配置获取
```

### 4. 数据库迁移系统

#### 迁移执行流程
```go
func (vm *VersionManager) CheckAndMigrate() error {
    // 1. 获取数据库当前版本
    dbVersion, _ := vm.getCurrentVersion()

    // 2. 比较程序版本和数据库版本
    if dbVersion == vm.currentVersion {
        return nil // 版本一致，无需迁移
    }

    // 3. 执行线性迁移
    return vm.executeLinearMigration(dbVersion, vm.currentVersion)
}

func (vm *VersionManager) executeLinearMigration(fromVersion, toVersion string) error {
    // 1. 创建数据库备份
    vm.backupDatabase()

    // 2. 获取需要执行的迁移
    migrationsToRun, _ := vm.getMigrationsToRun(fromVersion, toVersion)

    // 3. 逐步执行迁移
    for _, migration := range migrationsToRun {
        migration.Execute(vm.ctx)
        vm.setCurrentVersion(migration.Version())
        vm.addMigrationLog(fromVersion, migration.Version(), migration.Description(), "success")
    }

    return nil
}
```

#### 迁移类示例
```go
// backend/utils/migrations/migration_25053002_to_25053003.go
type Migration_25053002_to_25053003 struct{}

func (m *Migration_25053002_to_25053003) Execute(ctx *MigrationContext) error {
    // 1. 更新Web前端文件
    if err := UpdateWebDistFromVersion(ctx, "25053003"); err != nil {
        return err
    }

    // 2. 数据库结构变更
    sqls := []string{
        `ALTER TABLE packages ADD COLUMN new_field TEXT DEFAULT ''`,
        `CREATE INDEX IF NOT EXISTS idx_packages_new_field ON packages(new_field)`,
    }
    for _, sql := range sqls {
        ctx.DB.Exec(sql)
    }

    // 3. 文件操作
    os.MkdirAll("./config/v25053003", 0755)

    return nil
}

func (m *Migration_25053002_to_25053003) Rollback(ctx *MigrationContext) error {
    // 回滚操作
    return nil
}
```

### 5. 数据库表结构

#### 版本管理表
```sql
CREATE TABLE app_versions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    version TEXT NOT NULL UNIQUE,
    installed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_current BOOLEAN DEFAULT FALSE,
    school_client_path TEXT DEFAULT '',
    web_dist_path TEXT DEFAULT '',
    student_client_path TEXT DEFAULT '',
    student_client_url_suffix TEXT DEFAULT '',
    school_client_file_hash TEXT DEFAULT '',
    web_dist_file_hash TEXT DEFAULT '',
    student_client_file_hash TEXT DEFAULT '',
    school_update_description TEXT DEFAULT '',
    student_update_description TEXT DEFAULT '',
    package_client_update_id TEXT DEFAULT ''
);
```

#### 迁移日志表
```sql
CREATE TABLE migration_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    from_version TEXT NOT NULL,
    to_version TEXT NOT NULL,
    description TEXT NOT NULL,
    executed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TEXT NOT NULL,
    error_message TEXT
);
```

## 完整更新流程

### 自动更新流程
1. **程序启动** → 检查数据库版本 → 执行必要的迁移
2. **更新服务启动** → 立即检查中央服务器更新
3. **发现更新** → 下载完整更新包（学校端+Web前端+学生端）
4. **保存版本信息** → 将下载的文件信息保存到数据库
5. **启动更新器** → 传递新程序路径，退出当前程序
6. **更新器执行** → 备份→替换→重启
7. **程序重启** → 检测版本差异 → 执行线性迁移
8. **迁移完成** → 启动正常业务服务

### 手动更新流程
1. **前端触发** → 调用 `/api/update/check` 检查更新
2. **下载更新** → 调用 `/api/update/download-complete` 下载完整包
3. **启动更新** → 调用 `/api/update/auto` 启动自动更新
4. **后续流程** → 同自动更新流程

## 关键特性

### 1. 安全性保障
- **文件哈希验证** - 下载文件完整性校验
- **数据库备份** - 迁移前自动备份
- **回滚机制** - 更新失败时自动回滚
- **版本验证** - 防止无限循环更新

### 2. 跨版本升级
- **线性迁移** - 支持从任意老版本升级到最新版本
- **增量迁移** - 每个版本间的迁移独立执行
- **迁移日志** - 完整记录迁移过程和结果

### 3. 集中化管理
- **单点配置** - 所有版本信息集中在 `version_config.go`
- **自动同步** - 程序版本自动从配置获取
- **配置验证** - 内置配置一致性检查

### 4. 学生端服务
- **静态文件服务** - 通过 `/updates` 路径提供学生端下载
- **下载统计** - 记录学生端下载日志
- **版本同步** - 学生端自动获取最新版本

## 新增版本步骤

1. **创建迁移脚本** - `migrations/migration_XXX_to_YYY.go`
2. **更新版本配置** - 修改 `version_config.go` 中的配置
3. **测试验证** - 在测试环境验证迁移脚本
4. **部署发布** - 发布到中央服务器

就这么简单！系统会自动处理版本同步、迁移注册等所有细节。

## 技术实现细节

### 1. 中央API对接

#### 更新检查API
```
GET /v1/packageSystem/packageClientUpdate/getLatestPushedVersion
参数:
- currentVersionNumber: 当前版本号（数字格式）
- type: 客户端类型（school/student）

响应:
{
  "code": "000000",
  "data": {
    "packageClientUpdateId": "update_id",
    "versionNumber": 25053003,
    "updateConfig": {
      "schoolClientWindowsDownloadUrl": "https://...",
      "schoolClientLinuxDownloadUrl": "https://...",
      "webDistZipDownloadUrl": "https://...",
      "studentClientDownloadUrl": "https://...",
      "schoolClientWindowsFileHash": "sha256_hash",
      "schoolClientLinuxFileHash": "sha256_hash",
      "webDistZipFileHash": "sha256_hash",
      "studentClientFileHash": "sha256_hash",
      "schoolUpdateDes": "更新说明",
      "studentClientUpdateDes": "学生端更新说明"
    }
  }
}
```

### 2. 文件存储结构

#### 更新文件目录
```
./updates/
├── v25053001/
│   ├── school-package-system-windows.exe
│   ├── school-package-system-linux
│   ├── web-dist.zip
│   └── student-client.exe
├── v25053002/
│   ├── school-package-system-windows.exe
│   ├── school-package-system-linux
│   ├── web-dist.zip
│   └── student-client.exe
└── v25053003/
    ├── school-package-system-windows.exe
    ├── school-package-system-linux
    ├── web-dist.zip
    └── student-client.exe
```

#### 学生端下载URL
```
# 学生端通过以下URL下载更新
GET /updates/v{version}/student-client.exe
GET /api/student/update/check  # 获取最新版本信息
```

### 3. 错误处理机制

#### 下载失败处理
```go
func (us *UpdateService) DownloadUpdateWithHash(downloadURL, expectedHash, version string) (string, error) {
    // 1. 下载文件
    if err := us.downloadFile(downloadURL, tempFilePath); err != nil {
        return "", fmt.Errorf("下载文件失败: %v", err)
    }

    // 2. 验证文件哈希
    if expectedHash != "" {
        if err := us.verifyFileHash(tempFilePath, expectedHash); err != nil {
            os.Remove(tempFilePath) // 删除损坏的文件
            return "", fmt.Errorf("文件哈希验证失败: %v", err)
        }
    }

    return tempFilePath, nil
}
```

#### 迁移失败回滚
```go
func (vm *VersionManager) executeMigration(migration Migration) error {
    if err := migration.Execute(vm.ctx); err != nil {
        // 记录失败日志
        vm.addMigrationLog(fromVersion, migration.Version(),
            migration.Description(), "failed", err.Error())

        // 尝试回滚
        if rollbackErr := migration.Rollback(vm.ctx); rollbackErr != nil {
            vm.logger.Error("Migration", "Rollback",
                fmt.Sprintf("回滚失败: %v", rollbackErr))
        }

        return fmt.Errorf("迁移失败: %v", err)
    }

    return nil
}
```

### 4. 性能优化

#### 并发下载
```go
func (us *UpdateService) DownloadCompleteUpdate(updateData *CentralUpdateData, version string) (*CompleteUpdateFiles, error) {
    var wg sync.WaitGroup
    var mu sync.Mutex
    result := &CompleteUpdateFiles{}
    errors := make([]error, 0)

    // 并发下载三个文件
    downloadTasks := []struct {
        url, hash string
        target *string
    }{
        {schoolClientURL, schoolClientHash, &result.SchoolClientPath},
        {webDistURL, webDistHash, &result.WebDistPath},
        {studentClientURL, studentClientHash, &result.StudentClientPath},
    }

    for _, task := range downloadTasks {
        wg.Add(1)
        go func(t struct{url, hash string; target *string}) {
            defer wg.Done()
            path, err := us.DownloadUpdateWithHash(t.url, t.hash, version)
            mu.Lock()
            if err != nil {
                errors = append(errors, err)
            } else {
                *t.target = path
            }
            mu.Unlock()
        }(task)
    }

    wg.Wait()

    if len(errors) > 0 {
        return nil, errors[0]
    }

    return result, nil
}
```

#### 增量检查
```go
func (us *UpdateService) ScheduleUpdateCheck() {
    interval := 6 * time.Hour // 每6小时检查一次

    ticker := time.NewTicker(interval)
    go func() {
        for range ticker.C {
            // 避免频繁检查
            if time.Since(us.lastCheckTime) < time.Hour {
                continue
            }

            us.CheckAndAutoUpdate()
        }
    }()
}
```

### 5. 监控和日志

#### 更新状态监控
```go
// API: GET /api/update/status
func GetUpdateStatus(c *gin.Context) {
    status := gin.H{
        "currentVersion": BuildVersion,
        "lastCheckTime": UpdateServiceInstance.lastCheckTime,
        "isChecking": UpdateServiceInstance.isChecking,
        "autoUpdateEnabled": UpdateServiceInstance.autoUpdate,
    }

    // 检查是否有待处理的更新
    versions, _ := models.GetAllVersions()
    for _, v := range versions {
        if !v.IsCurrent && v.SchoolClientPath != "" {
            status["pendingUpdate"] = v
            break
        }
    }

    c.JSON(200, gin.H{"code": 200, "data": status})
}
```

#### 迁移日志查询
```go
// API: GET /api/update/migrations
func GetMigrationLogs(c *gin.Context) {
    limit := 50
    if l := c.Query("limit"); l != "" {
        if parsed, err := strconv.Atoi(l); err == nil {
            limit = parsed
        }
    }

    logs, err := models.GetMigrationLogs(limit)
    if err != nil {
        c.JSON(500, gin.H{"code": 500, "message": "获取迁移日志失败"})
        return
    }

    c.JSON(200, gin.H{"code": 200, "data": logs})
}
```

## 部署和运维

### 1. 初始部署
```bash
# 1. 部署学校端程序
./school-package-system

# 2. 确保更新器存在
./updater --help

# 3. 检查目录结构
ls -la ./updates/
ls -la ./web/dist/
ls -la ./data/
```

### 2. 版本发布流程
```bash
# 1. 开发环境测试
go run main.go

# 2. 构建发布版本
go build -o school-package-system main.go

# 3. 上传到中央服务器
# 4. 推送版本更新
# 5. 学校端自动检测并更新
```

### 3. 故障排查
```bash
# 查看更新日志
tail -f ./logs/app.log | grep "UpdateService\|Migration"

# 查看数据库版本
sqlite3 ./data/school-package.db "SELECT * FROM app_versions ORDER BY installed_at DESC;"

# 查看迁移日志
sqlite3 ./data/school-package.db "SELECT * FROM migration_logs ORDER BY executed_at DESC;"

# 手动触发更新检查
curl -X POST http://localhost:18080/api/update/auto
```

## 总结

学校端自动更新系统通过集中化版本管理、外部更新器、线性迁移等技术，实现了完整、安全、可靠的自动更新机制。系统具有以下优势：

1. **简化管理** - 集中化版本配置，新增版本只需修改一处
2. **安全可靠** - 完整的备份、验证、回滚机制
3. **跨版本升级** - 支持从任意版本升级到最新版本
4. **自动化程度高** - 启动时自动检查、下载、更新
5. **易于维护** - 清晰的代码结构和完整的日志记录

该系统为学校端提供了企业级的自动更新能力，确保软件始终保持最新状态。
