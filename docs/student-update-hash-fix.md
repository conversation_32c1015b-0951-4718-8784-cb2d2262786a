# 学生端更新哈希算法修正文档

## 问题描述

学生端在下载更新文件时出现哈希验证失败的错误。经过分析发现，学校端使用的是xxHash算法计算文件哈希，而学生端的更新服务使用的是SHA256算法，导致哈希值不匹配。

## 问题原因

### 1. 哈希算法不一致

**学校端（正确）**：
- 使用xxHash算法（`github.com/cespare/xxhash/v2`）
- 在`backend/utils/file.go`中实现
- 输出16位十六进制字符串格式

**学生端（错误）**：
- 更新服务使用SHA256算法（`crypto/sha256`）
- 在`services/update_service.go`的`calculateFileHash`方法中
- 输出64位十六进制字符串格式

### 2. 数据类型不匹配

**学校端API响应**：
```json
{
  "code": "000000",
  "data": {
    "versionNumber": "25053001",  // 字符串类型
    "updateConfig": {
      "studentClientFileHash": "fdbe6c7151715498"  // xxHash值
    }
  }
}
```

**学生端期望**：
- `versionNumber`被定义为`int64`类型
- 哈希验证使用SHA256算法

## 修正方案

### 1. 统一哈希算法

修改学生端更新服务，使用与学校端一致的xxHash算法：

```go
// 修改前（错误）
func (us *UpdateService) calculateFileHash(filePath string) (string, error) {
    file, err := os.Open(filePath)
    if err != nil {
        return "", err
    }
    defer file.Close()

    hash := sha256.New()  // 使用SHA256
    if _, err := io.Copy(hash, file); err != nil {
        return "", err
    }

    return fmt.Sprintf("%x", hash.Sum(nil)), nil  // 64位输出
}

// 修改后（正确）
func (us *UpdateService) calculateFileHash(filePath string) (string, error) {
    return utils.CalculateFileHash(filePath)  // 使用xxHash
}
```

### 2. 修正数据类型

修改`CentralUpdateData`结构体中的版本号类型：

```go
// 修改前
type CentralUpdateData struct {
    VersionNumber int64 `json:"versionNumber"`  // 错误：期望数字
    // ...
}

// 修改后
type CentralUpdateData struct {
    VersionNumber string `json:"versionNumber"`  // 正确：接收字符串
    // ...
}
```

### 3. 修正URL路径处理

处理学校端返回的Windows路径格式：

```go
// 构建完整URL
fullURL := downloadURL
if !strings.HasPrefix(downloadURL, "http") {
    // 处理Windows路径分隔符
    downloadURL = strings.ReplaceAll(downloadURL, "\\", "/")
    if !strings.HasPrefix(downloadURL, "/") {
        downloadURL = "/" + downloadURL
    }
    fullURL = us.schoolAPIURL + downloadURL
}
```

## 修改的文件

### 1. `student-client/services/update_service.go`

**主要修改**：
- 修改`CentralUpdateData.VersionNumber`类型从`int64`改为`string`
- 修改`calculateFileHash`方法使用`utils.CalculateFileHash`
- 移除`crypto/sha256`导入
- 添加URL路径处理逻辑
- 修正版本号处理逻辑

### 2. `student-client/app.go`

**主要修改**：
- 在`startup`方法中设置Wails上下文到更新服务
- 在`checkUpdateOnStartup`方法中添加详细的日志记录
- 使用`utils.LogPrintf`记录到日志文件

### 3. `student-client/frontend/src/views/Layout.vue`

**主要修改**：
- 添加更新状态显示组件
- 实现事件监听机制
- 添加更新状态的CSS样式
- 集成"立即更新"按钮

## 哈希算法对比

### xxHash算法特点

- **高性能**：比SHA256快数倍
- **一致性**：整个包系统统一使用
- **输出格式**：16位十六进制字符串
- **库依赖**：`github.com/cespare/xxhash/v2`

### SHA256算法特点

- **安全性**：密码学安全哈希
- **性能**：相对较慢
- **输出格式**：64位十六进制字符串
- **库依赖**：Go标准库`crypto/sha256`

## 验证方法

### 1. 手动验证

使用相同的文件在学校端和学生端分别计算哈希：

**学校端**：
```go
hash, err := utils.CalculateFileHash("/path/to/file")
// 输出：fdbe6c7151715498
```

**学生端（修正后）**：
```go
hash, err := utils.CalculateFileHash("/path/to/file")
// 输出：fdbe6c7151715498（相同）
```

### 2. 日志验证

查看学生端日志文件`logs/student-client-*.log`：

```
[INFO] [UpdateService] [checkUpdateOnStartup] 启动时检查更新
[INFO] [UpdateService] [checkUpdateOnStartup] 发现新版本: 25052901 → 25053001
[INFO] [UpdateService] [checkUpdateOnStartup] 开始下载更新文件: updates\student-client-v25053001-20250530152609.exe
[INFO] [UpdateService] [DownloadUpdate] 文件哈希验证: 期望=fdbe6c7151715498, 实际=fdbe6c7151715498
[INFO] [UpdateService] [DownloadUpdate] 文件哈希验证成功
[INFO] [UpdateService] [checkUpdateOnStartup] 更新文件下载完成: ./updates/student-client-v25053001.exe
```

## 测试步骤

### 1. 编译测试

```bash
cd student-client
go build -o student-client-test main.go app.go
```

### 2. 运行测试

启动学生端程序，观察：
- 界面右下角版本号显示
- 界面底部更新状态显示
- `logs/`目录下的日志文件
- `updates/`目录下的下载文件

### 3. 验证流程

1. **启动检查**：程序启动3秒后自动检查更新
2. **状态显示**：界面底部显示"正在检查程序更新..."
3. **发现更新**：显示"发现新版本: 25052901 → 25053001"
4. **下载进度**：显示"开始下载更新文件..."
5. **哈希验证**：使用xxHash验证文件完整性
6. **准备就绪**：显示"更新已准备就绪，是否立即重启更新？"
7. **用户确认**：点击"立即更新"按钮执行更新

## 注意事项

### 1. 依赖库

确保学生端已安装xxHash库：
```bash
go get github.com/cespare/xxhash/v2
```

### 2. 文件权限

确保`updates/`目录有写入权限：
```bash
chmod 755 ./updates
```

### 3. 网络连接

确保学生端能够访问学校端API：
```
http://[学校端IP]:18080/api/student/update/check
```

### 4. 更新器程序

确保更新器程序存在：
- Windows: `./updater.exe`
- Linux: `./updater`

## 总结

通过统一使用xxHash算法，学生端现在可以正确验证从学校端下载的更新文件。主要改进包括：

✅ **哈希算法统一**：学校端和学生端都使用xxHash
✅ **数据类型修正**：正确处理字符串类型的版本号
✅ **路径处理优化**：正确处理Windows路径格式
✅ **UI状态集成**：用户可以实时看到更新进度
✅ **日志记录完善**：详细的更新日志便于问题排查
✅ **用户体验提升**：提供更新确认机制和状态反馈

现在学生端的自动更新功能应该能够正常工作，用户可以在图形界面中看到完整的更新过程。
