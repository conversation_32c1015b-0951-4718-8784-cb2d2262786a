# 学校端包管理系统 - 后端设计文档

## 1. 开发背景

### 1.1 项目概述

学校端包管理系统是中央云管理平台包版本管理系统的配套客户端，旨在为学校提供一个便捷的方式来接收、同步和管理从中央平台推送的软件包。该系统采用Go语言开发后端，使用SQLite作为本地数据库，通过与中央平台的API交互，实现包版本的自动同步和管理。

### 1.2 系统定位

本系统作为中央云管理平台与学校本地环境之间的桥梁，主要负责：

1. 与中央平台进行身份认证
2. 定期检查并同步中央平台推送的包版本
3. 管理本地包文件存储
4. 为学生端提供包下载服务
5. 记录同步和操作日志

### 1.3 技术选型理由

- **Go语言**：高性能、跨平台、易于部署，适合开发需要长时间运行的服务
- **Gin框架**：轻量级Web框架，提供良好的路由和中间件支持
- **SQLite**：嵌入式数据库，无需额外安装数据库服务，便于部署和维护
- **跨平台支持**：可同时支持Windows和Linux环境，满足不同学校的部署需求

## 2. 系统架构

### 2.1 整体架构

```
+-------------------+      +-------------------+      +-------------------+
|                   |      |                   |      |                   |
|  中央云管理平台    | <--> |    学校端系统     | <--> |     学生客户端     |
|                   |      |                   |      |                   |
+-------------------+      +-------------------+      +-------------------+
                                    ^
                                    |
                                    v
                           +-------------------+
                           |                   |
                           |   本地文件存储    |
                           |                   |
                           +-------------------+
```

### 2.2 模块划分

1. **认证模块**：负责与中央平台的身份认证和令牌管理
2. **同步模块**：负责检查、下载和管理包版本
3. **存储模块**：负责本地文件的存储和管理
4. **API模块**：提供RESTful API接口，供前端调用
5. **日志模块**：记录系统操作和同步日志
6. **配置模块**：管理系统配置信息

## 3. 数据库设计

### 3.1 数据表设计

#### 3.1.1 配置表 (configs)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INTEGER | 主键 |
| key | TEXT | 配置键 |
| value | TEXT | 配置值 |
| description | TEXT | 配置描述 |
| updated_at | TIMESTAMP | 更新时间 |

#### 3.1.2 用户表 (users)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INTEGER | 主键 |
| username | TEXT | 用户名 |
| password | TEXT | 密码（加密存储） |
| school_id | TEXT | 学校ID |
| token | TEXT | 中央平台令牌 |
| token_expires_at | TIMESTAMP | 令牌过期时间 |
| last_login | TIMESTAMP | 最后登录时间 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

#### 3.1.3 包版本表 (package_versions)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INTEGER | 主键 |
| package_version_id | TEXT | 中央平台包版本ID |
| package_push_id | TEXT | 中央平台推送ID |
| experiment_id | TEXT | 实验ID |
| experiment_name | TEXT | 实验名称 |
| experiment_version_id | TEXT | 实验版本ID |
| version | TEXT | 版本号 |
| version_name | TEXT | 版本名称 |
| version_desc | TEXT | 版本描述 |
| file_hash | TEXT | 文件哈希值 |
| file_path | TEXT | 本地文件路径 |
| file_size | INTEGER | 文件大小 |
| download_url | TEXT | 中央平台下载URL |
| sync_status | INTEGER | 同步状态 (0:未同步, 1:同步中, 2:已同步, 3:同步失败) |
| download_count | INTEGER | 下载次数 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

#### 3.1.4 同步日志表 (sync_logs)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INTEGER | 主键 |
| package_version_id | TEXT | 包版本ID |
| package_push_id | TEXT | 推送ID |
| action | TEXT | 操作类型 (check, download, etc.) |
| status | INTEGER | 状态 (0:失败, 1:成功) |
| message | TEXT | 日志消息 |
| created_at | TIMESTAMP | 创建时间 |

#### 3.1.5 系统日志表 (system_logs)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INTEGER | 主键 |
| level | TEXT | 日志级别 (info, warn, error) |
| module | TEXT | 模块名称 |
| action | TEXT | 操作类型 |
| message | TEXT | 日志消息 |
| created_at | TIMESTAMP | 创建时间 |

## 4. API设计

### 4.1 内部API (与中央平台交互)

#### 4.1.1 认证API

- `POST /api/auth/login`: 登录中央平台
- `GET /api/auth/status`: 获取认证状态

#### 4.1.2 同步API

- `GET /api/sync/check`: 检查新的推送
- `POST /api/sync/download`: 下载包文件
- `POST /api/sync/update-status`: 更新同步状态

### 4.2 外部API (提供给前端)

#### 4.2.1 用户API

- `POST /api/user/login`: 用户登录
- `GET /api/user/info`: 获取用户信息
- `POST /api/user/logout`: 用户登出

#### 4.2.2 包管理API

- `GET /api/packages`: 获取包列表
- `GET /api/packages/:id`: 获取包详情
- `POST /api/packages/:id/sync`: 手动同步包

#### 4.2.3 日志API

- `GET /api/logs/sync`: 获取同步日志
- `GET /api/logs/system`: 获取系统日志

#### 4.2.4 配置API

- `GET /api/config`: 获取配置
- `PUT /api/config`: 更新配置

## 5. 开发规划

### 5.1 开发环境

- Go 1.18+
- SQLite 3
- Git 版本控制

### 5.2 开发阶段

1. **基础架构搭建**：项目结构、数据库初始化、配置管理
2. **认证模块开发**：实现与中央平台的认证
3. **同步模块开发**：实现包版本检查和下载
4. **API模块开发**：实现RESTful API接口
5. **日志模块开发**：实现日志记录和查询
6. **集成测试**：与前端和中央平台的集成测试
7. **打包部署**：为Windows和Linux平台构建可执行文件

### 5.3 技术难点

1. 文件下载的断点续传和完整性验证
2. 定时任务的可靠执行和状态管理
3. 跨平台兼容性保证
4. 与中央平台的安全通信

## 6. 部署说明

### 6.1 系统要求

- Windows 7+ 或 Linux (Ubuntu 18.04+, CentOS 7+)
- 最小硬盘空间: 500MB (不含包文件存储)
- 最小内存: 512MB

### 6.2 部署步骤

1. 解压程序包到目标目录
2. 修改 `config/config.yaml` 配置文件
3. 运行可执行文件 `./school-package-system`
4. 访问 `http://localhost:8080` 进入管理界面

### 6.3 配置说明

详见 `config/config.yaml` 文件中的注释说明。
