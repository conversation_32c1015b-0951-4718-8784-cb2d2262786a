# 跨平台编译指南

本文档记录了如何在不同操作系统上编译学生端应用程序的方法，特别是从macOS交叉编译Windows应用程序的步骤。

## 从macOS交叉编译Windows应用程序

### 前提条件

1. 安装Go (1.18或更高版本)
2. 安装Wails CLI
3. 安装MinGW-w64 (用于交叉编译)

```bash
# 安装MinGW-w64
brew install mingw-w64
```

### 成功的编译命令

以下命令已被验证可以在macOS上成功交叉编译Windows应用程序：

```bash
env GOOS=windows GOARCH=amd64 CGO_ENABLED=1 CC=x86_64-w64-mingw32-gcc CXX=x86_64-w64-mingw32-g++ CGO_CXXFLAGS="-IC:\msys64\mingw64\include" wails build -ldflags '-extldflags "-static"' -skipbindings
```

### 命令解析

1. **环境变量设置**:
   - `GOOS=windows`: 设置目标操作系统为Windows
   - `GOARCH=amd64`: 设置目标架构为64位
   - `CGO_ENABLED=1`: 启用CGO，允许Go代码调用C代码
   - `CC=x86_64-w64-mingw32-gcc`: 设置C编译器为MinGW的GCC
   - `CXX=x86_64-w64-mingw32-g++`: 设置C++编译器为MinGW的G++
   - `CGO_CXXFLAGS="-IC:\msys64\mingw64\include"`: 设置C++编译器的额外标志

2. **Wails构建命令**:
   - `wails build`: Wails的构建命令
   - `-ldflags '-extldflags "-static"'`: 链接标志，指定静态链接
   - `-skipbindings`: 跳过生成绑定

3. **关于 `-IC:\msys64\mingw64\include`**:
   - `-I` 是编译器标志，用于指定包含文件的搜索路径
   - `C:\msys64\mingw64\include` 是Windows上MSYS2/MinGW的包含文件目录
   - 这个标志告诉编译器在交叉编译时去哪里查找Windows平台的头文件
   - 虽然这个路径看起来是Windows格式的，但在macOS上的交叉编译环境中它被正确解释

### 为什么需要 `-skipbindings`

`-skipbindings` 参数告诉Wails跳过生成前端和后端之间的绑定代码。在交叉编译环境中，绑定生成器可能会遇到问题，因为它需要在目标平台上执行。通过跳过这一步，我们避免了这个潜在的问题。

在使用这个参数之前，确保你已经在开发环境中生成了绑定，或者你的项目不依赖于自动生成的绑定。

### 静态链接的重要性

命令中的 `-ldflags '-extldflags "-static"'` 参数指定了静态链接，这意味着所有依赖库都会被包含在最终的可执行文件中。这对于分发应用程序非常重要，因为它减少了对目标系统上已安装库的依赖。

### 常见问题及解决方案

1. **找不到头文件**:
   如果编译器报错找不到某些头文件，可能需要调整 `CGO_CXXFLAGS` 中的包含路径。

2. **链接错误**:
   如果遇到链接错误，可能需要添加额外的库路径，例如:
   ```bash
   CGO_LDFLAGS="-LC:\msys64\mingw64\lib"
   ```

3. **绑定生成错误**:
   如果即使使用 `-skipbindings` 仍然遇到绑定相关错误，可以尝试先在开发环境中运行 `wails generate bindings`，然后再进行交叉编译。

## 其他交叉编译方法

### 使用Docker

如果直接交叉编译遇到困难，可以考虑使用Docker:

```bash
docker run --rm -v $(pwd):/app -w /app wailsapp/wails:latest wails build -platform windows/amd64
```

### 使用GitHub Actions

可以设置GitHub Actions工作流来自动构建不同平台的应用程序:

```yaml
name: Build
on: [push, pull_request]
jobs:
  build:
    strategy:
      matrix:
        os: [windows-latest, macos-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-go@v2
        with:
          go-version: '1.18'
      - name: Install Wails
        run: go install github.com/wailsapp/wails/v2/cmd/wails@latest
      - name: Build
        run: wails build
      - name: Upload artifacts
        uses: actions/upload-artifact@v2
        with:
          name: app-${{ matrix.os }}
          path: build/bin/*
```

## 参考资料

- [Wails官方文档](https://wails.io/docs/guides/cross-compilation)
- [Go交叉编译文档](https://golang.org/doc/install/source#environment)
- [MinGW-w64文档](https://www.mingw-w64.org/)
