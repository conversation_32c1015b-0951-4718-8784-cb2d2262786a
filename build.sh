#!/bin/bash

# 学校端包管理系统完整构建脚本
# 使用方法：在项目根目录下执行 ./build.sh

set -e

echo "=== 学校端包管理系统完整构建 ==="
echo ""

# 检查是否在正确的目录
if [ ! -d "frontend" ] || [ ! -d "backend" ]; then
    echo "错误：请在项目根目录下执行此脚本"
    echo "当前目录应包含 frontend 和 backend 目录"
    exit 1
fi

# 检查Node.js和npm
if ! command -v node &> /dev/null; then
    echo "错误：未找到 Node.js"
    echo "请先安装 Node.js"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "错误：未找到 npm"
    echo "请先安装 npm"
    exit 1
fi

# 检查Go
if ! command -v go &> /dev/null; then
    echo "错误：未找到 Go"
    echo "请先安装 Go"
    exit 1
fi

# 构建前端
echo "1. 构建前端..."
cd frontend

echo "   安装前端依赖..."
npm install

echo "   构建前端项目..."
npm run build

# 检查前端构建是否成功（前端配置为直接输出到 ../backend/web/dist）
if [ ! -d "backend/web/dist" ]; then
    echo "错误：前端构建失败，未找到 backend/web/dist 目录"
    exit 1
fi

echo "   前端构建完成 ✓"
cd ..

# 构建后端
echo ""
echo "2. 构建后端..."
cd backend

# 检查操作系统并选择构建方式
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    echo "   检测到 macOS，构建 Windows 版本需要交叉编译..."

    # 检查是否安装了mingw-w64
    if ! command -v x86_64-w64-mingw32-gcc &> /dev/null; then
        echo "   警告：未找到 x86_64-w64-mingw32-gcc"
        echo "   如需构建 Windows 版本，请安装 mingw-w64："
        echo "   brew install mingw-w64"
        echo ""
        echo "   构建 macOS 版本..."
        go build -ldflags "-s -w" -o school-package-system .
    else
        echo "   构建 Windows 版本..."
        env GOOS=windows GOARCH=amd64 CGO_ENABLED=1 CC=x86_64-w64-mingw32-gcc \
            go build -ldflags "-s -w" -o school-package-system.exe .
    fi
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    echo "   检测到 Linux，构建本地版本..."
    go build -ldflags "-s -w" -o school-package-system .
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
    # Windows
    echo "   检测到 Windows，构建本地版本..."
    go build -ldflags "-s -w" -o school-package-system.exe .
else
    echo "   未知操作系统，尝试构建本地版本..."
    go build -ldflags "-s -w" -o school-package-system .
fi

echo "   后端构建完成 ✓"

cd ..

echo ""
echo "=== 构建完成 ==="
echo ""
echo "输出文件位置："
if [ -f "backend/school-package-system.exe" ]; then
    echo "  - Windows 可执行文件: backend/school-package-system.exe"
elif [ -f "backend/school-package-system" ]; then
    echo "  - 可执行文件: backend/school-package-system"
fi
echo "  - 前端文件: backend/web/dist/"
echo ""
echo "运行说明："
echo "1. 进入 backend 目录"
echo "2. 配置 .env 文件"
echo "3. 运行可执行文件"
echo ""
echo "如需创建完整的分发包，请运行："
echo "  cd backend && ./build-windows.sh"
