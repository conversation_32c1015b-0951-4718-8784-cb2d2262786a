# 学校端包管理系统完整构建脚本 (PowerShell版本)
# 使用方法：在项目根目录下执行 .\build.ps1

Write-Host "=== 学校端包管理系统完整构建 ===" -ForegroundColor Green
Write-Host ""

# 检查是否在正确的目录
if (-not (Test-Path "frontend") -or -not (Test-Path "backend")) {
    Write-Host "错误：请在项目根目录下执行此脚本" -ForegroundColor Red
    Write-Host "当前目录应包含 frontend 和 backend 目录" -ForegroundColor Red
    exit 1
}

# 检查Node.js和npm
if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
    Write-Host "错误：未找到 Node.js" -ForegroundColor Red
    Write-Host "请先安装 Node.js" -ForegroundColor Red
    exit 1
}

if (-not (Get-Command npm -ErrorAction SilentlyContinue)) {
    Write-Host "错误：未找到 npm" -ForegroundColor Red
    Write-Host "请先安装 npm" -ForegroundColor Red
    exit 1
}

# 检查Go
if (-not (Get-Command go -ErrorAction SilentlyContinue)) {
    Write-Host "错误：未找到 Go" -ForegroundColor Red
    Write-Host "请先安装 Go" -ForegroundColor Red
    exit 1
}

# 构建前端
Write-Host "1. 构建前端..." -ForegroundColor Yellow
Push-Location frontend

Write-Host "   安装前端依赖..." -ForegroundColor Gray
npm install

Write-Host "   构建前端项目..." -ForegroundColor Gray
npm run build

# 检查前端构建是否成功（前端配置为直接输出到 ../backend/web/dist）
if (-not (Test-Path "backend\web\dist")) {
    Write-Host "错误：前端构建失败，未找到 backend\web\dist 目录" -ForegroundColor Red
    exit 1
}

Write-Host "   前端构建完成 ✓" -ForegroundColor Green
Pop-Location

# 构建后端
Write-Host ""
Write-Host "2. 构建后端..." -ForegroundColor Yellow
Push-Location backend

Write-Host "   检测到 Windows，构建本地版本..." -ForegroundColor Gray
$env:GOOS="windows"; $env:GOARCH="amd64"; $env:CGO_ENABLED="1"; $env:CC="gcc"
go build -ldflags "-s -w" -o "school-package-system.exe" .

if ($LASTEXITCODE -eq 0) {
    Write-Host "   后端构建完成 ✓" -ForegroundColor Green
} else {
    Write-Host "   后端构建失败！" -ForegroundColor Red
    exit 1
}

Pop-Location

Write-Host ""
Write-Host "=== 构建完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "输出文件位置：" -ForegroundColor White
if (Test-Path "backend\school-package-system.exe") {
    Write-Host "  - Windows 可执行文件: backend\school-package-system.exe" -ForegroundColor Cyan
}
Write-Host "  - 前端文件: backend\web\dist\" -ForegroundColor Cyan
Write-Host ""
Write-Host "运行说明：" -ForegroundColor White
Write-Host "1. 进入 backend 目录" -ForegroundColor Gray
Write-Host "2. 配置 .env 文件" -ForegroundColor Gray
Write-Host "3. 运行可执行文件" -ForegroundColor Gray
Write-Host ""
Write-Host "如需创建完整的分发包，请运行：" -ForegroundColor White
Write-Host "  cd backend && .\build-windows.ps1" -ForegroundColor Cyan
